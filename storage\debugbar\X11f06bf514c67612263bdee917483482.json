{"__meta": {"id": "X11f06bf514c67612263bdee917483482", "datetime": "2025-06-26 22:42:39", "utime": 1750977759.015533, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.564849, "end": 1750977759.015548, "duration": 0.4506990909576416, "duration_str": "451ms", "measures": [{"label": "Booting", "start": **********.564849, "relative_start": 0, "end": **********.941678, "relative_end": **********.941678, "duration": 0.3768291473388672, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.941686, "relative_start": 0.37683701515197754, "end": 1750977759.01555, "relative_end": 1.9073486328125e-06, "duration": 0.07386398315429688, "duration_str": "73.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042280, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022019999999999998, "accumulated_duration_str": "22.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.970343, "duration": 0.02104, "duration_str": "21.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.55}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750977759.0021079, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.55, "width_percent": 1.953}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750977759.0080109, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.502, "width_percent": 2.498}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-729864858 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-729864858\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2045372468 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2045372468\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-484204808 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484204808\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-361968603 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977756448%7C12%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InV2UEIxcUk3NmNHNXhONDE4WkhkR2c9PSIsInZhbHVlIjoiN3h6YTNVTm1HWGNZeVdQQ0tOVDBPYlJadEgyODJzQ28zelJxV0lBRDNjL1M3R3l3VjJSOGdBc0JreE1WYk9nRlhnWGNvYXpvd3V0Z1VsWi9KaUVtQks1dHZZUDMwRGZCTHdxaVVSNXNvdVdBTDNnSVluZTIySVAxUndBRXc1ZkxSMVgwL3N3bmtNcEpyRGZxWWhDS21YMkFRa3dDZkJDTTh0NEQ0MDZJZmgyeG0zNGMzaEprUU9EZXc1UnMyY2laRjFhSTd1SXY3REkrR1Q4R2U4SFZMelFwOXNMd1JQMnJwdHBVZDVVL2VRcktjUnArcVdMM1dBMHVDY2NZcDNGcTU2ZUQydDQvL2ZkZWdCNlZhcUIyOXowS3ovVHlqL1ZoUVpnQndpazI1alI0RzJDWGl3M2lRWmkvYjB1OVQ1eU01NVFJd1BQRStFZlpSVFpKNVRSMVVpL1NLNWhCN0t5T2tNY0JQbzUvRkkzT2F3QVZSaEFUalExRFVRSGt4aDNzbjdBaFBTRVY1SzN6ZzJxWjBiMTJrenVUYnBTSGVHelNzQmxSc0lpTm5ZUkdnM0wwUTVDbFdXM0FSa1EvYkU4enY1MVdtYjkrdTM4c0JFcnFlVklFZGt4dGk2b2V6aXIrUG81Wjhvc05qVUpQbVJnc1pYYjJqSzVmMXpvdmlMWWQiLCJtYWMiOiJhYjFmMWQyNjUwYmJkMWMxYjQ5NDkxMjg4ZGRmYWNiOGYzN2U4NDkxNzk4NzdjZmZiNThiOGM0NmMyZmZiOThmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpNQmxHVkN4ZlJmQ1lHNXIwdHdkSVE9PSIsInZhbHVlIjoiUS9waHd1QTN0TTRjRnhpSmNYZmJ5Wk82STBlWTF6Y1VUbFlNQ01JS2pQQ1lEVFNhWUQzTnJjNUJ2UWYxM203WXIvSEU5bzFYSGYyZThhTVY3QTM2MnJwWDF6UE9NZXludkF1YUN6RE5nQ2pPd2RRdnRPSFpSQ0RHNFdYcmYyT01NT2pqbFFwRXgzUWI4YTBqL3RlMGwvMXVPbngza2ZhVFduU08wZEZob1AvNWVTU3Z0dERaQUdvNjBRb2NIaFZuaUV4bCtGdUlOZGJnaDE5TEcvUEdGQzBhbFhHb3RkKzNTeDhjaTdHenZBNGpOaDRVWHQwb3VoeHpsZU5iTjZiRzF5Q3R1emMzSjdTMCthbDdpOEVleWkzOFJTRFhHaURpalF3SmVqZWs5cTNPRmJVaUd2dVNKS00vT0t6aUtwQlhtQzZXNXZkaFc1b1hmdVdwUjVTYmkwNlVaYkhkRmxZR2VxN2c0bGQ0Z2k2TUtNUmhac0dTcHNYaWhBem1lZFVtREVPd2Q2c1RYY3dYS1RaczJlV3NUUVhMU2NRVEZQR29GdjhGd0xPL2NjYm9xWmNSaWN6czNGck1KWkVoSi84Uy8wWlJ2UGx2ZWFFR2pJMlhzUkh4RWFMV2IyTngyRGRlelNWUlpXbjlXTmR6NTUvdkJvYzJaTHRDa0g2ZWF1elMiLCJtYWMiOiI0MDhiYTgxNmU0MzQ1YTEyYzEyODBjZDQxYWQ3NDkxODcyYTA3NTJlZDlhYmM5ZDdjYTMxMDUwZmNiMGM1OTNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-361968603\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-791563264 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791563264\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:42:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdQRVEwVTltVXlSRjdaeUhPelBGM0E9PSIsInZhbHVlIjoiR1Vkc2kxKzVtbmIyVTF4QzFlbC9kYXJMUEx5a2JxazdqM0pFRit1VTUyTkhSYWJjd0E0V3c0TXVFdXhmTnArRzY5cTROd08xZlRZbkJmQVYrckV5ekdHQUNEOE03VWhQMzh0RmNuYWp1N0p1OGY0WlhMdXphZ1BmbkowZWdJL2JXc0RscmFid0thNmtVUTgzQTcvbnpUR3ZwVUxRZndscnFDTmo5WFlLVUV6N0pQVnE2U2Zyb0wvMzUzS25TSmJLcnhFQUhGcDJMbFNMMXhZVldtU25DbmcwVUtFUTYyTGdONXh5endUSktiUEZSSkxrTlhoMXlxVU5TUUo4UEJwMklTWUZDdkkwOEpUbXd6Uk1UeXZMbFg5UXRHU3BLZ1gzcEtZcXRSY0w1U2puM0FhOHRJQzFDK1d2dEtINHdyaHZPWXBjL0h3dWZhOXlZMzh0OU5qclZMK0lxREk4WmhGNXBRWXpqa3dTTXo0SXc2ZzFJYSsrdzJlWGRUVitVNEt0TGFHQVJLZ2lZeUl0MHdBSnNxQUF6UktONzlMby9kKzNQR1lqckJra3V1cUUxa2JSYTlReTczejlZb3JpTUgxTmcyQ09MdCtOcHNwNHBJSFlZamkrU0ViMkJ3MXhTL0laT0RraFNnNXI0TUZValN4MVVMa2Q0TWNhMUNKWXdNaTIiLCJtYWMiOiIwOGJjN2Y1ZTM3MWFhYjZiNTE5ZjYyM2RmZTU1YTJjYjQ3MGFhZDhhNjM0YjIyMmQ1NmRjN2EyNTM5NzZkMmEyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNlQjEyS1NOZkV0VjNPNEdwQ3pLYWc9PSIsInZhbHVlIjoiREdKSklDMk9RNnovSk5UbVo1QjlPMVhiam9ncEF0MHV0eDczem1WZ09oOG94Smo2d211N2c0RWs5ODFXTkduQXc0ZTJ6YzN2MkR0ekpzbmFTamlYYUVjeVduVkp2aWNrdmRnTkFxMkdZOXhNM0J6VW5FUFkvblk2TWd0dkhWWm1WTVB4b2V0Qy9JRHJLNHA3MStwbjBpZlVNU25vVExvQUZSdFRQRDk1a3pUUm9LZkU1aU1RWDhyWjFvZ1pmM1Jwb1hrWUpTa21jMFZxdERTUXFyeCtrRmpOeFhrZWU2L2NRQXpjWUY5bFZ6Z3VDRUppaUtFNVF3Ly9VQjdaVnI0ZUVXSXY0Vi9IdmRvbjVackQwZjRPc1RlRWdTRkxFYUJ2RThZRDY2KzY5emI1MGtLVzkxa2lPV0dHNGJZemdpTFUxRnJIQ1ZYR3diekNVSlRjb1VDYU9jMnB3cDNtUGZnMU12cHBnQW9XMlBhbkUwb2xQcTNlVXYyTjVtYXhEUjM2WWRTcGhrblZtb2NvcEZHd1FkbjJUTjdmRFhYYWYzUEQ5cXI1Zk5ZS3Z4OHcxelYzZVlNMEgxZ1F3MkV2aXNISy9UUzU4T05JaHBNUEg5U04rbC9sOFVsakhjeHBkV2VxdHFjbFRRZ2hDUjRscHdJcXdXaHNVNjdVNHNVQmtXMTIiLCJtYWMiOiJjNjAxZjY3MDQxOWQ5YTQ4N2UyYmUzNDlkZGFlYThiMjQ1MjQxNjM5N2M2NjJiMGFjYmZlZjQ2Y2Q0Y2IxOTFiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdQRVEwVTltVXlSRjdaeUhPelBGM0E9PSIsInZhbHVlIjoiR1Vkc2kxKzVtbmIyVTF4QzFlbC9kYXJMUEx5a2JxazdqM0pFRit1VTUyTkhSYWJjd0E0V3c0TXVFdXhmTnArRzY5cTROd08xZlRZbkJmQVYrckV5ekdHQUNEOE03VWhQMzh0RmNuYWp1N0p1OGY0WlhMdXphZ1BmbkowZWdJL2JXc0RscmFid0thNmtVUTgzQTcvbnpUR3ZwVUxRZndscnFDTmo5WFlLVUV6N0pQVnE2U2Zyb0wvMzUzS25TSmJLcnhFQUhGcDJMbFNMMXhZVldtU25DbmcwVUtFUTYyTGdONXh5endUSktiUEZSSkxrTlhoMXlxVU5TUUo4UEJwMklTWUZDdkkwOEpUbXd6Uk1UeXZMbFg5UXRHU3BLZ1gzcEtZcXRSY0w1U2puM0FhOHRJQzFDK1d2dEtINHdyaHZPWXBjL0h3dWZhOXlZMzh0OU5qclZMK0lxREk4WmhGNXBRWXpqa3dTTXo0SXc2ZzFJYSsrdzJlWGRUVitVNEt0TGFHQVJLZ2lZeUl0MHdBSnNxQUF6UktONzlMby9kKzNQR1lqckJra3V1cUUxa2JSYTlReTczejlZb3JpTUgxTmcyQ09MdCtOcHNwNHBJSFlZamkrU0ViMkJ3MXhTL0laT0RraFNnNXI0TUZValN4MVVMa2Q0TWNhMUNKWXdNaTIiLCJtYWMiOiIwOGJjN2Y1ZTM3MWFhYjZiNTE5ZjYyM2RmZTU1YTJjYjQ3MGFhZDhhNjM0YjIyMmQ1NmRjN2EyNTM5NzZkMmEyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNlQjEyS1NOZkV0VjNPNEdwQ3pLYWc9PSIsInZhbHVlIjoiREdKSklDMk9RNnovSk5UbVo1QjlPMVhiam9ncEF0MHV0eDczem1WZ09oOG94Smo2d211N2c0RWs5ODFXTkduQXc0ZTJ6YzN2MkR0ekpzbmFTamlYYUVjeVduVkp2aWNrdmRnTkFxMkdZOXhNM0J6VW5FUFkvblk2TWd0dkhWWm1WTVB4b2V0Qy9JRHJLNHA3MStwbjBpZlVNU25vVExvQUZSdFRQRDk1a3pUUm9LZkU1aU1RWDhyWjFvZ1pmM1Jwb1hrWUpTa21jMFZxdERTUXFyeCtrRmpOeFhrZWU2L2NRQXpjWUY5bFZ6Z3VDRUppaUtFNVF3Ly9VQjdaVnI0ZUVXSXY0Vi9IdmRvbjVackQwZjRPc1RlRWdTRkxFYUJ2RThZRDY2KzY5emI1MGtLVzkxa2lPV0dHNGJZemdpTFUxRnJIQ1ZYR3diekNVSlRjb1VDYU9jMnB3cDNtUGZnMU12cHBnQW9XMlBhbkUwb2xQcTNlVXYyTjVtYXhEUjM2WWRTcGhrblZtb2NvcEZHd1FkbjJUTjdmRFhYYWYzUEQ5cXI1Zk5ZS3Z4OHcxelYzZVlNMEgxZ1F3MkV2aXNISy9UUzU4T05JaHBNUEg5U04rbC9sOFVsakhjeHBkV2VxdHFjbFRRZ2hDUjRscHdJcXdXaHNVNjdVNHNVQmtXMTIiLCJtYWMiOiJjNjAxZjY3MDQxOWQ5YTQ4N2UyYmUzNDlkZGFlYThiMjQ1MjQxNjM5N2M2NjJiMGFjYmZlZjQ2Y2Q0Y2IxOTFiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2119221881 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119221881\", {\"maxDepth\":0})</script>\n"}}