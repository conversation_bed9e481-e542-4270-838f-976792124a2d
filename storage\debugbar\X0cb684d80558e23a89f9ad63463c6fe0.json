{"__meta": {"id": "X0cb684d80558e23a89f9ad63463c6fe0", "datetime": "2025-06-26 23:20:42", "utime": **********.067827, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980041.626667, "end": **********.067844, "duration": 0.4411768913269043, "duration_str": "441ms", "measures": [{"label": "Booting", "start": 1750980041.626667, "relative_start": 0, "end": **********.000496, "relative_end": **********.000496, "duration": 0.3738288879394531, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.000505, "relative_start": 0.373837947845459, "end": **********.067846, "relative_end": 2.1457672119140625e-06, "duration": 0.06734108924865723, "duration_str": "67.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028192, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01542, "accumulated_duration_str": "15.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0278528, "duration": 0.01448, "duration_str": "14.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.904}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0541651, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.904, "width_percent": 3.048}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0601392, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.952, "width_percent": 3.048}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget/eyJpdiI6IlVLU3pDQ0FXT05GSVJUaDFlS0wrZ3c9PSIsInZhbHVlIjoiZGxUbHd6R2RaaDR4bzhuTHZSeEY4dz09IiwibWFjIjoiZTFiMDZlMzAzYWQ1ZDhlMDliOTc1MzRkYjRkZGE3ZmUzMzRkMGI2YjQ1ZDI1YzdmZDQ4Mjg2ZmJkYjk0NTUwZiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-874770279 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-874770279\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-210188541 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-210188541\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1620278136 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620278136\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1651050265 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/budget/eyJpdiI6IlVLU3pDQ0FXT05GSVJUaDFlS0wrZ3c9PSIsInZhbHVlIjoiZGxUbHd6R2RaaDR4bzhuTHZSeEY4dz09IiwibWFjIjoiZTFiMDZlMzAzYWQ1ZDhlMDliOTc1MzRkYjRkZGE3ZmUzMzRkMGI2YjQ1ZDI1YzdmZDQ4Mjg2ZmJkYjk0NTUwZiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980037648%7C17%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im0vWmo4ZWFsWWxnSjQ5R3k2TVJYZ1E9PSIsInZhbHVlIjoiUVhZOXlMM2JOQjQ4U0NKTjJWOFNOMWFvblhuSmpicFFsOUYwcXNkRFdRdXZrNjBvVEM2aGF1Y215QjEwQjE0YUJKTDIrUHRhWG9takMybHpOQ2NFRUxkRzFCbHpWNVhvTjcyeUo4amZYYnRFUGNzWXNENHl6TThad1ROOG1yNko5dXNJbHM1aEUydjM0YUJpUUVUN05DbFR3ajJGTkJBcmRqQkd4djg2YWJGVitjVUUzcFpCcVI0WFJHWi8yakw0Sk1WTWVXWjJWNHp2eHRTOEdPeHRZbzhla2VWd2NZR2lsYVNNRmRYbm9Nd0tJSVBJeVpaMXlDUGNManE2M2VhNWV0QkJVRXh3MFJ6TFdQYWNIc0VaZitCV2VETk1zSzNabWlqYkVsTkQvYUxxbWI1eUpmZW1hVE5WWUI0KzF0c3Fra1dNV2xvYnJqQXlsRmkwajhMNmQrMXJOYW53SUhYanRaM2YyN0lhUTJYcHVhM3R5Qi9lRUpRWFJBU3pXMCtMZU1oOVIrbWpnajNaVHhBUCt5bWRTcnJPblZTQUdCU3dTczgyL0J6b1BRQno1cXV4dy9VakhYVFBJWXkwRjhRdDJhUFl4eDRScmFzZmcvNWNDWjFlSUNsbS9RSGswa3pXWjZubVY2Z3NJcGV4SUJ6bVhjTEUrMHNjOTlYcnhHTEEiLCJtYWMiOiI3ZTk4MTYyOTFjMDgzMzBmZGZlZjFmYmU0NGUwMGJhYTJjZDUyMTFkZDMxMWEzNGM4YjQ4ZTgzZWE2ZGE2OWI4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFqTS9KejF0d1NXTllNeUdTbENQdmc9PSIsInZhbHVlIjoiSkt0dXZBNlphZVpWUkljWVNUb1ZUSkFoaFgrUnJ1aWg1Y2JJWlRpb2Z2MFhUVHdsenV2VnB3d2daVHFTWjRjeWxCSmUwMXFrUFM2dVBsZmFTYldZQ2JhMHEzamFPZWtydXFHZ3pCcWtTRGI1aUVzTlUwSU1YMkNIKzVqQnF3dU9VVVcvN0NCaFBmR3oxV1A2RE55aUNZRXN1WnZtV1pwQnlIbS9RalhNVnhheFpidjJ6YjhHdFo5T0ZXbG10TTU0RzJpWVVQWjFwYkJ0ZDVuaUo1bmVUdVlFcElGanRNa3dCUGpRS0ZKbXJuNENCZmpYcGc3azB3M085SXJacWplaE1EL3RaNEp6b3hDODlHcTVBRjNjdWtvVFRYUWFDc1hCcjRWWldyd0hYZmNPNndUODM0alJNZjJIN3BObjJUTVJjdVVzUTZabCt4K2tUU1ZoL1hGL2czaTVpczVUWll3aDFScEhzTEFtWlJaUUl4MlhMcFdSQVc3ZkhrY2FHS1NWRTIzUHMweEVnY2Y5QUZSZFJiN3ZsRm9JbVNyR0hFOGhDR2I0TzZvb1d1VzlQNjBWZ3hNTUluUU5XRGMrQkEvRmpvSXY2RHhhWUsyQTdWQWp2eHhOWm5CWVNiNGFlNUNxemZzMkJMbnY4MXdrVHlUUGZnRXQxQjhNOGtpV3J3OE0iLCJtYWMiOiI0Y2Q3ZTI4MzUzYjY0MTM3NTFlNzFhZTk2MDIxYmYxNzFmODFjMjNlY2NhYjY1OTk3NGUwYmY1NTA0ZDRjMGM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651050265\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2074755580 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2074755580\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1288440027 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImN2aDBmS3BNZEo5NWtYc3pXb3BWZEE9PSIsInZhbHVlIjoiS0taYk01Umc0VVcwMUtQRkpmYWZXc1BXZnduWFUzMklBNjJqYlpVOTlZTHRsK2IwaTA2N0RXdkxHbFpFK3FFSFhJaWlpTTdlTVR2dG9MOGdEeTN0K2FvdityRkVqbmV2dWdpV1ZOMjdTWUNnS3h0WlRNOGxVMW9wWjhRVHRuODU3TitjekRNejY0Y3orcWpQcjF1QjVWU0RWSEFVZEkzVEpXaHBmMkplTC9ORnJ4WTFNdlp1Mm94Q0pja2pSMDBZWi9DdUN0dDZLd3I0T2NqZjlvVXBTOFF2QlA3RVlOQUJSTStUK3hKK0FiNTVGaWdkd3dzNlNVSnFlVlp3RDFLQU90VDJUY1R1R0duZ1JYZFdjaUdiOVdONTFnR0laWHpBZzlDclRWNll0UmlpbUtQeHVkcFRYWnlLR1lBd1ZYdHJhWDdwV3lmOUFWRXZHMDlOVTUrUGtub1h2emFIZmZYb2hwcW9nZklXcTJDVU0xZm82QVdTQzNsdjVRZlFib2YxNmNZTzN3TzRyZWVpOXE1SVg5V0F2WUNNdUF2cVJLVU81THBTSFNUemt0YWRWRVFXd3lpT3J2eVRDRmtQenc0UGllOEJ5a3J3b2hWMmEySytpN1JEcjhOTUc4WVF3SHhaRWpmazVRZ05XQWxMZmo2bFNHV1N3Skh1OFVaKy94a2UiLCJtYWMiOiIwZDUxZTFjMjc1M2Q2NzQ1NDMyZGFjY2ZkYjUyMGNlMDY5NGY1ZGM5ZTRmNmNhMTdlNjRmNDZjNWE5Y2I4ZjViIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik9TM29yby9ST0pDdGdSSGxLN0l4Y3c9PSIsInZhbHVlIjoiVHV3czFPQWxnQ2VDWjRST2M1ci85cCtyTE50VWpjUHFUME9FQVFaRmJHM0E4YzdIejhMTkFaVDVBdVNYZ05NZlhSdnF3YnRWeUExUldNQ1lEM2pYd2loc1ZxUTViTjd5bzRENnpEZHBMWEJsZ1VNMXYvbGk0WVVQVGlFR2IveWE4enoxVWtxZ0laV2xQSjZjdXpmN25ZZVNwUGdkMVcyQW1NUGYrSjdJL0QyVGwwOU5nb3U4Y3VNWVlyeDRrVHZoWTNPK3E1TStoMktQRnFna3dvdm9mV2c2SUJuYnlKbGRtTXg4dk56NVdYQjJUMjdINEs1Wi9BdUZvbDVUUGd6MGh2TVhOUnk3MXRzYVNMOEJpMzlyVW1kOUZiV2NYZUg0N3g1c1N2SHJCcThITDhxeFUraERaZzkzZE1ZV1ZoaWFkaWZDMHRveDkxKzZQR0ZWU1VITkJkNEZJVS9SSkM1cDdKN216NU96T3EzdXZNc0xoQVM4SHc0OVNYSklqOGE4aEE0d1VtV3BmbnE5RTlpdGdlSDkvZmJkZ2dpbEh5eEdzZDI4UEFxd0FsY2pSVHV6cHo1RllML2tFZkhlc3RCMnlweVVxTEhtMFpXOUhCdDZNQ0hROEIzTGhZN3FMMm9FTEpUN1NSNGlYYVgrUmFTcHFVeGRGSFBwMEtVdEM1UFoiLCJtYWMiOiIyZmRkMzYzNTk3YmRhZWYxMjFjNmUyYTRmMzdiYjg5NTY2NjBhMjJlOGI5N2E5YTYwNGJkZmZhMzU5MjAxMTEwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImN2aDBmS3BNZEo5NWtYc3pXb3BWZEE9PSIsInZhbHVlIjoiS0taYk01Umc0VVcwMUtQRkpmYWZXc1BXZnduWFUzMklBNjJqYlpVOTlZTHRsK2IwaTA2N0RXdkxHbFpFK3FFSFhJaWlpTTdlTVR2dG9MOGdEeTN0K2FvdityRkVqbmV2dWdpV1ZOMjdTWUNnS3h0WlRNOGxVMW9wWjhRVHRuODU3TitjekRNejY0Y3orcWpQcjF1QjVWU0RWSEFVZEkzVEpXaHBmMkplTC9ORnJ4WTFNdlp1Mm94Q0pja2pSMDBZWi9DdUN0dDZLd3I0T2NqZjlvVXBTOFF2QlA3RVlOQUJSTStUK3hKK0FiNTVGaWdkd3dzNlNVSnFlVlp3RDFLQU90VDJUY1R1R0duZ1JYZFdjaUdiOVdONTFnR0laWHpBZzlDclRWNll0UmlpbUtQeHVkcFRYWnlLR1lBd1ZYdHJhWDdwV3lmOUFWRXZHMDlOVTUrUGtub1h2emFIZmZYb2hwcW9nZklXcTJDVU0xZm82QVdTQzNsdjVRZlFib2YxNmNZTzN3TzRyZWVpOXE1SVg5V0F2WUNNdUF2cVJLVU81THBTSFNUemt0YWRWRVFXd3lpT3J2eVRDRmtQenc0UGllOEJ5a3J3b2hWMmEySytpN1JEcjhOTUc4WVF3SHhaRWpmazVRZ05XQWxMZmo2bFNHV1N3Skh1OFVaKy94a2UiLCJtYWMiOiIwZDUxZTFjMjc1M2Q2NzQ1NDMyZGFjY2ZkYjUyMGNlMDY5NGY1ZGM5ZTRmNmNhMTdlNjRmNDZjNWE5Y2I4ZjViIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik9TM29yby9ST0pDdGdSSGxLN0l4Y3c9PSIsInZhbHVlIjoiVHV3czFPQWxnQ2VDWjRST2M1ci85cCtyTE50VWpjUHFUME9FQVFaRmJHM0E4YzdIejhMTkFaVDVBdVNYZ05NZlhSdnF3YnRWeUExUldNQ1lEM2pYd2loc1ZxUTViTjd5bzRENnpEZHBMWEJsZ1VNMXYvbGk0WVVQVGlFR2IveWE4enoxVWtxZ0laV2xQSjZjdXpmN25ZZVNwUGdkMVcyQW1NUGYrSjdJL0QyVGwwOU5nb3U4Y3VNWVlyeDRrVHZoWTNPK3E1TStoMktQRnFna3dvdm9mV2c2SUJuYnlKbGRtTXg4dk56NVdYQjJUMjdINEs1Wi9BdUZvbDVUUGd6MGh2TVhOUnk3MXRzYVNMOEJpMzlyVW1kOUZiV2NYZUg0N3g1c1N2SHJCcThITDhxeFUraERaZzkzZE1ZV1ZoaWFkaWZDMHRveDkxKzZQR0ZWU1VITkJkNEZJVS9SSkM1cDdKN216NU96T3EzdXZNc0xoQVM4SHc0OVNYSklqOGE4aEE0d1VtV3BmbnE5RTlpdGdlSDkvZmJkZ2dpbEh5eEdzZDI4UEFxd0FsY2pSVHV6cHo1RllML2tFZkhlc3RCMnlweVVxTEhtMFpXOUhCdDZNQ0hROEIzTGhZN3FMMm9FTEpUN1NSNGlYYVgrUmFTcHFVeGRGSFBwMEtVdEM1UFoiLCJtYWMiOiIyZmRkMzYzNTk3YmRhZWYxMjFjNmUyYTRmMzdiYjg5NTY2NjBhMjJlOGI5N2E5YTYwNGJkZmZhMzU5MjAxMTEwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1288440027\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2021381195 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/budget/eyJpdiI6IlVLU3pDQ0FXT05GSVJUaDFlS0wrZ3c9PSIsInZhbHVlIjoiZGxUbHd6R2RaaDR4bzhuTHZSeEY4dz09IiwibWFjIjoiZTFiMDZlMzAzYWQ1ZDhlMDliOTc1MzRkYjRkZGE3ZmUzMzRkMGI2YjQ1ZDI1YzdmZDQ4Mjg2ZmJkYjk0NTUwZiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021381195\", {\"maxDepth\":0})</script>\n"}}