{"__meta": {"id": "X09e3ead26b8e36da63e327c21df00e3f", "datetime": "2025-06-26 23:21:36", "utime": **********.932995, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.51846, "end": **********.933019, "duration": 0.41455888748168945, "duration_str": "415ms", "measures": [{"label": "Booting", "start": **********.51846, "relative_start": 0, "end": **********.876139, "relative_end": **********.876139, "duration": 0.3576788902282715, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.876146, "relative_start": 0.35768604278564453, "end": **********.933021, "relative_end": 2.1457672119140625e-06, "duration": 0.056874990463256836, "duration_str": "56.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029728, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00291, "accumulated_duration_str": "2.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.908418, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.196}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.919615, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.196, "width_percent": 14.089}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.925025, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.285, "width_percent": 12.715}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-670488233 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-670488233\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-279663260 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-279663260\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2045610846 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045610846\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1476655997 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980092648%7C23%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVpelFXWndWd2xWRHVtbnFsbkd1Qmc9PSIsInZhbHVlIjoiTnpCODAyTTYrWFdyQWxVb2tISkZpaW40SmYwNG82TEgzMzhmVUZMSi9pa3hOclhtZEdObnNNU0xKc2V1ekhJS0QvdCs4RnNocW9LYkFBbjZmM01ibjlwZVNDb0c3U2Z1ajBTNUNwUHNoNXM3WHJFbUc3MG5HYWF3em5YaEZpcjVzSzFBN2tBNy9hS0FCYWQ3U3VuSHhLM2o1azI3SGdWUzJKTGZSNUF3U3YrU25RZk1LZE90eTNPbjA4TVNsUG1ZQjRzd2RzNEZJMWM2Z3lDSVZWZFVEY29QTzRCQnp5RHkrbEJIMlUrMmdqVjcraldnN09IT2ZGbDl4NW1GNVNTM3QyRHpkRVVwZXVpYkttdWFqTDRXRDRBQ0oxYnhUMi9vM1huWUx1RUhGK2tQRGRpTE1GQmxucFBVMmpVanpVOWg2eHhJR2JxS0hVNlhlRU1zalJZVWdMYXNsRVJWV0pocFR2aHRJNStCMGhVZWl0S3MvWXY0U3VTNE1ldlBxd1ZrNHJ1S01JNk5SWDhmWmlCYXBSWDRpam1hZzBzc0Nyak94WFNraHplRWVTdkJuSFowTnkwOEpUVXRYRmdwNVBtM2Y1YmF3MWhiN1l2eEFsYUlSR1ozcmZUcGNOOVRhaUtDWkg5NUk0c2thNkJrbGthb3dLN0gyZEU3aEh1Rm1ybFkiLCJtYWMiOiI2MjJjN2ExZjU5NWIwYjlkNGU3MDM5MTk5Zjg0ZTQ1YWEzNmZhZjUwZTJiMmU1NGM5YmEyMWVmZDU4MzJhMzYxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IklkSEhPaGI0dUFEaE12RHpuamNUdGc9PSIsInZhbHVlIjoiQVk4clZmSlRja3A2K2prendhNmlHRGcxYlN3NjZLemtiNmR2WDZLbzEzaVVVQXNrS3VRUnowUjV1cjViK3poN0dhZEY2K1BvV0RCWEFTUkd6NG5IbHFaZ3c0emJRRDI2Nmxlbyt6V1RlelREZTNMWExKMzA0RUFyQmJDREUwemVNQWRYcFBUWHBMOUhPdU94dDZ6Z2dmYzc2NVpiSEFMVGtUaFo3VlphcXBXUmg5ZUJhQVN6WHorcStXc1BQNGJaU3dqeXRSaUhZT3BTSzlybkI0cEdnVG5lT2hFaytGY2Y1dFF6RDlVWEtGc1ZTR2Z4WEFBV1c3VUthcTc4ZGVCb001ZXF1VnlNZ1pmektTbTdRV25JdUlSb3l6cnl6aXdZbmhlNW1PK2RKbjdIRFl4cEU3bVM2R3VDMy91YlM1alZ3VzBSRHVVbFlodEcrNjUxT2tZdjFqanlnVlM1blp6Tkx2cnZ6OUFSb1kyTVRwM2RwS2JaMThXZEsyMFpLNjlpWnlkNTJFaUo0Z1dIdFdEK2pZaWl5NlNPUytzVVhLTDdlN0hjR2p2UUgvaFR4TGpUVkpxeGR1M3ZFRGV4Nlg3bWJpeWNnOUdLaGpXQzZ1WndsR2c1d3BFYXV5SEFsVnM3ZnJYeEtFWHZHOTdOS0dEWit3aE54K0ZWQ0cvaml3TWkiLCJtYWMiOiI0NzUyMTlhOGYyYzE2ZGVmN2Q5YjQ2MmNjYmE1MGVjMTY2MzkyMWFlZjBjYTg4OTdmYTkwMDM2ZWRmOWZhMjBmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1476655997\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1498027974 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498027974\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1742680200 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:21:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktDOG1Lb0xiS09SOGo5WUVUTVFyT2c9PSIsInZhbHVlIjoidFBlMldDMlBhb2gxbXVIaVBLVTE3aTdaaTdjQ2wvU2l4YUdyU1RZZzBQc00zbTluU0luUTZDMytFSGJYWlpmZFdnRWllcW5uMXZ6WVlTWWRzc2R1WmQ4dE40RFhTN2k4VS9yRmxDeE03WjdYZzQxdE4zMUYzOE9PbFNWdnR2RVZtcWZSYjNNV2VLTmxmTkRsOUY3T2VpRjhvbVBCcVRBS3RxZjJwbkJRb0pxVFhXbFpqRUVRUlRtREtVWklJRmErMVFrRC8yaVJPSWlqY0pIQitpbkI3K0h4bnBwbkZwQnIxNUprVDBIUWJpQVYrMHgzSHMyNUxaSCtIaGRaaFFqUnRRZzV1YlpkQ2RkWFZ0dm14L1kxVHFDdERERG41Uk15cktjZ2M3MTlsanp4anBSMVlSL21CZytKV0ZvM3B5UWl1UG01WGtwaXVqQWQ3aXpYbVlBd2U2cGhEMG9SU0NKQ2cwSUtXeVdNWGlQUFdTK3EwVXE3UC8rRTBodVY4YUtlMVZueCtjZXFCWFBEUDExTGFRNkN0K2gxbXBZa1lQejRJSWtCYzZYbG9JNkVaT2w4ZXF1UFF3cDJvMDdhNzE0Tm93U1MxSGJnNVFzV1NvNzNXT2J0RWZ1K0VvenkwMHpPeXdHR2NSZGtINGJhdFpIMGZwZE5uZ08rNzNjRUk3d0kiLCJtYWMiOiIyYTY4OGFiYTFjZDQ0MjMzZmI5NzFmOWNiNWE0ODNkMDBlZTg5Mjg3M2I2YTUxMmNhODA4NGE5NjlmNzVmMTQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJnUkxwV0daaldxYnF3VTR6dHlnM3c9PSIsInZhbHVlIjoiemZuVnptUDg4MUxrU1hzRGNBSHZESW1yamhaVkN3NWMzTWx1aFlPREZaV3F4Z2JTMjIxc2VjenZ1aXpPTTQ2Y21zZC9MMEF0K3pXcDhsR0RnQ01VRzRsZDc1dGpkTGpuMVNQT2lrUVI1eVMzRHZsbzVqRkI0ZGhlQ21UQnF2S1ZnaXc1VVhvQ0Y4eElodjJGNC9JYzE2Yk15VU9QTUlDTld4YUVUM0xNc2hrNGUwcUlzNzBRM2pNaG84dlR6Qk1STzZnZlpOWUhXb0xVbWFZMXlrRDBWdWlaQkViM3dUN3h5WklFOGNkUlN0dDUyYTlyUXg2N0JkVUJLUjVPUXJnZlBFV3dEbmtsWlBJNDNQZTFkWStxbTUzc3Ztd0VDbU1JTlpkL2ZzNlNaaExHeURldlI5Qy9OdVUwNUVBeXEwL2VrSUhJNjBkdWZjb1dBdTlnNWxXdnJSVW85OFBEbHpLNHZtS1ZLTGw5WkoySG8xN0NuV2FxUHlIQmNPOGU4aE5CSHFLWkplcDJ1eUY3dk8zVldlaEM1TThnOGhvbXBEUWJMRmNCTlNvRjl1QTlXaXlCQjBBVkF0a1E0bWh0RGJqclJtVHFFVWZacStCYlR5TTNoblBmSUlZVDhnaUpUb2NNNG9DeGE5RjN1Q2UzaEJReDBleDBXM3d1MWpEMU4xQWciLCJtYWMiOiIyMWI4ZmViNTgwYjQ5NmE1MTUzOTgyOTY2NjBjYTI5ZmY2Nzg2OWJmYWZmMTY0OGE4MjAxMGRmZTdlNzNkMjE0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktDOG1Lb0xiS09SOGo5WUVUTVFyT2c9PSIsInZhbHVlIjoidFBlMldDMlBhb2gxbXVIaVBLVTE3aTdaaTdjQ2wvU2l4YUdyU1RZZzBQc00zbTluU0luUTZDMytFSGJYWlpmZFdnRWllcW5uMXZ6WVlTWWRzc2R1WmQ4dE40RFhTN2k4VS9yRmxDeE03WjdYZzQxdE4zMUYzOE9PbFNWdnR2RVZtcWZSYjNNV2VLTmxmTkRsOUY3T2VpRjhvbVBCcVRBS3RxZjJwbkJRb0pxVFhXbFpqRUVRUlRtREtVWklJRmErMVFrRC8yaVJPSWlqY0pIQitpbkI3K0h4bnBwbkZwQnIxNUprVDBIUWJpQVYrMHgzSHMyNUxaSCtIaGRaaFFqUnRRZzV1YlpkQ2RkWFZ0dm14L1kxVHFDdERERG41Uk15cktjZ2M3MTlsanp4anBSMVlSL21CZytKV0ZvM3B5UWl1UG01WGtwaXVqQWQ3aXpYbVlBd2U2cGhEMG9SU0NKQ2cwSUtXeVdNWGlQUFdTK3EwVXE3UC8rRTBodVY4YUtlMVZueCtjZXFCWFBEUDExTGFRNkN0K2gxbXBZa1lQejRJSWtCYzZYbG9JNkVaT2w4ZXF1UFF3cDJvMDdhNzE0Tm93U1MxSGJnNVFzV1NvNzNXT2J0RWZ1K0VvenkwMHpPeXdHR2NSZGtINGJhdFpIMGZwZE5uZ08rNzNjRUk3d0kiLCJtYWMiOiIyYTY4OGFiYTFjZDQ0MjMzZmI5NzFmOWNiNWE0ODNkMDBlZTg5Mjg3M2I2YTUxMmNhODA4NGE5NjlmNzVmMTQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJnUkxwV0daaldxYnF3VTR6dHlnM3c9PSIsInZhbHVlIjoiemZuVnptUDg4MUxrU1hzRGNBSHZESW1yamhaVkN3NWMzTWx1aFlPREZaV3F4Z2JTMjIxc2VjenZ1aXpPTTQ2Y21zZC9MMEF0K3pXcDhsR0RnQ01VRzRsZDc1dGpkTGpuMVNQT2lrUVI1eVMzRHZsbzVqRkI0ZGhlQ21UQnF2S1ZnaXc1VVhvQ0Y4eElodjJGNC9JYzE2Yk15VU9QTUlDTld4YUVUM0xNc2hrNGUwcUlzNzBRM2pNaG84dlR6Qk1STzZnZlpOWUhXb0xVbWFZMXlrRDBWdWlaQkViM3dUN3h5WklFOGNkUlN0dDUyYTlyUXg2N0JkVUJLUjVPUXJnZlBFV3dEbmtsWlBJNDNQZTFkWStxbTUzc3Ztd0VDbU1JTlpkL2ZzNlNaaExHeURldlI5Qy9OdVUwNUVBeXEwL2VrSUhJNjBkdWZjb1dBdTlnNWxXdnJSVW85OFBEbHpLNHZtS1ZLTGw5WkoySG8xN0NuV2FxUHlIQmNPOGU4aE5CSHFLWkplcDJ1eUY3dk8zVldlaEM1TThnOGhvbXBEUWJMRmNCTlNvRjl1QTlXaXlCQjBBVkF0a1E0bWh0RGJqclJtVHFFVWZacStCYlR5TTNoblBmSUlZVDhnaUpUb2NNNG9DeGE5RjN1Q2UzaEJReDBleDBXM3d1MWpEMU4xQWciLCJtYWMiOiIyMWI4ZmViNTgwYjQ5NmE1MTUzOTgyOTY2NjBjYTI5ZmY2Nzg2OWJmYWZmMTY0OGE4MjAxMGRmZTdlNzNkMjE0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742680200\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1303210757 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303210757\", {\"maxDepth\":0})</script>\n"}}