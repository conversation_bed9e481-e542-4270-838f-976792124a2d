{"__meta": {"id": "X92c63e432a798f84b4891015a71d38ee", "datetime": "2025-06-26 23:14:50", "utime": **********.941184, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.50706, "end": **********.941199, "duration": 0.4341390132904053, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.50706, "relative_start": 0, "end": **********.884442, "relative_end": **********.884442, "duration": 0.3773820400238037, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.88445, "relative_start": 0.37738990783691406, "end": **********.941201, "relative_end": 1.9073486328125e-06, "duration": 0.05675101280212402, "duration_str": "56.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042024, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029899999999999996, "accumulated_duration_str": "2.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9150422, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.211}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.925615, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.211, "width_percent": 21.739}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.933352, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.95, "width_percent": 15.05}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1079291759 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1079291759\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1614750882 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1614750882\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1744351083 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1744351083\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-287093248 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979688859%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkF5NEh3NUM3Y3g4b1g3MFY5ZnFWNWc9PSIsInZhbHVlIjoiOFFEZEIzOHYwcGJLRjFjSk94N1A0RDFJd0E3cmFKRTNwMzBpVHN3RHNZWU9FUDkrNUVIdyt6a3RpSzhkT29lZndxK1dWdFE1SnBBaytPNjNERE85bHBselpUUGNDZk5yM3dwUU40TEpnbWhsYVJIMnE1QmtYeUc1SDc2bXJtT3lzcm9ub2s3NjVGa3pjV1Q3bVhGRzBmdmZBajhhR0d1TzgvMmxEaEVHZ2ZIK2RzQ0x3S2R6N0d4V09oR240U0hzRVFIblpZMkY4eGMwQVpMakczM0tzUnlhUGVieGRnS1NNbnloa2l5Tk1hL1VZV1ltWUlzdm9udmN4U3d1VGlqODZydnIycktiQkg1UkxNcis2aFdiSlMxOWhkc0JrWlFZTFlMTjBEUFZRdURqbXA0OHNRSWRBNnNZSEZ5d1NDbUdPUVF4Mm1EdHB4eUV6bXdtdWdHbXZ1UDVnVENkWUtyV0V4N2NqNU81NkdpSEx2ejRrbnJFbyt3VTZXb2poRVFEK1MyY00ySjdxeFduTmxoWEl1WWtuUDhKR29Dc0g2eDA0dmxWZ3dkemtGUnNhRU95U3V0elpIbno2cUlQSnBRUHRQNE9ybjJHRnhZZkRldUJCaXV2WmhsV1lMa0NkZXhMeGVRcDhUQk5mVS9TeUlXY3VodmVwazhBeUw0THpZOEsiLCJtYWMiOiJmNTY1ZjY4OThmZjBiZGNjYzllY2QzMDBlOWU1MDM3YWMzYTA5ODlkNWJlMDkxZTBkZTkzZTE0ODg0MjVlODE3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Iks4cE03NWtYbHBrK0VkeFkzY0RRdUE9PSIsInZhbHVlIjoidXh5aEhlSzlPUDNiM2sveFVoMSsvZXMyMkl6QWpzZ1FlY3p5OGh1VXAxQS9lUWFEaWpDUTZHQXBiOU9iRE1JSThFUVZ5NjRUWnVWcGozd0VJUnd0dDNWYmhvdVlML3VHRmJBRGlyamhPemhsNDhkb2N5QUFRTWJlSUNLK2hDNDVYZy85YmlsL2EvRnBpRVdsT2o0c3hIUll5b1hEemhlT1VIVXZXN0lOUHVrcHprb2o5R25OTGJUcTY4WnhaVUlwWlJxekN1Z3UvOUhkeFV4Qm9FZlZ3Rm9WOHZnTjRPNGw1SGgwT3hIS1JoTERoZUphMVM5Z2lFNnRSc0xrVHpkVFVXeHk4YzBMS01wY3pOUUw3ZWpIUG1DZkl2VnBjeTRzb1dQZUNLaDBGZEljT2N4bTUwM2RaVmxjWGJta2NHZW5HYTNvYWQreEhwK1krdElmL0ZoSVpMbXkxQndZa2tHZndSRENKdzM1SnJ1NDI4bVdTVEtkMHFmblBsTUdJQlduVDhvVXpBbTNjUDRHWHNESFZ5ak5mRnBWK2NFbEY5L1YybUtTNVBTeDliaFRRRmRWQjVUbkwrUytoM1hJb2Q3SUEybHhKTjRTL2lEckhSNFE4VmlEL1N3Y1hPSjNZb0ZoY0lUZkJqakxReVIydWEzRWxtOUROckFYTDAvcXdweDEiLCJtYWMiOiI2OTQwN2Q1YzdmMjJkZWJjNTI4YTgwMDFmZGE3MjE5YmZjYzRmMzQyMTc5M2I0ZGQwNTE2Nzk0ZjE0MzE0NjEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-287093248\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1953640340 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953640340\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-564722747 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:14:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik82QThtNFFmNFUrUG1tN1A4VXJsV1E9PSIsInZhbHVlIjoienRTNGdhSEc4TWU1S2dMWXJmaUo2ekZJNjdGQmFSamt6THNSbmNyRHIwRmtROHdJV0drRGl2YVVBcVlIR3NINWlncXBZZDdVMXA4QlFiN24wU3hjdUpLRlBBWHZtRkFmZVBSb3Bjb0s2dmtzYjZXZU9ZcVpTM1c5UkFUWmlKcHZ4NjFQUW5vaERXR2dOVkltdFVJQjI1M3l3eTVPYzJSR1VGZXhxVGhlWFhuVzFTUm9HTXo4UUFERjhBOWkwV3c1SGhPK2szeE9Geldmak1WSC9pbllYdVdHN3d0ME9Kb1VkcjZRelFpWkdUY05ZVFFNR0M2QUo2bDdXSEwvc1B5SzZkeUkwa29iRE9DV2ZOWFdpb29CNWNXTEhMZ3cwN0hLbXd3Q05UUkRac3dwUXI4QVhBUDdUS1Y1aXpVOEhYNFU1bUpjSmsvUURNY0tGT0VDTjYyTFE0VHNjSUpwamFuWmcxTWpQQ3JQWkRzVVJ1WWhseUtHT1dXbzQ5SDRjUjZWTFNoMXlYQ0V2WE1qQnRNTlJlQXZ5Mjg3VFFueHF4Vlh1WG9PZDh0Tk9IdmlBSjd0NGZzWHlsYlQwWVR3WWFOdEZmN283ZFpxNnV1eWc5OUg1WkRFTzd0a3FIZVFCMnBwOEY5VUt0Q2VjaEVZSERtRUJTR2NuaVlGWExJVzIwN28iLCJtYWMiOiJlYjk2YTAwNWIyNTFkYTZlZDljMzY5ZmEwY2UyMzcyOGM5YmI1MDZkNmZlM2ZjYzRkYjQ4Yzk0NDcwYzc5ZjZmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:14:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBPV1RmckZlZ0ordHlvU0NUYUFZdmc9PSIsInZhbHVlIjoiQWw2UnhZaWxEYTZSZTBIakJDSmRSKzluWTVham9mK0tTaFVha1U5NVF3a1hsbW0zMXFGMTI2bjhZU2ZickdnbGlRZ1UxUEhPb05acFdPVUIyd1RsbUtQR3JMUXR3RVV3VVpjRGpMMUZqaGpaYUVZb0VaWUZzMjNqWGJYMzBNSG04aU1OdW9XSDViNW1RQlNmUWdyTW4yd3hsL3hWTm1DTXd5QVZPUUh1RjJEbXVWMDErMnJjc01uNTNRc1ozNnN6NzQrR1dITUl5b1dkR1NWSFR5VjdXQkIzZ2YzRE5xemZCcGg2ZjVvZ0srODNlVzJlNjY2NzhGcFBNOXN6dDJaN0RxaWovaUFEMkdqb2VsU2dQb3NEWEdCRG5vRVR0U09kbEZtcTlDcFVBMmMvaXZtY0lTdEFqQ1JDcGlva1RyT3R2dXZUekVJd25GeERBNkZ2b2c4R1hVeEs2bG5oL3VLVDF3dGlZVHNodlF4QVJ1aktidTFocFg0ZFBNNlR5TDNqeWRIaWxkaXgrTUowMEp2dkFSRzdqcnJwTVVacFNCUmNQQW80Zjl6Q05sOFNzWmJEbENtMEFsdGcxRTU3N1htUEhla0swWlNNNXpRYklYOXdkNjk1eDZ3dG5ZSC90ckVVS3daNVptYXgzTkZybmJEcnQxWkNDWk5mR2dJbTdOc1QiLCJtYWMiOiIxY2E0ZGY1MzgxNmJmZjkyNGM3ZjVlOWEzOWNmNmJlYjZkZmExZWZmNjcwOTlkZTU5Y2I2YjJmZGFlZDM3MzI2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:14:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik82QThtNFFmNFUrUG1tN1A4VXJsV1E9PSIsInZhbHVlIjoienRTNGdhSEc4TWU1S2dMWXJmaUo2ekZJNjdGQmFSamt6THNSbmNyRHIwRmtROHdJV0drRGl2YVVBcVlIR3NINWlncXBZZDdVMXA4QlFiN24wU3hjdUpLRlBBWHZtRkFmZVBSb3Bjb0s2dmtzYjZXZU9ZcVpTM1c5UkFUWmlKcHZ4NjFQUW5vaERXR2dOVkltdFVJQjI1M3l3eTVPYzJSR1VGZXhxVGhlWFhuVzFTUm9HTXo4UUFERjhBOWkwV3c1SGhPK2szeE9Geldmak1WSC9pbllYdVdHN3d0ME9Kb1VkcjZRelFpWkdUY05ZVFFNR0M2QUo2bDdXSEwvc1B5SzZkeUkwa29iRE9DV2ZOWFdpb29CNWNXTEhMZ3cwN0hLbXd3Q05UUkRac3dwUXI4QVhBUDdUS1Y1aXpVOEhYNFU1bUpjSmsvUURNY0tGT0VDTjYyTFE0VHNjSUpwamFuWmcxTWpQQ3JQWkRzVVJ1WWhseUtHT1dXbzQ5SDRjUjZWTFNoMXlYQ0V2WE1qQnRNTlJlQXZ5Mjg3VFFueHF4Vlh1WG9PZDh0Tk9IdmlBSjd0NGZzWHlsYlQwWVR3WWFOdEZmN283ZFpxNnV1eWc5OUg1WkRFTzd0a3FIZVFCMnBwOEY5VUt0Q2VjaEVZSERtRUJTR2NuaVlGWExJVzIwN28iLCJtYWMiOiJlYjk2YTAwNWIyNTFkYTZlZDljMzY5ZmEwY2UyMzcyOGM5YmI1MDZkNmZlM2ZjYzRkYjQ4Yzk0NDcwYzc5ZjZmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:14:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBPV1RmckZlZ0ordHlvU0NUYUFZdmc9PSIsInZhbHVlIjoiQWw2UnhZaWxEYTZSZTBIakJDSmRSKzluWTVham9mK0tTaFVha1U5NVF3a1hsbW0zMXFGMTI2bjhZU2ZickdnbGlRZ1UxUEhPb05acFdPVUIyd1RsbUtQR3JMUXR3RVV3VVpjRGpMMUZqaGpaYUVZb0VaWUZzMjNqWGJYMzBNSG04aU1OdW9XSDViNW1RQlNmUWdyTW4yd3hsL3hWTm1DTXd5QVZPUUh1RjJEbXVWMDErMnJjc01uNTNRc1ozNnN6NzQrR1dITUl5b1dkR1NWSFR5VjdXQkIzZ2YzRE5xemZCcGg2ZjVvZ0srODNlVzJlNjY2NzhGcFBNOXN6dDJaN0RxaWovaUFEMkdqb2VsU2dQb3NEWEdCRG5vRVR0U09kbEZtcTlDcFVBMmMvaXZtY0lTdEFqQ1JDcGlva1RyT3R2dXZUekVJd25GeERBNkZ2b2c4R1hVeEs2bG5oL3VLVDF3dGlZVHNodlF4QVJ1aktidTFocFg0ZFBNNlR5TDNqeWRIaWxkaXgrTUowMEp2dkFSRzdqcnJwTVVacFNCUmNQQW80Zjl6Q05sOFNzWmJEbENtMEFsdGcxRTU3N1htUEhla0swWlNNNXpRYklYOXdkNjk1eDZ3dG5ZSC90ckVVS3daNVptYXgzTkZybmJEcnQxWkNDWk5mR2dJbTdOc1QiLCJtYWMiOiIxY2E0ZGY1MzgxNmJmZjkyNGM3ZjVlOWEzOWNmNmJlYjZkZmExZWZmNjcwOTlkZTU5Y2I2YjJmZGFlZDM3MzI2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:14:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-564722747\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1143615979 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143615979\", {\"maxDepth\":0})</script>\n"}}