{"__meta": {"id": "X7baf98077775d078f8a2772cdec8510d", "datetime": "2025-06-26 22:42:36", "utime": **********.405313, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750977755.940792, "end": **********.405332, "duration": 0.4645400047302246, "duration_str": "465ms", "measures": [{"label": "Booting", "start": 1750977755.940792, "relative_start": 0, "end": **********.33517, "relative_end": **********.33517, "duration": 0.3943779468536377, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.335192, "relative_start": 0.39439988136291504, "end": **********.405336, "relative_end": 3.814697265625e-06, "duration": 0.0701439380645752, "duration_str": "70.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015519999999999999, "accumulated_duration_str": "15.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.368322, "duration": 0.01458, "duration_str": "14.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.943}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.391573, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.943, "width_percent": 3.415}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.397748, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.358, "width_percent": 2.642}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-966706177 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-966706177\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-104279050 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-104279050\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2014306859 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014306859\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1219909429 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977750219%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjkrTVdERGVyTzRzcFU1U01Zbk5aMkE9PSIsInZhbHVlIjoiTmpIRDFKVWFZQ3hyZDJlQlJ2bEpoRXF1bXQ5a3Vuejh5cUFyWFdaZEN3SE5GNmF0dHBRV0VUWmdpeUllOEVneGhFcm5VKytveThHalI5MXVKTWI0NWczcllMRk55ZG9scHdFeTZPSzJkS3FMS3pUSWRNRysxZ2JIY0lRZm0xa0NXUTEwU2NOWWpITzVWWU5DQStUSURYNThLL2laRVZaQldpVGZXNmdraHA5STJSclJYQWFJWmhVaUVnK1JaQkdvakhjaEwyUUZaUk02a0hCSjVQbk5CREJhcGZjbHpFVzJ1RGxiNlpJb3k3RmovYmJWWTBFSUw0djVxaW9Od1JHMk9PQjdVTmpTYUo2a0pvR2VzeE8zMSsvUDVQSFdKSXFNUDY1TFFONlBtOVBXUTNZUFY5ZnkyejRlNWl2VGFwLzI2eVpwc2NuUzc5RVJxbzlCMldqaVA5M0JOTi94Y0w4cDRQNVN0WU1kRFFjZnh5N1N3T3dpOGRSQUVqbW1uSElXVk5HVkFQaS80YVR4Z2Q2YlpwUEptclAvbXQvV0p6bUllYXo5OVEvQzEyZkhnUHNteVZ4UmtsY1Z4b2VXRk5sclRCcjZnU3lnb2xnMHFYbFdwbmMzR1c2V2xOK2VjMVpMMmVhNUJ6T1N5aUltSFVJM1FZbElRMnNpQ0RTYWFFMkgiLCJtYWMiOiI3ZmQ2MjQ2MGZjMWQzNWQzYjFjZjczYzIyMjU0NThjNjhkMWQxZTdkOTMyNjVmMmJiYzMxYzk1NWRiNmVlYTUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhoUkFyVmVHWElZV29tQVc4MFhTVkE9PSIsInZhbHVlIjoiRFN6N0JLQyt2Z3c2L0phMFhHdC9XTUF0Y212U05lREJVRzl6cVlaY0RNWG1IS1NhYnBlOG9qVThwUFdmakJ0TzhCOWQvME9tVUF1aHRUTDAwRGJTcTZlaGtCZ1h1TEhNWm5mMkhqRkhsRldMS3A2Q2VGMGpHcCtpSko4NmpMdVRVU2ZHdlNGMDhxeDc3V0FDWU1ZOTBmVmROM3MwaW4vdjA3VEttSzhuRGJObWlaV291YXZnUnFoZTJzU0paWXp0UUZYUUlLcU14dlNNUnFVc2FxUUVuTFBaeTZIRzlBdmR3bXltQlNVZm0rY3JOd2h2M1VGMG9HRnRZaE9SdlJLQlhEU0NaeU1qNlRsS3E5REVQUUZ5VDVHL3R2UHM1bTV5MU9rcTh4M0UvbVpWZElCelFLSjF1Y2dCMHJ2cTBKWmVwSGVSbjdKRGwzZTJ4bHYwa1pydHorOGpqREkwdUNRSk9rd2Q0V1ExbWgyMlo3bjhTdkc0VGdWSy9YN2tkYTFrMVNCNm5oSFNZWm4zYlUyV3V4S1NLaFBTZWJ1QXJFTjdZYUFuWEptR254UDdGeUg2MFZLanJMZ1R1eTN4RC9vT1FOcFZvM2J3ZDZYSE1pT1JMTmpjMGdnUXZYa1p2ZnVSSENwdkhUelJtMVhmdS9wRjlzT3R1M3BXYURLeVhtS2UiLCJtYWMiOiJjOWMzYWVmMTA2NGQ0ZmY5OTQzMmFkMjI3ZTMyYmZmZTNhYWQyMmY0NzBhMzhlYzgyNjc4OTQzMDQwMjFhNDAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219909429\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1050383574 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050383574\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-241448850 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:42:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBQYUltcjVFRGVSakVFeEI4TndLRlE9PSIsInZhbHVlIjoiMW1Fajh0Y1l2b3Q4WCsrbFlISkhQS1l4ZVBhQVdRTGRhYk94OU5xRkU0UENvd3dEeGVXMWR0WHhGenQ0aUhRZ0ZzY0FPdHg0WTBQZE5wR1d6NUYzUUVGbWFVUjgvU2R2OENKUkJ5UWdEWTR0ZHNQR0N0Y0FaUzB2RTNRZlkwblRJK2xuUnREc0VTQlQvTWJwQlI5OVJIM2l2d04vMmNJTzQ5Z2VaeHB6Z0p2Z0dRNjduMDNORll6cCtKRUJ6R0tpWDl4UXFUZk1hOUdzaU9zNHdJeGpsVHVCL2kxU1ZUZUZ5SDZWZWpUdGtvVUhCbU9hUy9naU44YjVaejI3UVR3dWpXcVdZcmNJekszRUFwUFlFYkYyZ0dVTlp6eXJzT2lKQkZUZmppWHRVVzJqVGtIVVJBZ01HZ1JEQ000alVmM3Yxc0dUWGhXZWpGcnZ2QTdLYWNFUDJ2RGR4bGRqVENKa2ZXM0tBTkI1b0JoK2lUNjcwR2VZZUV1STVjLzMyWnNCc2RWdnNOV0d4MmpJQXNORWI2Z3BKYVowSUVIbXFPMzJyOGdqOFRqUlcvakl6aFpyY2prWE9sS0ZiVlhxbjFPZlNPNWxVVEx2eFBsRU5ZQmtPa2dRZHpkREJIRDJMcXFsYngxWU5LQTZSY2JVNDJDWk5jZG5xdkRkZEtTdllQdUEiLCJtYWMiOiJlNTYwYjliMjBmNzdjODBiMzkyZTkyMzIwYTU4ODI1MGNlMThmZDE5MGNhMWMwODg4NmJkZjQzN2I1ZDhkZTg3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5pSjdUN1A1Y3FQSEJEY0xxWkQwUWc9PSIsInZhbHVlIjoiSnppSTN1UVV4WWwxUGNtZGc1c0IwbnI1Rys3b2Vudi9DSXBVMVhGT0RKejJUZ0xrSWJlYWcrSVBhYzN5K1JYWGtQYkhiT01CMjAxSEI1YmZWSThjamJ2UFVqY2tjNWQwUi9kVlJacWZreGQrbk50Nk4wTG1HTkY2RlZWODlqK3c5N3FKTDZ0dy9ES2x4L0tqWHJTQ3loaXM0R0xIN1dRbDdZeDhZOXU3OTZUL29ra0tLdHVabXFudks5bFZyZGV2bUZvaE0wUUovZzBTYjdybTdHN0xVN0VmU0FJRjg5cG9vdFZ3eWkrUGE4ZjdTa1daS1RnQUFldlZqaEhKZmQ5V01RQ1B2VEJVNUluajJ4L2VJVFJKVm9pTE81cFlvTG02U0dydTM1MXRqTlZ5S05FMmZTL3ZVQUJCbFNXTVdPTVFoM1dFd0JFUlVMaDhId2dNcUh4RTJza2dQVmovbTBndjVSbDU1QVNsaEZaY1hvTkpmTWNrcldZSmcxWFd1azVyRnZBVEpraWZJSXhBVko5d3NoOE5HOFlFM1YwTURBYVREZEFhaEJ5WnhSQ1l3UTFXamt5eHVxaFhSVnVJdm9JaHUybnJBZkJ2OTBzR0dpZUwxd0wzRU5QSG0vSE5tRWdVNXdjcm5BclNJajhwaUxkOWUxWEZSck10Y3NpRGVxa0EiLCJtYWMiOiJkYTgzZThlZjk1MzkyMDdiMjE2Zjc0MTJiNjg3MGZkYTYwMzZlODFmMWMxODBkMzU4MTcyMTY3MmUyMDZhNjllIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBQYUltcjVFRGVSakVFeEI4TndLRlE9PSIsInZhbHVlIjoiMW1Fajh0Y1l2b3Q4WCsrbFlISkhQS1l4ZVBhQVdRTGRhYk94OU5xRkU0UENvd3dEeGVXMWR0WHhGenQ0aUhRZ0ZzY0FPdHg0WTBQZE5wR1d6NUYzUUVGbWFVUjgvU2R2OENKUkJ5UWdEWTR0ZHNQR0N0Y0FaUzB2RTNRZlkwblRJK2xuUnREc0VTQlQvTWJwQlI5OVJIM2l2d04vMmNJTzQ5Z2VaeHB6Z0p2Z0dRNjduMDNORll6cCtKRUJ6R0tpWDl4UXFUZk1hOUdzaU9zNHdJeGpsVHVCL2kxU1ZUZUZ5SDZWZWpUdGtvVUhCbU9hUy9naU44YjVaejI3UVR3dWpXcVdZcmNJekszRUFwUFlFYkYyZ0dVTlp6eXJzT2lKQkZUZmppWHRVVzJqVGtIVVJBZ01HZ1JEQ000alVmM3Yxc0dUWGhXZWpGcnZ2QTdLYWNFUDJ2RGR4bGRqVENKa2ZXM0tBTkI1b0JoK2lUNjcwR2VZZUV1STVjLzMyWnNCc2RWdnNOV0d4MmpJQXNORWI2Z3BKYVowSUVIbXFPMzJyOGdqOFRqUlcvakl6aFpyY2prWE9sS0ZiVlhxbjFPZlNPNWxVVEx2eFBsRU5ZQmtPa2dRZHpkREJIRDJMcXFsYngxWU5LQTZSY2JVNDJDWk5jZG5xdkRkZEtTdllQdUEiLCJtYWMiOiJlNTYwYjliMjBmNzdjODBiMzkyZTkyMzIwYTU4ODI1MGNlMThmZDE5MGNhMWMwODg4NmJkZjQzN2I1ZDhkZTg3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5pSjdUN1A1Y3FQSEJEY0xxWkQwUWc9PSIsInZhbHVlIjoiSnppSTN1UVV4WWwxUGNtZGc1c0IwbnI1Rys3b2Vudi9DSXBVMVhGT0RKejJUZ0xrSWJlYWcrSVBhYzN5K1JYWGtQYkhiT01CMjAxSEI1YmZWSThjamJ2UFVqY2tjNWQwUi9kVlJacWZreGQrbk50Nk4wTG1HTkY2RlZWODlqK3c5N3FKTDZ0dy9ES2x4L0tqWHJTQ3loaXM0R0xIN1dRbDdZeDhZOXU3OTZUL29ra0tLdHVabXFudks5bFZyZGV2bUZvaE0wUUovZzBTYjdybTdHN0xVN0VmU0FJRjg5cG9vdFZ3eWkrUGE4ZjdTa1daS1RnQUFldlZqaEhKZmQ5V01RQ1B2VEJVNUluajJ4L2VJVFJKVm9pTE81cFlvTG02U0dydTM1MXRqTlZ5S05FMmZTL3ZVQUJCbFNXTVdPTVFoM1dFd0JFUlVMaDhId2dNcUh4RTJza2dQVmovbTBndjVSbDU1QVNsaEZaY1hvTkpmTWNrcldZSmcxWFd1azVyRnZBVEpraWZJSXhBVko5d3NoOE5HOFlFM1YwTURBYVREZEFhaEJ5WnhSQ1l3UTFXamt5eHVxaFhSVnVJdm9JaHUybnJBZkJ2OTBzR0dpZUwxd0wzRU5QSG0vSE5tRWdVNXdjcm5BclNJajhwaUxkOWUxWEZSck10Y3NpRGVxa0EiLCJtYWMiOiJkYTgzZThlZjk1MzkyMDdiMjE2Zjc0MTJiNjg3MGZkYTYwMzZlODFmMWMxODBkMzU4MTcyMTY3MmUyMDZhNjllIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-241448850\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-271562920 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271562920\", {\"maxDepth\":0})</script>\n"}}