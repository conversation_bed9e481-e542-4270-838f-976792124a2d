{"__meta": {"id": "Xebd951658cd4d6bfe2da84d037a2cf71", "datetime": "2025-06-26 22:42:53", "utime": **********.898937, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.499147, "end": **********.898953, "duration": 0.39980602264404297, "duration_str": "400ms", "measures": [{"label": "Booting", "start": **********.499147, "relative_start": 0, "end": **********.849478, "relative_end": **********.849478, "duration": 0.35033106803894043, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.849487, "relative_start": 0.3503401279449463, "end": **********.898954, "relative_end": 9.5367431640625e-07, "duration": 0.049466848373413086, "duration_str": "49.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042024, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027300000000000002, "accumulated_duration_str": "2.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8758612, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.132}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.88631, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.132, "width_percent": 18.681}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.89205, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.813, "width_percent": 13.187}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-547747376 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-547747376\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-13249003 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-13249003\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-757108023 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757108023\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977771320%7C15%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNKMTRYSXJOYWQ0bVFmOXZpd1ZqTHc9PSIsInZhbHVlIjoiU1gwa1MxSnJyb0VIM3lIdmFTK0xQb1dVTVhDb2g2QjkxWXB1ZjVUK1B5UjZmWEdXQ3NwYkFnbmowNGY0VjRtcVJHWnNnN2sza2pkbFJ0a2hzdGhEd1U3NUVtKytDNktIUFNCalJWQXloV1JqbFBzVVJTTE5WakxOVDlUc0s5OUE5RDZPMCtlNzlGMTJodjJIS3hHT05JakFzeTFmSExHbDFaeitzbU8zaGllUUZCYVBueXV3SjZpeFErVzc2VzhtdTkrQ2FqaElySnUzcTNXQmJCMVcvcVdVTjJuTXVzQVdPR1VHd2NKRm5TS0N0SGc2cDB5bjBZd3d5NEc2T1YzWTJ2MUY3dFVPWTB5VjJvNVVLakZGU2c1RzVtdXJYZHB0UmF1QWV0MGhrQ25iaHNGT1krSVZlQmFsbEJDMzUzSlFzbVJNNit6UkpVRlVlSWxoOXNkK2d6R3c0eDJhbHkrNXpBWXVMR1JpNmNGeXE3emtYQmdaTHRrQXZIZk9rdEszdWNMYTFVOWUxVkpjTDV6MTNoRVU1Yy9XbHRXMUxMdjFTaDZobTc2bWR0WEZjL1BJaDRLU0pZUVpSME8vUDhBWkZrdENyYXZGOEh0SUFNb2doeS9zSzgvaTdEUnB6aVhES2FTbXRvZjFkSGROcjcyZnF6dHY0d2ROa3lVb0lMaGYiLCJtYWMiOiJjZDI4ZmQ5NzU5NjU2OTZmN2NiYmYyNzQyZGVkNDhjZDdkZjRlMmY5NGQzNDUyM2JkZDIxODUwMjM0NmE4ODhjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVSTS9McktxR2FvQjF5VVpZRE9JMHc9PSIsInZhbHVlIjoiczY2Qlh4TWlJT2xYZm9KUFlSTnNYSml5OUFKUFlRYzgySGFhZGl5d1VrYWp5eFM4WUk0dkpUaGZCc2JNcHYvSDBqWmFTNjRZeG9qRkphS2d0cUdqcEV4K2JFMEFEMFVOTFdaNlZGQjI1UzlHN0FrajJ1Q0wzbnNJVlIxQnhvWGV1aFNudDRZN2pCNkpoK3pXZVluUVRGazg0VmdScWd0Z1pwREZtQmduY2hkSjVPR2JIdC9aNzNUVmY3Wjc1VCs0SC9YUW1yN2c4VzlZcHhLeEhEeW9zdUNJWVpFcWc4ZG5MNkJlOWRub0x0S3Y2ZjFkSTlTdnRIdVJES3hTQlY1eUV3VVhxQVppTzM0cXlhRXNmUEhMVTVGRHM1Qk9nQS9wb28rZ01rZ1VMQ2dGWWVPdWpZWXJaOE9ibFdETlVuS1NXdlBrWVhHTTVIVmtCUW8wSU8yVWZsU3NGRDhpV1BONHFIYjhLbmxuMnVHamoxc1NWWDgvMFgwUVdvWnV5RE1LU2lvU041MzhFSGtXVzJ6ajRSR1RUWkd2SXBKdUZIUFVxOCt0QmVtaXdzdUJFa0owWWtKQk1zQjBxNHZWcFdXUWZKU2h0QjVQRGlmMmtUcURmYjFlY3lTVkRvU0JvRWNQL3FpWSs2RFE5ZEVUVTAyWDE0T2hnU3FMdzlVckRyR0ciLCJtYWMiOiIxZWFmYjA3ZTk0Y2ZkZjlmNTMzMjIzOGVlMDA5MTkxMmNhZTZjZDI0YTZmNDM2YTgyNDg0YmQyM2VkZjdhNjM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1096273892 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:42:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJhTEpxK1NLT2x6ak1xbE41WVgzRVE9PSIsInZhbHVlIjoiNW9keXNqakxBcTZqcXhVbWVzelNpdi9xRW1JK25xbVBtb3NEQ1FLWm1DeFBTQ2Y1dWRaQnY3Vjc2bkhubFpCZnA0R2U2d3N3QXAzS2t2SVV4OElEU3Frb0NzOFNybWRjNkdzS2QzNEYweVZjWDhXc0U1VFVIY1plZHEzQVBSRDE5ZVhkeTNvRzNJNEgvc0VGUjFQd1o0Wi9DQUdnUE9TcDVQSHJQb1VzMDc1T1BONVR1SGlpL0E3RTFNd1gzbnY4NGlVQnlONk1vdS9Od3ZtaEpQUTR1YlpNQ3NrbHFvNWlnL2RReUJpU0ZUakdLaStyMFowU3MrQ05mUGlrR3U1a0hJcU1QQjlpM0t5UXVWM1lMeHd4eUZWM3d5UVJtVkZ6cC9sRmRmZ0FFQnVTejBObzZ4L2xBcURXNlBrUVpVYWgwMEVXa0pSb3JSUzdOcmpWaGJaaHAwSmdhdmhqWFI4UG1tdlhES2xJdWcveFI2TGYxQjdqQ0ZoOVB6MDZzd2Z5bzlOdnBIUy83SXFzRVpSSmVaVG1jZEV3SndPRXlmZlUxcGxnMkFySFZRS0RXQ09aT1ppSVRaMFhYTWdCNEJ3VUNFTUNNdkhjWEtjTmtzcFdhMlFnYk9zYS9oUllNVGxyN1Q4WGdPdmlXdXBmMkMyRm1ibXJlSXpobzF5dnkrRmsiLCJtYWMiOiIxMzBlZjA3NDk0NmJhYmIzNmY1ZmE2YTdhNzQxZjk0MDE0ZjE1NmZkYjdhYzNlMWQ4Y2QzN2RkMmYwYmQxNzUxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVQVlNSMlBGVlVFaER6cEM5RW9mcGc9PSIsInZhbHVlIjoiVXluWTdQeDZOQVkrSUcrRm1NRzZaUUIyY0haWE44bXRXWmQxTi85T1djWW5hNFRPVXF1c1hsVmF2eDVYODlGVitIekl6TXRnaTNqdlNrWXpQMVlEWk1FN0tpV2V3U3A0dGFGVXh3M0VlV1E2OUU2UmRoSlZRa3VXT3kxbzRvcDdhZHJnaFdxZVoyS2oxV09PbllsZGgzVTVMbFovRkk4SGg4MS95YWZVSG5PQkhacm5tQTFWdXF6YmM4bHRxM2t4Vk5nbnUrK0JWUnF0ellqd05la1B6WUd1Mkp6RGc0VGkrRlZORjJ4RVNVeW1NeHo4RjFOSTNuUlFnc0QydE5NalhraFlGUFlWWFY0YldwK2FrUzNGTjhnRU1pZEVzdmtCUUlaSjNweEg0M0FINE9pa1NOUzBVS1pSQWhPNDdaZG13bG8rSjVGYndPZVRpbFlKaXNZVXVJbDRBMDhRMlZNaFVBVXdBRjl1RnI0M3ZocjFnbGphc003b1EwQW9yNnhRTnlFTGlTcDhpZVk5WEVVRlV5enFUYjdLUUxZSnpZdjNpS0dJdDhoeXN1NER1QVZqMWp6VEMzMUpkbGNndk9BTU9TNHB1UHB4UGw5SEdvdk9VNThUQ1FMYmN1bUF4THlyaDFQTjRwLytONmVUd3dEL0pzSEhPY3FNamk4Q1hpMVMiLCJtYWMiOiJiZTc3NDg2YTZhY2VlMDdmYmRhMzAwYTBmNmUyNjQzN2VjZTBlYjYzZGVlMDRiYTBhODEyMDVmZjRhMGViNzNlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJhTEpxK1NLT2x6ak1xbE41WVgzRVE9PSIsInZhbHVlIjoiNW9keXNqakxBcTZqcXhVbWVzelNpdi9xRW1JK25xbVBtb3NEQ1FLWm1DeFBTQ2Y1dWRaQnY3Vjc2bkhubFpCZnA0R2U2d3N3QXAzS2t2SVV4OElEU3Frb0NzOFNybWRjNkdzS2QzNEYweVZjWDhXc0U1VFVIY1plZHEzQVBSRDE5ZVhkeTNvRzNJNEgvc0VGUjFQd1o0Wi9DQUdnUE9TcDVQSHJQb1VzMDc1T1BONVR1SGlpL0E3RTFNd1gzbnY4NGlVQnlONk1vdS9Od3ZtaEpQUTR1YlpNQ3NrbHFvNWlnL2RReUJpU0ZUakdLaStyMFowU3MrQ05mUGlrR3U1a0hJcU1QQjlpM0t5UXVWM1lMeHd4eUZWM3d5UVJtVkZ6cC9sRmRmZ0FFQnVTejBObzZ4L2xBcURXNlBrUVpVYWgwMEVXa0pSb3JSUzdOcmpWaGJaaHAwSmdhdmhqWFI4UG1tdlhES2xJdWcveFI2TGYxQjdqQ0ZoOVB6MDZzd2Z5bzlOdnBIUy83SXFzRVpSSmVaVG1jZEV3SndPRXlmZlUxcGxnMkFySFZRS0RXQ09aT1ppSVRaMFhYTWdCNEJ3VUNFTUNNdkhjWEtjTmtzcFdhMlFnYk9zYS9oUllNVGxyN1Q4WGdPdmlXdXBmMkMyRm1ibXJlSXpobzF5dnkrRmsiLCJtYWMiOiIxMzBlZjA3NDk0NmJhYmIzNmY1ZmE2YTdhNzQxZjk0MDE0ZjE1NmZkYjdhYzNlMWQ4Y2QzN2RkMmYwYmQxNzUxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVQVlNSMlBGVlVFaER6cEM5RW9mcGc9PSIsInZhbHVlIjoiVXluWTdQeDZOQVkrSUcrRm1NRzZaUUIyY0haWE44bXRXWmQxTi85T1djWW5hNFRPVXF1c1hsVmF2eDVYODlGVitIekl6TXRnaTNqdlNrWXpQMVlEWk1FN0tpV2V3U3A0dGFGVXh3M0VlV1E2OUU2UmRoSlZRa3VXT3kxbzRvcDdhZHJnaFdxZVoyS2oxV09PbllsZGgzVTVMbFovRkk4SGg4MS95YWZVSG5PQkhacm5tQTFWdXF6YmM4bHRxM2t4Vk5nbnUrK0JWUnF0ellqd05la1B6WUd1Mkp6RGc0VGkrRlZORjJ4RVNVeW1NeHo4RjFOSTNuUlFnc0QydE5NalhraFlGUFlWWFY0YldwK2FrUzNGTjhnRU1pZEVzdmtCUUlaSjNweEg0M0FINE9pa1NOUzBVS1pSQWhPNDdaZG13bG8rSjVGYndPZVRpbFlKaXNZVXVJbDRBMDhRMlZNaFVBVXdBRjl1RnI0M3ZocjFnbGphc003b1EwQW9yNnhRTnlFTGlTcDhpZVk5WEVVRlV5enFUYjdLUUxZSnpZdjNpS0dJdDhoeXN1NER1QVZqMWp6VEMzMUpkbGNndk9BTU9TNHB1UHB4UGw5SEdvdk9VNThUQ1FMYmN1bUF4THlyaDFQTjRwLytONmVUd3dEL0pzSEhPY3FNamk4Q1hpMVMiLCJtYWMiOiJiZTc3NDg2YTZhY2VlMDdmYmRhMzAwYTBmNmUyNjQzN2VjZTBlYjYzZGVlMDRiYTBhODEyMDVmZjRhMGViNzNlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096273892\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1677017418 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677017418\", {\"maxDepth\":0})</script>\n"}}