{"__meta": {"id": "X13025d26331b659e2eaaeead8a71ed6f", "datetime": "2025-06-26 22:43:01", "utime": **********.368506, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750977780.938653, "end": **********.368519, "duration": 0.42986607551574707, "duration_str": "430ms", "measures": [{"label": "Booting", "start": 1750977780.938653, "relative_start": 0, "end": **********.312587, "relative_end": **********.312587, "duration": 0.3739340305328369, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.312595, "relative_start": 0.37394189834594727, "end": **********.368521, "relative_end": 1.9073486328125e-06, "duration": 0.05592608451843262, "duration_str": "55.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45030352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026100000000000003, "accumulated_duration_str": "2.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.344637, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.199}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.355224, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.199, "width_percent": 16.475}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.361125, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.674, "width_percent": 15.326}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InJYTjJ6RlpNeS9OM2FhZ1d3dDY2a3c9PSIsInZhbHVlIjoidloyT0twSm5OQ3NYM29BWUIwWnUrQT09IiwibWFjIjoiZDkyYjdkZDE5NzdkMmRiYWY4MjhkZWRiMGI1N2M2NmI3YmQyNTllM2IzY2ZlMjg2MmJjNDVhODEzYmRhNDliNiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-509969625 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-509969625\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-903256990 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-903256990\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1745975013 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1745975013\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6InJYTjJ6RlpNeS9OM2FhZ1d3dDY2a3c9PSIsInZhbHVlIjoidloyT0twSm5OQ3NYM29BWUIwWnUrQT09IiwibWFjIjoiZDkyYjdkZDE5NzdkMmRiYWY4MjhkZWRiMGI1N2M2NmI3YmQyNTllM2IzY2ZlMjg2MmJjNDVhODEzYmRhNDliNiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977773814%7C16%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkQ1dDRlNmUvNDVpczlvS0ZZTlo0V1E9PSIsInZhbHVlIjoiS2ZldUlMdjg2SEtYL2JQZHB1R2xPY0JRcUtBQkZYdTRCTnlBb3JteWpOU05ra3FsS2twSU1oKzhGeHR3KzJDMWNhaHdVeXZOZE1qZVdmYVM4N2ZOYWVzSEtiQitXQUk2Zmo2aTZ1N0doNG1SQWhwRXRKbGxRSXFWeUdpUnZ6S1NBcExmZDBKTS9sL0J3T29wQ24xU1MzRjIvWUpSYXoxeE9maUJXbFJnWXBaOUdWdGoyVVkvQWgzeVY4S0hRVG9YeXVIRlI5ZURXcTBQTGhiVXV5enFHcDRXTnEvclc0ejZialpyKzI2M1pTMGhyWlFCOVBzcVU2WXEyR05iSGNIQmtyckVxby9VNXhSQVc1WDYwYnJjMHgrU3RlaVN6MTVGUnBiU2ZmMEdmS0ZCUEF6TmhlYXdNeUZBS2tYMFlwSThxWkQza0tMQXZ4S1pac2lUU3pPOUE2TzNSL0psL3p1c1lVdFBQTTlvc1daemo4aE1SbkEwTFZkSkl4RlkzSUZrRjk1VXFYTVorOEZWamkxRU5mcUo5ZmxkM001QndLczZJOUtISVNUbzBKUk16R2QydmJwTmZsdW5HNzd3bWgzMGUwWFVQTVVLbHYwRmEraTFnUHllcTZwNk9SZ1hFR1FTYjNnWlNPeGhVTlQvelJlRXhQQndpWE96dXdUMnFKS0YiLCJtYWMiOiI4ZTgxMjc3N2ViMDEyMDRkODc3ZDk4ZWRmYWRlZjNhNTg1ZDAyZDRlOTg4YjUyZWYyYWMxOTUwYTZkYjYyOWExIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtlQjI3ZGtBaGhtaHdWRldPSDVaZnc9PSIsInZhbHVlIjoiRU5kc0pOYUpWUmxMZ2c3elh2RkpKMi9laUo3bnBDYmZ0cnVNSG82eE84RVhJNHhWNEtEVXpJWi9sVTVJT1lXaitGblljYTI2WlBOMElJeG9Dem44am15dVN6VlV2MitWcHBIMGVKK25IUzlsREdpbzh5VFcyRkZPcUxoUTJ1ZExHSHF1ZEUzRGxNUTZYRGRJaEJlMXUyaG5VMTFwaDFjZE9wYzlzZCtVVjBEQ25xOXlNTzMzWW4zbXMxamZ4aFQzSnJVUXd4bnAwcGYwZnZQQm00SkdGbU80am5STG80OTFsaE80d0xnYmNRTFowcERJQmowR1JxZ2RpTFpYU2p6bkhMQWd3cGpqY1R1Q1hMelhzdVJsRlM4YjFrQllMUy96aDlkaGtyOVZXdkNBQXFiVGJGUnE3UDRKOHJuampJaWFJK0RXZ1dyS3BIRWVQemFBQUZqNWtMMW50di8xbHFVY1J6M0svTkNHVVduSDd3dCswTmxLcmRBbjVEV1dVOEhkSVNkMTVINEY4TGtPRGdNaG84eCtpUjFVUWI3N3BHWHNNK2RhdDduMUxzQ01LNFZRdzM2c2RORXBHLzBPYmtMWHhlUndKZFVxQ2t2U0pmTURZSElUMW9WQlRUdjRqMkpxM0lSd25BcGNXRXNmaW55cDVrTDBFU3g4c2V1OEd4eDIiLCJtYWMiOiJmZmE3NDU5ODljOGZiMGE2ZmM3YjM2YzdkYWYwMWM1ZGYzZGQ4ODBlNWU4NTk3ZjMwNTQ3MDk5M2M4NGIyZTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-233207949 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:43:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVaakthdldER2pSeWJxanE1N1ZURVE9PSIsInZhbHVlIjoiSGhPL1BmVEUxVktxK0E4ZHlsRmtlWklpeTRkaHFKVlNSNDcyU29IZk5rVWgrTHNRdGU1ZlRSTkdhTUJMc0xIaStEUUo0VHp0NWsxQklWVThKZGxibkx6L2s4ZUJxWERxWWxldXVzZ1FqWVhhc3dJMXBCY25qU21GMXVOelIzc09MaFZyTUtPeWtMcDRSVHZtak9OTlhwZWZLZVRjRk5HQmtUT3ZMNnc0SVVCV05VdzVXeExhTTE4MTJLSjBiTkpBeEVVVWg5Tm9Ealg1a21RTDRVYjkxejVUblVUcUFZZW9JNGtqdVZad3JYQllRdCthWDN1eXRER0FVazJ1RVBKUzhIQnBUZVZjUzFOc3VhWm96ZndtcmZzYUZTSW5KZkxXOGUwVjVLRndmZEMrdW5LaUhHWWVkcjdOSXJkd0tWVEd1T1Z0eWQ2cExPVUNmMFNIS2lMdGhjdFNuMmlKY3JjdllLVDFWeVRJQ0p5TXllMkhHcjJ6WHZTMmNCUnlmL3l3YUwxSlRnQm5wOXNrMzRENWM1TW9JR2c4QVh3QkZUc0dtem00M1lNRkQ5VTJ4TmJkVzFSVEd2ZzJFSGFMZmdoTlhySHlVUFJJdXdEQWxldWtVWGp2NFl5SjN6RHZhcWhuWHljS2wvZ1Mxb2RPUW15ZitvRlhrR045WkhHT2N1RHoiLCJtYWMiOiJlZGE0OGI2OTBhZDA1ZWE4N2Y3YzJkZGFhZTdkODU4YTJhNjBkZmI3N2ZhODI0MWE5ZDE5NDU3ZGE1M2QyYzI2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:43:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IldVcGdlSlhxWXpmVXlVaXo4MGljK3c9PSIsInZhbHVlIjoiM3QzYnI1dkh1QnkxeTcyZmZ6dncvZE9hQys5VWVlUTZKVXQ5Q1IrOVFEZ2JuUDlVc3RmN3c5dmgyMS85UTNBM1lWTHBRZFQ4NHRNVFMyRlpQdlBJN0xYdUpPVjQ3Y3J0dUs5R2M2K01HUXcwTmRoTG81dWdrdkZwb2dQL1FxUTdZRW9ZK0dMQ0x4V09TaWdRRG5IV1JFcDFOSUVlNVJZVUxnVW9pWDVLSlYwWUN1cFdCYVhoNlRwK0ZVUVk0RlFjdkdxc1QxR200aStGWEhwZzh6T2lEN2h6YjhWVTNBTEZJMlJXZkw1RmtFaVV4YkszN1Vjc3JyT3VpQlVoYmpVNnF0cmE5ZURHVXZVVlVKL3BJYm5ObTZsWlZsTDJlK2ZzRVpvbU5VcTZnVVRHZE9IQXNRMWdOYmdRcUhiS2RnRy83dnE2Lzcyb2ZIMnFKNU04OFR1Y1oxMXIxZ3N6ZS9jM1Q4ajkrVmRUQ3c0Zi9FTUFwanF5ajBRc1dFamhUT2gwQ25paTJwY0ppUjduU1FoNXFCMjNrdUJOOEtjb1JUM1NxcHdna0JQQnE1ckV4VkNTQnhBNWJlMXVObTRhZHdnRVJxcWVHUUMrd3BLSzMrMktGeXk2MXNpM1YvbU84SHl0WUJQM0M1cnZJU1NlSCttU1RvcmNaSUZxSW1oYTR4TzMiLCJtYWMiOiJmNzBkM2M3ZmZkMjcxNWVlZTNhYWYzYTUxNWRhZmZlNTJiNzY5NWEyYmFhNDZlMTBmZTBmNmQ2NWUxNzFjNWE4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:43:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVaakthdldER2pSeWJxanE1N1ZURVE9PSIsInZhbHVlIjoiSGhPL1BmVEUxVktxK0E4ZHlsRmtlWklpeTRkaHFKVlNSNDcyU29IZk5rVWgrTHNRdGU1ZlRSTkdhTUJMc0xIaStEUUo0VHp0NWsxQklWVThKZGxibkx6L2s4ZUJxWERxWWxldXVzZ1FqWVhhc3dJMXBCY25qU21GMXVOelIzc09MaFZyTUtPeWtMcDRSVHZtak9OTlhwZWZLZVRjRk5HQmtUT3ZMNnc0SVVCV05VdzVXeExhTTE4MTJLSjBiTkpBeEVVVWg5Tm9Ealg1a21RTDRVYjkxejVUblVUcUFZZW9JNGtqdVZad3JYQllRdCthWDN1eXRER0FVazJ1RVBKUzhIQnBUZVZjUzFOc3VhWm96ZndtcmZzYUZTSW5KZkxXOGUwVjVLRndmZEMrdW5LaUhHWWVkcjdOSXJkd0tWVEd1T1Z0eWQ2cExPVUNmMFNIS2lMdGhjdFNuMmlKY3JjdllLVDFWeVRJQ0p5TXllMkhHcjJ6WHZTMmNCUnlmL3l3YUwxSlRnQm5wOXNrMzRENWM1TW9JR2c4QVh3QkZUc0dtem00M1lNRkQ5VTJ4TmJkVzFSVEd2ZzJFSGFMZmdoTlhySHlVUFJJdXdEQWxldWtVWGp2NFl5SjN6RHZhcWhuWHljS2wvZ1Mxb2RPUW15ZitvRlhrR045WkhHT2N1RHoiLCJtYWMiOiJlZGE0OGI2OTBhZDA1ZWE4N2Y3YzJkZGFhZTdkODU4YTJhNjBkZmI3N2ZhODI0MWE5ZDE5NDU3ZGE1M2QyYzI2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:43:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IldVcGdlSlhxWXpmVXlVaXo4MGljK3c9PSIsInZhbHVlIjoiM3QzYnI1dkh1QnkxeTcyZmZ6dncvZE9hQys5VWVlUTZKVXQ5Q1IrOVFEZ2JuUDlVc3RmN3c5dmgyMS85UTNBM1lWTHBRZFQ4NHRNVFMyRlpQdlBJN0xYdUpPVjQ3Y3J0dUs5R2M2K01HUXcwTmRoTG81dWdrdkZwb2dQL1FxUTdZRW9ZK0dMQ0x4V09TaWdRRG5IV1JFcDFOSUVlNVJZVUxnVW9pWDVLSlYwWUN1cFdCYVhoNlRwK0ZVUVk0RlFjdkdxc1QxR200aStGWEhwZzh6T2lEN2h6YjhWVTNBTEZJMlJXZkw1RmtFaVV4YkszN1Vjc3JyT3VpQlVoYmpVNnF0cmE5ZURHVXZVVlVKL3BJYm5ObTZsWlZsTDJlK2ZzRVpvbU5VcTZnVVRHZE9IQXNRMWdOYmdRcUhiS2RnRy83dnE2Lzcyb2ZIMnFKNU04OFR1Y1oxMXIxZ3N6ZS9jM1Q4ajkrVmRUQ3c0Zi9FTUFwanF5ajBRc1dFamhUT2gwQ25paTJwY0ppUjduU1FoNXFCMjNrdUJOOEtjb1JUM1NxcHdna0JQQnE1ckV4VkNTQnhBNWJlMXVObTRhZHdnRVJxcWVHUUMrd3BLSzMrMktGeXk2MXNpM1YvbU84SHl0WUJQM0M1cnZJU1NlSCttU1RvcmNaSUZxSW1oYTR4TzMiLCJtYWMiOiJmNzBkM2M3ZmZkMjcxNWVlZTNhYWYzYTUxNWRhZmZlNTJiNzY5NWEyYmFhNDZlMTBmZTBmNmQ2NWUxNzFjNWE4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:43:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-233207949\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1442779708 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6InJYTjJ6RlpNeS9OM2FhZ1d3dDY2a3c9PSIsInZhbHVlIjoidloyT0twSm5OQ3NYM29BWUIwWnUrQT09IiwibWFjIjoiZDkyYjdkZDE5NzdkMmRiYWY4MjhkZWRiMGI1N2M2NmI3YmQyNTllM2IzY2ZlMjg2MmJjNDVhODEzYmRhNDliNiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442779708\", {\"maxDepth\":0})</script>\n"}}