{"__meta": {"id": "X17292aa6e568f9e0379a22fa3e79a023", "datetime": "2025-06-26 23:14:54", "utime": **********.470102, "method": "GET", "uri": "/vender/4/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750979693.704063, "end": **********.470115, "duration": 0.7660520076751709, "duration_str": "766ms", "measures": [{"label": "Booting", "start": 1750979693.704063, "relative_start": 0, "end": **********.044927, "relative_end": **********.044927, "duration": 0.3408639430999756, "duration_str": "341ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.044936, "relative_start": 0.34087300300598145, "end": **********.470116, "relative_end": 9.5367431640625e-07, "duration": 0.42517995834350586, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51486488, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x vender.edit", "param_count": null, "params": [], "start": **********.117592, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/vender/edit.blade.phpvender.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvender%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "vender.edit"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.394111, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}, {"name": "1x components.mobile", "param_count": null, "params": [], "start": **********.395198, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/components/mobile.blade.phpcomponents.mobile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fcomponents%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.mobile"}]}, "route": {"uri": "GET vender/{vender}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "vender.edit", "controller": "App\\Http\\Controllers\\VenderController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=168\" onclick=\"\">app/Http/Controllers/VenderController.php:168-182</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00543, "accumulated_duration_str": "5.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.070812, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 35.912}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.080896, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 35.912, "width_percent": 6.63}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.094456, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 42.541, "width_percent": 11.05}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.0963662, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 53.591, "width_percent": 8.103}, {"sql": "select * from `venders` where `venders`.`id` = '4' limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\VenderController.php", "line": 172}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.101084, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "VenderController.php:172", "source": "app/Http/Controllers/VenderController.php:172", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=172", "ajax": false, "filename": "VenderController.php", "line": "172"}, "connection": "kdmkjkqknb", "start_percent": 61.694, "width_percent": 6.814}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'vendor' and `record_id` = 4", "type": "query", "params": [], "bindings": ["vendor", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\VenderController.php", "line": 173}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.103283, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "kdmkjkqknb", "start_percent": 68.508, "width_percent": 24.125}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'vendor'", "type": "query", "params": [], "bindings": ["15", "vendor"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\VenderController.php", "line": 174}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.106577, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "VenderController.php:174", "source": "app/Http/Controllers/VenderController.php:174", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=174", "ajax": false, "filename": "VenderController.php", "line": "174"}, "connection": "kdmkjkqknb", "start_percent": 92.634, "width_percent": 7.366}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Vender": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=1", "ajax": false, "filename": "Vender.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-316049467 data-indent-pad=\"  \"><span class=sf-dump-note>edit vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-316049467\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100099, "xdebug_link": null}]}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/vender/4/edit", "status_code": "<pre class=sf-dump id=sf-dump-1556951912 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1556951912\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-377946567 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-377946567\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1697750861 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1697750861\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-642325003 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979690900%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdwRzFyaE54V3ljU0NrVDk0Sjl4bVE9PSIsInZhbHVlIjoiK1BnYytlMmwyNXpTV0d0SkxBVGFKeDRqOTY5a0lpcWlxWHRpRThSamNUWFhvSFBXWTFLV2YxRGZpSS9SOG00N0U2b3I5SWhxd3RlZjE0ZDN0RFAyYm9oZmsyVTVOdUVCWERXZkRPbGtaVGppaUgrZkQ4RGpWK0NjTHJ2ZytwSU5NVVgrcmFqZWFIODBxK1VYdlBNMXA4cDR1OVhHTkd0RmFaOW1NOHNwVUJhdDIxYnEyZmc5U2dZNVdHcG9ySTA0cW1JK01HcGF6ZEZSL2VPbEE3UnFiYmZNVXdNWi9xaE44VDljanRGSy84bWhsN0wzTHpEVnFDM3F4ZzhPZlcyMnM5K1VDNnJDRWYvTy9XV29nS0FHdkJZWjQyWGRhVE9yUWdvdENiQkU4WFloWEJQd3ZqQ0ZNNGpSMzJ1azRmRG1YNTZVZHhnN010RWFENnVYSEhVQ1Y4Z013cHJTbEtJQzBRa2ZGL0RoNGljL3V2K3RscjJ1cWlFRHJkb25taVkrR24xOXIwUTBOWGw0Wm90a25ydUVNSWFBSXNMVVhaemhMcWFMVUNreUxuUmR4QTkyVTBIa1NtZ0hSWlg1RnkzUjRSc0krQUhVRU9FWVYxK3BmeHM1VmlJNFZBNnl6TXBmUE8xUTVIbTh2TkdVdTVmRXdLWCtCQTZ6cHFrMVhMNVgiLCJtYWMiOiJjYWE3MDFkMjc3MjJkN2U0YTUxOWY4ZTJhOGQwOGIyYTNkZTlmNTUwOTY0Zjk2MTc0YzYyMzE0MGRkMGJkOTgzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJXTlBhVUp4aUVvM0FJVGt1U3lva2c9PSIsInZhbHVlIjoiNnpDbjRHODdVazEvRlU0VkthZmhrQy9EZ1VKV0piUldqL3RmWUdPU3J5bkxOMUdWeE5PWno4RDZCTXNBMURGVUI2ZGdzQkdWQ1QwMHVFU1dGRWJKNzBiMVV6cGtyQ2ExK0d1d0xFdW4wZXdXTjd1STI4YzFZZTQ3c0F0REpJYi85VE9WVWhWTm5OWlZvbUM0RU5SdEJsR1l6L25SN0JxREhUZS9wRklTWWxuc1grS2p0SHo5ZUlkWXNLK2REN3hSTXJwT1NrSVlpRU4yeThqRWJ6T3JhMTBCT0paaVhZVFBQZ0V5Mk5mUXo3MGNhbWpmVXpWa3h6L2o0eDZpR3R2VXZUL2pzMzVuQXVJZjE1ZUZ3Um1kTXlCbWFCTStKejl0Qm05Z2g2WThuajVpOWlud0FCenhBdmlKS2pnR0ZQTEQvYURBZWxqVGQ4c0Y5cVpBci95NjExL1dUb3ozRFkwN0g0c0VXR243eHIrQzc4RU85MEo3VGhveXpiTVZHaGdLSitFMGZidW5SaEVPMGQxV3d5K1IvNU10OG10NFFDRmpaTDY4OGliblZsZlJqZC9GU2xFekJnNktDWGp0cGcyZEVhRFluWWprQ01PcGhoQTRjanpUT29LQk51Z04vRXFUcTlkRVI3dGFsUkd0S3dCRW9OL1Y1VVBHeE5VT0JreDkiLCJtYWMiOiI0ODEwMjE2MGYxYjk5Y2IwNjMyNjA2YjA3NmFkNWY4YmFlYWI1ZDcxZjA4OTAzZTRiNjZlMGY1NzA1NjQ5ODQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-642325003\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-338803847 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-338803847\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1189401269 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:14:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imt5bU9zNWZBTkJLS1FhaEphZWxjOFE9PSIsInZhbHVlIjoiY3djclIzVXhZRGdnVm5ZSmtQTEI1cFdwTnN5L3VMQ2hGUXMzV01SQUpKNW8wRFNCbkxyYmZ2WFVIcVlaV0w1c1JwWVpKazJFc1RMOVUzaEZBcExQVFFoY0ZGSWpkS3J2TlVpRHVwazNGdW93dWNPdmJkcUtvMXNZK1JzcnE5U0Z4bHh3WGo4NFVUZnQ1WWtkSkpMeURCbGxCeUpIY0xSRng2Y1Bub2syNEZzMTVZNWVWcndVNlFFVVc2T2dkOHgvNUQvUDUweE5vaHpnTnNYNTZOcnFpVHZ6dzdRSWpGWGsyaVZnY3dCMU1HYUpVK2hPa1I1MHRuclZtWGtFM3J0eGtLdlJVODdrTFpFeDRWVTVTVUxlaUxDNlpnSHEya0dDME9veGZaTlo3MmNaaTlyeTFzZUdGUzV3SHB2cGNHaTgxM0thdWxLNklZcU5zaVJGajlnMFg5a1JueXc1VWVmN1VEaTUyblFkMDBCTHRaM2lDeW9DYzYwaVRxUmpDUlJiYnIwUHpsY3FTMmlIUVYxRFVlQ2QyWGd6Y2V4RUw3VmdJZVpzam9UQmo5ZU5aemdDbFpYUUZFN3Z0UndWSDVIdlFnMEx5TWQ2KzJlUE5ZM1IyOXNQenZ3Sm5BeUVNdVFrbTRQejI3VzJvQ1JqM1BpOU9XNTBDdG5kcDMyWkl6VysiLCJtYWMiOiJlMjJiYTM3YTg1YTJiMmNkOTZiOTUyYWJhODQ3ZDQyM2ZiOGQ1YzljYWI1ZTlkYWI5Mzg5MWIwZmRjMWJlMzAxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:14:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlcvNEYveC9uRUJQdGVucDBtMHJnQ3c9PSIsInZhbHVlIjoic2xnZzdHVXA4YVdTSy80NHF0T1VPcDd3and5akRIRWYrOEZOc3NLL0xQU0VOYUhpYlNWOTZqOXkydDVpSWpMTFpocHdpUU1NamRrN1pLU2wraEdEdmNUZUVqL3VmZWt1bU5OdXpUcjBTeWxFQXN4SzAxaFBWeFpzN3FQRDV2QnMwMWhaRTF1MTB5ZFZ1RzkxQmg0b1NBend4Q0ZIUmJoamNCOThSeXZlM2Z5QlR6czFhbHFiYmsyc2I3cUk1TUdRT1RkTDhORnY2bGtTeld4bm9WVkNmNzMzSUZ0bzB4S0xaWFYvV0dHQldjQmdBS0VIc2hFejBzb3A4MTczZ0F2RkN4YnFjSmlieEh5ek1Lanh2c25PNzBwNVR1NUQxQWIxeDY3ZEhVVTNOVTBwWGoxclYxTTBQbUE3MVJWOG8yNk5xTDg5R2x2MnVGeitFM1NjeHRqb0NEQjBDS3NXT2ZCcjV0WDV5RXlzSVQ5b1QwQzNvd3RMTHd1cGtDY2NrTTlRMmRtM0NMUVNNcFdPaFdwa2NtL2EvMCt1N3lobmp2Z2h6V202dXNETWlwMGh4bVE4aTdJR0lscE4wcFhuRWM4dzJRZ05wQlpMTHF4VFhubk1DVWErbzdzWEhxQ0ZoZG9pNCtYNWF3dEtrNEFUdEFlUWxqN0ZEMnp3Y2ppemxGWmUiLCJtYWMiOiJhMzYxNzQ1ZDlhMjZiODBkMDE1NDFiOTQ3MzJmZWFlOTcyZGRmMmE5MDJlYTFkOTQ5NWQ2NWRkZjM5OGMwYjJhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:14:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imt5bU9zNWZBTkJLS1FhaEphZWxjOFE9PSIsInZhbHVlIjoiY3djclIzVXhZRGdnVm5ZSmtQTEI1cFdwTnN5L3VMQ2hGUXMzV01SQUpKNW8wRFNCbkxyYmZ2WFVIcVlaV0w1c1JwWVpKazJFc1RMOVUzaEZBcExQVFFoY0ZGSWpkS3J2TlVpRHVwazNGdW93dWNPdmJkcUtvMXNZK1JzcnE5U0Z4bHh3WGo4NFVUZnQ1WWtkSkpMeURCbGxCeUpIY0xSRng2Y1Bub2syNEZzMTVZNWVWcndVNlFFVVc2T2dkOHgvNUQvUDUweE5vaHpnTnNYNTZOcnFpVHZ6dzdRSWpGWGsyaVZnY3dCMU1HYUpVK2hPa1I1MHRuclZtWGtFM3J0eGtLdlJVODdrTFpFeDRWVTVTVUxlaUxDNlpnSHEya0dDME9veGZaTlo3MmNaaTlyeTFzZUdGUzV3SHB2cGNHaTgxM0thdWxLNklZcU5zaVJGajlnMFg5a1JueXc1VWVmN1VEaTUyblFkMDBCTHRaM2lDeW9DYzYwaVRxUmpDUlJiYnIwUHpsY3FTMmlIUVYxRFVlQ2QyWGd6Y2V4RUw3VmdJZVpzam9UQmo5ZU5aemdDbFpYUUZFN3Z0UndWSDVIdlFnMEx5TWQ2KzJlUE5ZM1IyOXNQenZ3Sm5BeUVNdVFrbTRQejI3VzJvQ1JqM1BpOU9XNTBDdG5kcDMyWkl6VysiLCJtYWMiOiJlMjJiYTM3YTg1YTJiMmNkOTZiOTUyYWJhODQ3ZDQyM2ZiOGQ1YzljYWI1ZTlkYWI5Mzg5MWIwZmRjMWJlMzAxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:14:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlcvNEYveC9uRUJQdGVucDBtMHJnQ3c9PSIsInZhbHVlIjoic2xnZzdHVXA4YVdTSy80NHF0T1VPcDd3and5akRIRWYrOEZOc3NLL0xQU0VOYUhpYlNWOTZqOXkydDVpSWpMTFpocHdpUU1NamRrN1pLU2wraEdEdmNUZUVqL3VmZWt1bU5OdXpUcjBTeWxFQXN4SzAxaFBWeFpzN3FQRDV2QnMwMWhaRTF1MTB5ZFZ1RzkxQmg0b1NBend4Q0ZIUmJoamNCOThSeXZlM2Z5QlR6czFhbHFiYmsyc2I3cUk1TUdRT1RkTDhORnY2bGtTeld4bm9WVkNmNzMzSUZ0bzB4S0xaWFYvV0dHQldjQmdBS0VIc2hFejBzb3A4MTczZ0F2RkN4YnFjSmlieEh5ek1Lanh2c25PNzBwNVR1NUQxQWIxeDY3ZEhVVTNOVTBwWGoxclYxTTBQbUE3MVJWOG8yNk5xTDg5R2x2MnVGeitFM1NjeHRqb0NEQjBDS3NXT2ZCcjV0WDV5RXlzSVQ5b1QwQzNvd3RMTHd1cGtDY2NrTTlRMmRtM0NMUVNNcFdPaFdwa2NtL2EvMCt1N3lobmp2Z2h6V202dXNETWlwMGh4bVE4aTdJR0lscE4wcFhuRWM4dzJRZ05wQlpMTHF4VFhubk1DVWErbzdzWEhxQ0ZoZG9pNCtYNWF3dEtrNEFUdEFlUWxqN0ZEMnp3Y2ppemxGWmUiLCJtYWMiOiJhMzYxNzQ1ZDlhMjZiODBkMDE1NDFiOTQ3MzJmZWFlOTcyZGRmMmE5MDJlYTFkOTQ5NWQ2NWRkZjM5OGMwYjJhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:14:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189401269\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-189686526 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-189686526\", {\"maxDepth\":0})</script>\n"}}