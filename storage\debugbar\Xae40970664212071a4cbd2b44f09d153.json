{"__meta": {"id": "Xae40970664212071a4cbd2b44f09d153", "datetime": "2025-06-26 23:22:09", "utime": **********.147061, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980128.705928, "end": **********.147076, "duration": 0.4411478042602539, "duration_str": "441ms", "measures": [{"label": "Booting", "start": 1750980128.705928, "relative_start": 0, "end": **********.096837, "relative_end": **********.096837, "duration": 0.39090895652770996, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.096846, "relative_start": 0.3909180164337158, "end": **********.147078, "relative_end": 2.1457672119140625e-06, "duration": 0.05023193359375, "duration_str": "50.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027099999999999997, "accumulated_duration_str": "2.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.124432, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.362}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.133767, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.362, "width_percent": 14.022}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1401992, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.384, "width_percent": 23.616}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Ikg4MlkzMlRpNmxOQ213YmdkQnNWa3c9PSIsInZhbHVlIjoibFVSWjhlMDUzTDMwNXZmTTR2bFQ1Zz09IiwibWFjIjoiMmVkMjk1Yzk5MjVmZjMxYzk1ZWI5NWE1M2UxOGI1YzM4MWJmMzZlOTkyMjZjNzFjZmY1ZDFhOWQ0NDYzN2Y2YiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-589052050 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-589052050\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-382699148 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-382699148\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1523595638 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523595638\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-623426078 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Ikg4MlkzMlRpNmxOQ213YmdkQnNWa3c9PSIsInZhbHVlIjoibFVSWjhlMDUzTDMwNXZmTTR2bFQ1Zz09IiwibWFjIjoiMmVkMjk1Yzk5MjVmZjMxYzk1ZWI5NWE1M2UxOGI1YzM4MWJmMzZlOTkyMjZjNzFjZmY1ZDFhOWQ0NDYzN2Y2YiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980108782%7C26%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkQrRXM1TndyNXV3L3JCUVZaMFNWN2c9PSIsInZhbHVlIjoiSlVPbE9MOWloVGFXeTUrTWx5cDN2UGY2T0srVTRYY2drTktwQWo1SElTc3V0b3ZMVHJGK2gvdU1tWWJQSjNLaVA1dFRaRTJibTNMNDZvU2VDczBIYVVvQTZmL0VYdFZJNUdmVngxbmNzK1FNSVZiREhhZ0YzVVJpQ1QwZDZtcDQwNU9NZWhqYi90ZWpSMDcveFNVNzQ4ajhMa1J5cVdNanBpckwwZGxNRFRUT1h6VktPanNpMDF3eTJIa3lRKy9JSEJvODVVUElmT2ZibnFPSktHbVgzRC8ydEpvaE14N01kUE5OTVF3Wi9PZHl6ZGI5Nm9jVTlTaTdkQ2pTcVF2TUJpdFNYMFdLRVd6Wng0SjZKNWhQaGhlRXBGcGlKdGNPVmhXUUkzQnNVMmhWeDErL2dzUmkzdXJaM0NBdWd4SUVsVFdOKysvTDZ5a0tQSFd0WXZ4NGFIUDEwNFRMMjY0Skl2OXlxcnhpajArZVV3cHM3TkxrNnZJVnFOdkpsaTdUY1hGOC9JOFF2V2ZmdVBUWm8rOURIbE5MaGxCNDF2V2FPWFFOUmR3Mml0SUJ0R1FyQTh0MVJHWTVQNFZCOUNPa2l1UWRQVHBHdCtQR2Z5NkVLNndvU2ZKNFRtZ0pCOFU3QkVVNTU0ZkhEY0pjeGc4bnprOGZLTDFLbDNZazBMTWkiLCJtYWMiOiIxNDcyOWM0YjBhZjkzZGQxMDFkOTIwZjQ5NDdjZjE2MWIxNmZlYjJiN2ZjMzNmNTdiNTlkYzI4MTgyOTI4MDE3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJBaHQ3S1Q5RGVTRks3NzVpczBlRlE9PSIsInZhbHVlIjoiUWtWcjYzZkk1d2p3cm5jZWtHZ1JKUGJrMkhrUEtBdHZZL2dBSnZieWI5cFprdTFzbkgydHdXUzlNSVVLeVVDZzJWQmZNZEViS3RXRGh1N1N3VWlGMFM3UHZXeVBKMnpTK0lyZnFqTHdQa2V2ZXJONG10NHlFaDEzdHpzRGJXSGtTSWNibVlvb3VPSTI5SWd4N3lIMngyU1dkT3daZlZ4cElNSE8wQmZDa0UzazRwaFpBNmdCYlY3L2kzekFxN0I4bFVjZ1UrRmViZ0ZKMXEweVkvSjhEUkM2WHgvaHdmaEIweStUam9NT3p6MTJ2MExVWHFZQ3RaUGVPZE00ekhoRFhKMDB2ZUhwcWR3c21RSllENFBEd1U1cGVrUG9HRzE1aXlzRm5EVXNtT1lLTkI1MTRWNmxjZGpFTEhpKzNLS3Y0NjU4QmVvMlorOUVROEp1dEpGUnJqNXZKMHFMZ2RvbXJaR2hhbWNka3loY0lOQmo3TXYvMStMTlZMVkljM2FzL3B1R1R4dVJxaFg1RjE4eWNsWTZ3QjlBTHZ5M256dEJ4aE1DY0tsYkxVdGkyNDdmWVhlcVFZNUtyZG95ckdPUW5xRkRnZEdMTldIWjF4V2F4L3ZxNlR6cmo4VVRsSGoyN0RjQnFMSzdPQ3JqaytRaHdCYmx2NHh6K0U3emlDV0ciLCJtYWMiOiIwMjA3ZTcwNTY5MjY4NWMzN2E2OTA5ZjQ4NjY2M2U2YWFlODFlMDNiM2QyMjIzNzBlNzBmYjljYjEyYThjZTZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623426078\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-891982601 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891982601\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1057927068 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:22:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldhVkhEWTc1bzFkQjI5RjFVbkZrQkE9PSIsInZhbHVlIjoidTd1ZHgrQ3pDU1hXbFlEVDJkNTBWZW5NcTZnb3dwNVpRV1JJeHIzTzBPN1ZDc3NqVUhTN2lld3FESC9ESHQ4M2RhSzhVRXkxdVFqS3F3K01tZmFJUDZsYWdZQnJTNEszdHdzZHVYRDJkMEo1dGRWbnRhaDVhWXhFMEpSNk9pQXZVN0JtdDhwV0dDWnU1RzNONVBZOFVJbDdsaXRyVmg0TWpRNldiR0VzZm1YcXF2d1NGSFZERnVGOTBGVzhRN1c4N3p3b3RTd2V5c2Y3d2I0bXUrelZJRi94NlBzOUgvSzVHVkh4UmRUWjk1RG5TaWdMRFRwU2liSy83b3VTZC9MdUVvaG9CbXByRTh1Y0U3c1o2VlNWd3BqRng0RDJtYjdJN1o0akhpN2dSMXFwNEc3MVJtd2N1enRpM1RRNTZlWkxxNVBkQXptVkMyV2MvVU5UTkt2QjZyVno4d1hWdUxJTEs1YzdiWlJabUZkRGpOU2x5SWlpVTE5M0krRnVRczVoaGdPWTI0RTMvVDZTejFLOG1EWit1YU1vSnRobmpONURxNko4c2ZxUjgxdmI2ampObkFpK2dJVCs2U0VDMlVQZkJ5c2ZaeXlTdDNobkpxd0NNSlYvZnhLb3g2ZlVyQjI1cFNNM1NEQ1FqWGwwVzg3ZWdicXA0Wk1MTEJMMndkS0wiLCJtYWMiOiI0OWMwMWRlZWI0OTVhOGJhNjliMmQ0NmMwZjdhZTM3OThmODI5NzNiMzY3MzM4Y2MzMjE3NWVmZGJjY2JkZWRjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im8yYXBiSUhaSi9PZzBKNlQwdnVZRlE9PSIsInZhbHVlIjoiQTZQUUxkRDRaQjRIVlJkODBMWVArUWVaSHZkTlpHRVExdG5hSXVIN1lTUHkwdzBkWXJBWE1PNEUzOHBJaVpSdzFKSktsZkRGRDJqSXNYMHZ6NnNqUXVQRjBaRHBLcUxRZE9tYnFjN2JpRjBQOWpwQTN0bnZoKzVDZ2Z2b1RmZWQzY2V5K3F3SjdFSnUzWlFJOEZ6djU0dldzaTRCSlo2YUkwN09lelFZM25LK2F4YytSdExib1p4eG9TYlZoTGN1aStvdU9laHJacVZ3R3hmb25XZi80amlKVERaQURxWHN3Sy9pakJJd1ZCSE54aUJNd0k0YTVvSW9QYmNsazN2dW9za1RVaHRVTUJ1OGloNWRjMVhia2tLRjhwWDVlRm50MXk5K05pbUsrN3IzcGpLbGNTK09VOGFQUElDVU05bGpySjhVV2VkZEczSExjTFFqdTVWTis0VkVwdmhubk95Q3I5WmZLYUplZlpaWnh1dlNNdVFKMUdVN0kxUE96OTVVSVkvbllwTHZ5SUMyL0pYTDc0VG5yZEJacWoyOFl1QmFxeDhLOERIQXdtOHFiNThidGFZMlBKRzNmenNqK2t5eUlnYUEveU5SclB3MCt3VnlxTkNLS2hOT25xZlNXKzI2bGVRU3UvNVJSZy92RytHRmNDZkJOSWRiczlPeDRtTFgiLCJtYWMiOiIzYjIwZDhlZTIzMzRhYjUyNmM1MzRjYTQzNDU3ZGQ1YTQ4YzZiMDFjM2EzZDQ5YjY4MzFmNTYyNjcyMzE3Mjc2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldhVkhEWTc1bzFkQjI5RjFVbkZrQkE9PSIsInZhbHVlIjoidTd1ZHgrQ3pDU1hXbFlEVDJkNTBWZW5NcTZnb3dwNVpRV1JJeHIzTzBPN1ZDc3NqVUhTN2lld3FESC9ESHQ4M2RhSzhVRXkxdVFqS3F3K01tZmFJUDZsYWdZQnJTNEszdHdzZHVYRDJkMEo1dGRWbnRhaDVhWXhFMEpSNk9pQXZVN0JtdDhwV0dDWnU1RzNONVBZOFVJbDdsaXRyVmg0TWpRNldiR0VzZm1YcXF2d1NGSFZERnVGOTBGVzhRN1c4N3p3b3RTd2V5c2Y3d2I0bXUrelZJRi94NlBzOUgvSzVHVkh4UmRUWjk1RG5TaWdMRFRwU2liSy83b3VTZC9MdUVvaG9CbXByRTh1Y0U3c1o2VlNWd3BqRng0RDJtYjdJN1o0akhpN2dSMXFwNEc3MVJtd2N1enRpM1RRNTZlWkxxNVBkQXptVkMyV2MvVU5UTkt2QjZyVno4d1hWdUxJTEs1YzdiWlJabUZkRGpOU2x5SWlpVTE5M0krRnVRczVoaGdPWTI0RTMvVDZTejFLOG1EWit1YU1vSnRobmpONURxNko4c2ZxUjgxdmI2ampObkFpK2dJVCs2U0VDMlVQZkJ5c2ZaeXlTdDNobkpxd0NNSlYvZnhLb3g2ZlVyQjI1cFNNM1NEQ1FqWGwwVzg3ZWdicXA0Wk1MTEJMMndkS0wiLCJtYWMiOiI0OWMwMWRlZWI0OTVhOGJhNjliMmQ0NmMwZjdhZTM3OThmODI5NzNiMzY3MzM4Y2MzMjE3NWVmZGJjY2JkZWRjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im8yYXBiSUhaSi9PZzBKNlQwdnVZRlE9PSIsInZhbHVlIjoiQTZQUUxkRDRaQjRIVlJkODBMWVArUWVaSHZkTlpHRVExdG5hSXVIN1lTUHkwdzBkWXJBWE1PNEUzOHBJaVpSdzFKSktsZkRGRDJqSXNYMHZ6NnNqUXVQRjBaRHBLcUxRZE9tYnFjN2JpRjBQOWpwQTN0bnZoKzVDZ2Z2b1RmZWQzY2V5K3F3SjdFSnUzWlFJOEZ6djU0dldzaTRCSlo2YUkwN09lelFZM25LK2F4YytSdExib1p4eG9TYlZoTGN1aStvdU9laHJacVZ3R3hmb25XZi80amlKVERaQURxWHN3Sy9pakJJd1ZCSE54aUJNd0k0YTVvSW9QYmNsazN2dW9za1RVaHRVTUJ1OGloNWRjMVhia2tLRjhwWDVlRm50MXk5K05pbUsrN3IzcGpLbGNTK09VOGFQUElDVU05bGpySjhVV2VkZEczSExjTFFqdTVWTis0VkVwdmhubk95Q3I5WmZLYUplZlpaWnh1dlNNdVFKMUdVN0kxUE96OTVVSVkvbllwTHZ5SUMyL0pYTDc0VG5yZEJacWoyOFl1QmFxeDhLOERIQXdtOHFiNThidGFZMlBKRzNmenNqK2t5eUlnYUEveU5SclB3MCt3VnlxTkNLS2hOT25xZlNXKzI2bGVRU3UvNVJSZy92RytHRmNDZkJOSWRiczlPeDRtTFgiLCJtYWMiOiIzYjIwZDhlZTIzMzRhYjUyNmM1MzRjYTQzNDU3ZGQ1YTQ4YzZiMDFjM2EzZDQ5YjY4MzFmNTYyNjcyMzE3Mjc2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1057927068\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1584847558 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Ikg4MlkzMlRpNmxOQ213YmdkQnNWa3c9PSIsInZhbHVlIjoibFVSWjhlMDUzTDMwNXZmTTR2bFQ1Zz09IiwibWFjIjoiMmVkMjk1Yzk5MjVmZjMxYzk1ZWI5NWE1M2UxOGI1YzM4MWJmMzZlOTkyMjZjNzFjZmY1ZDFhOWQ0NDYzN2Y2YiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584847558\", {\"maxDepth\":0})</script>\n"}}