{"__meta": {"id": "Xac112eb356f70dcb4fdead53962d6f9a", "datetime": "2025-06-26 23:22:58", "utime": **********.222971, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980177.72864, "end": **********.222984, "duration": 0.49434399604797363, "duration_str": "494ms", "measures": [{"label": "Booting", "start": 1750980177.72864, "relative_start": 0, "end": **********.151118, "relative_end": **********.151118, "duration": 0.42247796058654785, "duration_str": "422ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.151127, "relative_start": 0.4224870204925537, "end": **********.222986, "relative_end": 1.9073486328125e-06, "duration": 0.07185888290405273, "duration_str": "71.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45030352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02623, "accumulated_duration_str": "26.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.176479, "duration": 0.02521, "duration_str": "25.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.111}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.210441, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.111, "width_percent": 1.258}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.215897, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.369, "width_percent": 2.631}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Ijh0L3BWSXJldWNSUEp3NTQ1Vk56MVE9PSIsInZhbHVlIjoiMEZKeFVVWGZwSnBMZFpkNnEwUU5Udz09IiwibWFjIjoiYzBjYTE1ZjMzZWVhOTEzYjlmYjYwNzM3NDM2ODcyYzVhMGYyYTg2YmQ2OWE5MGJlMGMzZjdjMWVkYmNjZWQ5MCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-171560956 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-171560956\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1801913530 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1801913530\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-715780133 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715780133\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1113212774 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6Ijh0L3BWSXJldWNSUEp3NTQ1Vk56MVE9PSIsInZhbHVlIjoiMEZKeFVVWGZwSnBMZFpkNnEwUU5Udz09IiwibWFjIjoiYzBjYTE1ZjMzZWVhOTEzYjlmYjYwNzM3NDM2ODcyYzVhMGYyYTg2YmQ2OWE5MGJlMGMzZjdjMWVkYmNjZWQ5MCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980169650%7C31%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9wM280OWxIUHJQVmVPQ2UzTGdsa3c9PSIsInZhbHVlIjoiUWplTEpYUVVLcExLYWJETzZucHltZW1QekVJR3ZzdmprYnM3cTdibjV0NExBSGJvTjF4SHVMS3U4SXF4OFJKcTg3NGw2YzEwYWxQeU9nV0hFYm41VTNPVVRZMERiS2dxb3YrOE9RckpDalo1Z1h6Zyt1b1UvVUp1Z3lrUGxWYUpRTlRyc1pnY3ZxdzY5dFkxVVVQbXNLNG1JYnhWd25RY0Q5MTBWK0tQWTdxS0doNVY5eUU2QWU0VmFNZjlqL252OFhXTlhTZHNvdmY1VnV1U29UZmtyTGtuRldUZzdWZEZoU0l2TnVVS1lERGZ0SU9ialVvSjQwVE55UjRpNC9EdWtUWXNwMW9IZGNtc3plT01PWkRKUS9GUEtvYnNTQU5iWlp3TTFKMnlXS0Rpc2FVSXhMWk1UTXo3SDJCMEJROVgyVDJlbzJBcVBsRkRUdm40aGduN0xTWXZRdEE3NWtWd215U1liL2syWnlqTWM0eitSQitsaXVDdTA3VmJ5M1FYZUJCR0FpTFBwRDVEZHhRb0xOOEdtanoyTVdsbUpvZ2pJbng0c2hYUVY3MVJ0V3RwUy9ndzcwZHNMQ3dBem05T3lNTDNTR0Mzb21BNkdmNUIrRUZGaFN3eGZFNERYR0VWYTM1TWloRC9vSitvSkdNWHduSGtmcktSZTJBdExpQW8iLCJtYWMiOiJhYmVhMWIwNjk4ODY2OGUxYTQ4MjdhZmZhZTY3MTRkNWJjN2U2NDk2ZDgzMTBlNDY4ZmZlZjhhNGRlYzdmN2IwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZ2dXR5VERmTERoLzNmeHYyd3IwU2c9PSIsInZhbHVlIjoiUzVEcEJ5Ti81TTlkZ1R2Nm9lK2VSbldSOXVPcWpoYlZ1OVVleWYvV2JrYlZrZXNjSUpaOFhrYTJkM0tYNjlORmlWRy9ZK3JlSkNPYUJYckNsdEI1dnhteldxc3hpSDUzOElUKzRXcjVobm1aUFd1NGc1NmQyTU50SDlOdGFhT2VYalBpbEZIYjhxczF0bjQ4UTVLRnFhTlpTRDh3dFNzMXNaMitQY0xiK0ovMVk3UTlZMzAwbnJ4dlhoTDZ2QU8xS2dpbDJvWUEwcDZiV0RrdkJIMmdzQy9ZSmdWQmhRNGpnMy8xZjVZZ0FiMTlDY1QzN1NPSk9vY0hsRzFvTWNOUmR2UUlRUWpxaVBteVFQSnQ0SDV0K0YwUlZuU2NoZEhQY2d1UkluVGpEOEtXV1BYSjJFV1MzM1dLNHllM2F2ZnNCZ3JPQTc3T3dNMjc4MlBndTVmVitkcjhxamR3YjdBNDBGRktCVitLdTNyS1BSMzBYRUVudjhSci9PSE9wUERKamhWUFBrVGZqa3d4NTl6UVFwNGQ5L2dHTDYwR29HejR2aU5PZDBNanhIS0FMZWpjWFpWUW1VQ2RBbEtRYXY4bUpQN2tKTW1ELzlCRm41V1ozeDZhb3dyeHlzZ3lzQzk4NWF2NFNEeE1sampVeHhSMFJqRkhaT1E4RlBuMkdNQWIiLCJtYWMiOiI0MzI4YTMxOGY3YThmOTQ2YmE5NTI1NTQxZTcyYzc3YjY0Y2Q3ZDI2MDhlZjUyMTZlMzAwMWZhYTBiY2VjNmU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113212774\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1615409630 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615409630\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2070103849 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:22:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhkYmxBcDZxRHJIWkJtSGIweitGSGc9PSIsInZhbHVlIjoia25DK3lmNG5mODBUQUJmOEJoU3BNelBTWFR2VTFZMFJrU0cySys3ZDNoVEdJc1VHeFJjNG4vemFDU25MUDh0Sm4yWHYyb2p3TEpzaVNqNlduZS9tN0Z6UDB1UlVUbFhLcUZyUUxjOUZwanFYbG8vQUJFY0NDcHhNMlVNYXp5bDZpL2k1bTQzYytobXdySmtaY08waDZJZm05cHlDZXNxVEpZaExqWkVVSllGb1FjVm1hQTIwdjBpQktCWjZNTkVUR0NmaXk5NVZVY3UybkhxVENaQ3E2T2x3YUdRb1NVcVh4UmZ0K0RzMmRWTWJmdGhUZ2owVlFDZXJJY3VFdVRmbEIyZm50OXRiWGJNNFBIc1NWK2t3dDZZZDhOYS9QMzRWYXNhWlJXdDRRS0NXdUZEYkdmelJDa3Nvem5zWktObFhNcFhXNDN6MlA4Znl0d2daZTdMUUhhNWV2dHN6cWZVenpLa3c3dmpMcFRYK05lTS9OVnI2enp3aFlmNFdYZlQwOUZPMzgwYlU4OWs1cC9kMVpMT0sxaWFqeFRIN3FSYURCbHB1YkI5N0FwdWt5VTBSR2lPOTZsajY5cGh5bVFxbytFc2xySkozeDErSGxNY2NFUTdLeko3K0xia3ZHZnB2dklHbWI4aXNaS25PU0Z0eWRkSTMxYUYyNzZGcW1BS3IiLCJtYWMiOiI2NzNlN2I5ZWVhYjIyYmI5OTUzNjExMGUxZWRhZWRhZGJkMDc0MmI3MDQ3M2RiZDhkMmU2MWQ0NzFiMTJmM2Q5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVGZVZnZjdUWjNmSUpEa0lhZVQyTFE9PSIsInZhbHVlIjoiTzJqNmJMTjVJcERpSjBkbXBYV0IycHVMK2trRVN3c3pmckZOTHBTWVoyc3g1eGlYMW8wUXN5TTB1YWdZR0tsRXU4aDIxZW9pZERtZWhwOU8wNm5RTDNLakNPRDJFeEhqU095cm5LQ0dRbGRnTVJQT3lBM05vOWRxUGZTS1Q3NWlwNVg0dEJwREpRMk83UUw4YUVvQnRIVUpsU2l0d29Pejl1cXFlYkc4WTR4aDVOVUdKLzNCcWJLNzdUYWFFOXlzTFVSTXVudTREUmNJK21uRkJVR2d3OHdhS2w3QkNHMDU4ZlFmWGg4MWVMZHhjOFpoSXV6a1NhWEYzbmx0ZTJRdEpCMFN0bU5PYVRXWWVheEcxRktnYkJMdG14a1cwVTJqRkNOQzd4RzMveXEyVVNxZEtZMVBXdlM4MEFwRmg3Wmx4ZlMzSGxsSUlOcjVWNS92cmZkWEZjWG1RQ0F6NVJuOHZmaU9JcldsdmFtRzlMWXdFN2dpaGVGU2lFNHE1VUZ5VHU1enFzVkNuMHZQZW9INksyRHdUbzNUWnZvRDcwaHc0Nk5mOU1MazkwSHlnQzRaWmVROE9CM2ZLVUhRdDhXTHgxQk5qbUhiQ0hVSERJYVYreVJaUE13VTYwSSsyeFd4NkxDcXorMUtld09LWGdTZE1wc1U0VEhIRWhIYnZnZlEiLCJtYWMiOiIxMzJlZDFkMWE2MjM1NWQ2N2Y4OTkxYmNjOGM3ZmQ1NWIyMTlhYjQyMDFiNjlhNmQxNmY1NmM2N2RhNzUyYTQyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhkYmxBcDZxRHJIWkJtSGIweitGSGc9PSIsInZhbHVlIjoia25DK3lmNG5mODBUQUJmOEJoU3BNelBTWFR2VTFZMFJrU0cySys3ZDNoVEdJc1VHeFJjNG4vemFDU25MUDh0Sm4yWHYyb2p3TEpzaVNqNlduZS9tN0Z6UDB1UlVUbFhLcUZyUUxjOUZwanFYbG8vQUJFY0NDcHhNMlVNYXp5bDZpL2k1bTQzYytobXdySmtaY08waDZJZm05cHlDZXNxVEpZaExqWkVVSllGb1FjVm1hQTIwdjBpQktCWjZNTkVUR0NmaXk5NVZVY3UybkhxVENaQ3E2T2x3YUdRb1NVcVh4UmZ0K0RzMmRWTWJmdGhUZ2owVlFDZXJJY3VFdVRmbEIyZm50OXRiWGJNNFBIc1NWK2t3dDZZZDhOYS9QMzRWYXNhWlJXdDRRS0NXdUZEYkdmelJDa3Nvem5zWktObFhNcFhXNDN6MlA4Znl0d2daZTdMUUhhNWV2dHN6cWZVenpLa3c3dmpMcFRYK05lTS9OVnI2enp3aFlmNFdYZlQwOUZPMzgwYlU4OWs1cC9kMVpMT0sxaWFqeFRIN3FSYURCbHB1YkI5N0FwdWt5VTBSR2lPOTZsajY5cGh5bVFxbytFc2xySkozeDErSGxNY2NFUTdLeko3K0xia3ZHZnB2dklHbWI4aXNaS25PU0Z0eWRkSTMxYUYyNzZGcW1BS3IiLCJtYWMiOiI2NzNlN2I5ZWVhYjIyYmI5OTUzNjExMGUxZWRhZWRhZGJkMDc0MmI3MDQ3M2RiZDhkMmU2MWQ0NzFiMTJmM2Q5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVGZVZnZjdUWjNmSUpEa0lhZVQyTFE9PSIsInZhbHVlIjoiTzJqNmJMTjVJcERpSjBkbXBYV0IycHVMK2trRVN3c3pmckZOTHBTWVoyc3g1eGlYMW8wUXN5TTB1YWdZR0tsRXU4aDIxZW9pZERtZWhwOU8wNm5RTDNLakNPRDJFeEhqU095cm5LQ0dRbGRnTVJQT3lBM05vOWRxUGZTS1Q3NWlwNVg0dEJwREpRMk83UUw4YUVvQnRIVUpsU2l0d29Pejl1cXFlYkc4WTR4aDVOVUdKLzNCcWJLNzdUYWFFOXlzTFVSTXVudTREUmNJK21uRkJVR2d3OHdhS2w3QkNHMDU4ZlFmWGg4MWVMZHhjOFpoSXV6a1NhWEYzbmx0ZTJRdEpCMFN0bU5PYVRXWWVheEcxRktnYkJMdG14a1cwVTJqRkNOQzd4RzMveXEyVVNxZEtZMVBXdlM4MEFwRmg3Wmx4ZlMzSGxsSUlOcjVWNS92cmZkWEZjWG1RQ0F6NVJuOHZmaU9JcldsdmFtRzlMWXdFN2dpaGVGU2lFNHE1VUZ5VHU1enFzVkNuMHZQZW9INksyRHdUbzNUWnZvRDcwaHc0Nk5mOU1MazkwSHlnQzRaWmVROE9CM2ZLVUhRdDhXTHgxQk5qbUhiQ0hVSERJYVYreVJaUE13VTYwSSsyeFd4NkxDcXorMUtld09LWGdTZE1wc1U0VEhIRWhIYnZnZlEiLCJtYWMiOiIxMzJlZDFkMWE2MjM1NWQ2N2Y4OTkxYmNjOGM3ZmQ1NWIyMTlhYjQyMDFiNjlhNmQxNmY1NmM2N2RhNzUyYTQyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070103849\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1980598899 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6Ijh0L3BWSXJldWNSUEp3NTQ1Vk56MVE9PSIsInZhbHVlIjoiMEZKeFVVWGZwSnBMZFpkNnEwUU5Udz09IiwibWFjIjoiYzBjYTE1ZjMzZWVhOTEzYjlmYjYwNzM3NDM2ODcyYzVhMGYyYTg2YmQ2OWE5MGJlMGMzZjdjMWVkYmNjZWQ5MCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980598899\", {\"maxDepth\":0})</script>\n"}}