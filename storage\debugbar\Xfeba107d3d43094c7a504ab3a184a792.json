{"__meta": {"id": "Xfeba107d3d43094c7a504ab3a184a792", "datetime": "2025-06-26 23:21:13", "utime": 1750980073.0184, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.566557, "end": 1750980073.018417, "duration": 0.4518599510192871, "duration_str": "452ms", "measures": [{"label": "Booting", "start": **********.566557, "relative_start": 0, "end": **********.939944, "relative_end": **********.939944, "duration": 0.37338709831237793, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.939951, "relative_start": 0.3733940124511719, "end": 1750980073.018418, "relative_end": 1.1920928955078125e-06, "duration": 0.07846713066101074, "duration_str": "78.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044440, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026690000000000002, "accumulated_duration_str": "26.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.969205, "duration": 0.025670000000000002, "duration_str": "25.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.178}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750980073.003043, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.178, "width_percent": 2.136}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750980073.009107, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.314, "width_percent": 1.686}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2144363097 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2144363097\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-111813225 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-111813225\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1381470540 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1381470540\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1405008130 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980050982%7C20%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZaa0I4K3dmWUcrUUVaU0s4Q09DY2c9PSIsInZhbHVlIjoiVjA1VTNFS21MVTNKV2RBMmRkUWhrTCtEbEh3dnRuRkxIRmpLRmdZWERRZWptVTVEUkdyN3B1VHFyd3JyV0JFY1B3eVBZZGY3NnVvQy9YWVJUaUM4SDVQd3pRR3p5eVV2VE5FOUlOL1NCWmt2QkVFSXM5ZXNQVHRqNzdaVURyRWwxV2FuMlVGS3BFUkpYVjhVQVZZeUNidWNjdGxzS0NWWnNRUU95ZForSDh5Y2FnRzJoN1JTYXNUbUtzbGhZQkUyajRrU2lpSWY2RHRCeXJOTGJDZWY2dDhEeEFjYWFMaFFKYmFyUHRVWDEzZ0ZwbWV2ZEhYY2w5UFNzRjZNTWJ4YVJoVzNpa2VRZFVHaE95V3JyL25WMnA3cnZ0YkFSZTFpdVRFdjRxanR4dXlZZE1MTWo1TmxoSEpGcERtS1JkaDJBVHQvUjRMTzBrcmZxakVtemQxTWFKZHh0cUtvTGVqcndIbWpQaG9PODZENXBWLyt3YWlPUkw4Z1ZPM0ZGN3Y5d2k3dG5DcFJWdk1LVDB2QWtKMVErRnRJMVY4SG9TS2ZBd3FwN3VtQU9JRlRKUVNRcFErUGROSlFiSjhNNDgyS3NPdXRwTFBKWXVtc0V5UGVkVWZ0VGROTTVqOGJtdnBSdVBTK05DazVEZDAxSzk3MVg2dXgrYTFOT2doN29CUUMiLCJtYWMiOiJkMDkyYmQ3ZTI2NmVjZTZkM2IwYzc1ZThkMGU4MDNhMzI3ZGI5NmJhZmJhNWFjNzQ4MTAxNmI2YWJlY2U1NWE3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InUyeWdOV0JGek5yNkFuRU0xRUhEV2c9PSIsInZhbHVlIjoiUUZURDVxcFZJZ0QzcC9xZFFRNTc0UzZuSGtHOG5SM3JZa284OFlmWi9iSTRBR1FZSndzTU9qTWxCZ0ZuSk5FQytBTW03UDNpc1BCTVNzc2FhTTBaMDAwc3ZPTTRKS1Y3MVVxUTBIaURFQ25Rc1VNQjJ4S0NnUnkwN3ZUZWo0bmNHK1g4NjhGYkFqUlV1bzMwZWVXbFhERnlCZ3RnaGZadldDUTc2ejE0K1RydkFJMFpnY2cxSXpUSXFONkI3by9EOWtIZmpPV21SV0N6QVlFVjh2NEhqWEhwYTM5ZDh2TkpBVGt5ck4rdzdFNlhTWXM3RldGZ2c4NHZWbFplSXQ4MDlkVTdxRFk1ZHNpUGpqV2EyWTZWeDA2NGhHOWVZbHRmNkxNSHpnQWtnd3VraWxTWkFqRFEvRGV5ekNHd1pmRytmb1BEdWMwYk1NMHNWb0ZvbkRmYUdoaFN0WEx6eTlSMFRDVXlPZ2lBQXZMYnNld2lvSzZ4Tit4dW1mODBibzdvVVRkN1c5UmcvTXBhT0p2MVNBakg5NkZoMURidXArQWc0WGhvZ1YyTU43aGQ4SUFrTkZ5Qk5lZ29PeUJ3T1l2ODZBQkF0RjRyM0tLR0lZWVplcmFzZGJpeEExanJWcVR5UGVOd21mYXhiaFExV0JLdkQ2S3pGUzZMZ1ZaU0JiTjAiLCJtYWMiOiJmZTQ4MjYzMTEzNTAyOTgzMzA3ODk4ZjVkOTkwMjY3NjA4NzgyM2M2YmExYzI3YjdiNzBkZDZkMmUyOTE5OGY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405008130\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1487431446 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487431446\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2051632770 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:21:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhkSkZlUUNVOGJadkl6Vm1mZDhzOUE9PSIsInZhbHVlIjoiZFM5TENIVXJHRlZFMk1PaHJmQTd4U3hnQVN4QzR6UVJqMjN2eEJOTmdJYXlZQTAxd2poTHhmWlRrRWlLK3g3eFl6ejNwTDZUTFNneU1iWWgzZGNUUE41Sis4aEZZWEpnZHl5ak85NngwZ1YxNlBjTklWUFVZVTJDVUlqUktNdzBFTGpyeDc4ZUx2VTFmbHg1WXJKY0lhSnlOYzROb0s1aUVsOWVXZkVUL1BnMmFMVDNXdStyK1czeEdzNWErL2NhY25BM1lsNGR0L0diN1RqTWVFYjBVVUNDNnRFcVNlOXg1UVZlRTlTdkUwUUI5NTJqZXlkcW1WNmR6a0FDZGx4amtNZFZxUmNkcTk2SWZ1YUlaMHFyV0lsL2pQeHFqWllTWG9DQks4UUxvY0tvdnAzZ3diTytiQStsRWJWelNVMjJxTW40bjdjYm5MSWdGWVA1OE82RExMY2hEUmZhTktmbTZsMm9ILzhCSXRCaGhrbEFKY004MnJjZHFVeE9XczRDeVQwbUxlMG9kSjhIWGliTUxPdWRrelpoeFlxQkthTHV1Q0hhU29md2RxbzRMSi82aVc2WmxkeVpJR1UrRzgvL1lUQjlWWU9XL0NZQkdyUmJYNWxxMUVtRkxlcWs2ZWszSk9tMDN3NlZHMXFiSGgrWis4K0x6eGl4eVFaOHJtLzMiLCJtYWMiOiI4NDY1MzZjNWE4N2EwZWMwYjNlZjg4Y2Y5NjFmOTYxNGZmZjkyMmVkNTI3NWNmOWJhNjI4Njg2MDI5ZmYyMTM0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InF2bHp3dThLa0R2ajZYQ1dkaXhzVWc9PSIsInZhbHVlIjoiQzIwb2RiT0xFQkRPSmY2WEgrTVJOSnIvdDY4TW1xMUxaTzRJV1pDQWlhd1BZaUZWSHhzQmpkZlZYSVNSWEtjbEV2ZFlGT2pabEpHYTlnUnplQWNrQjJnUmNWSURCZy8zckg0WGNWaGJTcUNTcmhsaTlMS0ZjT2h6SUh6dm5EeXdOZU93NGpXQzd6MDhVZkJ5YjBHdlZ3Tm4rYUhPS29kNXhyR2xvY2xoTjFwdzc1S3ZHL3EwMlhqdHRCVjdvQnFaR1VpNHJ2SmthMmV5Mk02WlFPeVN3QzJDdStaQWRlMWEvT3NNcFlOQ1dmQWcrT3hYTTlKTmNDaHBpVThaTkwwWTNGOC8rYkVqMkNZQUQwTitsZmI2RFFyRnlnUHVMT2Rlc0hpOFpyZ2FTS0lTTmZJUDZ5K0lIcThuWTJjQ09DSHJoa0hkTCtNSWF0U1lsUXdYNlltZnh3eTd5ZjZmb2ZFMi9FVit2NkR3WVN4UW8yZDBHVlpsQ1NMTmpabit4cy9vcnU4VFhPb3pESjMyTzB3OW1QT1BPcW95WWxqYXRtZmg1K0lldTFGM3hwbkNHbkcvVTdvV0lTckFQY2dIWHNzM0ZiekJPQVM0MEt6R0ZENEF4OUFiamE3WVlaVGM5ZkxkWEZwRHZBVXJ3VUtHWHpoRC9GZmlVRUJUYmNlQnVUbFgiLCJtYWMiOiI3MjljODk5NjA4YzM4NGMwMGY5ZjEyODcxZDA3NDQ3MTc5YzA4MTI4ZjJlMDQwNTkwNGU5ZDM2NmFmNWExZWQ5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhkSkZlUUNVOGJadkl6Vm1mZDhzOUE9PSIsInZhbHVlIjoiZFM5TENIVXJHRlZFMk1PaHJmQTd4U3hnQVN4QzR6UVJqMjN2eEJOTmdJYXlZQTAxd2poTHhmWlRrRWlLK3g3eFl6ejNwTDZUTFNneU1iWWgzZGNUUE41Sis4aEZZWEpnZHl5ak85NngwZ1YxNlBjTklWUFVZVTJDVUlqUktNdzBFTGpyeDc4ZUx2VTFmbHg1WXJKY0lhSnlOYzROb0s1aUVsOWVXZkVUL1BnMmFMVDNXdStyK1czeEdzNWErL2NhY25BM1lsNGR0L0diN1RqTWVFYjBVVUNDNnRFcVNlOXg1UVZlRTlTdkUwUUI5NTJqZXlkcW1WNmR6a0FDZGx4amtNZFZxUmNkcTk2SWZ1YUlaMHFyV0lsL2pQeHFqWllTWG9DQks4UUxvY0tvdnAzZ3diTytiQStsRWJWelNVMjJxTW40bjdjYm5MSWdGWVA1OE82RExMY2hEUmZhTktmbTZsMm9ILzhCSXRCaGhrbEFKY004MnJjZHFVeE9XczRDeVQwbUxlMG9kSjhIWGliTUxPdWRrelpoeFlxQkthTHV1Q0hhU29md2RxbzRMSi82aVc2WmxkeVpJR1UrRzgvL1lUQjlWWU9XL0NZQkdyUmJYNWxxMUVtRkxlcWs2ZWszSk9tMDN3NlZHMXFiSGgrWis4K0x6eGl4eVFaOHJtLzMiLCJtYWMiOiI4NDY1MzZjNWE4N2EwZWMwYjNlZjg4Y2Y5NjFmOTYxNGZmZjkyMmVkNTI3NWNmOWJhNjI4Njg2MDI5ZmYyMTM0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InF2bHp3dThLa0R2ajZYQ1dkaXhzVWc9PSIsInZhbHVlIjoiQzIwb2RiT0xFQkRPSmY2WEgrTVJOSnIvdDY4TW1xMUxaTzRJV1pDQWlhd1BZaUZWSHhzQmpkZlZYSVNSWEtjbEV2ZFlGT2pabEpHYTlnUnplQWNrQjJnUmNWSURCZy8zckg0WGNWaGJTcUNTcmhsaTlMS0ZjT2h6SUh6dm5EeXdOZU93NGpXQzd6MDhVZkJ5YjBHdlZ3Tm4rYUhPS29kNXhyR2xvY2xoTjFwdzc1S3ZHL3EwMlhqdHRCVjdvQnFaR1VpNHJ2SmthMmV5Mk02WlFPeVN3QzJDdStaQWRlMWEvT3NNcFlOQ1dmQWcrT3hYTTlKTmNDaHBpVThaTkwwWTNGOC8rYkVqMkNZQUQwTitsZmI2RFFyRnlnUHVMT2Rlc0hpOFpyZ2FTS0lTTmZJUDZ5K0lIcThuWTJjQ09DSHJoa0hkTCtNSWF0U1lsUXdYNlltZnh3eTd5ZjZmb2ZFMi9FVit2NkR3WVN4UW8yZDBHVlpsQ1NMTmpabit4cy9vcnU4VFhPb3pESjMyTzB3OW1QT1BPcW95WWxqYXRtZmg1K0lldTFGM3hwbkNHbkcvVTdvV0lTckFQY2dIWHNzM0ZiekJPQVM0MEt6R0ZENEF4OUFiamE3WVlaVGM5ZkxkWEZwRHZBVXJ3VUtHWHpoRC9GZmlVRUJUYmNlQnVUbFgiLCJtYWMiOiI3MjljODk5NjA4YzM4NGMwMGY5ZjEyODcxZDA3NDQ3MTc5YzA4MTI4ZjJlMDQwNTkwNGU5ZDM2NmFmNWExZWQ5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051632770\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1203231267 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203231267\", {\"maxDepth\":0})</script>\n"}}