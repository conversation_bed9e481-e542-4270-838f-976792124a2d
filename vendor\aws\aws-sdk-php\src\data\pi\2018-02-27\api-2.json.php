<?php
// This file was auto-generated from sdk-root/src/data/pi/2018-02-27/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-02-27', 'endpointPrefix' => 'pi', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'AWS PI', 'serviceFullName' => 'AWS Performance Insights', 'serviceId' => 'PI', 'signatureVersion' => 'v4', 'signingName' => 'pi', 'targetPrefix' => 'PerformanceInsightsv20180227', 'uid' => 'pi-2018-02-27', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CreatePerformanceAnalysisReport' => [ 'name' => 'CreatePerformanceAnalysisReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePerformanceAnalysisReportRequest', ], 'output' => [ 'shape' => 'CreatePerformanceAnalysisReportResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'DeletePerformanceAnalysisReport' => [ 'name' => 'DeletePerformanceAnalysisReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePerformanceAnalysisReportRequest', ], 'output' => [ 'shape' => 'DeletePerformanceAnalysisReportResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'DescribeDimensionKeys' => [ 'name' => 'DescribeDimensionKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDimensionKeysRequest', ], 'output' => [ 'shape' => 'DescribeDimensionKeysResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'GetDimensionKeyDetails' => [ 'name' => 'GetDimensionKeyDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDimensionKeyDetailsRequest', ], 'output' => [ 'shape' => 'GetDimensionKeyDetailsResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'GetPerformanceAnalysisReport' => [ 'name' => 'GetPerformanceAnalysisReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPerformanceAnalysisReportRequest', ], 'output' => [ 'shape' => 'GetPerformanceAnalysisReportResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'GetResourceMetadata' => [ 'name' => 'GetResourceMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourceMetadataRequest', ], 'output' => [ 'shape' => 'GetResourceMetadataResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'GetResourceMetrics' => [ 'name' => 'GetResourceMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourceMetricsRequest', ], 'output' => [ 'shape' => 'GetResourceMetricsResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'ListAvailableResourceDimensions' => [ 'name' => 'ListAvailableResourceDimensions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAvailableResourceDimensionsRequest', ], 'output' => [ 'shape' => 'ListAvailableResourceDimensionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'ListAvailableResourceMetrics' => [ 'name' => 'ListAvailableResourceMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAvailableResourceMetricsRequest', ], 'output' => [ 'shape' => 'ListAvailableResourceMetricsResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'ListPerformanceAnalysisReports' => [ 'name' => 'ListPerformanceAnalysisReports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPerformanceAnalysisReportsRequest', ], 'output' => [ 'shape' => 'ListPerformanceAnalysisReportsResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'NotAuthorizedException', ], ], ], ], 'shapes' => [ 'AcceptLanguage' => [ 'type' => 'string', 'enum' => [ 'EN_US', ], ], 'AdditionalMetricsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SanitizedString', ], 'max' => 30, 'min' => 1, ], 'AdditionalMetricsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'RequestString', ], 'value' => [ 'shape' => 'Double', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => '^arn:.*:pi:.*$', ], 'AnalysisReport' => [ 'type' => 'structure', 'required' => [ 'AnalysisReportId', ], 'members' => [ 'AnalysisReportId' => [ 'shape' => 'AnalysisReportId', ], 'Identifier' => [ 'shape' => 'IdentifierString', ], 'ServiceType' => [ 'shape' => 'ServiceType', ], 'CreateTime' => [ 'shape' => 'ISOTimestamp', ], 'StartTime' => [ 'shape' => 'ISOTimestamp', ], 'EndTime' => [ 'shape' => 'ISOTimestamp', ], 'Status' => [ 'shape' => 'AnalysisStatus', ], 'Insights' => [ 'shape' => 'InsightList', ], ], ], 'AnalysisReportId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => 'report-[0-9a-f]{17}', ], 'AnalysisReportSummary' => [ 'type' => 'structure', 'members' => [ 'AnalysisReportId' => [ 'shape' => 'String', ], 'CreateTime' => [ 'shape' => 'ISOTimestamp', ], 'StartTime' => [ 'shape' => 'ISOTimestamp', ], 'EndTime' => [ 'shape' => 'ISOTimestamp', ], 'Status' => [ 'shape' => 'AnalysisStatus', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'AnalysisReportSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnalysisReportSummary', ], ], 'AnalysisStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'SUCCEEDED', 'FAILED', ], ], 'AuthorizedActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FineGrainedAction', ], 'max' => 3, 'min' => 0, ], 'Boolean' => [ 'type' => 'boolean', ], 'ContextType' => [ 'type' => 'string', 'enum' => [ 'CAUSAL', 'CONTEXTUAL', ], ], 'CreatePerformanceAnalysisReportRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', 'StartTime', 'EndTime', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'IdentifierString', ], 'StartTime' => [ 'shape' => 'ISOTimestamp', ], 'EndTime' => [ 'shape' => 'ISOTimestamp', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreatePerformanceAnalysisReportResponse' => [ 'type' => 'structure', 'members' => [ 'AnalysisReportId' => [ 'shape' => 'AnalysisReportId', ], ], ], 'Data' => [ 'type' => 'structure', 'members' => [ 'PerformanceInsightsMetric' => [ 'shape' => 'PerformanceInsightsMetric', ], ], ], 'DataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Data', ], ], 'DataPoint' => [ 'type' => 'structure', 'required' => [ 'Timestamp', 'Value', ], 'members' => [ 'Timestamp' => [ 'shape' => 'ISOTimestamp', ], 'Value' => [ 'shape' => 'Double', ], ], ], 'DataPointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataPoint', ], ], 'DeletePerformanceAnalysisReportRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', 'AnalysisReportId', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'IdentifierString', ], 'AnalysisReportId' => [ 'shape' => 'AnalysisReportId', ], ], ], 'DeletePerformanceAnalysisReportResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeDimensionKeysRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', 'StartTime', 'EndTime', 'Metric', 'GroupBy', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'IdentifierString', ], 'StartTime' => [ 'shape' => 'ISOTimestamp', ], 'EndTime' => [ 'shape' => 'ISOTimestamp', ], 'Metric' => [ 'shape' => 'RequestString', ], 'PeriodInSeconds' => [ 'shape' => 'Integer', ], 'GroupBy' => [ 'shape' => 'DimensionGroup', ], 'AdditionalMetrics' => [ 'shape' => 'AdditionalMetricsList', ], 'PartitionBy' => [ 'shape' => 'DimensionGroup', ], 'Filter' => [ 'shape' => 'MetricQueryFilterMap', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDimensionKeysResponse' => [ 'type' => 'structure', 'members' => [ 'AlignedStartTime' => [ 'shape' => 'ISOTimestamp', ], 'AlignedEndTime' => [ 'shape' => 'ISOTimestamp', ], 'PartitionKeys' => [ 'shape' => 'ResponsePartitionKeyList', ], 'Keys' => [ 'shape' => 'DimensionKeyDescriptionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'DescriptiveMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'DescriptiveString', ], 'value' => [ 'shape' => 'DescriptiveString', ], ], 'DescriptiveString' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '^.*$', ], 'DetailStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'PROCESSING', 'UNAVAILABLE', ], ], 'DimensionDetail' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => 'String', ], ], ], 'DimensionDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionDetail', ], ], 'DimensionGroup' => [ 'type' => 'structure', 'required' => [ 'Group', ], 'members' => [ 'Group' => [ 'shape' => 'SanitizedString', ], 'Dimensions' => [ 'shape' => 'SanitizedStringList', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DimensionGroupDetail' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'String', ], 'Dimensions' => [ 'shape' => 'DimensionDetailList', ], ], ], 'DimensionGroupDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionGroupDetail', ], ], 'DimensionKeyDescription' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'DimensionMap', ], 'Total' => [ 'shape' => 'Double', ], 'AdditionalMetrics' => [ 'shape' => 'AdditionalMetricsMap', ], 'Partitions' => [ 'shape' => 'MetricValuesList', ], ], ], 'DimensionKeyDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionKeyDescription', ], ], 'DimensionKeyDetail' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'String', ], 'Dimension' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'DetailStatus', ], ], ], 'DimensionKeyDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DimensionKeyDetail', ], ], 'DimensionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'RequestString', ], 'value' => [ 'shape' => 'RequestString', ], ], 'DimensionsMetricList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SanitizedString', ], 'max' => 5, 'min' => 1, ], 'Double' => [ 'type' => 'double', ], 'ErrorString' => [ 'type' => 'string', ], 'FeatureMetadata' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'FeatureStatus', ], ], ], 'FeatureMetadataMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'FeatureMetadata', ], ], 'FeatureStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', 'UNSUPPORTED', 'ENABLED_PENDING_REBOOT', 'DISABLED_PENDING_REBOOT', 'UNKNOWN', ], ], 'FineGrainedAction' => [ 'type' => 'string', 'enum' => [ 'DescribeDimensionKeys', 'GetDimensionKeyDetails', 'GetResourceMetrics', ], ], 'GetDimensionKeyDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', 'Group', 'GroupIdentifier', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'IdentifierString', ], 'Group' => [ 'shape' => 'RequestString', ], 'GroupIdentifier' => [ 'shape' => 'RequestString', ], 'RequestedDimensions' => [ 'shape' => 'RequestedDimensionList', ], ], ], 'GetDimensionKeyDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'DimensionKeyDetailList', ], ], ], 'GetPerformanceAnalysisReportRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', 'AnalysisReportId', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'IdentifierString', ], 'AnalysisReportId' => [ 'shape' => 'AnalysisReportId', ], 'TextFormat' => [ 'shape' => 'TextFormat', ], 'AcceptLanguage' => [ 'shape' => 'AcceptLanguage', ], ], ], 'GetPerformanceAnalysisReportResponse' => [ 'type' => 'structure', 'members' => [ 'AnalysisReport' => [ 'shape' => 'AnalysisReport', ], ], ], 'GetResourceMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'IdentifierString', ], ], ], 'GetResourceMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => 'String', ], 'Features' => [ 'shape' => 'FeatureMetadataMap', ], ], ], 'GetResourceMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', 'MetricQueries', 'StartTime', 'EndTime', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'IdentifierString', ], 'MetricQueries' => [ 'shape' => 'MetricQueryList', ], 'StartTime' => [ 'shape' => 'ISOTimestamp', ], 'EndTime' => [ 'shape' => 'ISOTimestamp', ], 'PeriodInSeconds' => [ 'shape' => 'Integer', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'PeriodAlignment' => [ 'shape' => 'PeriodAlignment', ], ], ], 'GetResourceMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'AlignedStartTime' => [ 'shape' => 'ISOTimestamp', ], 'AlignedEndTime' => [ 'shape' => 'ISOTimestamp', ], 'Identifier' => [ 'shape' => 'String', ], 'MetricList' => [ 'shape' => 'MetricKeyDataPointsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ISOTimestamp' => [ 'type' => 'timestamp', ], 'IdentifierString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'Insight' => [ 'type' => 'structure', 'required' => [ 'InsightId', ], 'members' => [ 'InsightId' => [ 'shape' => 'String', ], 'InsightType' => [ 'shape' => 'String', ], 'Context' => [ 'shape' => 'ContextType', ], 'StartTime' => [ 'shape' => 'ISOTimestamp', ], 'EndTime' => [ 'shape' => 'ISOTimestamp', ], 'Severity' => [ 'shape' => 'Severity', ], 'SupportingInsights' => [ 'shape' => 'InsightList', ], 'Description' => [ 'shape' => 'MarkdownString', ], 'Recommendations' => [ 'shape' => 'RecommendationList', ], 'InsightData' => [ 'shape' => 'DataList', ], 'BaselineData' => [ 'shape' => 'DataList', ], ], ], 'InsightList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Insight', ], ], 'Integer' => [ 'type' => 'integer', ], 'InternalServiceError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorString', ], ], 'exception' => true, 'fault' => true, ], 'InvalidArgumentException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorString', ], ], 'exception' => true, ], 'Limit' => [ 'type' => 'integer', 'max' => 25, 'min' => 1, ], 'ListAvailableResourceDimensionsRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', 'Metrics', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'IdentifierString', ], 'Metrics' => [ 'shape' => 'DimensionsMetricList', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'AuthorizedActions' => [ 'shape' => 'AuthorizedActionsList', ], ], ], 'ListAvailableResourceDimensionsResponse' => [ 'type' => 'structure', 'members' => [ 'MetricDimensions' => [ 'shape' => 'MetricDimensionsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAvailableResourceMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', 'MetricTypes', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'IdentifierString', ], 'MetricTypes' => [ 'shape' => 'MetricTypeList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListAvailableResourceMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'Metrics' => [ 'shape' => 'ResponseResourceMetricList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPerformanceAnalysisReportsRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'Identifier', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'Identifier' => [ 'shape' => 'IdentifierString', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'ListTags' => [ 'shape' => 'Boolean', ], ], ], 'ListPerformanceAnalysisReportsResponse' => [ 'type' => 'structure', 'members' => [ 'AnalysisReports' => [ 'shape' => 'AnalysisReportSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'ResourceARN', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'MarkdownString' => [ 'type' => 'string', 'max' => 8000, 'min' => 0, 'pattern' => '(.|\\n)*', 'sensitive' => true, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 25, 'min' => 0, ], 'MetricDimensionGroups' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'String', ], 'Groups' => [ 'shape' => 'DimensionGroupDetailList', ], ], ], 'MetricDimensionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDimensionGroups', ], ], 'MetricKeyDataPoints' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'ResponseResourceMetricKey', ], 'DataPoints' => [ 'shape' => 'DataPointsList', ], ], ], 'MetricKeyDataPointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricKeyDataPoints', ], ], 'MetricQuery' => [ 'type' => 'structure', 'required' => [ 'Metric', ], 'members' => [ 'Metric' => [ 'shape' => 'SanitizedString', ], 'GroupBy' => [ 'shape' => 'DimensionGroup', ], 'Filter' => [ 'shape' => 'MetricQueryFilterMap', ], ], ], 'MetricQueryFilterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'SanitizedString', ], 'value' => [ 'shape' => 'RequestString', ], ], 'MetricQueryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricQuery', ], 'max' => 15, 'min' => 1, ], 'MetricTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SanitizedString', ], ], 'MetricValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], ], 'NextToken' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_=-]+$', ], 'NotAuthorizedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorString', ], ], 'exception' => true, ], 'PerformanceInsightsMetric' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'DescriptiveString', ], 'DisplayName' => [ 'shape' => 'DescriptiveString', ], 'Dimensions' => [ 'shape' => 'DescriptiveMap', ], 'Value' => [ 'shape' => 'Double', ], ], ], 'PeriodAlignment' => [ 'type' => 'string', 'enum' => [ 'END_TIME', 'START_TIME', ], ], 'Recommendation' => [ 'type' => 'structure', 'members' => [ 'RecommendationId' => [ 'shape' => 'String', ], 'RecommendationDescription' => [ 'shape' => 'MarkdownString', ], ], ], 'RecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Recommendation', ], ], 'RequestString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*\\S.*', ], 'RequestedDimensionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SanitizedString', ], 'max' => 10, 'min' => 1, ], 'ResponsePartitionKey' => [ 'type' => 'structure', 'required' => [ 'Dimensions', ], 'members' => [ 'Dimensions' => [ 'shape' => 'DimensionMap', ], ], ], 'ResponsePartitionKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponsePartitionKey', ], ], 'ResponseResourceMetric' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'Description', ], 'Unit' => [ 'shape' => 'String', ], ], ], 'ResponseResourceMetricKey' => [ 'type' => 'structure', 'required' => [ 'Metric', ], 'members' => [ 'Metric' => [ 'shape' => 'String', ], 'Dimensions' => [ 'shape' => 'DimensionMap', ], ], ], 'ResponseResourceMetricList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseResourceMetric', ], ], 'SanitizedString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[a-zA-Z0-9-_\\.:/*)( ]+$', ], 'SanitizedStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SanitizedString', ], 'max' => 10, 'min' => 1, ], 'ServiceType' => [ 'type' => 'string', 'enum' => [ 'RDS', 'DOCDB', ], ], 'Severity' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', ], ], 'String' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*\\S.*', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^.*$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'ResourceARN', 'Tags', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^.*$', ], 'TextFormat' => [ 'type' => 'string', 'enum' => [ 'PLAIN_TEXT', 'MARKDOWN', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ServiceType', 'ResourceARN', 'TagKeys', ], 'members' => [ 'ServiceType' => [ 'shape' => 'ServiceType', ], 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], ],];
