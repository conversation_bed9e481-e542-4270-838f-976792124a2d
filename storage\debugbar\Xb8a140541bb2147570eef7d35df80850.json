{"__meta": {"id": "Xb8a140541bb2147570eef7d35df80850", "datetime": "2025-06-26 23:20:03", "utime": **********.306113, "method": "POST", "uri": "/bill", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980002.776716, "end": **********.30613, "duration": 0.5294139385223389, "duration_str": "529ms", "measures": [{"label": "Booting", "start": 1750980002.776716, "relative_start": 0, "end": **********.142424, "relative_end": **********.142424, "duration": 0.3657081127166748, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.142441, "relative_start": 0.365725040435791, "end": **********.306132, "relative_end": 2.1457672119140625e-06, "duration": 0.16369104385375977, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51683016, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST bill", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.store", "controller": "App\\Http\\Controllers\\BillController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=116\" onclick=\"\">app/Http/Controllers/BillController.php:116-320</a>"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 0.07077, "accumulated_duration_str": "70.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.17479, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.84}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1863768, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 2.84, "width_percent": 0.608}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.200557, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 3.448, "width_percent": 1.215}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.203151, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 4.663, "width_percent": 0.579}, {"sql": "select * from `bills` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 742}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 172}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2132459, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BillController.php:742", "source": "app/Http/Controllers/BillController.php:742", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=742", "ajax": false, "filename": "BillController.php", "line": "742"}, "connection": "kdmkjkqknb", "start_percent": 5.242, "width_percent": 0.551}, {"sql": "insert into `bills` (`bill_id`, `vender_id`, `bill_date`, `status`, `type`, `user_type`, `due_date`, `category_id`, `order_number`, `created_by`, `updated_at`, `created_at`) values (4, '5', '2025-06-27', 0, 'Bill', 'vendor', '2025-06-27', '31', 0, 15, '2025-06-26 23:20:03', '2025-06-26 23:20:03')", "type": "query", "params": [], "bindings": ["4", "5", "2025-06-27", "0", "Bill", "vendor", "2025-06-27", "31", "0", "15", "2025-06-26 23:20:03", "2025-06-26 23:20:03"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 182}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.215247, "duration": 0.05496, "duration_str": "54.96ms", "memory": 0, "memory_str": null, "filename": "BillController.php:182", "source": "app/Http/Controllers/BillController.php:182", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=182", "ajax": false, "filename": "BillController.php", "line": "182"}, "connection": "kdmkjkqknb", "start_percent": 5.793, "width_percent": 77.66}, {"sql": "insert into `bill_products` (`bill_id`, `product_id`, `product_name`, `quantity`, `tax`, `discount`, `price`, `description`, `updated_at`, `created_at`) values (5, 0, 'فاتروة  مشتريات البان', '1', '', '0', '150', '', '2025-06-26 23:20:03', '2025-06-26 23:20:03')", "type": "query", "params": [], "bindings": ["5", "0", "فاتروة  مشتريات البان", "1", "", "0", "150", "", "2025-06-26 23:20:03", "2025-06-26 23:20:03"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 220}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.272759, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "BillController.php:220", "source": "app/Http/Controllers/BillController.php:220", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=220", "ajax": false, "filename": "BillController.php", "line": "220"}, "connection": "kdmkjkqknb", "start_percent": 83.453, "width_percent": 3.278}, {"sql": "insert into `bill_accounts` (`chart_account_id`, `price`, `description`, `type`, `ref_id`, `updated_at`, `created_at`) values ('0', '150', '', 'Bill', 5, '2025-06-26 23:20:03', '2025-06-26 23:20:03')", "type": "query", "params": [], "bindings": ["0", "150", "", "Bill", "5", "2025-06-26 23:20:03", "2025-06-26 23:20:03"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 231}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.276624, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "BillController.php:231", "source": "app/Http/Controllers/BillController.php:231", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=231", "ajax": false, "filename": "BillController.php", "line": "231"}, "connection": "kdmkjkqknb", "start_percent": 86.732, "width_percent": 3.462}, {"sql": "insert into `bill_products` (`bill_id`, `product_id`, `product_name`, `quantity`, `tax`, `discount`, `price`, `description`, `updated_at`, `created_at`) values (5, 0, 'ضريبة مدخلات', '0', '', '0', '0', '', '2025-06-26 23:20:03', '2025-06-26 23:20:03')", "type": "query", "params": [], "bindings": ["5", "0", "ضريبة مدخلات", "0", "", "0", "0", "", "2025-06-26 23:20:03", "2025-06-26 23:20:03"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 220}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2803812, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "BillController.php:220", "source": "app/Http/Controllers/BillController.php:220", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=220", "ajax": false, "filename": "BillController.php", "line": "220"}, "connection": "kdmkjkqknb", "start_percent": 90.194, "width_percent": 4.352}, {"sql": "insert into `bill_accounts` (`chart_account_id`, `price`, `description`, `type`, `ref_id`, `updated_at`, `created_at`) values ('252', '150', '', 'Bill', 5, '2025-06-26 23:20:03', '2025-06-26 23:20:03')", "type": "query", "params": [], "bindings": ["252", "150", "", "Bill", "5", "2025-06-26 23:20:03", "2025-06-26 23:20:03"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 231}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2848449, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "BillController.php:231", "source": "app/Http/Controllers/BillController.php:231", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=231", "ajax": false, "filename": "BillController.php", "line": "231"}, "connection": "kdmkjkqknb", "start_percent": 94.546, "width_percent": 4.239}, {"sql": "select * from `venders` where `venders`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 270}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.289837, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BillController.php:270", "source": "app/Http/Controllers/BillController.php:270", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=270", "ajax": false, "filename": "BillController.php", "line": "270"}, "connection": "kdmkjkqknb", "start_percent": 98.785, "width_percent": 0.551}, {"sql": "select * from `webhook_settings` where `module` = 'New Bill' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["New Bill", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4909}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 297}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.291613, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4909", "source": "app/Models/Utility.php:4909", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4909", "ajax": false, "filename": "Utility.php", "line": "4909"}, "connection": "kdmkjkqknb", "start_percent": 99.336, "width_percent": 0.664}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Bill": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=1", "ajax": false, "filename": "Bill.php", "line": "?"}}, "App\\Models\\Vender": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=1", "ajax": false, "filename": "Vender.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-648766136 data-indent-pad=\"  \"><span class=sf-dump-note>create bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648766136\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.206844, "xdebug_link": null}]}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/create?0=\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "بيل تم إنشاؤه بنجاح.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill", "status_code": "<pre class=sf-dump id=sf-dump-614684163 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-614684163\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1996478048 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1996478048\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>vender_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>bill_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-27</span>\"\n  \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-27</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">31</span>\"\n  \"<span class=sf-dump-key>order_number</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>item_name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1601;&#1575;&#1578;&#1585;&#1608;&#1577;  &#1605;&#1588;&#1578;&#1585;&#1610;&#1575;&#1578; &#1575;&#1604;&#1576;&#1575;&#1606;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n      \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>itemTaxPrice</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n      \"<span class=sf-dump-key>itemTaxRate</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>chart_account_id</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n      \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>item_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1590;&#1585;&#1610;&#1576;&#1577; &#1605;&#1583;&#1582;&#1604;&#1575;&#1578;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>itemTaxPrice</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n      \"<span class=sf-dump-key>itemTaxRate</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>chart_account_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">252</span>\"\n      \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n      \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3177</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryDsj4hd2x1w1yvIb0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/bill/create?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; XSRF-TOKEN=eyJpdiI6Im9BWXZzN2xDVkZOcXI1a3ZSeFdRTmc9PSIsInZhbHVlIjoiSVk3OVU0Q1JJejkzVHlXcjc3NU11Y2QvQ25OTHVSR0FhOXFMcGpJTlZUT2NqNVc1eEs0V3JzdlJuSFpXUnNkMHVrQzREZVNudzZHdnlmZUpFNXZGTjUvaXd2L0hSVzhtalAzRGdlbVROUHRyWGxLeGpzeklUKytyK1lOSkQyR1V0RW9jUnRrZEw5WWduWlROR0x1TTFrKyt5K2xjb05GZ0lWdmx5SHBRc3UxM0VqU1lyeWFQeDNGbjRQcklkR3o1Y2hDNXU2S2U0UFZrSW85QmdPbS9obEdaSjhGR01rOG5zTzZ0UzNOVmgzRjVtQ1NnOXlHenZMVEdHaExJZmFHYUY0d0E2SXpDY3E4NlJ6YXJRVEhjbHdUc2hHcjlyMlg5M3VFaDRYRDBpM1l1b0ZEclA1SEx6VGFSSEpjZXhjdlV5OGpsQnU0ODRYMnBiYm5TMlZ2OFZtb1JNR2NPQ0FNNkxRQml6ZlZscmNnQzNiSnhrWnU5R283SGI2eWl6SzQwM1huZGhCNW9MTy9DcmJaYWJaM21CVlI1RXVwUVIzcUFSZjNpVjBDTzY2ZEI3djlPdXY0S25aZENqaWdaVGhPRUd5UE9zbGtZM3hDTGZTQlhOT2FJMnFZOWNHRVc3ZXl4dXpOWjMrVW5FbkdrT25DSzRQTnhjTThUWG1CZGVyV2IiLCJtYWMiOiI5Zjk3NzU1NmJmNzU1Nzk5ZGUzNWU5NmZkMzNhMjc4MmRkY2M2ZDc3Zjc4MzEwMWU3MmY0YzBkNTQwYThiY2Y3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkE0YVhvdTJudW1CNUdqdHpiWjdEWnc9PSIsInZhbHVlIjoibEpZWXd4UE01Z212SngwdVB5ZHdzdTJxYzNvRWNOT0lSbHpEWmx6YUF4K3pCMHhMQzFLcStlMHYweGhaNEtQYzVMM0JNTEZQMDlFUGJuQzB6ckxTU3RpRjVNMm9CODhyL2V6SnlpZG5lOHpNWE9ZRE1GQWsvYkdZcDNOSm5FTjhGZ29GNlZ3K3BGTWZGMlBRMTI5RVMxTTB2YlhmOXprRkZxdFpUNktlaWo5a2dJa0hkOWlQSlFVazdsQXo4UThLWi82RjhpVEFWeVhzT1pjUW1qYkwwUEU1d0JidjB5RG5kckdFby9Ub0J4dWpYSERLWDBGWU02NE1iQVl3ZkEySENzcklxbm5xTEwzSlUvOE93SWdxMVkvODJ4N2k4dDJzTXEzK1R3cTg0akNyV2NCbUVtOE5ldDNSWnMzS2ptV3VjY0NDSWdZQlQvMnZLYUxjVTFOUVJFemN5ZEdrcTFOQ1JXWFMybzIxQ3ZGWDVRZERVVmxkWHhJcDFwYjNyK2I2aU1LTmI2cUtXaDR5Zy9ZVnZ3WC9MeldqNXRaajV3ZGZQRHVXU2xCL3lDN29GcU1GYnV0V1J0Z1hxTnJJRnB1TWRQRTlVREF0SHNrQ21aWE9ldXBvVy9uQVFCTjhhbUp4SjRtVXNtUERSb0Fzazk0VUtKaDBKdkhINTd6U1RsTXkiLCJtYWMiOiI3MGFjNmIzZjNhN2NiMzRkODcwZDM5NzE1ODk3MGJkMmZhYTE3OWI4MGU2Mzk1YzFlYWY1ZWJmMWY0MDY4OWM3IiwidGFnIjoiIn0%3D; _clsk=8maz9g%7C1750979881159%7C12%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-763427153 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-763427153\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-35378693 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/bill?5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZRYXE1RmxBcjRWU092SjBqTEdyMlE9PSIsInZhbHVlIjoiVUpPNjM4TjNSczRoZ1dZdHk1MjNQWnZ2bEZpSHdFU2d1OFFqZktJTjhpQm05eGJaT2ZBVHB4eVRaQ1V5VWVXY2pzOWhXanJubVZYa1VjTUJzZFlITGRZWmJ0L1c4SFpYK2JkS1ZCK0JDOHFYdTdabkdoYzA1bWJOT2ZXeEZPMklFTXNsM3ZBLzg0UW45UU5idFVEVWM3UHVCS1Ntc0p1TmZaU0dXSXVCWFRqY2pQdzZZRUd1U3A2Zm5uUS9RU1lZTHRPSVA3WkIyMDkxT2pBa0xsdU8wSyt5QURwOHZCSmMxNjRjZHVWWGlJNHpXM3MwVGpKYysxVXM3MzJHODdZMHkvNTJES2MzaFRCL0JISVRuR3VibW1vWm1nbG5TMzdadk1qY1IxRnFiejJuR1ZFYlRrV0E1QzJ3VzRWN2lBNmlXL3l2QVpxcGx5Uk9udkVXNkJaLytlbWExSkRrKzRTcVpHY2tJb2IyOEFZMy9MY3RPMXpEd1NUbm9NTTA3djZZN1lvOVFVZlg3bFZLVDlTc0twVlZ6Q3ZoSjdCK29WMnhXZndPRHY0TnJNbUZXalFsNVNIQ01XOFNzWWp6QkhiazV2ZmFVSGpRSVJvd0JtT0VTWDZQRnVlNk9kdWFjazF6eUlrekxwU0VBc3VsSms0STZoS3BGa24zL2xjanh1dWYiLCJtYWMiOiJjYmY1N2U1OTJlN2Q5NzFlMDE1YmI3ODZhNDMzMDkzYzNlZjljZmRhMzU4YzYzOTQ0MDc4MDc0YWFkMjgyZjhhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdIcXR1QWRWQmlDUEI4dlZGbHVReWc9PSIsInZhbHVlIjoic0NuTWtXd0JUSHB4N2RlRFlncG5ESFhEaGtITC95eXR4UW8yaWtRVllxZmowaWJmTFZrUnpWZkQyRHNJbWg4c2ozNk5FV2pHSjJSRGJSMDlLZlk2U3dwZ09IVmwxUWo3aGpxSStpcWJZZHR6aEpraFRGbEV4cGpaNzAwem9ycksyQkwvNFljVWpId1AvZGFBL0F0RzhhUGZDb3d1U3ZxM2YxSXl5YXlWaWVrR0ZmR0JTaldQOXp2MXI1SEFEU3JzMlBmc0JmY3NpcUc3QzdCSkg2TWJtcU8zNUFDeVFUaXMxS0NvaGx3T01oVmx0ZHVVemdMN1l1ZnpHVDF6UWhRWk1Dclp5V0oraUVaM3JwekprMkgyRWNvL3VUVGx2bWN4QlEySGQ5SEtLODVZM0NKeG1CVVZRdlRwWHBEL3VBN1lWVWd5aHBHQ0lNSzJmVUZybGFEQWh2ajR3MmxSZ0ViWlRsWVFqYmtVTEEva2laWk1KVy9XYXdrL2NXQmtVOWxEVTJNTmxIWHpQcnVFVGgwSmRWU0UyejcwWTZxYUVCV2xRZy9QUlliaXlhNktoczJZemQ0blVRenhUbXB5Z0UwbEVQZmRUY0Qrc3I0cVBPSUs5aWVCbzE1bHdhcGdJNDNLS0NyQ0IveitlLzYrdmNFM3dGR1FFdEVUMHV3K2FUc20iLCJtYWMiOiJjNmIzMzJhYjZiOWIzZWU1Mjc4NThmNTI2OGU0OWRjNDI5MmVhMjQxZmU0OWZkZWY5OWNlODgzZWRlZjc2ZTYyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZRYXE1RmxBcjRWU092SjBqTEdyMlE9PSIsInZhbHVlIjoiVUpPNjM4TjNSczRoZ1dZdHk1MjNQWnZ2bEZpSHdFU2d1OFFqZktJTjhpQm05eGJaT2ZBVHB4eVRaQ1V5VWVXY2pzOWhXanJubVZYa1VjTUJzZFlITGRZWmJ0L1c4SFpYK2JkS1ZCK0JDOHFYdTdabkdoYzA1bWJOT2ZXeEZPMklFTXNsM3ZBLzg0UW45UU5idFVEVWM3UHVCS1Ntc0p1TmZaU0dXSXVCWFRqY2pQdzZZRUd1U3A2Zm5uUS9RU1lZTHRPSVA3WkIyMDkxT2pBa0xsdU8wSyt5QURwOHZCSmMxNjRjZHVWWGlJNHpXM3MwVGpKYysxVXM3MzJHODdZMHkvNTJES2MzaFRCL0JISVRuR3VibW1vWm1nbG5TMzdadk1qY1IxRnFiejJuR1ZFYlRrV0E1QzJ3VzRWN2lBNmlXL3l2QVpxcGx5Uk9udkVXNkJaLytlbWExSkRrKzRTcVpHY2tJb2IyOEFZMy9MY3RPMXpEd1NUbm9NTTA3djZZN1lvOVFVZlg3bFZLVDlTc0twVlZ6Q3ZoSjdCK29WMnhXZndPRHY0TnJNbUZXalFsNVNIQ01XOFNzWWp6QkhiazV2ZmFVSGpRSVJvd0JtT0VTWDZQRnVlNk9kdWFjazF6eUlrekxwU0VBc3VsSms0STZoS3BGa24zL2xjanh1dWYiLCJtYWMiOiJjYmY1N2U1OTJlN2Q5NzFlMDE1YmI3ODZhNDMzMDkzYzNlZjljZmRhMzU4YzYzOTQ0MDc4MDc0YWFkMjgyZjhhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdIcXR1QWRWQmlDUEI4dlZGbHVReWc9PSIsInZhbHVlIjoic0NuTWtXd0JUSHB4N2RlRFlncG5ESFhEaGtITC95eXR4UW8yaWtRVllxZmowaWJmTFZrUnpWZkQyRHNJbWg4c2ozNk5FV2pHSjJSRGJSMDlLZlk2U3dwZ09IVmwxUWo3aGpxSStpcWJZZHR6aEpraFRGbEV4cGpaNzAwem9ycksyQkwvNFljVWpId1AvZGFBL0F0RzhhUGZDb3d1U3ZxM2YxSXl5YXlWaWVrR0ZmR0JTaldQOXp2MXI1SEFEU3JzMlBmc0JmY3NpcUc3QzdCSkg2TWJtcU8zNUFDeVFUaXMxS0NvaGx3T01oVmx0ZHVVemdMN1l1ZnpHVDF6UWhRWk1Dclp5V0oraUVaM3JwekprMkgyRWNvL3VUVGx2bWN4QlEySGQ5SEtLODVZM0NKeG1CVVZRdlRwWHBEL3VBN1lWVWd5aHBHQ0lNSzJmVUZybGFEQWh2ajR3MmxSZ0ViWlRsWVFqYmtVTEEva2laWk1KVy9XYXdrL2NXQmtVOWxEVTJNTmxIWHpQcnVFVGgwSmRWU0UyejcwWTZxYUVCV2xRZy9QUlliaXlhNktoczJZemQ0blVRenhUbXB5Z0UwbEVQZmRUY0Qrc3I0cVBPSUs5aWVCbzE1bHdhcGdJNDNLS0NyQ0IveitlLzYrdmNFM3dGR1FFdEVUMHV3K2FUc20iLCJtYWMiOiJjNmIzMzJhYjZiOWIzZWU1Mjc4NThmNTI2OGU0OWRjNDI5MmVhMjQxZmU0OWZkZWY5OWNlODgzZWRlZjc2ZTYyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35378693\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1159588381 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/bill/create?0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"20 characters\">&#1576;&#1610;&#1604; &#1578;&#1605; &#1573;&#1606;&#1588;&#1575;&#1572;&#1607; &#1576;&#1606;&#1580;&#1575;&#1581;.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1159588381\", {\"maxDepth\":0})</script>\n"}}