{"__meta": {"id": "X7a7e2f043ad6308a18bb921e4bd19a73", "datetime": "2025-06-26 23:15:02", "utime": **********.462844, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.000834, "end": **********.462861, "duration": 0.46202707290649414, "duration_str": "462ms", "measures": [{"label": "Booting", "start": **********.000834, "relative_start": 0, "end": **********.382031, "relative_end": **********.382031, "duration": 0.3811969757080078, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.382038, "relative_start": 0.38120412826538086, "end": **********.462862, "relative_end": 9.5367431640625e-07, "duration": 0.08082389831542969, "duration_str": "80.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029728, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023010000000000003, "accumulated_duration_str": "23.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.411026, "duration": 0.021920000000000002, "duration_str": "21.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.263}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.442765, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.263, "width_percent": 2.434}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.449097, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.697, "width_percent": 2.303}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-995445320 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-995445320\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-712432594 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-712432594\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1191248431 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1191248431\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-443845616 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979694284%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikh5WVNxWW5FN3lWNEpyUzNEQ2FnaEE9PSIsInZhbHVlIjoiVjQxTGVHWjdiTHVtUXZINnhUS1JOeGUrR0ZQWlRSYTZ1Y1RoemRUYnoyVDk1TU1TQ2s1TVBCajA4bjZkYTFUZW1lUHZ0aDk4dTUveHF3cE5VRHJCS0xRaDBkbkZaVllEcC9xK1pZNXd2NFd2RjVubDQ0UXJLYmdWRHEyVVdqRWM3V1dtNjlKM2x0L3pGdTRKTUQxc2ZwdFo5ZDVWNW5KTkt2eEpkK3Z2ckdORXRUSEd0c1pyZUZTUXJRVHhXVGpwVzBMb2FPRDVxNG9YVXYraFlYcXVadEl0VHpqckhrTDRiY1lpOUQxTkFrWTFlNENuckdnSDhzRnhMN3lreGNKZitYYTZyVTdaeHpNdXJmTkJTL0lMMW5TdlpyajQ2M2ZaMUF4T3JnN2FNVGJ6b0VGeGV2Qy9yZ3N5SEZTMC9tcnVCb0ppcmIzc01aWFQ1bVFoZ2t6YndCL2dnakVYelh6cEx5UHFNUE9tZm5jakZYcnRZcnZSOFJOTVh6ZUd4VktBQ3htVGpDcGU3blIrOEJrWGtIbUNtSmNtWjdnK3ZMQ28xWG1PWkRBVVFibVR1NXlkQ3BleHI1NHJIcVlrZnVVT3U1dFFUUWpsTjN1UUM4ZThaNjR1K2kyUzVMYWNVeGtCOW11dTZ6U0syMjJkemRrUndnTlQyMWUvZS9BOTVuYnYiLCJtYWMiOiIxN2M2NzEwNzNkYzQ0ZDVlMWRkMGE4ZWUyOTM5NmZhNjgyMDBhYWI3ODE1NWVhZmFkMDk2ZWQ3N2FlOTNkMTk1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImRMVURNUE1PUjc0cVF5Ym5lWFV3WUE9PSIsInZhbHVlIjoiZEhEblRXNFFIYnhESGE2bWZaNUUwZG93K0MxZENxZW52d2pOWWVzRVVkazF4aVRQK25JUWNlcGNzN1FPUHZwc2x2RGFhUFVjODJUa3lhT2J2RHFDakI0cjZlc1ZGa3p3d01hWFhnckxQWnllb2crQndvTTFIZlc3M3JrL0xOOEVRTG5PRkxLRzBVc3lGK2NocURpWnM0Q3pzbVU2VTllN2tmTHp3R1VmNGY2QldNeTJlUnBINnkzTndVVHFzTTNQdS8vMEVlMDZGWFhjYnVZdW9DdjFKTG9LYVRac2VTUkp0eGx1U0hzbU1MSmhBQWwzSXRKMXBodkJJVmRQdWRFNXlKN2kxekQvZWN3OHp2WTgzLzlYcFlWTTdLdGI1NU1JL2lpemgvWEZibEJyWDUwcDRlU2N5eE9RK0RSUHNhU0owTmozNEpWVmxUQWcvQUV0d1NnVnZXWCtVQkdNRVVjVFhSOXFxY1dRc0s1Y1RFRThGRktaakVSamVZMHZsVk4zQ2dUU2VsRTVwUXI5eXpqaTdlOGF6ZHNwQVRDVVgyK0ZSUjNOK2lQcUVGeW1UeW92N1hIY3BEb255TGZyWHVXSktNb3JUTGJGS3F6dDN2MkxoaFQ5VGN5NCtDOVVWQkhHMEFQdWlDRE53UEw1d3pNQUZNWUw5NTFrVlBxWGpSUlMiLCJtYWMiOiI5YjVhZTk0ODNlMGVhZDJkZjRhMzMzYjUxM2NmNGU5ZTViOTcxOTcwYzI3MDFlNTFjNmRkZjUwMDY3ZjIxNTdjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443845616\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-750623002 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750623002\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:15:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdiSVE5WlV2VXp5VVYxLytzMWZ2cnc9PSIsInZhbHVlIjoiZ2h3M2tHRTJRWHNCY21mY3hmYjlRUEwzVnlrRDVFajVPbnF1MGN3bS9QYy9PamtsL2E4NS9IbG9rTXhRZk1WZXVQWjJRNjQ5cmtjTGpZZEcrckREVDlUNlRRS29hQXYwRjIzaHFWaUJPd0llMDBjRFFRWGJHaGFjbFF5ZE9CdGNxKzdKRHRBMG5aZ0RpVDBHVkszT21tcVlDV2NxdGx4UHBOd3RMNmtxWHhoclhVRkh5N3lTcmlaSDNZUVd6NHBqZHlscXlZeVhya0laOGN0TDNoTUN4ZlVQT3VGK1ArTnBZd2JvWGJSRmRmS0o4R1hMYTZYdktWb2E5QWd5eEZrYUZyQ25IS2c3TWRSZ0k1RzQ3Rkk3czdxNzZjNE96eGs0SGxaSytiZ0lKaFovcm5TdlhBdm15emYzNTQ3NVZjbDh4cVhoK014YXNxcWswODF6SExMOWFIVlF2L1FVZ0VHa0Q5dm5XUzhtd0JjSGtWc296TkJ5QysxTWRVN1hsOVBuTzNETitHdUpmc2pYd0M1bEFSakRzNUVlTi9NcGNwL2JYL25Xb255TmZSUStxQnErb0cxZ1RLZi9vTDFGbW1iSENsRm9lb2M3U1BjRVozR1lHYnRpQzdNeHM3MGZINVBuUnpJcGRhVytzaUIyUUJOTjY3NzRPNFVXUGxHU28yZXEiLCJtYWMiOiIxYWZlMjQzYTNiMGM3MGM2MDE3OTUxM2RhMGQwNDY2ZmRkNzIzMmE5YWVlODYyMjM5ZDYwZDUwZWJmNDMxMzFkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:15:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImYwczJTNkNISWV3a2VhUXNZQm9rU0E9PSIsInZhbHVlIjoiaVFmYWNJZWs5a2YydVNvVzU3SGUxdCtyaXZDck1nbEVLUGFSTFVNRVpxdXVHa3JqL2tETkNTOFY5QklEVVRhMlJMbVZVMXZFdWsvaXlheitkYVQwM3kwQlBLRjBPWkRlWlN1VjRNeXhqdmg3cSt3ZHYvS29KTFVTell1akhTSjBjZ3lMdmIwZUFrN0VoSjJUK2d1S2ZNTS9qRE1hdnZRajRvSUVHNmYxcGxQQmJCdmQ0aHhCZDIvcnZTamNDalQ1dEpuV1Zabzdlb1ZDL0tyWnY1OXRpUUkvK0NzSVFHRFEwUmdRd0daWTE0YVhqZktkY1pDeDY3NmFXZjdBMXExRDEvVXZhVEtyQlM0Z01hbS9qWnFxcjNmeEtpYVR1bG96YjdiOFBmOC9oWmRLaTZOc0hreU1VZmFoRExmMXd2NmZYSzNrTnplcHdWQytXYVFrNlFDaThNMUt6L0crdXIyUVYxN3huT2Q3RzFHVlZoazErYi9zUmFoWDZSSklJQWs3RGN2NlExNk5TUWo0M0tTYzRuWmQyVmx0VWhMM0Uvb25wRmxuL1pqUDU0MlNwNmRJMVV6ZGg4bTVFRWpwdjhWdXRud3dranY5WkMyeGRYYjRSRHM1TXNSRUlEM2xHUnp2UEpDUVp5dFI3VGo5NG04cm5IaHNtbEw2b21FWCthS2MiLCJtYWMiOiJkZDczZTI5YzdjMmM5NmRlYmM3NTAxZmY4YjlkZWEzYjQzY2E5NDc5MjlhYjIwMjY0NWJkMzdiZjg1NGQxYWE2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:15:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdiSVE5WlV2VXp5VVYxLytzMWZ2cnc9PSIsInZhbHVlIjoiZ2h3M2tHRTJRWHNCY21mY3hmYjlRUEwzVnlrRDVFajVPbnF1MGN3bS9QYy9PamtsL2E4NS9IbG9rTXhRZk1WZXVQWjJRNjQ5cmtjTGpZZEcrckREVDlUNlRRS29hQXYwRjIzaHFWaUJPd0llMDBjRFFRWGJHaGFjbFF5ZE9CdGNxKzdKRHRBMG5aZ0RpVDBHVkszT21tcVlDV2NxdGx4UHBOd3RMNmtxWHhoclhVRkh5N3lTcmlaSDNZUVd6NHBqZHlscXlZeVhya0laOGN0TDNoTUN4ZlVQT3VGK1ArTnBZd2JvWGJSRmRmS0o4R1hMYTZYdktWb2E5QWd5eEZrYUZyQ25IS2c3TWRSZ0k1RzQ3Rkk3czdxNzZjNE96eGs0SGxaSytiZ0lKaFovcm5TdlhBdm15emYzNTQ3NVZjbDh4cVhoK014YXNxcWswODF6SExMOWFIVlF2L1FVZ0VHa0Q5dm5XUzhtd0JjSGtWc296TkJ5QysxTWRVN1hsOVBuTzNETitHdUpmc2pYd0M1bEFSakRzNUVlTi9NcGNwL2JYL25Xb255TmZSUStxQnErb0cxZ1RLZi9vTDFGbW1iSENsRm9lb2M3U1BjRVozR1lHYnRpQzdNeHM3MGZINVBuUnpJcGRhVytzaUIyUUJOTjY3NzRPNFVXUGxHU28yZXEiLCJtYWMiOiIxYWZlMjQzYTNiMGM3MGM2MDE3OTUxM2RhMGQwNDY2ZmRkNzIzMmE5YWVlODYyMjM5ZDYwZDUwZWJmNDMxMzFkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:15:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImYwczJTNkNISWV3a2VhUXNZQm9rU0E9PSIsInZhbHVlIjoiaVFmYWNJZWs5a2YydVNvVzU3SGUxdCtyaXZDck1nbEVLUGFSTFVNRVpxdXVHa3JqL2tETkNTOFY5QklEVVRhMlJMbVZVMXZFdWsvaXlheitkYVQwM3kwQlBLRjBPWkRlWlN1VjRNeXhqdmg3cSt3ZHYvS29KTFVTell1akhTSjBjZ3lMdmIwZUFrN0VoSjJUK2d1S2ZNTS9qRE1hdnZRajRvSUVHNmYxcGxQQmJCdmQ0aHhCZDIvcnZTamNDalQ1dEpuV1Zabzdlb1ZDL0tyWnY1OXRpUUkvK0NzSVFHRFEwUmdRd0daWTE0YVhqZktkY1pDeDY3NmFXZjdBMXExRDEvVXZhVEtyQlM0Z01hbS9qWnFxcjNmeEtpYVR1bG96YjdiOFBmOC9oWmRLaTZOc0hreU1VZmFoRExmMXd2NmZYSzNrTnplcHdWQytXYVFrNlFDaThNMUt6L0crdXIyUVYxN3huT2Q3RzFHVlZoazErYi9zUmFoWDZSSklJQWs3RGN2NlExNk5TUWo0M0tTYzRuWmQyVmx0VWhMM0Uvb25wRmxuL1pqUDU0MlNwNmRJMVV6ZGg4bTVFRWpwdjhWdXRud3dranY5WkMyeGRYYjRSRHM1TXNSRUlEM2xHUnp2UEpDUVp5dFI3VGo5NG04cm5IaHNtbEw2b21FWCthS2MiLCJtYWMiOiJkZDczZTI5YzdjMmM5NmRlYmM3NTAxZmY4YjlkZWEzYjQzY2E5NDc5MjlhYjIwMjY0NWJkMzdiZjg1NGQxYWE2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:15:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}