{"__meta": {"id": "Xd16ae628bc09d5d6cedf393852cc7fe9", "datetime": "2025-06-26 23:21:48", "utime": **********.889068, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.464885, "end": **********.889082, "duration": 0.4241969585418701, "duration_str": "424ms", "measures": [{"label": "Booting", "start": **********.464885, "relative_start": 0, "end": **********.817897, "relative_end": **********.817897, "duration": 0.3530120849609375, "duration_str": "353ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.817908, "relative_start": 0.35302305221557617, "end": **********.889084, "relative_end": 2.1457672119140625e-06, "duration": 0.07117605209350586, "duration_str": "71.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042280, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01817, "accumulated_duration_str": "18.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.848418, "duration": 0.01724, "duration_str": "17.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.882}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.874416, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.882, "width_percent": 2.532}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.88211, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.413, "width_percent": 2.587}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1627975855 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1627975855\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-960392264 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-960392264\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1512084804 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1512084804\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-387163952 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980100946%7C25%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRUemNKRlBLb0NiVnZrbTV4eXlVaVE9PSIsInZhbHVlIjoid04wcVBCT3VRTDRJN244SzllWUdNTXNhR1kzTFpTMEVreitiZ1dsdXphMWFaa3JQMWhEMWphWHh5WXRDWnVSaXl3MXBxcHZrdUV3NGo2a2dsdlo3VGpkYjQ5UGRsSStIenhyM1VGK0QvNzlrYWlGYVcvQ0J5M2dmUFVlRWlFeEFwRmdYbGZxMElCcHJzT3RxaGtGcWdGb3dBd1pSUkNIVW84OUVhbWVHdXJQSEN3MUd5OUxlSDJnS0ZialFQS0ppRmErMWM2aktqYWY3MVBIc1oxV1pHb3hxL2R6ZjArL3V1UnlXeHcrbDUwVHBjUkRFUkUwc1cwMDVtNnZXZUlwV25OVlVhSCtSSnJpV2w5eUo0ZWFKdU5jUWVtRXF1bGFoNGp1ZVJiU3RURmtzVEFNV2dVOFN0eUU4WnRjNUEyK0s4eVczVHdYSWl3cDJLVkRtWWV6UEF0YjVjYmVvYWxLRnhsaC9FU096ZlVIZnd2WGxMQ29vODBUSTI3Z29hdmdoYjl5UGtNYzlQWHM3YWk4Y3BpOGgySkJtREJXa2RhbXRCcjRtMDN5aTczZ0dFd0Z4R0toakZMVDZWM1NEUFhSTCtKVGltaWNLeUxnczBJRmhXRlZzVFVyLzhYN0FQZ0s4SEROR1dpTUVjVTdFK3ljRWVyUzNpWngvdXpxNVdGTjQiLCJtYWMiOiJlYzk5NDk0ZmU0ZjYyMmQyMmUzYzhhMWNiNGI3NzkxZmNkNTExMmQ1MWU4OWIyYmM2ZjM2MTI1YjcwMjlhOWIxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkR6cURXa3FtbWdLYUJSeVBGaUpUWUE9PSIsInZhbHVlIjoiN0d0VXpzQldTQlNXR2NZbVpUdG4rZE5KZDZMclR0U09reGVYZVlqQmpWR3h4ZS9iNTZuMHAwaU01M1Z2WDIwTGZnd2FmbWRhc3puSGlOdE92akhvalR2anhwbHh3VmZiUll5V1pZY1NSbUtvb3VWWnE3b1pVQmp6VUdQdE0xUWN3ejlUOWtmYmMxWXcxTTZobXF1Wk9XMFhxVkd6djEzOWxuNFhQaUFONnE3MDJKTFdiQ1RWbGFIUjJMcEp5MzlDRVlUN09LSFd6OU5ndVdITHczRnZQNG5VQ1JjQlJrV1lGWXh5ZU5yYUpVdzRzL3pKMWVWRmtqMkpHMUZPQkdWaWljR0xOdW5GMjR4ai9RZUtmREpReVEzenN6TnVBZElma0NMaWNwa0JmdzFvVU1QeGx6Y3l3WDMyUjFCSG42RUdNaE1SK0I0TkY2QXR1T1FBWis1STJNMVhwTmd6Q2ZKaGVLSzU0MEI3Vk5Cak9CMERzdUErWGhvdWUrVnVBN1RSMTk2Ly9pRnNGZFRzRHhQNkI0V0pXQlM0aWYzT1VKSjdyd2UrNnFqOUVOb2loQkVsVml2NFhocFFnMTZiM0VtWGhEdVAxUGVwd1NaR1R2K1NOTG5UQ3dJeGpENlFGcmRURTdqUDIvR0MzdXZrdjROZ2hFdFVWWVdNWG1KN0xKVlQiLCJtYWMiOiI1MWE3Y2Y5NWNhOWJmZmQ5NmM5NDI5NDEzNDc1MjVmMmM2YjI5NTUyYWUxYWIxZWVlNTAzMTRiODc3YzM3NWE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387163952\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-206570571 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-206570571\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1148220618 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:21:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFnWlRtdUMxSFpqc3pybjhJK0pCdUE9PSIsInZhbHVlIjoiSC94K1BXQ3hXQXc4dXdMV0YrV29paEFCSWYxOXZRTVFtZ2hLTmNpTTg3WklDZ09GL3hpYXN2N1d5QkRmRnlQOTVTMDNJSUg0bEFDVVArWWE4bHg0QTRQeEdONkZOb0NCb0xrTStoNUI4S0FmT1pjNlBUL2thNnRXNWlZakMrSkJ1NSsxSHZTTFdSci9ZTlRCRUtEMWtoc2hIbThhTUc2UVpxYW1ucFFCQWJNREZPSFFCdjM4ZXVNVzE1eWVNYVlqZjJuOHQ4aXk3Ky9ZQUZKUUJnZDJYelhMMTVlWUdnVnF2a0xXWS9naEQ2VjgvTVVhVUYvYnl2UnM5Z2s2TEZqRjVweTQvOWNyNTNha0dycTVFOWVJVm05aEVMa2I2MmZEQUNzSEwyN1ozWUxFUUgwMnJqZmw4aVl0dVQzM2RBQVd6WWtjVGJBbmNwSnlXS2k5bnk2SXRoVHNWRlozRFNVeWNOYi9sOERUNjlzUnZSTXdsZmtkak84N0J4dW1vVWRrUmlFNytNWitrWXpYUU1RU1ZCZllxRDVlM3RRc1FubzR5ZnZPaVBYM2RUcDhCYTRCQzJWclpST0dXQmZYQ2o2eVlsU0Nkc0p3NHB3T1k5NzcrTWNVb0ZxUXJtZUxjL0hGcnZ1VnpkN0pvQytydm1jQmkxOGFqdFdFcWc0cG9YTjMiLCJtYWMiOiJjZjI4MGIzMDg4ODBlY2VjZWNjZDkxNTMwY2ZhNWMyNWE0OWM4YWQ0YjgxZTEyZDM1YjA2M2UyMjllMGQ4ZjZiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpOUkJnSmlibmEybTNEeGczcVZIcmc9PSIsInZhbHVlIjoibk5ncGJTc3djUmlad0RIMXBkNkYxdVVzSWphK1cwMmJxUGJNcjR1TzRhUGoxU2ZVTzAxQUtvdTFuS1ljSDVBd1ZqUHVWLzdQK0ltdTdZRmU5RVlBQlozcDEyL095Ti9qQWhYeFJwbisvWk1rTi9jZHpDc05PK0pmQkRkOTAwMDI4TlZweDRycWE3cng1NjQ5MXRHREN0ZmNiSnM5RkNiZlE4OHpsaWVlaDFoVkVQRElnR0hoc0k1RWlia3MrM0dHWDkrajI0Z2pkNGdvVG0yOG1RUXA3dSsvekllVmdna2FwY1Nnd3BpZUUrRFp1dCtjeE9mcXhtQmtOMHlaTGlmd2Y5MytOekxxcnNhMmJDTENYSnBLeTdIVStHU25TY1lQcjYyOGp1Zk53ZEl2cXlYZjFXUEY5RERWL0JENm9QdWxwM3A1U1FONENYMmJRK1hnQUZGYkk5V3RJNkFpS0c5VHpnMjFNYjIybTUyUlNFZjhFYk5la0dGQjNWeUU2NE1zQ3dRNXRvUUllVk8zbG9BMWxMR2tzSlYyc0wrR1MwazlwMTFkRTZLZTE4TWFDa25DdE9uZnJST21ybFpVOW5iSDVxWUlNcHh3dktvejgwSi96OHp3Yml3SVdYa3ljR01vVUhBaU5HUVVsKzJOT1BhbjBqNXYvWElQRWIralM1eWgiLCJtYWMiOiI5ZjM5M2ZlYzUyNjc3MmY1YTU0NTZkYzk4MGNmMWUxYmQ2YjVlMjc0ZWM1YjdjZTNlOTYxY2Q5ZTdlMGRlNjIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFnWlRtdUMxSFpqc3pybjhJK0pCdUE9PSIsInZhbHVlIjoiSC94K1BXQ3hXQXc4dXdMV0YrV29paEFCSWYxOXZRTVFtZ2hLTmNpTTg3WklDZ09GL3hpYXN2N1d5QkRmRnlQOTVTMDNJSUg0bEFDVVArWWE4bHg0QTRQeEdONkZOb0NCb0xrTStoNUI4S0FmT1pjNlBUL2thNnRXNWlZakMrSkJ1NSsxSHZTTFdSci9ZTlRCRUtEMWtoc2hIbThhTUc2UVpxYW1ucFFCQWJNREZPSFFCdjM4ZXVNVzE1eWVNYVlqZjJuOHQ4aXk3Ky9ZQUZKUUJnZDJYelhMMTVlWUdnVnF2a0xXWS9naEQ2VjgvTVVhVUYvYnl2UnM5Z2s2TEZqRjVweTQvOWNyNTNha0dycTVFOWVJVm05aEVMa2I2MmZEQUNzSEwyN1ozWUxFUUgwMnJqZmw4aVl0dVQzM2RBQVd6WWtjVGJBbmNwSnlXS2k5bnk2SXRoVHNWRlozRFNVeWNOYi9sOERUNjlzUnZSTXdsZmtkak84N0J4dW1vVWRrUmlFNytNWitrWXpYUU1RU1ZCZllxRDVlM3RRc1FubzR5ZnZPaVBYM2RUcDhCYTRCQzJWclpST0dXQmZYQ2o2eVlsU0Nkc0p3NHB3T1k5NzcrTWNVb0ZxUXJtZUxjL0hGcnZ1VnpkN0pvQytydm1jQmkxOGFqdFdFcWc0cG9YTjMiLCJtYWMiOiJjZjI4MGIzMDg4ODBlY2VjZWNjZDkxNTMwY2ZhNWMyNWE0OWM4YWQ0YjgxZTEyZDM1YjA2M2UyMjllMGQ4ZjZiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpOUkJnSmlibmEybTNEeGczcVZIcmc9PSIsInZhbHVlIjoibk5ncGJTc3djUmlad0RIMXBkNkYxdVVzSWphK1cwMmJxUGJNcjR1TzRhUGoxU2ZVTzAxQUtvdTFuS1ljSDVBd1ZqUHVWLzdQK0ltdTdZRmU5RVlBQlozcDEyL095Ti9qQWhYeFJwbisvWk1rTi9jZHpDc05PK0pmQkRkOTAwMDI4TlZweDRycWE3cng1NjQ5MXRHREN0ZmNiSnM5RkNiZlE4OHpsaWVlaDFoVkVQRElnR0hoc0k1RWlia3MrM0dHWDkrajI0Z2pkNGdvVG0yOG1RUXA3dSsvekllVmdna2FwY1Nnd3BpZUUrRFp1dCtjeE9mcXhtQmtOMHlaTGlmd2Y5MytOekxxcnNhMmJDTENYSnBLeTdIVStHU25TY1lQcjYyOGp1Zk53ZEl2cXlYZjFXUEY5RERWL0JENm9QdWxwM3A1U1FONENYMmJRK1hnQUZGYkk5V3RJNkFpS0c5VHpnMjFNYjIybTUyUlNFZjhFYk5la0dGQjNWeUU2NE1zQ3dRNXRvUUllVk8zbG9BMWxMR2tzSlYyc0wrR1MwazlwMTFkRTZLZTE4TWFDa25DdE9uZnJST21ybFpVOW5iSDVxWUlNcHh3dktvejgwSi96OHp3Yml3SVdYa3ljR01vVUhBaU5HUVVsKzJOT1BhbjBqNXYvWElQRWIralM1eWgiLCJtYWMiOiI5ZjM5M2ZlYzUyNjc3MmY1YTU0NTZkYzk4MGNmMWUxYmQ2YjVlMjc0ZWM1YjdjZTNlOTYxY2Q5ZTdlMGRlNjIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148220618\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1693770794 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693770794\", {\"maxDepth\":0})</script>\n"}}