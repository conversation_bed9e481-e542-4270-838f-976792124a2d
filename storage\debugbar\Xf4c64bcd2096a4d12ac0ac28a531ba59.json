{"__meta": {"id": "Xf4c64bcd2096a4d12ac0ac28a531ba59", "datetime": "2025-06-26 22:42:30", "utime": **********.136052, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750977749.707633, "end": **********.136067, "duration": 0.428433895111084, "duration_str": "428ms", "measures": [{"label": "Booting", "start": 1750977749.707633, "relative_start": 0, "end": **********.071997, "relative_end": **********.071997, "duration": 0.3643639087677002, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.072006, "relative_start": 0.36437296867370605, "end": **********.136068, "relative_end": 1.1920928955078125e-06, "duration": 0.06406211853027344, "duration_str": "64.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044808, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013850000000000001, "accumulated_duration_str": "13.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.100815, "duration": 0.01285, "duration_str": "12.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.78}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.122112, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.78, "width_percent": 3.61}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.128406, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.39, "width_percent": 3.61}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-138886610 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-138886610\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-246368457 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-246368457\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1592361128 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1592361128\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-51550125 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977742699%7C10%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFlQnpCU3g1ak94T0VPamFDaldSY3c9PSIsInZhbHVlIjoiUWNoTlF6SlQvUk0xank3UThQTXpjNXpqZG5qQTlaSmdCVmV6dG1kZ3BNb0FWT3JVbTdyNElHeXBpZmFYeVhrVHBZN09LSGF3Zk9KcGlGVUJOakFxdHYvOGJvK0tyWStONWJTNHkxU3N3L3YwVkVxempEc2xCQmVHcm1aTm94Z2l6NlhERFFpMEFNWVJkamM3eFlxNXYzZUJid1pLNGFVOUI3TC96SnFpTTREYUNCeTlRZUVTL01OWXNXRGYvbXhpVUhWRU5KdlZaV2w3ZVhvWVBRSXJTSFU4WDErSDVxNHVld1RpK3FnWEtBYUpveTVHS1c0aUx5VmtxVSt5QUJtaXpWMGRCaDd3akU0VHBCRWN0WnBkbURiZTFjeWtVWlgvam8vSjJpbGtIOEdpaHJwa3J4QXUwa0VENDRyOVpjQUVXS3NRSzkxN2xFdlJjVm9qdk1uSXlVV2dZMzZRcU4xZTY0cjFKTFc3N3BmcXp0RTRwZStHa2xuMUlqK3pGc28xc3pLRUFCUForaWVPcEs3NnI1bExlaDNJdEpNQXdhN01qelpDZm44RXNneGsrNFlQaXVSaVU0UlpXb3M2dzlCVmN5MEFJdDJtdEoxcWVCUzdHYVljSUJmMWcvd3dGQzUwa3NQRTZTbm9LRkR1cW9OYzk4dkFhVGRCS0E2bjAwcHoiLCJtYWMiOiI1NWI2Nzk0MWU5MmIzZTI2OGIyMTM5MzY4NGE1ZGVkMDEyNDMyMGFmNjc0YTcwMDMwNDA0YTczODYyODhlZDdhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpvaTU1Z0JMN2k1UUZQcHdPSWlmM3c9PSIsInZhbHVlIjoiRWRtcXQyTWEzOU5kbTB1eTk4MUJ0cjdDb0R5T01ZQkxqSEZ6THVlUDRUcG43WU9GMlQ5Zk5sSHpLUVdoOFJqNVh6RFVyZE1nUENGc0JWVkRSenBlRnhaenZzRzB2OE53UzMxSFIzWWJqdVdMeFZTSjdHVEF0VW1kVlBRc0w2bFBSOE9NbFdibitkZW9oa2VBSEE3TmltT2xZcHFqai84dE16cmdEZlNTN0tPTnpaSlp0aS84VVlsWVpUdjZhSmlxRFRoVXdxM3lRTkxmZk5Lc0ZzbnJyMk1pYlhvYlZrakl5R24rQUc0WGZ4M2U0ZktzUlpic2QyQjV1d1hQTWRPTFdYeWVRLzdiOW42V1NaRnZ4R0p0dG5wNHhzd0lqZzBqMmtzTUltLzIvMmwrS0daK1NaUzdZc09vUDlVZ3FYUXRJOG1QMzMzRmlIOVFyYmMvSUlGbWptdkVJYXJodW1iWFhGSEl5dUZ6a1hrcEpScTc0WEdaL0dOaXhMUUR2RXFYaXFIRlJYSWFFU2pvT1BmWElLNEViKzFySlR4WjBFMCtoTS9xZUo4WTRUU3V3L0Z3dnlyOVp3ME85Qy90bVM1YXFja2lnUmtaL3F4RFJ2RGwreHNxMGMwQ2ZLWXAzd1RQcW1uUnFJa0pTdTVqL3lvT21vOXQzZ1c4SmRJT0k5SjYiLCJtYWMiOiI0OTUwZGU0YzdjYWUwNDdhMDU1ZmY0ZTIzMjU3Y2FmYmYxZDEzZmVmMzdhODU1NDdhMDQ3NjUzYWVlNDU4MmUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-51550125\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1911064216 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1911064216\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1671526964 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:42:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjB5MVBDYzNIQkNjaGR6NlpmdTFHQ0E9PSIsInZhbHVlIjoiL3RZbUhVNDRrdFhsVmRSTzUxR3VYMkgxVEQ3eWVRZ3lwU2FFV1M0M0ZBSUZuQVJqTG5vTnlqVnpwelA0MkJROVFMUjNLQTlKdkhzNWhzZWtDR2hlRm5XZTNsL29Ic0FkMytwRi9xRVY1bDlSdCtDSHlnRGdtZGRIUkFEZjZuQ0FiZ2l4enc1WmpsNldwZ1dCazBTN2JOd21LNDhwQ2NOeDZSY05HSElHcVpSVXhKd1F2Zm14TitlVWFXTnZpZ0N4MzFtS0gzVHlHMURGOTdBSHd5bmtzeHBiN0Y0bDNXS3JTQjVMakQ2RzNqMmNSNlNJREpwcnV3ZDFpSTBudEdOeDlYcDY1UDRQTExPYXJqZlZkQzQzM2M3T1ZqcitVZmpSR2dmcGc0b0tybUNJajVIMVlQcVZCZm5rUkdXZWg3NUlUTG5hVUh4dG9qcmF5QnFkZXRDbkxFaFcrOEJmQ0JuS1o2VVNHNWtKejk2QTBpU0dJRHhkbXFQblhaZ0V3SEZxQUtudGJTTlRmRlFBOVZ0VEZ1Rmo1OVhmSXpJZHBFOFhMaGNza0plS3NmbUJJTVB4NHAvamNNQXRKczFibGdlZ2UyZjlsbFR0UlB1MGxmVzUrMm5KTUJjWmhRUnVCbFFOMFkwbkRvdUtDdTdQSlJENU5VUEl3R2JCRzdMR3A5SnEiLCJtYWMiOiI3MjBmYWFlZTdkODdlMjk0Y2JjNGU1ODU5YmRhMjU2NDUxZDdhMmZhYjEzMzNiNjExMjliMmVlMzE5NzBiMDIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBnRHFJN0FYcDZvQXkyWVhQSUQwTEE9PSIsInZhbHVlIjoiQWUrbmZtMFU2TXFBeHl1WWtJRnlYMEl0SEw0NmhrUTltYlh2MDg0SmNOMzJzVW9nYUJpY0F1NFBYRWY5TlNZalU0NzFlQ0JrTUoyWW11Q24wVmZrTE5IUDQ4eEFsSzRNK2RrSkN0cmt2V3FUSkJDMHJRb2xuYlB1SkY1dTl3TXN6dFQvR3hpakVRbFN0MkJRS1M1ckhhVnk5c3hGSHd0SHIxK1kwSmgxSXJFQno4a1dGdXBaeFFUUFFoODdodnNvUkpyU2VLQm9FbnJ4NTJDcTRHODlpc0hCdFV1NDJZQ3c4SVFPT2RQT2ZEbWlMRWZjOWNDbjNTb1Z2dlhNcFczVVZaa0R6aE9UZWE1dHE4dlpFVzg1NGpyNE9MbldDUjUvbzBUMllKZEYvYWJWQVRoN3MwZ3pGZEFXU3VzcTdvV1R2NXh6citMOFByWEdRencrOVVoWjBlMVp3QkZpWUFWd0tjQ0JuRHE5Mkw5d0x1Rk1OY3RYS0xmcFN5ZVZrN1hjNVg3c1VMZDBzZ0NFUjMzOWJibHVUMWpxT0h1UE1DbkNCdEtCQnBoem1QcG0rKzF4Zk5nb1lDOGU2d050SEhDU1NFQ0ZuR1ZLa2x1bDRRQUM2RURVdjRIRi9CdCtUSnpwTFY2dVFjRmZtbDA2SzBJZFVmaE8rYkJhMm1HaVB3TkoiLCJtYWMiOiJhMzg1YTQwN2Q2NTJjZjM1NzQ1YzA0NDk5ZmJkOGQ2NGFiYmNjZTM0YmRmMDI5Njk4ZTZkNjZiYjk0MTg4OTFkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjB5MVBDYzNIQkNjaGR6NlpmdTFHQ0E9PSIsInZhbHVlIjoiL3RZbUhVNDRrdFhsVmRSTzUxR3VYMkgxVEQ3eWVRZ3lwU2FFV1M0M0ZBSUZuQVJqTG5vTnlqVnpwelA0MkJROVFMUjNLQTlKdkhzNWhzZWtDR2hlRm5XZTNsL29Ic0FkMytwRi9xRVY1bDlSdCtDSHlnRGdtZGRIUkFEZjZuQ0FiZ2l4enc1WmpsNldwZ1dCazBTN2JOd21LNDhwQ2NOeDZSY05HSElHcVpSVXhKd1F2Zm14TitlVWFXTnZpZ0N4MzFtS0gzVHlHMURGOTdBSHd5bmtzeHBiN0Y0bDNXS3JTQjVMakQ2RzNqMmNSNlNJREpwcnV3ZDFpSTBudEdOeDlYcDY1UDRQTExPYXJqZlZkQzQzM2M3T1ZqcitVZmpSR2dmcGc0b0tybUNJajVIMVlQcVZCZm5rUkdXZWg3NUlUTG5hVUh4dG9qcmF5QnFkZXRDbkxFaFcrOEJmQ0JuS1o2VVNHNWtKejk2QTBpU0dJRHhkbXFQblhaZ0V3SEZxQUtudGJTTlRmRlFBOVZ0VEZ1Rmo1OVhmSXpJZHBFOFhMaGNza0plS3NmbUJJTVB4NHAvamNNQXRKczFibGdlZ2UyZjlsbFR0UlB1MGxmVzUrMm5KTUJjWmhRUnVCbFFOMFkwbkRvdUtDdTdQSlJENU5VUEl3R2JCRzdMR3A5SnEiLCJtYWMiOiI3MjBmYWFlZTdkODdlMjk0Y2JjNGU1ODU5YmRhMjU2NDUxZDdhMmZhYjEzMzNiNjExMjliMmVlMzE5NzBiMDIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBnRHFJN0FYcDZvQXkyWVhQSUQwTEE9PSIsInZhbHVlIjoiQWUrbmZtMFU2TXFBeHl1WWtJRnlYMEl0SEw0NmhrUTltYlh2MDg0SmNOMzJzVW9nYUJpY0F1NFBYRWY5TlNZalU0NzFlQ0JrTUoyWW11Q24wVmZrTE5IUDQ4eEFsSzRNK2RrSkN0cmt2V3FUSkJDMHJRb2xuYlB1SkY1dTl3TXN6dFQvR3hpakVRbFN0MkJRS1M1ckhhVnk5c3hGSHd0SHIxK1kwSmgxSXJFQno4a1dGdXBaeFFUUFFoODdodnNvUkpyU2VLQm9FbnJ4NTJDcTRHODlpc0hCdFV1NDJZQ3c4SVFPT2RQT2ZEbWlMRWZjOWNDbjNTb1Z2dlhNcFczVVZaa0R6aE9UZWE1dHE4dlpFVzg1NGpyNE9MbldDUjUvbzBUMllKZEYvYWJWQVRoN3MwZ3pGZEFXU3VzcTdvV1R2NXh6citMOFByWEdRencrOVVoWjBlMVp3QkZpWUFWd0tjQ0JuRHE5Mkw5d0x1Rk1OY3RYS0xmcFN5ZVZrN1hjNVg3c1VMZDBzZ0NFUjMzOWJibHVUMWpxT0h1UE1DbkNCdEtCQnBoem1QcG0rKzF4Zk5nb1lDOGU2d050SEhDU1NFQ0ZuR1ZLa2x1bDRRQUM2RURVdjRIRi9CdCtUSnpwTFY2dVFjRmZtbDA2SzBJZFVmaE8rYkJhMm1HaVB3TkoiLCJtYWMiOiJhMzg1YTQwN2Q2NTJjZjM1NzQ1YzA0NDk5ZmJkOGQ2NGFiYmNjZTM0YmRmMDI5Njk4ZTZkNjZiYjk0MTg4OTFkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1671526964\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-18514907 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18514907\", {\"maxDepth\":0})</script>\n"}}