{"__meta": {"id": "Xe85544d0a763feef664349f979f9313d", "datetime": "2025-06-26 23:21:40", "utime": **********.124976, "method": "GET", "uri": "/bill/eyJpdiI6IllSSXVIVXRzS2Q2NUNicVR2cXg2SHc9PSIsInZhbHVlIjoiSUZKTDVVM2lKMTVVSEptS2luOHZpQT09IiwibWFjIjoiY2UwMmEyNDdjNWQ4OTJkODczMjQwNTI0OTc5ZGI1MzEwODdhZjE5MmY1Mzg2OWFmODg2MWNhMTNkMzc0MzMwZiIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 259, "messages": [{"message": "[23:21:40] LOG.warning: Implicit conversion from float 0.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.029267, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.029388, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 0.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.029459, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 1.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.029526, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.029589, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 1.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.029651, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 2.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.029717, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.029779, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 2.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.02984, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 3.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.029914, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.029978, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 3.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030039, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 4.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030103, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030166, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 4.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030229, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 5.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030294, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030356, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 5.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030419, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 6.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030483, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030546, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 6.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030607, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 7.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030671, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030734, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 7.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030795, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 8.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030859, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030931, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 8.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.030992, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 9.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031057, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03112, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 9.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031181, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 10.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031245, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031308, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 10.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031371, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 11.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031436, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031498, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 11.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03156, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 12.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031624, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031686, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 12.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031749, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 13.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031813, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031876, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 13.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.031947, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 14.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032012, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032073, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 14.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032134, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 15.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032198, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03226, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 15.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032322, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 16.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032387, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032448, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 16.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032509, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 17.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032574, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032636, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 17.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032697, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 18.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032763, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032825, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 18.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03289, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 19.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.032955, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033017, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 19.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033078, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 20.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033146, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033209, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 20.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033271, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 21.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033335, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033395, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 21.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033455, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 22.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033518, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033579, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 22.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03364, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 23.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033704, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033765, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 23.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033825, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 24.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033893, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.033956, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 24.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034025, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 25.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03409, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034152, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 25.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034213, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 26.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034276, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034338, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 26.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034399, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 27.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034464, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034526, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 27.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034591, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 28.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03466, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034723, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 28.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034792, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 29.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034861, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.034937, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 29.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035006, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 30.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035072, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035133, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 30.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035196, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 31.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035261, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035322, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 31.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035384, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 32.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035449, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035509, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 32.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03557, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 33.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035633, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035694, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 33.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035755, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 34.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035819, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035885, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 34.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.035969, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 35.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036042, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036106, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 35.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036173, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 36.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036242, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036307, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 36.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036376, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 37.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036449, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036517, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 37.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036581, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 38.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036646, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036708, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 38.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03677, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 39.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036833, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036899, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 39.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.036961, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 40.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037024, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037086, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 40.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037148, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 41.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037214, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037276, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 41.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03734, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 42.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037405, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037467, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 42.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03753, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 43.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037594, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037656, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 43.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037718, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 44.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037784, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037847, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 44.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037912, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 45.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.037978, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038041, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 45.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038104, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 46.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038168, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038232, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 46.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038294, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 47.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03836, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038421, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 47.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038482, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 48.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038548, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03861, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 48.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038672, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 49.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038736, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038798, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 49.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03886, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 50.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.038927, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03899, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 50.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039052, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 51.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039116, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039177, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 51.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039238, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 52.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039302, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039364, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 52.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039425, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 53.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03949, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039552, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 53.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039613, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 54.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039678, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039739, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 54.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039799, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 55.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039864, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.039929, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 55.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.03999, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 56.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040054, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040116, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 56.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040177, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 57.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040241, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040303, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 57.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040365, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 58.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04043, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040491, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 58.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040554, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 59.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04062, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040682, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 59.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040745, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 60.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04081, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.040875, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 60.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04094, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 61.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041006, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041068, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 61.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.04113, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 62.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041194, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041256, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 62.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041376, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 63.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041452, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041518, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 63.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041586, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 64.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041652, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041723, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 64.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041789, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 65.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.041854, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042016, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 65.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042151, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 66.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042314, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042388, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 66.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042455, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 67.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042523, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042588, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 67.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042652, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 68.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.042718, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 0.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.042787, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.042851, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 0.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.042914, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 1.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.04298, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043043, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 1.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043107, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 2.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043173, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043237, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 2.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.0433, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 3.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043365, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043429, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 3.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043492, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 4.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043558, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043621, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 4.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043685, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 5.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.04375, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043813, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 5.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043876, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 6.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.043943, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044005, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 6.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044067, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 7.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044132, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044195, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 7.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044257, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 8.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044322, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044385, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 8.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044447, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 9.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044515, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044578, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 9.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.04464, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 10.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044705, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044768, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 10.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044831, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 11.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044896, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.044961, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 11.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045024, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 12.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.04509, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045153, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 12.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045216, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 13.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045281, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045348, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 13.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045411, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 14.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045475, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045537, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 14.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045601, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 15.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045665, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045728, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 15.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045795, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 16.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.04586, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045922, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 16.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.045985, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 17.25 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.04605, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.046113, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:40] LOG.warning: Implicit conversion from float 17.75 to int loses precision in C:\\laragon\\www\\erpq24\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.046176, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.563168, "end": **********.125185, "duration": 0.5620169639587402, "duration_str": "562ms", "measures": [{"label": "Booting", "start": **********.563168, "relative_start": 0, "end": **********.936563, "relative_end": **********.936563, "duration": 0.3733949661254883, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.936572, "relative_start": 0.37340402603149414, "end": **********.125186, "relative_end": 9.5367431640625e-07, "duration": 0.1886138916015625, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54228288, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x bill.view", "param_count": null, "params": [], "start": **********.020102, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/bill/view.blade.phpbill.view", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fbill%2Fview.blade.php&line=1", "ajax": false, "filename": "view.blade.php", "line": "?"}, "render_count": 1, "name_original": "bill.view"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.063562, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.067425, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.104551, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.11463, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.116624, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.116984, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.show", "controller": "App\\Http\\Controllers\\BillController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=333\" onclick=\"\">app/Http/Controllers/BillController.php:333-393</a>"}, "queries": {"nb_statements": 28, "nb_failed_statements": 0, "accumulated_duration": 0.014240000000000001, "accumulated_duration_str": "14.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9669762, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 14.466}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.977007, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 14.466, "width_percent": 3.722}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.991048, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 18.188, "width_percent": 3.301}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.992752, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 21.489, "width_percent": 1.966}, {"sql": "select * from `bills` where `bills`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 345}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.996819, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BillController.php:345", "source": "app/Http/Controllers/BillController.php:345", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=345", "ajax": false, "filename": "BillController.php", "line": "345"}, "connection": "kdmkjkqknb", "start_percent": 23.455, "width_percent": 3.23}, {"sql": "select * from `debit_notes` where `debit_notes`.`bill` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 345}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.999381, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BillController.php:345", "source": "app/Http/Controllers/BillController.php:345", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=345", "ajax": false, "filename": "BillController.php", "line": "345"}, "connection": "kdmkjkqknb", "start_percent": 26.685, "width_percent": 2.177}, {"sql": "select * from `bill_payments` where `bill_payments`.`bill_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 345}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0008771, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BillController.php:345", "source": "app/Http/Controllers/BillController.php:345", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=345", "ajax": false, "filename": "BillController.php", "line": "345"}, "connection": "kdmkjkqknb", "start_percent": 28.862, "width_percent": 2.037}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 345}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.00235, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BillController.php:345", "source": "app/Http/Controllers/BillController.php:345", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=345", "ajax": false, "filename": "BillController.php", "line": "345"}, "connection": "kdmkjkqknb", "start_percent": 30.899, "width_percent": 2.388}, {"sql": "select * from `product_services` where `product_services`.`id` in (0)", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 27, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 345}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.004992, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BillController.php:345", "source": "app/Http/Controllers/BillController.php:345", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=345", "ajax": false, "filename": "BillController.php", "line": "345"}, "connection": "kdmkjkqknb", "start_percent": 33.287, "width_percent": 2.388}, {"sql": "select * from `bill_payments` where `bill_id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 349}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.006226, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BillController.php:349", "source": "app/Http/Controllers/BillController.php:349", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=349", "ajax": false, "filename": "BillController.php", "line": "349"}, "connection": "kdmkjkqknb", "start_percent": 35.674, "width_percent": 1.334}, {"sql": "select * from `venders` where `venders`.`id` = 4 and `venders`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 350}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0080671, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BillController.php:350", "source": "app/Http/Controllers/BillController.php:350", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=350", "ajax": false, "filename": "BillController.php", "line": "350"}, "connection": "kdmkjkqknb", "start_percent": 37.008, "width_percent": 2.177}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 353}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0095549, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BillController.php:353", "source": "app/Http/Controllers/BillController.php:353", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=353", "ajax": false, "filename": "BillController.php", "line": "353"}, "connection": "kdmkjkqknb", "start_percent": 39.185, "width_percent": 1.545}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'bill' and `record_id` = 4", "type": "query", "params": [], "bindings": ["bill", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 379}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.011053, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "kdmkjkqknb", "start_percent": 40.73, "width_percent": 3.23}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'bill'", "type": "query", "params": [], "bindings": ["15", "bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 380}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.012784, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BillController.php:380", "source": "app/Http/Controllers/BillController.php:380", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=380", "ajax": false, "filename": "BillController.php", "line": "380"}, "connection": "kdmkjkqknb", "start_percent": 43.961, "width_percent": 1.475}, {"sql": "select * from `chart_of_accounts` where `chart_of_accounts`.`id` = 229 limit 1", "type": "query", "params": [], "bindings": ["229"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "bill.view", "file": "C:\\laragon\\www\\erpq24\\resources\\views/bill/view.blade.php", "line": 362}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0528119, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "bill.view:362", "source": "view::bill.view:362", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fbill%2Fview.blade.php&line=362", "ajax": false, "filename": "view.blade.php", "line": "362"}, "connection": "kdmkjkqknb", "start_percent": 45.435, "width_percent": 5.197}, {"sql": "select * from `chart_of_accounts` where `chart_of_accounts`.`id` = 229 limit 1", "type": "query", "params": [], "bindings": ["229"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "bill.view", "file": "C:\\laragon\\www\\erpq24\\resources\\views/bill/view.blade.php", "line": 417}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.055286, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "bill.view:417", "source": "view::bill.view:417", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fbill%2Fview.blade.php&line=417", "ajax": false, "filename": "view.blade.php", "line": "417"}, "connection": "kdmkjkqknb", "start_percent": 50.632, "width_percent": 3.02}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 102}, {"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 21, "namespace": "view", "name": "bill.view", "file": "C:\\laragon\\www\\erpq24\\resources\\views/bill/view.blade.php", "line": 488}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.058324, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 53.652, "width_percent": 2.458}, {"sql": "select * from `bill_attachments` where `bill_attachments`.`bill_id` = 4 and `bill_attachments`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "bill.view", "file": "C:\\laragon\\www\\erpq24\\resources\\views/bill/view.blade.php", "line": 516}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.060719, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "bill.view:516", "source": "view::bill.view:516", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fbill%2Fview.blade.php&line=516", "ajax": false, "filename": "view.blade.php", "line": "516"}, "connection": "kdmkjkqknb", "start_percent": 56.11, "width_percent": 2.669}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.064031, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 58.778, "width_percent": 2.879}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.065566, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 61.657, "width_percent": 2.528}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.069869, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 64.185, "width_percent": 2.458}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.071575, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 66.643, "width_percent": 1.756}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.073064, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "kdmkjkqknb", "start_percent": 68.399, "width_percent": 1.896}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.104975, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 70.295, "width_percent": 2.879}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.106662, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 73.174, "width_percent": 21.208}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.111002, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 94.382, "width_percent": 2.177}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 15 and `seen` = 0", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.112738, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:18", "source": "view::partials.admin.header:18", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=18", "ajax": false, "filename": "header.blade.php", "line": "18"}, "connection": "kdmkjkqknb", "start_percent": 96.559, "width_percent": 1.756}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 6233}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1149938, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 98.315, "width_percent": 1.685}]}, "models": {"data": {"App\\Models\\ChartOfAccount": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FChartOfAccount.php&line=1", "ajax": false, "filename": "ChartOfAccount.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Bill": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=1", "ajax": false, "filename": "Bill.php", "line": "?"}}, "App\\Models\\BillProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillProduct.php&line=1", "ajax": false, "filename": "BillProduct.php", "line": "?"}}, "App\\Models\\Vender": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=1", "ajax": false, "filename": "Vender.php", "line": "?"}}, "App\\Models\\BillAccount": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillAccount.php&line=1", "ajax": false, "filename": "BillAccount.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 76, "messages": [{"message": "[ability => show bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.996061, "xdebug_link": null}, {"message": "[ability => send bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1040567229 data-indent-pad=\"  \"><span class=sf-dump-note>send bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">send bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1040567229\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.023481, "xdebug_link": null}, {"message": "[ability => edit bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1358457212 data-indent-pad=\"  \"><span class=sf-dump-note>edit bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1358457212\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.023833, "xdebug_link": null}, {"message": "[ability => create payment bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-845610631 data-indent-pad=\"  \"><span class=sf-dump-note>create payment bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">create payment bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845610631\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.024287, "xdebug_link": null}, {"message": "[ability => edit bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-263565785 data-indent-pad=\"  \"><span class=sf-dump-note>edit bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-263565785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.062406, "xdebug_link": null}, {"message": "[ability => delete payment bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-143431524 data-indent-pad=\"  \"><span class=sf-dump-note>delete payment bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">delete payment bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143431524\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.062731, "xdebug_link": null}, {"message": "[ability => edit debit note, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1634771763 data-indent-pad=\"  \"><span class=sf-dump-note>edit debit note</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">edit debit note</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634771763\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.063201, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1580116460 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580116460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.075124, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.075466, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.075683, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.075848, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.076127, "xdebug_link": null}, {"message": "[ability => statement report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-231597969 data-indent-pad=\"  \"><span class=sf-dump-note>statement report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-231597969\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.076422, "xdebug_link": null}, {"message": "[ability => invoice report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1046742421 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046742421\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.076692, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2078004033 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078004033\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.076998, "xdebug_link": null}, {"message": "[ability => stock report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1172947463 data-indent-pad=\"  \"><span class=sf-dump-note>stock report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172947463\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.077256, "xdebug_link": null}, {"message": "[ability => loss & profit report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1226791350 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226791350\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.077521, "xdebug_link": null}, {"message": "[ability => manage transaction, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2068372960 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2068372960\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.077767, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-987946781 data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987946781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.078029, "xdebug_link": null}, {"message": "[ability => expense report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-327984770 data-indent-pad=\"  \"><span class=sf-dump-note>expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327984770\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.078285, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-3262865 data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-3262865\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.078542, "xdebug_link": null}, {"message": "[ability => tax report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>tax report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.078818, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1423168097 data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423168097\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.079015, "xdebug_link": null}, {"message": "[ability => manage report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-271727988 data-indent-pad=\"  \"><span class=sf-dump-note>manage report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271727988\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.079641, "xdebug_link": null}, {"message": "[ability => show pos dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-924018482 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-924018482\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.079863, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1317481699 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1317481699\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.080763, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-577209757 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-577209757\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.0813, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-26801369 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26801369\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.081744, "xdebug_link": null}, {"message": "[ability => manage pay slip, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1309244319 data-indent-pad=\"  \"><span class=sf-dump-note>manage pay slip</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309244319\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.08222, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1152625082 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1152625082\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.082833, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-670332967 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-670332967\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.083451, "xdebug_link": null}, {"message": "[ability => manage attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-874433097 data-indent-pad=\"  \"><span class=sf-dump-note>manage attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-874433097\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.084101, "xdebug_link": null}, {"message": "[ability => create attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1777031360 data-indent-pad=\"  \"><span class=sf-dump-note>create attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1777031360\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.08486, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1679651238 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679651238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.085394, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-181004703 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-181004703\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.08607, "xdebug_link": null}, {"message": "[ability => manage transfer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1720221143 data-indent-pad=\"  \"><span class=sf-dump-note>manage transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1720221143\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.086694, "xdebug_link": null}, {"message": "[ability => manage resignation, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1634925886 data-indent-pad=\"  \"><span class=sf-dump-note>manage resignation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage resignation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634925886\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.087197, "xdebug_link": null}, {"message": "[ability => manage travel, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1853433931 data-indent-pad=\"  \"><span class=sf-dump-note>manage travel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage travel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1853433931\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.087692, "xdebug_link": null}, {"message": "[ability => manage promotion, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1509732015 data-indent-pad=\"  \"><span class=sf-dump-note>manage promotion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage promotion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509732015\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.088184, "xdebug_link": null}, {"message": "[ability => manage complaint, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-575160605 data-indent-pad=\"  \"><span class=sf-dump-note>manage complaint</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage complaint</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575160605\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.088674, "xdebug_link": null}, {"message": "[ability => manage warning, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-867951764 data-indent-pad=\"  \"><span class=sf-dump-note>manage warning</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage warning</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867951764\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.08919, "xdebug_link": null}, {"message": "[ability => manage termination, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-502369254 data-indent-pad=\"  \"><span class=sf-dump-note>manage termination</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage termination</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502369254\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.089808, "xdebug_link": null}, {"message": "[ability => manage announcement, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-888905196 data-indent-pad=\"  \"><span class=sf-dump-note>manage announcement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage announcement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888905196\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.090392, "xdebug_link": null}, {"message": "[ability => manage holiday, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2112898789 data-indent-pad=\"  \"><span class=sf-dump-note>manage holiday</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage holiday</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112898789\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.090952, "xdebug_link": null}, {"message": "[ability => manage document, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage document</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage document</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.091345, "xdebug_link": null}, {"message": "[ability => manage company policy, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1721610312 data-indent-pad=\"  \"><span class=sf-dump-note>manage company policy</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage company policy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721610312\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.091776, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.092038, "xdebug_link": null}, {"message": "[ability => manage bank account, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.092274, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.092498, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2147190284 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2147190284\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.092697, "xdebug_link": null}, {"message": "[ability => manage proposal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-529629345 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-529629345\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.092976, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1797889987 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1797889987\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093238, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093435, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093778, "xdebug_link": null}, {"message": "[ability => manage goal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.094169, "xdebug_link": null}, {"message": "[ability => manage constant tax, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.094357, "xdebug_link": null}, {"message": "[ability => manage print settings, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1315793126 data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315793126\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.094527, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1166021619 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1166021619\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.094682, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1921748670 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921748670\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.094825, "xdebug_link": null}, {"message": "[ability => manage role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2069498405 data-indent-pad=\"  \"><span class=sf-dump-note>manage role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069498405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.094977, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-407875613 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-407875613\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095276, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-605814410 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-605814410\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095492, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2020425479 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020425479\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095692, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1552827417 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552827417\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095906, "xdebug_link": null}, {"message": "[ability => show warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1460250074 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1460250074\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.096615, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1837810256 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1837810256\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.097318, "xdebug_link": null}, {"message": "[ability => show pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1297144008 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297144008\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098034, "xdebug_link": null}, {"message": "[ability => create barcode, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-631733450 data-indent-pad=\"  \"><span class=sf-dump-note>create barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-631733450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098746, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-429733793 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429733793\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.099484, "xdebug_link": null}, {"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-383276927 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-383276927\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100175, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2004552014 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2004552014\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101092, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1605828928 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605828928\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.102329, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1232175581 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232175581\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103134, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103333, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1824521175 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824521175\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.104, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.104281, "xdebug_link": null}]}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IllSSXVIVXRzS2Q2NUNicVR2cXg2SHc9PSIsInZhbHVlIjoiSUZKTDVVM2lKMTVVSEptS2luOHZpQT09IiwibWFjIjoiY2UwMmEyNDdjNWQ4OTJkODczMjQwNTI0OTc5ZGI1MzEwODdhZjE5MmY1Mzg2OWFmODg2MWNhMTNkMzc0MzMwZiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/eyJpdiI6IllSSXVIVXRzS2Q2NUNicVR2cXg2SHc9PSIsInZhbHVlIjoiSUZKTDVVM2lKMTVVSEptS2luOHZpQT09IiwibWFjIjoiY2UwMmEyNDdjNWQ4OTJkODczMjQwNTI0OTc5ZGI1MzEwODdhZjE5MmY1Mzg2OWFmODg2MWNhMTNkMzc0MzMwZiIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-1334059094 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1334059094\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1744335169 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1744335169\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2019886287 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2019886287\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-193000027 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980096846%7C24%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhWeVh5UWgrT0sxNnlJSmx1WTZrSWc9PSIsInZhbHVlIjoiU3RiUmVQbEJhT3hCLytlTEZnbm5wMEtCOE5vdzZjNXNGenM5RFAxMTBhYVg3YWNlRVhyanBaYzh0ZlN4ZFhDMUUrbmM5MHI4WnJQa09rcWVqNjRQZTFOZXlSN2REYlhNUldaeDJHYkVBcFFNZkZpR1p4aHlBVUtTa2RUYzQrUmczbmpHeDU4cGMxWko1ZWQ2U2lTMHNEOUVmTTVPV1NwTUdJeHhnRVlhMmVUYXNUM1RLNG50bEZIa2xZU0NwZ3F3SGhNKytoaFQwMVVCNm5BZE0yMHBjdEIxYjhjNFRQQ0w2QzF5dWhiUWtuaEx6VytSOWV1S25LWmRXTzdPNGlrRGg4Z0JnaE5HU3lPUFQvVzNJUHkxZm9sSGdOZllZeUVnUDc4dkJsWGt1ejJYZm5FMDVWa1lPZXpXSlRWRGFqZG9uUHdMRndNWHNKYXdzZnlRK1VMa296aU5NaU9NZk9pOFprUWhxdmlEbjBVQk9wMnFzRG9NaTNrdVozTUlPYmxVRGhGVGdyc2Q4Um44bHVsNGpDOURkK1VnZmZVM09Pc1JqNXVpSlVMSnJlNXg5YisrUHF2c2lxZnhnbGltVFlzU3BycEUxU3BxazYzUFIyZC8yZ2hpclpuQUtKdy9KME1lOFNRRGtWbzhxZTR6NlprYWVlS3Z2V2pJbmtqYmJEUTMiLCJtYWMiOiI5M2RiNWM4MDE1MmQzYjJiZTllYTJlMDVmZTU3MzMzMWUyOWQxMzY5NDczOGM2MmYxZTdkZGFhNDY3YzhiMGZiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFLSGp1VHBxU05lVFdGOGI5RHUvYmc9PSIsInZhbHVlIjoiRVlYK2YyVzQ0bTFrdCtiQ0dnMUkrbGVYWUV6bHlUV24wUndYQktJbHZIQVVaUnUwbURpWWg0RHVCRHpYU3JVbG5zcEhyRE9lREgrcU5OSDdlbkU4WUNsN2JGOFpqcjNiVW9oNkt0L2tyOGM5OWhNU3ZrdUVOZlJBWENYWFFadjNBbnNqMUFUN2x0UWdqdDVzTnJ0dVQ5dzg4SnRYZEdFSml3WmlqM2x5bkNTMHMyZlNUcDN3dU1maXRUelJaMTlHWUtOQUl1bHJOcERWWVBKNTMwai8xcHlLV0FUK3ZEVHhLQXJUVWZyaWI1K2ptclQzcUFCVVg5b2pMbWE0c3ZqWVo2bmZQMHRvR0RHWkhJRnowN0xRSkdqaTNnRC83VGROVFo0U3lTU0FublZUTkFIUTFFNnVQRTk4bnUzb0RCMGpXTXBKeCtjNldvdXN3ZmJhR3NTQjdqbStqd21ueTNoQmMxOG5lZyt0S3ZOeEdiVkhuWGRzR25OZlByNkcxTjIvNDMxTnJnMHhEeTNtZXhWVW1DWTZhZzA5ODV3bTZBZ3VOc2Rya0RIdE1OM05FR2ptYkx4azBoU0hETWhTMWFUcHNDbW1vaFFCM2lmbkdEUWp4M1BoaXFWSVc2ZWtNNjhHaXRRcHdYN0toUEtvRXdJOUR2bTV2QWFrQ0ZJeFlPSDgiLCJtYWMiOiJlMTJkNDdmYTQ0NzM5Y2Y5MjliYzRhOGVlNjM4NjZhZmMxOWMzZDg2YTRlZGVlZTc2OGZhNzNkYWRlZjI3YzI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193000027\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-782047564 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782047564\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1262966395 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:21:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdpVHJRZmx0bEhsUnB3MVhWanZyT3c9PSIsInZhbHVlIjoiZXRNUHI2V3o0U1QrQTBvSk12U0Zna3gzSVM3WEpTN2h2NFZBRVZtdW4rUlhvRnhuemVOT3RkaUZ4SUVhVVFUQW9KNUcrc3grSkxGSXZkeVRaNnNWRXk3MXM0WWxPeGFLT1ZvakJwbXU1Wk9OYmZ4SU9xQkJvd3h2S1ZOWksya2NyOHUrbnp2cXNHanR1NWNlclU3NTNjNE9FdjFxVCt2eVJCWkk1WkVMYzIvRTRrTDlhWjZQMi9LZDZqYmlPK2szS1puNTlhUDRpNFRiZFpWQm8weEoxSWUwZTFTTFVrYmVxc2lQNXAwVmtyMW1EajdQSCtxMC9XbmlJdUhSWG9FUWdRLzFKSTA4WWlVSVZYL0piMHBJKytyRFpDL09XN01SL3U4YUZ6SHhHSWthZmZKeEtWMVlWMnNETnpFOE1HODF5WHpKRERSSVpmRTU2Z1FSTjQwNVd6Sk9ZU1NaWSt1ei9ETW5NYzNGbjZVZWdTR0VBZ1VSR08vSlFhODdUVjBlSDNLaHBsc3ppbVZMSzVteHV1QXlld0RqRUlxTHVXL29Fd1VoVTlVWUZ2ZlFJblBFQ05Uc29kcDZrb1dobXhONkhiUHpYTHNRaDd4dGh4VUx6ZTBVeitvL2ttTUZZejY2UHlsRTBlVU9GaXhYcVEyYm15MGtwTDMwc3hlVndGL3oiLCJtYWMiOiJkNTRkZjExZDE2MzdiMjY4ZTJkMjRkYzRjNzhhMjQ4MjRlZWIyZTZkMmIwNGNhNDViYWI0YjQ0MGRkZWNmMTQ5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjlnT2dqZm92SUl4bUhlNmc0cWZ6L2c9PSIsInZhbHVlIjoiaTZia0FMNTFRekJYcnJRYk9lSnMwWnA2SUxFb2h1K0hOZjBHeElldFZMWUZwR0Nnckx5SXQveEprb0xMOFEwOEQySW11YWovQlQ2NjdkQkNUVkxmMTA4cTc5d1RzbzRBNnFvQTUxSU04ZXduZWFCTzF2QXErcmpmeExYQ1g2bVkxVnlQSW44Smsya05sR3REUnJjcFZleUszejR5ZTYwSitMUXdlMkkxVzY3YVRpbnUwMWw0Sk12Q0dFSmxqeERqT2srMzdGREwrV09LWjVJMHkyNU9TZlpCTFpmY1k3U09lb2NJK25mU0RxWWdoQ3llRzVhbXBKN045eXlXa0IyUzR4M0hNMmhVMkptWkl1V3MwZW44bzlmS1hybTd2UXJ5YXllRm1LNldFc3QzWkRjbFIxdDB1RUU4ODVmbWtnS1p4Unc0YlAxZGFXQ3Y5aVNHTVM1UUlLQ3pjSjFBenFsVllXVm5DMGpWc2pyQ3JNNXhvcHFHUmRkSmdXYVJJSytUZWpDZUJDdW16ekpYWGJJMThON3krUHdQS0xPQUQvbFpmOTFvTThIc04xcDUwaEJGWDluRnRFZW4yb0xkTEhjdjNBV2EvNnRwNGZkYUhnM3Y2SzhGSzJRdHRiSW1hUGZFQnFKYjdxcHZHUnpXcDZzMC83dlhtSVRUNytZaDdUZSsiLCJtYWMiOiI2MjBiNjA3ZjM0OTg3Y2I0N2VlZDM4MzgxODJlOWVkYmIwZDNmMjljNzA3NmZlMDY4MWRjODcyNzUzZGEzNjRiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdpVHJRZmx0bEhsUnB3MVhWanZyT3c9PSIsInZhbHVlIjoiZXRNUHI2V3o0U1QrQTBvSk12U0Zna3gzSVM3WEpTN2h2NFZBRVZtdW4rUlhvRnhuemVOT3RkaUZ4SUVhVVFUQW9KNUcrc3grSkxGSXZkeVRaNnNWRXk3MXM0WWxPeGFLT1ZvakJwbXU1Wk9OYmZ4SU9xQkJvd3h2S1ZOWksya2NyOHUrbnp2cXNHanR1NWNlclU3NTNjNE9FdjFxVCt2eVJCWkk1WkVMYzIvRTRrTDlhWjZQMi9LZDZqYmlPK2szS1puNTlhUDRpNFRiZFpWQm8weEoxSWUwZTFTTFVrYmVxc2lQNXAwVmtyMW1EajdQSCtxMC9XbmlJdUhSWG9FUWdRLzFKSTA4WWlVSVZYL0piMHBJKytyRFpDL09XN01SL3U4YUZ6SHhHSWthZmZKeEtWMVlWMnNETnpFOE1HODF5WHpKRERSSVpmRTU2Z1FSTjQwNVd6Sk9ZU1NaWSt1ei9ETW5NYzNGbjZVZWdTR0VBZ1VSR08vSlFhODdUVjBlSDNLaHBsc3ppbVZMSzVteHV1QXlld0RqRUlxTHVXL29Fd1VoVTlVWUZ2ZlFJblBFQ05Uc29kcDZrb1dobXhONkhiUHpYTHNRaDd4dGh4VUx6ZTBVeitvL2ttTUZZejY2UHlsRTBlVU9GaXhYcVEyYm15MGtwTDMwc3hlVndGL3oiLCJtYWMiOiJkNTRkZjExZDE2MzdiMjY4ZTJkMjRkYzRjNzhhMjQ4MjRlZWIyZTZkMmIwNGNhNDViYWI0YjQ0MGRkZWNmMTQ5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjlnT2dqZm92SUl4bUhlNmc0cWZ6L2c9PSIsInZhbHVlIjoiaTZia0FMNTFRekJYcnJRYk9lSnMwWnA2SUxFb2h1K0hOZjBHeElldFZMWUZwR0Nnckx5SXQveEprb0xMOFEwOEQySW11YWovQlQ2NjdkQkNUVkxmMTA4cTc5d1RzbzRBNnFvQTUxSU04ZXduZWFCTzF2QXErcmpmeExYQ1g2bVkxVnlQSW44Smsya05sR3REUnJjcFZleUszejR5ZTYwSitMUXdlMkkxVzY3YVRpbnUwMWw0Sk12Q0dFSmxqeERqT2srMzdGREwrV09LWjVJMHkyNU9TZlpCTFpmY1k3U09lb2NJK25mU0RxWWdoQ3llRzVhbXBKN045eXlXa0IyUzR4M0hNMmhVMkptWkl1V3MwZW44bzlmS1hybTd2UXJ5YXllRm1LNldFc3QzWkRjbFIxdDB1RUU4ODVmbWtnS1p4Unc0YlAxZGFXQ3Y5aVNHTVM1UUlLQ3pjSjFBenFsVllXVm5DMGpWc2pyQ3JNNXhvcHFHUmRkSmdXYVJJSytUZWpDZUJDdW16ekpYWGJJMThON3krUHdQS0xPQUQvbFpmOTFvTThIc04xcDUwaEJGWDluRnRFZW4yb0xkTEhjdjNBV2EvNnRwNGZkYUhnM3Y2SzhGSzJRdHRiSW1hUGZFQnFKYjdxcHZHUnpXcDZzMC83dlhtSVRUNytZaDdUZSsiLCJtYWMiOiI2MjBiNjA3ZjM0OTg3Y2I0N2VlZDM4MzgxODJlOWVkYmIwZDNmMjljNzA3NmZlMDY4MWRjODcyNzUzZGEzNjRiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262966395\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1377909891 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IllSSXVIVXRzS2Q2NUNicVR2cXg2SHc9PSIsInZhbHVlIjoiSUZKTDVVM2lKMTVVSEptS2luOHZpQT09IiwibWFjIjoiY2UwMmEyNDdjNWQ4OTJkODczMjQwNTI0OTc5ZGI1MzEwODdhZjE5MmY1Mzg2OWFmODg2MWNhMTNkMzc0MzMwZiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1377909891\", {\"maxDepth\":0})</script>\n"}}