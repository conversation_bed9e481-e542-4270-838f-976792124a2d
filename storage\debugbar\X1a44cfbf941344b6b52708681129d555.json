{"__meta": {"id": "X1a44cfbf941344b6b52708681129d555", "datetime": "2025-06-26 23:20:47", "utime": **********.71265, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.273235, "end": **********.712665, "duration": 0.43942999839782715, "duration_str": "439ms", "measures": [{"label": "Booting", "start": **********.273235, "relative_start": 0, "end": **********.647577, "relative_end": **********.647577, "duration": 0.3743419647216797, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.647585, "relative_start": 0.37434983253479004, "end": **********.712667, "relative_end": 1.9073486328125e-06, "duration": 0.06508207321166992, "duration_str": "65.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45027568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015120000000000001, "accumulated_duration_str": "15.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6779761, "duration": 0.01382, "duration_str": "13.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.402}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.699729, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.402, "width_percent": 3.571}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7056239, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 94.974, "width_percent": 5.026}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-809191388 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-809191388\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1893850571 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1893850571\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-682374623 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682374623\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-805878455 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980041966%7C18%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlraExWTGtoMEkxVGxQQzhzQnNVOFE9PSIsInZhbHVlIjoiaFNVQWRHd2JRbkF6cFI3Sm5WLzdQV1IxTVYxNU11TU80ZlpqZGMrTjdtODY3OVFmU3Rtc3RnU3VIOVlGdzI3b3RwUGJ5TURZRmFUcWljQlhabnNaQU5WR3A2b2xaa01uM1JFZjBHdGJUN2FsbmZBM3Z5RkpwZmhBbGQzbzdvTlI3SVNxK2J6WEZFd3VPdjZBdmVPcG5QWFkzUUlqZ1NqVUNEVENPaHVuZUNaN1F1OTA4OVRIRi9CZTR4dGhMUUU3K2FPQ2kwT1NPOXlPTXlWZWMwcHAvT2ZRcUI2TDJJMmg4b2ZyUlVsdHRhY1FUTVdWRHRXZjU3Yk4rV2REQTFvVUR0b3lCcjNmU20wQWNPZ2lvd0F6UDkwTk9vRDhrcjFlTEtyTkFEenhhOTlRUjladEZuUFJqQjNaRFdONHBtQTgzUjAzS3JCa2wwdFl4Z1lMYnplUzh4b2IyT0ZpWFVEM0t5RmZuMGhydTRDR1B3SHpSWmk0QlJvWmQ1SUI4SkVEM0lVZit4NUF1c3ZPSkVEcEVCTGdocmNMbWtFUzVucmRHNUl1RVNQN1MwM0N4RTRqU3FTcEVlVkNyNEdHYnFIRHcvaE9zOXZtdWE5NGpyS0ZEaU9maXd4VmRwSUxzbVBndy9IbmF3aHBaNU9lTGlTMWFqck1HTXJMNS9KNEFDSngiLCJtYWMiOiI1OTBiYjNmNGJiMTcxNTJiMTIyZGY1MzM0MDA5YmFhZjZhNzdjZmZlZGI5Yzc1YjEwMWRhZmFkNDIzOGVhZTlkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlQ3NEV6dk0vOTNhK2xXQm81UGdVMlE9PSIsInZhbHVlIjoibDRJR1F1TEVPc1FYVVRtbUFjOVJvUm4vOVB5SGRzMk4xTCtqZkVlV3hjbFBLWStpNXVTMGhzaDVOcXFBdUcxc1RRS3FsdFpUdU9RT0lRenhMYmpsdGp5aUxnVlhRYnJzQXlqTmZ0enBkVlRrVFZ3QXdGeGpYUjJyeVdWV09zZ2w2dXlPa01xL2Jnc0VTUk5HVzlYWmZtRkR3Ylc1UnlUdTJvVnRrS1dhUGs0UUR3ZGVwWDljSGV5VWZVNy9adDhvM25rRlVMNEkvS1IyY0U1NitLWjB6Qk16WUp3NUIxc3A0czFvdHN0SGNKWDU1b2VCRHZ4YTZmRjJYMEJLSzk5aklTbXQ0K0sydDd6dll1R05MOWM5djRTSWJjNldXcnk4OGJ0V0lpOXBsWlBSbHYrelFCOTEzRjFlT3NIVk40Y3ExWkhtcGp4L29EV1dtd2hCMndUS2xoa2hHdFMxV013NW9ya3VWZU5FUGd1K1N2aFFVcXdma2hNdlhMZW9naHBmbUw5L1poYVV3d09pY1BMdjlnaHh0UXgrRkRqWExUUUtGQ0ltR0xZa3l2S21OcEtpMTUwTVhvK0p6U3BTN1dJdi9PeVM3SlNUcWFHUkN3RTR5WHdrZFdxZzhoMysyU1grbjZtSU56R1V2OWRFWFpkRTlMQTVmemFaR0F3QWF2eGYiLCJtYWMiOiI1OTgyNDE2YTlmYjg2NTE4YjNhMzU4MWYyNDBkN2ZhMTA5ZDA1MTVjNzg3NTJiMmMyYzQyMGFhYTY3NDExNzA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-805878455\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1364705386 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1364705386\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1067059067 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilg0YVh0RTREZHNocnZLMGlLRytXWUE9PSIsInZhbHVlIjoicDhxdUpXUW1xM1FvQnB2djRyVmNzdE1GcHRlWUhkZlFvdEdYSGNUNm43c3YvM0JBVk5TY3YrSVdlbXVxK2RtaXFBSWxTZXM5MVl1QkFZOEw2WXNOL1pmNys3ZGVYOGZpalBIRXBTOFJUOHFKdUxla25iR1BadUw0M3d5ZzlFMGhMTTNkT3JlS0Y4U2FvaHR1cFhISTlYRjJnbHVKNFlXbFZFYzI1SGZjT1B5R3dYMVFmbFJHYWdSRFdENmlmUHhKQnJxbDY4KzNRWkdMK3R3b1FzdlF3QkMrcXBWeHdtRVFKNzlFUmhkYjd0ZlcrTjgybHU3TXlZck42c05RK0F2L09oZGRqaDBtdzVUYTExT0xNYlZKSEhhNkJCMWdPZUorWklmZjhYa3M5NGhmenUyWGZNVk13bDZvSU1IZCtFcnZJaTFlK3Q1TTVmeEE4Nm04WE93a3JYdFpLcEE5SlBEVE5uYjRvazBycG5UaElhaWNXeWs3Q2s4TzlxSkxDMk1adzQvUnF0cTJsVDhqZ25vbHFJU1ZLOFFBQlkxRUo1OXBpRDhMRjl2ZVZMaGZYZXN0bUk0SHVlekloRUF5Zi9nNUxIYnBucTV2cDhJbW9YS0xaa2p1bm1WV2FjclF1OXlpQ00wNTcvcWg1MjhtTVE5RERmMW4rN2o1Q1VvQmpLemciLCJtYWMiOiI3M2NjODA0MjhiY2EzYTc1MTQ2ZjY0OGM2MDgzOGRkN2E2MzIxN2U3ZTFiM2ZlZjlmMTE2ZDkwYTE5ZDZkOTc5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InhCVEhRai92azRiRzd2bmt2cTNHeGc9PSIsInZhbHVlIjoiYUMrNG91SkF4YUJZVDlPcW5XSnpzKzVHWUFnTWt6Vm1yU0dJS0lYVm9LdEoyY21wT2dNZTVCWlNJK1dXdndrd0xGSHVyNERLNEJwSWZIM3Y4c1JGSTJHUHIxQVI3Ukkza3h0SDI0R3ZwUTZlTXd4bXRONkFjb0pTSmdDV0tTbGpiU1NwY3F5MTliZVhpQzRqUTRVaElpendxak9wVFYyTVJJOFRzSk9nMVI4Zk9RREN5ZVByQ3EzL0RES0t6cVEyc3NkcWZ0bmRSUnFjeUg3bFdJajZIamVodUlqTTFBQnArUlhQVkthemtRSENGeFpsN2loWDdwTTRZa3J4bFgrQmVhMEdtaXFvUWdOb0R6SFkyd1RVNGFDTW9nd2hmZ1paRllBLzRJcnN5ZEdRa09FbnhNZjFSNnNQK3NmS1FJY2M5K0ttNjNQYzczOE40R3hpcU00WHRHbURDa1JiZnNsanI2dlhsU3NtVSsxNlYwYmRKZkRsRlE3ZEVoS0xBaU5neTFubG9ad0lHMnRoVUJGRUxPU2lSK1pyQkt5cGdKYkFCMEx0b3o1aCsvQ1FMbjRiTDVCdnVER2EwS2lZdWk5SUhHcll0MG9PbDAybGFrckRrZU9scFdXY0JTeUhBWHFTdG1ncWN0TW11VkFQNTdsWk9JL1NIZ3FVaGpuM2tKT0ciLCJtYWMiOiIzN2EzMWQ2ZTg3YWNhODI4NWY4OTJkMjIyOTliN2RiMmM0YjJiY2NhNWFkNTk5MThjMTk2ODg5NmRiMDgwZjMzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilg0YVh0RTREZHNocnZLMGlLRytXWUE9PSIsInZhbHVlIjoicDhxdUpXUW1xM1FvQnB2djRyVmNzdE1GcHRlWUhkZlFvdEdYSGNUNm43c3YvM0JBVk5TY3YrSVdlbXVxK2RtaXFBSWxTZXM5MVl1QkFZOEw2WXNOL1pmNys3ZGVYOGZpalBIRXBTOFJUOHFKdUxla25iR1BadUw0M3d5ZzlFMGhMTTNkT3JlS0Y4U2FvaHR1cFhISTlYRjJnbHVKNFlXbFZFYzI1SGZjT1B5R3dYMVFmbFJHYWdSRFdENmlmUHhKQnJxbDY4KzNRWkdMK3R3b1FzdlF3QkMrcXBWeHdtRVFKNzlFUmhkYjd0ZlcrTjgybHU3TXlZck42c05RK0F2L09oZGRqaDBtdzVUYTExT0xNYlZKSEhhNkJCMWdPZUorWklmZjhYa3M5NGhmenUyWGZNVk13bDZvSU1IZCtFcnZJaTFlK3Q1TTVmeEE4Nm04WE93a3JYdFpLcEE5SlBEVE5uYjRvazBycG5UaElhaWNXeWs3Q2s4TzlxSkxDMk1adzQvUnF0cTJsVDhqZ25vbHFJU1ZLOFFBQlkxRUo1OXBpRDhMRjl2ZVZMaGZYZXN0bUk0SHVlekloRUF5Zi9nNUxIYnBucTV2cDhJbW9YS0xaa2p1bm1WV2FjclF1OXlpQ00wNTcvcWg1MjhtTVE5RERmMW4rN2o1Q1VvQmpLemciLCJtYWMiOiI3M2NjODA0MjhiY2EzYTc1MTQ2ZjY0OGM2MDgzOGRkN2E2MzIxN2U3ZTFiM2ZlZjlmMTE2ZDkwYTE5ZDZkOTc5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InhCVEhRai92azRiRzd2bmt2cTNHeGc9PSIsInZhbHVlIjoiYUMrNG91SkF4YUJZVDlPcW5XSnpzKzVHWUFnTWt6Vm1yU0dJS0lYVm9LdEoyY21wT2dNZTVCWlNJK1dXdndrd0xGSHVyNERLNEJwSWZIM3Y4c1JGSTJHUHIxQVI3Ukkza3h0SDI0R3ZwUTZlTXd4bXRONkFjb0pTSmdDV0tTbGpiU1NwY3F5MTliZVhpQzRqUTRVaElpendxak9wVFYyTVJJOFRzSk9nMVI4Zk9RREN5ZVByQ3EzL0RES0t6cVEyc3NkcWZ0bmRSUnFjeUg3bFdJajZIamVodUlqTTFBQnArUlhQVkthemtRSENGeFpsN2loWDdwTTRZa3J4bFgrQmVhMEdtaXFvUWdOb0R6SFkyd1RVNGFDTW9nd2hmZ1paRllBLzRJcnN5ZEdRa09FbnhNZjFSNnNQK3NmS1FJY2M5K0ttNjNQYzczOE40R3hpcU00WHRHbURDa1JiZnNsanI2dlhsU3NtVSsxNlYwYmRKZkRsRlE3ZEVoS0xBaU5neTFubG9ad0lHMnRoVUJGRUxPU2lSK1pyQkt5cGdKYkFCMEx0b3o1aCsvQ1FMbjRiTDVCdnVER2EwS2lZdWk5SUhHcll0MG9PbDAybGFrckRrZU9scFdXY0JTeUhBWHFTdG1ncWN0TW11VkFQNTdsWk9JL1NIZ3FVaGpuM2tKT0ciLCJtYWMiOiIzN2EzMWQ2ZTg3YWNhODI4NWY4OTJkMjIyOTliN2RiMmM0YjJiY2NhNWFkNTk5MThjMTk2ODg5NmRiMDgwZjMzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067059067\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2023562407 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023562407\", {\"maxDepth\":0})</script>\n"}}