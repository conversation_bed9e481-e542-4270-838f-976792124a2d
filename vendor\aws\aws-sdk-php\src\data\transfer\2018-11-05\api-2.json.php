<?php
// This file was auto-generated from sdk-root/src/data/transfer/2018-11-05/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-11-05', 'endpointPrefix' => 'transfer', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'AWS Transfer', 'serviceFullName' => 'AWS Transfer Family', 'serviceId' => 'Transfer', 'signatureVersion' => 'v4', 'signingName' => 'transfer', 'targetPrefix' => 'TransferService', 'uid' => 'transfer-2018-11-05', ], 'operations' => [ 'CreateAccess' => [ 'name' => 'CreateAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAccessRequest', ], 'output' => [ 'shape' => 'CreateAccessResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], ], ], 'CreateAgreement' => [ 'name' => 'CreateAgreement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAgreementRequest', ], 'output' => [ 'shape' => 'CreateAgreementResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], ], ], 'CreateConnector' => [ 'name' => 'CreateConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConnectorRequest', ], 'output' => [ 'shape' => 'CreateConnectorResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], ], ], 'CreateProfile' => [ 'name' => 'CreateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProfileRequest', ], 'output' => [ 'shape' => 'CreateProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateServer' => [ 'name' => 'CreateServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateServerRequest', ], 'output' => [ 'shape' => 'CreateServerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], ], 'idempotent' => true, ], 'CreateWorkflow' => [ 'name' => 'CreateWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkflowRequest', ], 'output' => [ 'shape' => 'CreateWorkflowResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteAccess' => [ 'name' => 'DeleteAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAccessRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteAgreement' => [ 'name' => 'DeleteAgreement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAgreementRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteCertificate' => [ 'name' => 'DeleteCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCertificateRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteConnector' => [ 'name' => 'DeleteConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConnectorRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteHostKey' => [ 'name' => 'DeleteHostKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteHostKeyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteProfile' => [ 'name' => 'DeleteProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProfileRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteServer' => [ 'name' => 'DeleteServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteServerRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteSshPublicKey' => [ 'name' => 'DeleteSshPublicKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSshPublicKeyRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'idempotent' => true, ], 'DeleteWorkflow' => [ 'name' => 'DeleteWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWorkflowRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DescribeAccess' => [ 'name' => 'DescribeAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccessRequest', ], 'output' => [ 'shape' => 'DescribeAccessResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeAgreement' => [ 'name' => 'DescribeAgreement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAgreementRequest', ], 'output' => [ 'shape' => 'DescribeAgreementResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeCertificate' => [ 'name' => 'DescribeCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCertificateRequest', ], 'output' => [ 'shape' => 'DescribeCertificateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeConnector' => [ 'name' => 'DescribeConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConnectorRequest', ], 'output' => [ 'shape' => 'DescribeConnectorResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeExecution' => [ 'name' => 'DescribeExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeExecutionRequest', ], 'output' => [ 'shape' => 'DescribeExecutionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeHostKey' => [ 'name' => 'DescribeHostKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeHostKeyRequest', ], 'output' => [ 'shape' => 'DescribeHostKeyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeProfile' => [ 'name' => 'DescribeProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeProfileRequest', ], 'output' => [ 'shape' => 'DescribeProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeSecurityPolicy' => [ 'name' => 'DescribeSecurityPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSecurityPolicyRequest', ], 'output' => [ 'shape' => 'DescribeSecurityPolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeServer' => [ 'name' => 'DescribeServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeServerRequest', ], 'output' => [ 'shape' => 'DescribeServerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeUser' => [ 'name' => 'DescribeUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUserRequest', ], 'output' => [ 'shape' => 'DescribeUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeWorkflow' => [ 'name' => 'DescribeWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeWorkflowRequest', ], 'output' => [ 'shape' => 'DescribeWorkflowResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ImportCertificate' => [ 'name' => 'ImportCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportCertificateRequest', ], 'output' => [ 'shape' => 'ImportCertificateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ImportHostKey' => [ 'name' => 'ImportHostKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportHostKeyRequest', ], 'output' => [ 'shape' => 'ImportHostKeyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], ], ], 'ImportSshPublicKey' => [ 'name' => 'ImportSshPublicKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportSshPublicKeyRequest', ], 'output' => [ 'shape' => 'ImportSshPublicKeyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], ], ], 'ListAccesses' => [ 'name' => 'ListAccesses', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccessesRequest', ], 'output' => [ 'shape' => 'ListAccessesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListAgreements' => [ 'name' => 'ListAgreements', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAgreementsRequest', ], 'output' => [ 'shape' => 'ListAgreementsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListCertificates' => [ 'name' => 'ListCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCertificatesRequest', ], 'output' => [ 'shape' => 'ListCertificatesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListConnectors' => [ 'name' => 'ListConnectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListConnectorsRequest', ], 'output' => [ 'shape' => 'ListConnectorsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListExecutions' => [ 'name' => 'ListExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListExecutionsRequest', ], 'output' => [ 'shape' => 'ListExecutionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListHostKeys' => [ 'name' => 'ListHostKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListHostKeysRequest', ], 'output' => [ 'shape' => 'ListHostKeysResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListProfiles' => [ 'name' => 'ListProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListProfilesRequest', ], 'output' => [ 'shape' => 'ListProfilesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListSecurityPolicies' => [ 'name' => 'ListSecurityPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSecurityPoliciesRequest', ], 'output' => [ 'shape' => 'ListSecurityPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListServers' => [ 'name' => 'ListServers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListServersRequest', ], 'output' => [ 'shape' => 'ListServersResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListUsers' => [ 'name' => 'ListUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUsersRequest', ], 'output' => [ 'shape' => 'ListUsersResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListWorkflows' => [ 'name' => 'ListWorkflows', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWorkflowsRequest', ], 'output' => [ 'shape' => 'ListWorkflowsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'SendWorkflowStepState' => [ 'name' => 'SendWorkflowStepState', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendWorkflowStepStateRequest', ], 'output' => [ 'shape' => 'SendWorkflowStepStateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartDirectoryListing' => [ 'name' => 'StartDirectoryListing', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDirectoryListingRequest', ], 'output' => [ 'shape' => 'StartDirectoryListingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'StartFileTransfer' => [ 'name' => 'StartFileTransfer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartFileTransferRequest', ], 'output' => [ 'shape' => 'StartFileTransferResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'StartServer' => [ 'name' => 'StartServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartServerRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'StopServer' => [ 'name' => 'StopServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopServerRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'TestConnection' => [ 'name' => 'TestConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestConnectionRequest', ], 'output' => [ 'shape' => 'TestConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'TestIdentityProvider' => [ 'name' => 'TestIdentityProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestIdentityProviderRequest', ], 'output' => [ 'shape' => 'TestIdentityProviderResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateAccess' => [ 'name' => 'UpdateAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAccessRequest', ], 'output' => [ 'shape' => 'UpdateAccessResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], ], ], 'UpdateAgreement' => [ 'name' => 'UpdateAgreement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAgreementRequest', ], 'output' => [ 'shape' => 'UpdateAgreementResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], ], ], 'UpdateCertificate' => [ 'name' => 'UpdateCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCertificateRequest', ], 'output' => [ 'shape' => 'UpdateCertificateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateConnector' => [ 'name' => 'UpdateConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateConnectorRequest', ], 'output' => [ 'shape' => 'UpdateConnectorResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], ], ], 'UpdateHostKey' => [ 'name' => 'UpdateHostKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateHostKeyRequest', ], 'output' => [ 'shape' => 'UpdateHostKeyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateProfile' => [ 'name' => 'UpdateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateProfileRequest', ], 'output' => [ 'shape' => 'UpdateProfileResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'UpdateServer' => [ 'name' => 'UpdateServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateServerRequest', ], 'output' => [ 'shape' => 'UpdateServerResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateUser' => [ 'name' => 'UpdateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserRequest', ], 'output' => [ 'shape' => 'UpdateUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ServiceErrorMessage', ], ], 'exception' => true, ], 'AddressAllocationId' => [ 'type' => 'string', ], 'AddressAllocationIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddressAllocationId', ], ], 'AgreementId' => [ 'type' => 'string', 'max' => 19, 'min' => 19, 'pattern' => 'a-([0-9a-f]{17})', ], 'AgreementStatusType' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'Arn' => [ 'type' => 'string', 'max' => 1600, 'min' => 20, 'pattern' => 'arn:\\S+', ], 'As2ConnectorConfig' => [ 'type' => 'structure', 'members' => [ 'LocalProfileId' => [ 'shape' => 'ProfileId', ], 'PartnerProfileId' => [ 'shape' => 'ProfileId', ], 'MessageSubject' => [ 'shape' => 'MessageSubject', ], 'Compression' => [ 'shape' => 'CompressionEnum', ], 'EncryptionAlgorithm' => [ 'shape' => 'EncryptionAlg', ], 'SigningAlgorithm' => [ 'shape' => 'SigningAlg', ], 'MdnSigningAlgorithm' => [ 'shape' => 'MdnSigningAlg', ], 'MdnResponse' => [ 'shape' => 'MdnResponse', ], 'BasicAuthSecretId' => [ 'shape' => 'As2ConnectorSecretId', ], ], ], 'As2ConnectorSecretId' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'As2Id' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{Print}\\s]*', ], 'As2Transport' => [ 'type' => 'string', 'enum' => [ 'HTTP', ], ], 'As2Transports' => [ 'type' => 'list', 'member' => [ 'shape' => 'As2Transport', ], 'max' => 1, 'min' => 1, ], 'CallbackToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '\\w+', ], 'CertDate' => [ 'type' => 'timestamp', ], 'CertSerial' => [ 'type' => 'string', 'max' => 48, 'min' => 0, 'pattern' => '[\\p{XDigit}{2}:?]*', ], 'Certificate' => [ 'type' => 'string', 'max' => 1600, 'min' => 0, ], 'CertificateBodyType' => [ 'type' => 'string', 'max' => 16384, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u00FF]*', 'sensitive' => true, ], 'CertificateChainType' => [ 'type' => 'string', 'max' => 2097152, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u00FF]*', 'sensitive' => true, ], 'CertificateId' => [ 'type' => 'string', 'max' => 22, 'min' => 22, 'pattern' => 'cert-([0-9a-f]{17})', ], 'CertificateIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'CertificateId', ], ], 'CertificateStatusType' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'PENDING_ROTATION', 'INACTIVE', ], ], 'CertificateType' => [ 'type' => 'string', 'enum' => [ 'CERTIFICATE', 'CERTIFICATE_WITH_PRIVATE_KEY', ], ], 'CertificateUsageType' => [ 'type' => 'string', 'enum' => [ 'SIGNING', 'ENCRYPTION', 'TLS', ], ], 'CompressionEnum' => [ 'type' => 'string', 'enum' => [ 'ZLIB', 'DISABLED', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'ConnectorId' => [ 'type' => 'string', 'max' => 19, 'min' => 19, 'pattern' => 'c-([0-9a-f]{17})', ], 'ConnectorSecurityPolicyName' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => 'TransferSFTPConnectorSecurityPolicy-[A-Za-z0-9-]+', ], 'CopyStepDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'WorkflowStepName', ], 'DestinationFileLocation' => [ 'shape' => 'InputFileLocation', ], 'OverwriteExisting' => [ 'shape' => 'OverwriteExisting', ], 'SourceFileLocation' => [ 'shape' => 'SourceFileLocation', ], ], ], 'CreateAccessRequest' => [ 'type' => 'structure', 'required' => [ 'Role', 'ServerId', 'ExternalId', ], 'members' => [ 'HomeDirectory' => [ 'shape' => 'HomeDirectory', ], 'HomeDirectoryType' => [ 'shape' => 'HomeDirectoryType', ], 'HomeDirectoryMappings' => [ 'shape' => 'HomeDirectoryMappings', ], 'Policy' => [ 'shape' => 'Policy', ], 'PosixProfile' => [ 'shape' => 'PosixProfile', ], 'Role' => [ 'shape' => 'Role', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'ExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'CreateAccessResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'ExternalId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'ExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'CreateAgreementRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'LocalProfileId', 'PartnerProfileId', 'BaseDirectory', 'AccessRole', ], 'members' => [ 'Description' => [ 'shape' => 'Description', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'LocalProfileId' => [ 'shape' => 'ProfileId', ], 'PartnerProfileId' => [ 'shape' => 'ProfileId', ], 'BaseDirectory' => [ 'shape' => 'HomeDirectory', ], 'AccessRole' => [ 'shape' => 'Role', ], 'Status' => [ 'shape' => 'AgreementStatusType', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateAgreementResponse' => [ 'type' => 'structure', 'required' => [ 'AgreementId', ], 'members' => [ 'AgreementId' => [ 'shape' => 'AgreementId', ], ], ], 'CreateConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'Url', 'AccessRole', ], 'members' => [ 'Url' => [ 'shape' => 'Url', ], 'As2Config' => [ 'shape' => 'As2ConnectorConfig', ], 'AccessRole' => [ 'shape' => 'Role', ], 'LoggingRole' => [ 'shape' => 'Role', ], 'Tags' => [ 'shape' => 'Tags', ], 'SftpConfig' => [ 'shape' => 'SftpConnectorConfig', ], 'SecurityPolicyName' => [ 'shape' => 'ConnectorSecurityPolicyName', ], ], ], 'CreateConnectorResponse' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'ConnectorId', ], ], ], 'CreateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'As2Id', 'ProfileType', ], 'members' => [ 'As2Id' => [ 'shape' => 'As2Id', ], 'ProfileType' => [ 'shape' => 'ProfileType', ], 'CertificateIds' => [ 'shape' => 'CertificateIds', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateProfileResponse' => [ 'type' => 'structure', 'required' => [ 'ProfileId', ], 'members' => [ 'ProfileId' => [ 'shape' => 'ProfileId', ], ], ], 'CreateServerRequest' => [ 'type' => 'structure', 'members' => [ 'Certificate' => [ 'shape' => 'Certificate', ], 'Domain' => [ 'shape' => 'Domain', ], 'EndpointDetails' => [ 'shape' => 'EndpointDetails', ], 'EndpointType' => [ 'shape' => 'EndpointType', ], 'HostKey' => [ 'shape' => 'HostKey', ], 'IdentityProviderDetails' => [ 'shape' => 'IdentityProviderDetails', ], 'IdentityProviderType' => [ 'shape' => 'IdentityProviderType', ], 'LoggingRole' => [ 'shape' => 'NullableRole', ], 'PostAuthenticationLoginBanner' => [ 'shape' => 'PostAuthenticationLoginBanner', ], 'PreAuthenticationLoginBanner' => [ 'shape' => 'PreAuthenticationLoginBanner', ], 'Protocols' => [ 'shape' => 'Protocols', ], 'ProtocolDetails' => [ 'shape' => 'ProtocolDetails', ], 'SecurityPolicyName' => [ 'shape' => 'SecurityPolicyName', ], 'Tags' => [ 'shape' => 'Tags', ], 'WorkflowDetails' => [ 'shape' => 'WorkflowDetails', ], 'StructuredLogDestinations' => [ 'shape' => 'StructuredLogDestinations', ], 'S3StorageOptions' => [ 'shape' => 'S3StorageOptions', ], ], ], 'CreateServerResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'Role', 'ServerId', 'UserName', ], 'members' => [ 'HomeDirectory' => [ 'shape' => 'HomeDirectory', ], 'HomeDirectoryType' => [ 'shape' => 'HomeDirectoryType', ], 'HomeDirectoryMappings' => [ 'shape' => 'HomeDirectoryMappings', ], 'Policy' => [ 'shape' => 'Policy', ], 'PosixProfile' => [ 'shape' => 'PosixProfile', ], 'Role' => [ 'shape' => 'Role', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'SshPublicKeyBody' => [ 'shape' => 'SshPublicKeyBody', ], 'Tags' => [ 'shape' => 'Tags', ], 'UserName' => [ 'shape' => 'UserName', ], ], ], 'CreateUserResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'UserName', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'UserName' => [ 'shape' => 'UserName', ], ], ], 'CreateWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'Steps', ], 'members' => [ 'Description' => [ 'shape' => 'WorkflowDescription', ], 'Steps' => [ 'shape' => 'WorkflowSteps', ], 'OnExceptionSteps' => [ 'shape' => 'WorkflowSteps', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateWorkflowResponse' => [ 'type' => 'structure', 'required' => [ 'WorkflowId', ], 'members' => [ 'WorkflowId' => [ 'shape' => 'WorkflowId', ], ], ], 'CustomStepDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'WorkflowStepName', ], 'Target' => [ 'shape' => 'CustomStepTarget', ], 'TimeoutSeconds' => [ 'shape' => 'CustomStepTimeoutSeconds', ], 'SourceFileLocation' => [ 'shape' => 'SourceFileLocation', ], ], ], 'CustomStepStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'FAILURE', ], ], 'CustomStepTarget' => [ 'type' => 'string', 'max' => 170, 'min' => 0, 'pattern' => 'arn:[a-z-]+:lambda:.*', ], 'CustomStepTimeoutSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 1800, 'min' => 1, ], 'DateImported' => [ 'type' => 'timestamp', ], 'DecryptStepDetails' => [ 'type' => 'structure', 'required' => [ 'Type', 'DestinationFileLocation', ], 'members' => [ 'Name' => [ 'shape' => 'WorkflowStepName', ], 'Type' => [ 'shape' => 'EncryptionType', ], 'SourceFileLocation' => [ 'shape' => 'SourceFileLocation', ], 'OverwriteExisting' => [ 'shape' => 'OverwriteExisting', ], 'DestinationFileLocation' => [ 'shape' => 'InputFileLocation', ], ], ], 'DeleteAccessRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'ExternalId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'ExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'DeleteAgreementRequest' => [ 'type' => 'structure', 'required' => [ 'AgreementId', 'ServerId', ], 'members' => [ 'AgreementId' => [ 'shape' => 'AgreementId', ], 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'DeleteCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'CertificateId', ], 'members' => [ 'CertificateId' => [ 'shape' => 'CertificateId', ], ], ], 'DeleteConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'ConnectorId', ], ], ], 'DeleteHostKeyRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'HostKeyId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'HostKeyId' => [ 'shape' => 'HostKeyId', ], ], ], 'DeleteProfileRequest' => [ 'type' => 'structure', 'required' => [ 'ProfileId', ], 'members' => [ 'ProfileId' => [ 'shape' => 'ProfileId', ], ], ], 'DeleteServerRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'DeleteSshPublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'SshPublicKeyId', 'UserName', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'SshPublicKeyId' => [ 'shape' => 'SshPublicKeyId', ], 'UserName' => [ 'shape' => 'UserName', ], ], ], 'DeleteStepDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'WorkflowStepName', ], 'SourceFileLocation' => [ 'shape' => 'SourceFileLocation', ], ], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'UserName', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'UserName' => [ 'shape' => 'UserName', ], ], ], 'DeleteWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'WorkflowId', ], 'members' => [ 'WorkflowId' => [ 'shape' => 'WorkflowId', ], ], ], 'DescribeAccessRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'ExternalId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'ExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'DescribeAccessResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'Access', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'Access' => [ 'shape' => 'DescribedAccess', ], ], ], 'DescribeAgreementRequest' => [ 'type' => 'structure', 'required' => [ 'AgreementId', 'ServerId', ], 'members' => [ 'AgreementId' => [ 'shape' => 'AgreementId', ], 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'DescribeAgreementResponse' => [ 'type' => 'structure', 'required' => [ 'Agreement', ], 'members' => [ 'Agreement' => [ 'shape' => 'DescribedAgreement', ], ], ], 'DescribeCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'CertificateId', ], 'members' => [ 'CertificateId' => [ 'shape' => 'CertificateId', ], ], ], 'DescribeCertificateResponse' => [ 'type' => 'structure', 'required' => [ 'Certificate', ], 'members' => [ 'Certificate' => [ 'shape' => 'DescribedCertificate', ], ], ], 'DescribeConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'ConnectorId', ], ], ], 'DescribeConnectorResponse' => [ 'type' => 'structure', 'required' => [ 'Connector', ], 'members' => [ 'Connector' => [ 'shape' => 'DescribedConnector', ], ], ], 'DescribeExecutionRequest' => [ 'type' => 'structure', 'required' => [ 'ExecutionId', 'WorkflowId', ], 'members' => [ 'ExecutionId' => [ 'shape' => 'ExecutionId', ], 'WorkflowId' => [ 'shape' => 'WorkflowId', ], ], ], 'DescribeExecutionResponse' => [ 'type' => 'structure', 'required' => [ 'WorkflowId', 'Execution', ], 'members' => [ 'WorkflowId' => [ 'shape' => 'WorkflowId', ], 'Execution' => [ 'shape' => 'DescribedExecution', ], ], ], 'DescribeHostKeyRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'HostKeyId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'HostKeyId' => [ 'shape' => 'HostKeyId', ], ], ], 'DescribeHostKeyResponse' => [ 'type' => 'structure', 'required' => [ 'HostKey', ], 'members' => [ 'HostKey' => [ 'shape' => 'DescribedHostKey', ], ], ], 'DescribeProfileRequest' => [ 'type' => 'structure', 'required' => [ 'ProfileId', ], 'members' => [ 'ProfileId' => [ 'shape' => 'ProfileId', ], ], ], 'DescribeProfileResponse' => [ 'type' => 'structure', 'required' => [ 'Profile', ], 'members' => [ 'Profile' => [ 'shape' => 'DescribedProfile', ], ], ], 'DescribeSecurityPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'SecurityPolicyName', ], 'members' => [ 'SecurityPolicyName' => [ 'shape' => 'SecurityPolicyName', ], ], ], 'DescribeSecurityPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'SecurityPolicy', ], 'members' => [ 'SecurityPolicy' => [ 'shape' => 'DescribedSecurityPolicy', ], ], ], 'DescribeServerRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'DescribeServerResponse' => [ 'type' => 'structure', 'required' => [ 'Server', ], 'members' => [ 'Server' => [ 'shape' => 'DescribedServer', ], ], ], 'DescribeUserRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'UserName', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'UserName' => [ 'shape' => 'UserName', ], ], ], 'DescribeUserResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'User', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'User' => [ 'shape' => 'DescribedUser', ], ], ], 'DescribeWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'WorkflowId', ], 'members' => [ 'WorkflowId' => [ 'shape' => 'WorkflowId', ], ], ], 'DescribeWorkflowResponse' => [ 'type' => 'structure', 'required' => [ 'Workflow', ], 'members' => [ 'Workflow' => [ 'shape' => 'DescribedWorkflow', ], ], ], 'DescribedAccess' => [ 'type' => 'structure', 'members' => [ 'HomeDirectory' => [ 'shape' => 'HomeDirectory', ], 'HomeDirectoryMappings' => [ 'shape' => 'HomeDirectoryMappings', ], 'HomeDirectoryType' => [ 'shape' => 'HomeDirectoryType', ], 'Policy' => [ 'shape' => 'Policy', ], 'PosixProfile' => [ 'shape' => 'PosixProfile', ], 'Role' => [ 'shape' => 'Role', ], 'ExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'DescribedAgreement' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AgreementId' => [ 'shape' => 'AgreementId', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'AgreementStatusType', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'LocalProfileId' => [ 'shape' => 'ProfileId', ], 'PartnerProfileId' => [ 'shape' => 'ProfileId', ], 'BaseDirectory' => [ 'shape' => 'HomeDirectory', ], 'AccessRole' => [ 'shape' => 'Role', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'DescribedCertificate' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'CertificateId' => [ 'shape' => 'CertificateId', ], 'Usage' => [ 'shape' => 'CertificateUsageType', ], 'Status' => [ 'shape' => 'CertificateStatusType', ], 'Certificate' => [ 'shape' => 'CertificateBodyType', ], 'CertificateChain' => [ 'shape' => 'CertificateChainType', ], 'ActiveDate' => [ 'shape' => 'CertDate', ], 'InactiveDate' => [ 'shape' => 'CertDate', ], 'Serial' => [ 'shape' => 'CertSerial', ], 'NotBeforeDate' => [ 'shape' => 'CertDate', ], 'NotAfterDate' => [ 'shape' => 'CertDate', ], 'Type' => [ 'shape' => 'CertificateType', ], 'Description' => [ 'shape' => 'Description', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'DescribedConnector' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'ConnectorId' => [ 'shape' => 'ConnectorId', ], 'Url' => [ 'shape' => 'Url', ], 'As2Config' => [ 'shape' => 'As2ConnectorConfig', ], 'AccessRole' => [ 'shape' => 'Role', ], 'LoggingRole' => [ 'shape' => 'Role', ], 'Tags' => [ 'shape' => 'Tags', ], 'SftpConfig' => [ 'shape' => 'SftpConnectorConfig', ], 'ServiceManagedEgressIpAddresses' => [ 'shape' => 'ServiceManagedEgressIpAddresses', ], 'SecurityPolicyName' => [ 'shape' => 'ConnectorSecurityPolicyName', ], ], ], 'DescribedExecution' => [ 'type' => 'structure', 'members' => [ 'ExecutionId' => [ 'shape' => 'ExecutionId', ], 'InitialFileLocation' => [ 'shape' => 'FileLocation', ], 'ServiceMetadata' => [ 'shape' => 'ServiceMetadata', ], 'ExecutionRole' => [ 'shape' => 'Role', ], 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], 'PosixProfile' => [ 'shape' => 'PosixProfile', ], 'Status' => [ 'shape' => 'ExecutionStatus', ], 'Results' => [ 'shape' => 'ExecutionResults', ], ], ], 'DescribedHostKey' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'HostKeyId' => [ 'shape' => 'HostKeyId', ], 'HostKeyFingerprint' => [ 'shape' => 'HostKeyFingerprint', ], 'Description' => [ 'shape' => 'HostKeyDescription', ], 'Type' => [ 'shape' => 'HostKeyType', ], 'DateImported' => [ 'shape' => 'DateImported', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'DescribedProfile' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'ProfileId' => [ 'shape' => 'ProfileId', ], 'ProfileType' => [ 'shape' => 'ProfileType', ], 'As2Id' => [ 'shape' => 'As2Id', ], 'CertificateIds' => [ 'shape' => 'CertificateIds', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'DescribedSecurityPolicy' => [ 'type' => 'structure', 'required' => [ 'SecurityPolicyName', ], 'members' => [ 'Fips' => [ 'shape' => 'Fips', ], 'SecurityPolicyName' => [ 'shape' => 'SecurityPolicyName', ], 'SshCiphers' => [ 'shape' => 'SecurityPolicyOptions', ], 'SshKexs' => [ 'shape' => 'SecurityPolicyOptions', ], 'SshMacs' => [ 'shape' => 'SecurityPolicyOptions', ], 'TlsCiphers' => [ 'shape' => 'SecurityPolicyOptions', ], 'SshHostKeyAlgorithms' => [ 'shape' => 'SecurityPolicyOptions', ], 'Type' => [ 'shape' => 'SecurityPolicyResourceType', ], 'Protocols' => [ 'shape' => 'SecurityPolicyProtocols', ], ], ], 'DescribedServer' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Certificate' => [ 'shape' => 'Certificate', ], 'ProtocolDetails' => [ 'shape' => 'ProtocolDetails', ], 'Domain' => [ 'shape' => 'Domain', ], 'EndpointDetails' => [ 'shape' => 'EndpointDetails', ], 'EndpointType' => [ 'shape' => 'EndpointType', ], 'HostKeyFingerprint' => [ 'shape' => 'HostKeyFingerprint', ], 'IdentityProviderDetails' => [ 'shape' => 'IdentityProviderDetails', ], 'IdentityProviderType' => [ 'shape' => 'IdentityProviderType', ], 'LoggingRole' => [ 'shape' => 'NullableRole', ], 'PostAuthenticationLoginBanner' => [ 'shape' => 'PostAuthenticationLoginBanner', ], 'PreAuthenticationLoginBanner' => [ 'shape' => 'PreAuthenticationLoginBanner', ], 'Protocols' => [ 'shape' => 'Protocols', ], 'SecurityPolicyName' => [ 'shape' => 'SecurityPolicyName', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'State' => [ 'shape' => 'State', ], 'Tags' => [ 'shape' => 'Tags', ], 'UserCount' => [ 'shape' => 'UserCount', ], 'WorkflowDetails' => [ 'shape' => 'WorkflowDetails', ], 'StructuredLogDestinations' => [ 'shape' => 'StructuredLogDestinations', ], 'S3StorageOptions' => [ 'shape' => 'S3StorageOptions', ], 'As2ServiceManagedEgressIpAddresses' => [ 'shape' => 'ServiceManagedEgressIpAddresses', ], ], ], 'DescribedUser' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'HomeDirectory' => [ 'shape' => 'HomeDirectory', ], 'HomeDirectoryMappings' => [ 'shape' => 'HomeDirectoryMappings', ], 'HomeDirectoryType' => [ 'shape' => 'HomeDirectoryType', ], 'Policy' => [ 'shape' => 'Policy', ], 'PosixProfile' => [ 'shape' => 'PosixProfile', ], 'Role' => [ 'shape' => 'Role', ], 'SshPublicKeys' => [ 'shape' => 'SshPublicKeys', ], 'Tags' => [ 'shape' => 'Tags', ], 'UserName' => [ 'shape' => 'UserName', ], ], ], 'DescribedWorkflow' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Description' => [ 'shape' => 'WorkflowDescription', ], 'Steps' => [ 'shape' => 'WorkflowSteps', ], 'OnExceptionSteps' => [ 'shape' => 'WorkflowSteps', ], 'WorkflowId' => [ 'shape' => 'WorkflowId', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[\\p{Graph}]+', ], 'DirectoryId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => 'd-[0-9a-f]{10}', ], 'DirectoryListingOptimization' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Domain' => [ 'type' => 'string', 'enum' => [ 'S3', 'EFS', ], ], 'EfsFileLocation' => [ 'type' => 'structure', 'members' => [ 'FileSystemId' => [ 'shape' => 'EfsFileSystemId', ], 'Path' => [ 'shape' => 'EfsPath', ], ], ], 'EfsFileSystemId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '(arn:aws[-a-z]*:elasticfilesystem:[0-9a-z-:]+:(access-point/fsap|file-system/fs)-[0-9a-f]{8,40}|fs(ap)?-[0-9a-f]{8,40})', ], 'EfsPath' => [ 'type' => 'string', 'max' => 65536, 'min' => 1, 'pattern' => '[^\\x00]+', ], 'EncryptionAlg' => [ 'type' => 'string', 'enum' => [ 'AES128_CBC', 'AES192_CBC', 'AES256_CBC', 'DES_EDE3_CBC', 'NONE', ], ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'PGP', ], ], 'EndpointDetails' => [ 'type' => 'structure', 'members' => [ 'AddressAllocationIds' => [ 'shape' => 'AddressAllocationIds', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], ], ], 'EndpointType' => [ 'type' => 'string', 'enum' => [ 'PUBLIC', 'VPC', 'VPC_ENDPOINT', ], ], 'ExecutionError' => [ 'type' => 'structure', 'required' => [ 'Type', 'Message', ], 'members' => [ 'Type' => [ 'shape' => 'ExecutionErrorType', ], 'Message' => [ 'shape' => 'ExecutionErrorMessage', ], ], ], 'ExecutionErrorMessage' => [ 'type' => 'string', ], 'ExecutionErrorType' => [ 'type' => 'string', 'enum' => [ 'PERMISSION_DENIED', 'CUSTOM_STEP_FAILED', 'THROTTLED', 'ALREADY_EXISTS', 'NOT_FOUND', 'BAD_REQUEST', 'TIMEOUT', 'INTERNAL_SERVER_ERROR', ], ], 'ExecutionId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-fA-F]{8}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{4}\\-[0-9a-fA-F]{12}', ], 'ExecutionResults' => [ 'type' => 'structure', 'members' => [ 'Steps' => [ 'shape' => 'ExecutionStepResults', ], 'OnExceptionSteps' => [ 'shape' => 'ExecutionStepResults', ], ], ], 'ExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETED', 'EXCEPTION', 'HANDLING_EXCEPTION', ], ], 'ExecutionStepResult' => [ 'type' => 'structure', 'members' => [ 'StepType' => [ 'shape' => 'WorkflowStepType', ], 'Outputs' => [ 'shape' => 'StepResultOutputsJson', ], 'Error' => [ 'shape' => 'ExecutionError', ], ], ], 'ExecutionStepResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExecutionStepResult', ], 'max' => 50, 'min' => 1, ], 'ExternalId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'S-1-[\\d-]+', ], 'FileLocation' => [ 'type' => 'structure', 'members' => [ 'S3FileLocation' => [ 'shape' => 'S3FileLocation', ], 'EfsFileLocation' => [ 'shape' => 'EfsFileLocation', ], ], ], 'FilePath' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '(.)+', ], 'FilePaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilePath', ], 'max' => 10, 'min' => 1, ], 'Fips' => [ 'type' => 'boolean', 'box' => true, ], 'Function' => [ 'type' => 'string', 'max' => 170, 'min' => 1, 'pattern' => 'arn:[a-z-]+:lambda:.*', ], 'HomeDirectory' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '(|/.*)', ], 'HomeDirectoryMapEntry' => [ 'type' => 'structure', 'required' => [ 'Entry', 'Target', ], 'members' => [ 'Entry' => [ 'shape' => 'MapEntry', ], 'Target' => [ 'shape' => 'MapTarget', ], 'Type' => [ 'shape' => 'MapType', ], ], ], 'HomeDirectoryMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'HomeDirectoryMapEntry', ], 'max' => 50000, 'min' => 1, ], 'HomeDirectoryType' => [ 'type' => 'string', 'enum' => [ 'PATH', 'LOGICAL', ], ], 'HostKey' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'sensitive' => true, ], 'HostKeyDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => '[\\p{Print}]*', ], 'HostKeyFingerprint' => [ 'type' => 'string', ], 'HostKeyId' => [ 'type' => 'string', 'max' => 25, 'min' => 25, 'pattern' => 'hostkey-[0-9a-f]{17}', ], 'HostKeyType' => [ 'type' => 'string', ], 'IdentityProviderDetails' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'Url', ], 'InvocationRole' => [ 'shape' => 'Role', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Function' => [ 'shape' => 'Function', ], 'SftpAuthenticationMethods' => [ 'shape' => 'SftpAuthenticationMethods', ], ], ], 'IdentityProviderType' => [ 'type' => 'string', 'enum' => [ 'SERVICE_MANAGED', 'API_GATEWAY', 'AWS_DIRECTORY_SERVICE', 'AWS_LAMBDA', ], ], 'ImportCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'Usage', 'Certificate', ], 'members' => [ 'Usage' => [ 'shape' => 'CertificateUsageType', ], 'Certificate' => [ 'shape' => 'CertificateBodyType', ], 'CertificateChain' => [ 'shape' => 'CertificateChainType', ], 'PrivateKey' => [ 'shape' => 'PrivateKeyType', ], 'ActiveDate' => [ 'shape' => 'CertDate', ], 'InactiveDate' => [ 'shape' => 'CertDate', ], 'Description' => [ 'shape' => 'Description', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ImportCertificateResponse' => [ 'type' => 'structure', 'required' => [ 'CertificateId', ], 'members' => [ 'CertificateId' => [ 'shape' => 'CertificateId', ], ], ], 'ImportHostKeyRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'HostKeyBody', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'HostKeyBody' => [ 'shape' => 'HostKey', ], 'Description' => [ 'shape' => 'HostKeyDescription', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ImportHostKeyResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'HostKeyId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'HostKeyId' => [ 'shape' => 'HostKeyId', ], ], ], 'ImportSshPublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'SshPublicKeyBody', 'UserName', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'SshPublicKeyBody' => [ 'shape' => 'SshPublicKeyBody', ], 'UserName' => [ 'shape' => 'UserName', ], ], ], 'ImportSshPublicKeyResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'SshPublicKeyId', 'UserName', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'SshPublicKeyId' => [ 'shape' => 'SshPublicKeyId', ], 'UserName' => [ 'shape' => 'UserName', ], ], ], 'InputFileLocation' => [ 'type' => 'structure', 'members' => [ 'S3FileLocation' => [ 'shape' => 'S3InputFileLocation', ], 'EfsFileLocation' => [ 'shape' => 'EfsFileLocation', ], ], ], 'InternalServiceError' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, 'fault' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'ListAccessesRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'ListAccessesResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'Accesses', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'Accesses' => [ 'shape' => 'ListedAccesses', ], ], ], 'ListAgreementsRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'ListAgreementsResponse' => [ 'type' => 'structure', 'required' => [ 'Agreements', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Agreements' => [ 'shape' => 'ListedAgreements', ], ], ], 'ListCertificatesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListCertificatesResponse' => [ 'type' => 'structure', 'required' => [ 'Certificates', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Certificates' => [ 'shape' => 'ListedCertificates', ], ], ], 'ListConnectorsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListConnectorsResponse' => [ 'type' => 'structure', 'required' => [ 'Connectors', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Connectors' => [ 'shape' => 'ListedConnectors', ], ], ], 'ListExecutionsRequest' => [ 'type' => 'structure', 'required' => [ 'WorkflowId', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'WorkflowId' => [ 'shape' => 'WorkflowId', ], ], ], 'ListExecutionsResponse' => [ 'type' => 'structure', 'required' => [ 'WorkflowId', 'Executions', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'WorkflowId' => [ 'shape' => 'WorkflowId', ], 'Executions' => [ 'shape' => 'ListedExecutions', ], ], ], 'ListHostKeysRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'ListHostKeysResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'HostKeys', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'HostKeys' => [ 'shape' => 'ListedHostKeys', ], ], ], 'ListProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ProfileType' => [ 'shape' => 'ProfileType', ], ], ], 'ListProfilesResponse' => [ 'type' => 'structure', 'required' => [ 'Profiles', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Profiles' => [ 'shape' => 'ListedProfiles', ], ], ], 'ListSecurityPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSecurityPoliciesResponse' => [ 'type' => 'structure', 'required' => [ 'SecurityPolicyNames', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'SecurityPolicyNames' => [ 'shape' => 'SecurityPolicyNames', ], ], ], 'ListServersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListServersResponse' => [ 'type' => 'structure', 'required' => [ 'Servers', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Servers' => [ 'shape' => 'ListedServers', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ListUsersRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'ListUsersResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'Users', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'Users' => [ 'shape' => 'ListedUsers', ], ], ], 'ListWorkflowsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListWorkflowsResponse' => [ 'type' => 'structure', 'required' => [ 'Workflows', ], 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Workflows' => [ 'shape' => 'ListedWorkflows', ], ], ], 'ListedAccess' => [ 'type' => 'structure', 'members' => [ 'HomeDirectory' => [ 'shape' => 'HomeDirectory', ], 'HomeDirectoryType' => [ 'shape' => 'HomeDirectoryType', ], 'Role' => [ 'shape' => 'Role', ], 'ExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'ListedAccesses' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedAccess', ], ], 'ListedAgreement' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'AgreementId' => [ 'shape' => 'AgreementId', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'AgreementStatusType', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'LocalProfileId' => [ 'shape' => 'ProfileId', ], 'PartnerProfileId' => [ 'shape' => 'ProfileId', ], ], ], 'ListedAgreements' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedAgreement', ], ], 'ListedCertificate' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'CertificateId' => [ 'shape' => 'CertificateId', ], 'Usage' => [ 'shape' => 'CertificateUsageType', ], 'Status' => [ 'shape' => 'CertificateStatusType', ], 'ActiveDate' => [ 'shape' => 'CertDate', ], 'InactiveDate' => [ 'shape' => 'CertDate', ], 'Type' => [ 'shape' => 'CertificateType', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'ListedCertificates' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedCertificate', ], ], 'ListedConnector' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'ConnectorId' => [ 'shape' => 'ConnectorId', ], 'Url' => [ 'shape' => 'Url', ], ], ], 'ListedConnectors' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedConnector', ], ], 'ListedExecution' => [ 'type' => 'structure', 'members' => [ 'ExecutionId' => [ 'shape' => 'ExecutionId', ], 'InitialFileLocation' => [ 'shape' => 'FileLocation', ], 'ServiceMetadata' => [ 'shape' => 'ServiceMetadata', ], 'Status' => [ 'shape' => 'ExecutionStatus', ], ], ], 'ListedExecutions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedExecution', ], ], 'ListedHostKey' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'HostKeyId' => [ 'shape' => 'HostKeyId', ], 'Fingerprint' => [ 'shape' => 'HostKeyFingerprint', ], 'Description' => [ 'shape' => 'HostKeyDescription', ], 'Type' => [ 'shape' => 'HostKeyType', ], 'DateImported' => [ 'shape' => 'DateImported', ], ], ], 'ListedHostKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedHostKey', ], ], 'ListedProfile' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'ProfileId' => [ 'shape' => 'ProfileId', ], 'As2Id' => [ 'shape' => 'As2Id', ], 'ProfileType' => [ 'shape' => 'ProfileType', ], ], ], 'ListedProfiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedProfile', ], ], 'ListedServer' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Domain' => [ 'shape' => 'Domain', ], 'IdentityProviderType' => [ 'shape' => 'IdentityProviderType', ], 'EndpointType' => [ 'shape' => 'EndpointType', ], 'LoggingRole' => [ 'shape' => 'Role', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'State' => [ 'shape' => 'State', ], 'UserCount' => [ 'shape' => 'UserCount', ], ], ], 'ListedServers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedServer', ], ], 'ListedUser' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'HomeDirectory' => [ 'shape' => 'HomeDirectory', ], 'HomeDirectoryType' => [ 'shape' => 'HomeDirectoryType', ], 'Role' => [ 'shape' => 'Role', ], 'SshPublicKeyCount' => [ 'shape' => 'SshPublicKeyCount', ], 'UserName' => [ 'shape' => 'UserName', ], ], ], 'ListedUsers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedUser', ], ], 'ListedWorkflow' => [ 'type' => 'structure', 'members' => [ 'WorkflowId' => [ 'shape' => 'WorkflowId', ], 'Description' => [ 'shape' => 'WorkflowDescription', ], 'Arn' => [ 'shape' => 'Arn', ], ], ], 'ListedWorkflows' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListedWorkflow', ], ], 'ListingId' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[0-9a-zA-Z./-]+', ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\.\\-_/#A-Za-z0-9]*', ], 'LoggingConfiguration' => [ 'type' => 'structure', 'members' => [ 'LoggingRole' => [ 'shape' => 'Role', ], 'LogGroupName' => [ 'shape' => 'LogGroupName', ], ], ], 'MapEntry' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '/.*', ], 'MapTarget' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '/.*', ], 'MapType' => [ 'type' => 'string', 'enum' => [ 'FILE', 'DIRECTORY', ], ], 'MaxItems' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'MdnResponse' => [ 'type' => 'string', 'enum' => [ 'SYNC', 'NONE', ], ], 'MdnSigningAlg' => [ 'type' => 'string', 'enum' => [ 'SHA256', 'SHA384', 'SHA512', 'SHA1', 'NONE', 'DEFAULT', ], ], 'Message' => [ 'type' => 'string', ], 'MessageSubject' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\p{Print}\\p{Blank}]+', ], 'NextToken' => [ 'type' => 'string', 'max' => 6144, 'min' => 1, ], 'NullableRole' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '(|arn:.*role/\\S+)', ], 'OnPartialUploadWorkflowDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowDetail', ], 'max' => 1, 'min' => 0, ], 'OnUploadWorkflowDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowDetail', ], 'max' => 1, 'min' => 0, ], 'OutputFileName' => [ 'type' => 'string', 'max' => 537, 'min' => 26, 'pattern' => 'c-([0-9a-f]{17})-[0-9a-zA-Z./-]+.json', ], 'OverwriteExisting' => [ 'type' => 'string', 'enum' => [ 'TRUE', 'FALSE', ], ], 'PassiveIp' => [ 'type' => 'string', 'max' => 15, 'min' => 0, ], 'Policy' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'PosixId' => [ 'type' => 'long', 'box' => true, 'max' => 4294967295, 'min' => 0, ], 'PosixProfile' => [ 'type' => 'structure', 'required' => [ 'Uid', 'Gid', ], 'members' => [ 'Uid' => [ 'shape' => 'PosixId', ], 'Gid' => [ 'shape' => 'PosixId', ], 'SecondaryGids' => [ 'shape' => 'SecondaryGids', ], ], ], 'PostAuthenticationLoginBanner' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'pattern' => '[\\x09-\\x0D\\x20-\\x7E]*', ], 'PreAuthenticationLoginBanner' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'pattern' => '[\\x09-\\x0D\\x20-\\x7E]*', ], 'PrivateKeyType' => [ 'type' => 'string', 'max' => 16384, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u00FF]*', 'sensitive' => true, ], 'ProfileId' => [ 'type' => 'string', 'max' => 19, 'min' => 19, 'pattern' => 'p-([0-9a-f]{17})', ], 'ProfileType' => [ 'type' => 'string', 'enum' => [ 'LOCAL', 'PARTNER', ], ], 'Protocol' => [ 'type' => 'string', 'enum' => [ 'SFTP', 'FTP', 'FTPS', 'AS2', ], ], 'ProtocolDetails' => [ 'type' => 'structure', 'members' => [ 'PassiveIp' => [ 'shape' => 'PassiveIp', ], 'TlsSessionResumptionMode' => [ 'shape' => 'TlsSessionResumptionMode', ], 'SetStatOption' => [ 'shape' => 'SetStatOption', ], 'As2Transports' => [ 'shape' => 'As2Transports', ], ], ], 'Protocols' => [ 'type' => 'list', 'member' => [ 'shape' => 'Protocol', ], 'max' => 4, 'min' => 1, ], 'Resource' => [ 'type' => 'string', ], 'ResourceExistsException' => [ 'type' => 'structure', 'required' => [ 'Message', 'Resource', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'Resource' => [ 'shape' => 'Resource', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'Resource', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'Resource' => [ 'shape' => 'Resource', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', ], 'Response' => [ 'type' => 'string', ], 'RetryAfterSeconds' => [ 'type' => 'string', ], 'Role' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:.*role/\\S+', ], 'S3Bucket' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]', ], 'S3Etag' => [ 'type' => 'string', 'max' => 65536, 'min' => 1, 'pattern' => '.+', ], 'S3FileLocation' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'S3Bucket', ], 'Key' => [ 'shape' => 'S3Key', ], 'VersionId' => [ 'shape' => 'S3VersionId', ], 'Etag' => [ 'shape' => 'S3Etag', ], ], ], 'S3InputFileLocation' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'S3Bucket', ], 'Key' => [ 'shape' => 'S3Key', ], ], ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\P{M}\\p{M}]*', ], 'S3StorageOptions' => [ 'type' => 'structure', 'members' => [ 'DirectoryListingOptimization' => [ 'shape' => 'DirectoryListingOptimization', ], ], ], 'S3Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'S3TagKey', ], 'Value' => [ 'shape' => 'S3TagValue', ], ], ], 'S3TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)', ], 'S3TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)', ], 'S3Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Tag', ], 'max' => 10, 'min' => 1, ], 'S3VersionId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.+', ], 'SecondaryGids' => [ 'type' => 'list', 'member' => [ 'shape' => 'PosixId', ], 'max' => 16, 'min' => 0, ], 'SecretId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 20, 'min' => 11, 'pattern' => 'sg-[0-9a-f]{8,17}', ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], ], 'SecurityPolicyName' => [ 'type' => 'string', 'max' => 100, 'min' => 0, 'pattern' => 'Transfer[A-Za-z0-9]*SecurityPolicy-[A-Za-z0-9-]+', ], 'SecurityPolicyNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityPolicyName', ], ], 'SecurityPolicyOption' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'SecurityPolicyOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityPolicyOption', ], ], 'SecurityPolicyProtocol' => [ 'type' => 'string', 'enum' => [ 'SFTP', 'FTPS', ], ], 'SecurityPolicyProtocols' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityPolicyProtocol', ], 'max' => 5, 'min' => 1, ], 'SecurityPolicyResourceType' => [ 'type' => 'string', 'enum' => [ 'SERVER', 'CONNECTOR', ], ], 'SendWorkflowStepStateRequest' => [ 'type' => 'structure', 'required' => [ 'WorkflowId', 'ExecutionId', 'Token', 'Status', ], 'members' => [ 'WorkflowId' => [ 'shape' => 'WorkflowId', ], 'ExecutionId' => [ 'shape' => 'ExecutionId', ], 'Token' => [ 'shape' => 'CallbackToken', ], 'Status' => [ 'shape' => 'CustomStepStatus', ], ], ], 'SendWorkflowStepStateResponse' => [ 'type' => 'structure', 'members' => [], ], 'ServerId' => [ 'type' => 'string', 'max' => 19, 'min' => 19, 'pattern' => 's-([0-9a-f]{17})', ], 'ServiceErrorMessage' => [ 'type' => 'string', ], 'ServiceManagedEgressIpAddress' => [ 'type' => 'string', 'pattern' => '\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}', ], 'ServiceManagedEgressIpAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceManagedEgressIpAddress', ], ], 'ServiceMetadata' => [ 'type' => 'structure', 'required' => [ 'UserDetails', ], 'members' => [ 'UserDetails' => [ 'shape' => 'UserDetails', ], ], ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ServiceErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'SessionId' => [ 'type' => 'string', 'max' => 32, 'min' => 3, 'pattern' => '[\\w-]*', ], 'SetStatOption' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'ENABLE_NO_OP', ], ], 'SftpAuthenticationMethods' => [ 'type' => 'string', 'enum' => [ 'PASSWORD', 'PUBLIC_KEY', 'PUBLIC_KEY_OR_PASSWORD', 'PUBLIC_KEY_AND_PASSWORD', ], ], 'SftpConnectorConfig' => [ 'type' => 'structure', 'members' => [ 'UserSecretId' => [ 'shape' => 'SecretId', ], 'TrustedHostKeys' => [ 'shape' => 'SftpConnectorTrustedHostKeyList', ], ], ], 'SftpConnectorTrustedHostKey' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'SftpConnectorTrustedHostKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SftpConnectorTrustedHostKey', ], 'max' => 10, 'min' => 1, ], 'SigningAlg' => [ 'type' => 'string', 'enum' => [ 'SHA256', 'SHA384', 'SHA512', 'SHA1', 'NONE', ], ], 'SourceFileLocation' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '\\$\\{(\\w+.)+\\w+\\}', ], 'SourceIp' => [ 'type' => 'string', 'max' => 32, 'min' => 0, 'pattern' => '\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}', ], 'SshPublicKey' => [ 'type' => 'structure', 'required' => [ 'DateImported', 'SshPublicKeyBody', 'SshPublicKeyId', ], 'members' => [ 'DateImported' => [ 'shape' => 'DateImported', ], 'SshPublicKeyBody' => [ 'shape' => 'SshPublicKeyBody', ], 'SshPublicKeyId' => [ 'shape' => 'SshPublicKeyId', ], ], ], 'SshPublicKeyBody' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '\\s*(ssh|ecdsa)-[a-z0-9-]+[ \\t]+(([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{1,3})?(={0,3})?)(\\s*|[ \\t]+[\\S \\t]*\\s*)', ], 'SshPublicKeyCount' => [ 'type' => 'integer', 'box' => true, ], 'SshPublicKeyId' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => 'key-[0-9a-f]{17}', ], 'SshPublicKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'SshPublicKey', ], 'max' => 5, 'min' => 0, ], 'StartDirectoryListingRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', 'RemoteDirectoryPath', 'OutputDirectoryPath', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'ConnectorId', ], 'RemoteDirectoryPath' => [ 'shape' => 'FilePath', ], 'MaxItems' => [ 'shape' => 'MaxItems', ], 'OutputDirectoryPath' => [ 'shape' => 'FilePath', ], ], ], 'StartDirectoryListingResponse' => [ 'type' => 'structure', 'required' => [ 'ListingId', 'OutputFileName', ], 'members' => [ 'ListingId' => [ 'shape' => 'ListingId', ], 'OutputFileName' => [ 'shape' => 'OutputFileName', ], ], ], 'StartFileTransferRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'ConnectorId', ], 'SendFilePaths' => [ 'shape' => 'FilePaths', ], 'RetrieveFilePaths' => [ 'shape' => 'FilePaths', ], 'LocalDirectoryPath' => [ 'shape' => 'FilePath', ], 'RemoteDirectoryPath' => [ 'shape' => 'FilePath', ], ], ], 'StartFileTransferResponse' => [ 'type' => 'structure', 'required' => [ 'TransferId', ], 'members' => [ 'TransferId' => [ 'shape' => 'TransferId', ], ], ], 'StartServerRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'State' => [ 'type' => 'string', 'enum' => [ 'OFFLINE', 'ONLINE', 'STARTING', 'STOPPING', 'START_FAILED', 'STOP_FAILED', ], ], 'Status' => [ 'type' => 'string', ], 'StatusCode' => [ 'type' => 'integer', ], 'StepResultOutputsJson' => [ 'type' => 'string', 'max' => 65536, 'min' => 0, ], 'StopServerRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'StructuredLogDestinations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 1, 'min' => 0, ], 'SubnetId' => [ 'type' => 'string', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Tags', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagStepDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'WorkflowStepName', ], 'Tags' => [ 'shape' => 'S3Tags', ], 'SourceFileLocation' => [ 'shape' => 'SourceFileLocation', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 1, ], 'TestConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'ConnectorId', ], ], ], 'TestConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectorId' => [ 'shape' => 'ConnectorId', ], 'Status' => [ 'shape' => 'Status', ], 'StatusMessage' => [ 'shape' => 'Message', ], ], ], 'TestIdentityProviderRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'UserName', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'ServerProtocol' => [ 'shape' => 'Protocol', ], 'SourceIp' => [ 'shape' => 'SourceIp', ], 'UserName' => [ 'shape' => 'UserName', ], 'UserPassword' => [ 'shape' => 'UserPassword', ], ], ], 'TestIdentityProviderResponse' => [ 'type' => 'structure', 'required' => [ 'StatusCode', 'Url', ], 'members' => [ 'Response' => [ 'shape' => 'Response', ], 'StatusCode' => [ 'shape' => 'StatusCode', ], 'Message' => [ 'shape' => 'Message', ], 'Url' => [ 'shape' => 'Url', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'RetryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', ], ], 'exception' => true, ], 'TlsSessionResumptionMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', 'ENFORCED', ], ], 'TransferId' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[0-9a-zA-Z./-]+', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'TagKeys', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'TagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'UpdateAccessRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'ExternalId', ], 'members' => [ 'HomeDirectory' => [ 'shape' => 'HomeDirectory', ], 'HomeDirectoryType' => [ 'shape' => 'HomeDirectoryType', ], 'HomeDirectoryMappings' => [ 'shape' => 'HomeDirectoryMappings', ], 'Policy' => [ 'shape' => 'Policy', ], 'PosixProfile' => [ 'shape' => 'PosixProfile', ], 'Role' => [ 'shape' => 'Role', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'ExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'UpdateAccessResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'ExternalId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'ExternalId' => [ 'shape' => 'ExternalId', ], ], ], 'UpdateAgreementRequest' => [ 'type' => 'structure', 'required' => [ 'AgreementId', 'ServerId', ], 'members' => [ 'AgreementId' => [ 'shape' => 'AgreementId', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'AgreementStatusType', ], 'LocalProfileId' => [ 'shape' => 'ProfileId', ], 'PartnerProfileId' => [ 'shape' => 'ProfileId', ], 'BaseDirectory' => [ 'shape' => 'HomeDirectory', ], 'AccessRole' => [ 'shape' => 'Role', ], ], ], 'UpdateAgreementResponse' => [ 'type' => 'structure', 'required' => [ 'AgreementId', ], 'members' => [ 'AgreementId' => [ 'shape' => 'AgreementId', ], ], ], 'UpdateCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'CertificateId', ], 'members' => [ 'CertificateId' => [ 'shape' => 'CertificateId', ], 'ActiveDate' => [ 'shape' => 'CertDate', ], 'InactiveDate' => [ 'shape' => 'CertDate', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'UpdateCertificateResponse' => [ 'type' => 'structure', 'required' => [ 'CertificateId', ], 'members' => [ 'CertificateId' => [ 'shape' => 'CertificateId', ], ], ], 'UpdateConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'ConnectorId', ], 'Url' => [ 'shape' => 'Url', ], 'As2Config' => [ 'shape' => 'As2ConnectorConfig', ], 'AccessRole' => [ 'shape' => 'Role', ], 'LoggingRole' => [ 'shape' => 'Role', ], 'SftpConfig' => [ 'shape' => 'SftpConnectorConfig', ], 'SecurityPolicyName' => [ 'shape' => 'ConnectorSecurityPolicyName', ], ], ], 'UpdateConnectorResponse' => [ 'type' => 'structure', 'required' => [ 'ConnectorId', ], 'members' => [ 'ConnectorId' => [ 'shape' => 'ConnectorId', ], ], ], 'UpdateHostKeyRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'HostKeyId', 'Description', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'HostKeyId' => [ 'shape' => 'HostKeyId', ], 'Description' => [ 'shape' => 'HostKeyDescription', ], ], ], 'UpdateHostKeyResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'HostKeyId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'HostKeyId' => [ 'shape' => 'HostKeyId', ], ], ], 'UpdateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'ProfileId', ], 'members' => [ 'ProfileId' => [ 'shape' => 'ProfileId', ], 'CertificateIds' => [ 'shape' => 'CertificateIds', ], ], ], 'UpdateProfileResponse' => [ 'type' => 'structure', 'required' => [ 'ProfileId', ], 'members' => [ 'ProfileId' => [ 'shape' => 'ProfileId', ], ], ], 'UpdateServerRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', ], 'members' => [ 'Certificate' => [ 'shape' => 'Certificate', ], 'ProtocolDetails' => [ 'shape' => 'ProtocolDetails', ], 'EndpointDetails' => [ 'shape' => 'EndpointDetails', ], 'EndpointType' => [ 'shape' => 'EndpointType', ], 'HostKey' => [ 'shape' => 'HostKey', ], 'IdentityProviderDetails' => [ 'shape' => 'IdentityProviderDetails', ], 'LoggingRole' => [ 'shape' => 'NullableRole', ], 'PostAuthenticationLoginBanner' => [ 'shape' => 'PostAuthenticationLoginBanner', ], 'PreAuthenticationLoginBanner' => [ 'shape' => 'PreAuthenticationLoginBanner', ], 'Protocols' => [ 'shape' => 'Protocols', ], 'SecurityPolicyName' => [ 'shape' => 'SecurityPolicyName', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'WorkflowDetails' => [ 'shape' => 'WorkflowDetails', ], 'StructuredLogDestinations' => [ 'shape' => 'StructuredLogDestinations', ], 'S3StorageOptions' => [ 'shape' => 'S3StorageOptions', ], ], ], 'UpdateServerResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], ], ], 'UpdateUserRequest' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'UserName', ], 'members' => [ 'HomeDirectory' => [ 'shape' => 'HomeDirectory', ], 'HomeDirectoryType' => [ 'shape' => 'HomeDirectoryType', ], 'HomeDirectoryMappings' => [ 'shape' => 'HomeDirectoryMappings', ], 'Policy' => [ 'shape' => 'Policy', ], 'PosixProfile' => [ 'shape' => 'PosixProfile', ], 'Role' => [ 'shape' => 'Role', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'UserName' => [ 'shape' => 'UserName', ], ], ], 'UpdateUserResponse' => [ 'type' => 'structure', 'required' => [ 'ServerId', 'UserName', ], 'members' => [ 'ServerId' => [ 'shape' => 'ServerId', ], 'UserName' => [ 'shape' => 'UserName', ], ], ], 'Url' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'UserCount' => [ 'type' => 'integer', 'box' => true, ], 'UserDetails' => [ 'type' => 'structure', 'required' => [ 'UserName', 'ServerId', ], 'members' => [ 'UserName' => [ 'shape' => 'UserName', ], 'ServerId' => [ 'shape' => 'ServerId', ], 'SessionId' => [ 'shape' => 'SessionId', ], ], ], 'UserName' => [ 'type' => 'string', 'max' => 100, 'min' => 3, 'pattern' => '[\\w][\\w@.-]{2,99}', ], 'UserPassword' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'VpcEndpointId' => [ 'type' => 'string', 'max' => 22, 'min' => 22, 'pattern' => 'vpce-[0-9a-f]{17}', ], 'VpcId' => [ 'type' => 'string', ], 'WorkflowDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\w- ]*', ], 'WorkflowDetail' => [ 'type' => 'structure', 'required' => [ 'WorkflowId', 'ExecutionRole', ], 'members' => [ 'WorkflowId' => [ 'shape' => 'WorkflowId', ], 'ExecutionRole' => [ 'shape' => 'Role', ], ], ], 'WorkflowDetails' => [ 'type' => 'structure', 'members' => [ 'OnUpload' => [ 'shape' => 'OnUploadWorkflowDetails', ], 'OnPartialUpload' => [ 'shape' => 'OnPartialUploadWorkflowDetails', ], ], ], 'WorkflowId' => [ 'type' => 'string', 'max' => 19, 'min' => 19, 'pattern' => 'w-([a-z0-9]{17})', ], 'WorkflowStep' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'WorkflowStepType', ], 'CopyStepDetails' => [ 'shape' => 'CopyStepDetails', ], 'CustomStepDetails' => [ 'shape' => 'CustomStepDetails', ], 'DeleteStepDetails' => [ 'shape' => 'DeleteStepDetails', ], 'TagStepDetails' => [ 'shape' => 'TagStepDetails', ], 'DecryptStepDetails' => [ 'shape' => 'DecryptStepDetails', ], ], ], 'WorkflowStepName' => [ 'type' => 'string', 'max' => 30, 'min' => 0, 'pattern' => '[\\w-]*', ], 'WorkflowStepType' => [ 'type' => 'string', 'enum' => [ 'COPY', 'CUSTOM', 'TAG', 'DELETE', 'DECRYPT', ], ], 'WorkflowSteps' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowStep', ], 'max' => 8, 'min' => 0, ], ],];
