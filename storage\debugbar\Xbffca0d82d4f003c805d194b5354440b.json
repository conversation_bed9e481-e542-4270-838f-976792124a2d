{"__meta": {"id": "Xbffca0d82d4f003c805d194b5354440b", "datetime": "2025-06-26 23:15:02", "utime": **********.462846, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.000834, "end": **********.462863, "duration": 0.46202898025512695, "duration_str": "462ms", "measures": [{"label": "Booting", "start": **********.000834, "relative_start": 0, "end": **********.380585, "relative_end": **********.380585, "duration": 0.37975096702575684, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.380596, "relative_start": 0.3797619342803955, "end": **********.462865, "relative_end": 2.1457672119140625e-06, "duration": 0.08226919174194336, "duration_str": "82.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042280, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023629999999999998, "accumulated_duration_str": "23.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4104269, "duration": 0.022539999999999998, "duration_str": "22.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.387}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.442756, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.387, "width_percent": 2.412}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.44911, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.799, "width_percent": 2.201}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1745152093 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1745152093\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1756857216 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1756857216\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-311538927 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311538927\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-987451415 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979694284%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikh5WVNxWW5FN3lWNEpyUzNEQ2FnaEE9PSIsInZhbHVlIjoiVjQxTGVHWjdiTHVtUXZINnhUS1JOeGUrR0ZQWlRSYTZ1Y1RoemRUYnoyVDk1TU1TQ2s1TVBCajA4bjZkYTFUZW1lUHZ0aDk4dTUveHF3cE5VRHJCS0xRaDBkbkZaVllEcC9xK1pZNXd2NFd2RjVubDQ0UXJLYmdWRHEyVVdqRWM3V1dtNjlKM2x0L3pGdTRKTUQxc2ZwdFo5ZDVWNW5KTkt2eEpkK3Z2ckdORXRUSEd0c1pyZUZTUXJRVHhXVGpwVzBMb2FPRDVxNG9YVXYraFlYcXVadEl0VHpqckhrTDRiY1lpOUQxTkFrWTFlNENuckdnSDhzRnhMN3lreGNKZitYYTZyVTdaeHpNdXJmTkJTL0lMMW5TdlpyajQ2M2ZaMUF4T3JnN2FNVGJ6b0VGeGV2Qy9yZ3N5SEZTMC9tcnVCb0ppcmIzc01aWFQ1bVFoZ2t6YndCL2dnakVYelh6cEx5UHFNUE9tZm5jakZYcnRZcnZSOFJOTVh6ZUd4VktBQ3htVGpDcGU3blIrOEJrWGtIbUNtSmNtWjdnK3ZMQ28xWG1PWkRBVVFibVR1NXlkQ3BleHI1NHJIcVlrZnVVT3U1dFFUUWpsTjN1UUM4ZThaNjR1K2kyUzVMYWNVeGtCOW11dTZ6U0syMjJkemRrUndnTlQyMWUvZS9BOTVuYnYiLCJtYWMiOiIxN2M2NzEwNzNkYzQ0ZDVlMWRkMGE4ZWUyOTM5NmZhNjgyMDBhYWI3ODE1NWVhZmFkMDk2ZWQ3N2FlOTNkMTk1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImRMVURNUE1PUjc0cVF5Ym5lWFV3WUE9PSIsInZhbHVlIjoiZEhEblRXNFFIYnhESGE2bWZaNUUwZG93K0MxZENxZW52d2pOWWVzRVVkazF4aVRQK25JUWNlcGNzN1FPUHZwc2x2RGFhUFVjODJUa3lhT2J2RHFDakI0cjZlc1ZGa3p3d01hWFhnckxQWnllb2crQndvTTFIZlc3M3JrL0xOOEVRTG5PRkxLRzBVc3lGK2NocURpWnM0Q3pzbVU2VTllN2tmTHp3R1VmNGY2QldNeTJlUnBINnkzTndVVHFzTTNQdS8vMEVlMDZGWFhjYnVZdW9DdjFKTG9LYVRac2VTUkp0eGx1U0hzbU1MSmhBQWwzSXRKMXBodkJJVmRQdWRFNXlKN2kxekQvZWN3OHp2WTgzLzlYcFlWTTdLdGI1NU1JL2lpemgvWEZibEJyWDUwcDRlU2N5eE9RK0RSUHNhU0owTmozNEpWVmxUQWcvQUV0d1NnVnZXWCtVQkdNRVVjVFhSOXFxY1dRc0s1Y1RFRThGRktaakVSamVZMHZsVk4zQ2dUU2VsRTVwUXI5eXpqaTdlOGF6ZHNwQVRDVVgyK0ZSUjNOK2lQcUVGeW1UeW92N1hIY3BEb255TGZyWHVXSktNb3JUTGJGS3F6dDN2MkxoaFQ5VGN5NCtDOVVWQkhHMEFQdWlDRE53UEw1d3pNQUZNWUw5NTFrVlBxWGpSUlMiLCJtYWMiOiI5YjVhZTk0ODNlMGVhZDJkZjRhMzMzYjUxM2NmNGU5ZTViOTcxOTcwYzI3MDFlNTFjNmRkZjUwMDY3ZjIxNTdjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987451415\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1706815368 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706815368\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1094358863 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:15:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRUQ09pRnlEOVlBWnVyb2hJQVJlVmc9PSIsInZhbHVlIjoib2x1emcyNE1HQlMwa3VSaGVvb3Jsc0UwK3phRzlHWHJEOFRKeFMxckdBZW14cVduUkZWOUlLMjBMWTZpUG4xVWlmMWRhK09ZVmgzbUFNWitsV2ZUN252V1cxZEZTQzA2RWNHbnhDN2VNcEhoL1YwV0lkUEdIa3ZTNjU4bGJhcEttL2tBY2doVlVhKzZDUkFoRzVnTmhBaTZ6azFubjFyTzdTMEh4SS9rdHBqSFpnMnEvRzEvVCtvL25YKytSeWhma0JCOXJpa1lnYm1UbVk4SGpmMnBvbFBLdGpEdjIrL1VVZVVMdFZIdzZmWHBaOVo0NE12dk5heU9sbmRZcFhGNkJYOEtlT2xBTThqd2JLbkgvekNQNkFFMkVqWEJ5TFV1elpVb1E5MXgreDB0WFFFdnZ3OFJRQlNvQXpybkpXMnpaQXc5R1V4OFZ6UENhQW95SUpDS0gramFJZzV2enhrSU4rOE9DNlEreERkazBBOTZsa1RQeVpmbmxlRW5QOWJyaHQ0U2RDV3U1RGUvbkZ4dlBoWGVZRlJlRDBiV1lXaGFnN3VaQUpnT2tiMTh0aS9FZDhWdEFwN2tZMUxiZnY2RzZiS0MrV2kzUlZudjRyUFhqallGY01UN0ROd3JKQlROaDZyQjVpK21oc3cveTlLSDYybXQ1U2Q4L01XOENPc3UiLCJtYWMiOiI5OGYwYTQ4MjNmNDExMTc3OWM3NTMyN2JiYWEwYjZlZGY3YTI1NzE2M2UxNzZkNjIzYzFlNGEwNWUyMGJhYTYxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:15:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjdOOTdsRlB2d2pQNG9BV3dQdXU4TGc9PSIsInZhbHVlIjoieTJWK25YbUZkNFBXVEduc3FPMy9DTE9lL0h0VDVJekg2QXNxM2tLSy9zN1pNWHZMYVNVUmIrNVgvQU5JYzBIQkNOOTNnMWtKUWNQVmFxajVOR0FpcUg4V01jZHhVeGp2STNoSnUyUUwzUTRJRldJcXNJcG5sbXd4V1JteklxSDU1L1lQb3kwb0w3aVVOR2RHWXZIOXgyKzhkY0s4ZzA3a0FuTnlqQ3ZMVG90UEdwSTVpMUpMK2hWQ3Z2R0tYbm1HR3p1V0t1ZW1HRm9xVFUxRTZrSEdhNTNiczJIaVRBb3B6N0lmVnJMZENreXUvZjBrNHNyTTduNEpZVTV5UlJmM1MwKzZWdWs4ZWJaNG1sM3k0T2hNb05nR09uczlZbGVia25Ib2tlREJnMGVEWFpPQ2hvVnR6NExoNDRnNlE1K1hPWHVRaTRsQnBsdUxQVXRyV3hDM3FRSEhJbnNCZXZQZFkrTE01UTFlanExdDYxd1pPSzRnUzFUR2huTmo4SzBTd3VGQTZZaXRUVW5uZXRjWGM2Z053TjlMeVViNVRTSG5ydWI0RjRaYmF1dnFyRk1kY3c0S0JvS21HaUFOVFZMdTF5dG5PUG1XUGVIcURxUWJMWDlQNCtQRXMxT1hRYjZPa3ZHQ1NHYVZ3bVZ1VjdBZVRQSUpBUGhnS01MWHpuN2QiLCJtYWMiOiIzNjViYWZmMTI2ODY1NWU2NDU0ODM2OGQ3NTYxNjhhMDMyNWJlOWRmZjNmNDEzNGVhMTJhMzRkYTIwYzJiMWEyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:15:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRUQ09pRnlEOVlBWnVyb2hJQVJlVmc9PSIsInZhbHVlIjoib2x1emcyNE1HQlMwa3VSaGVvb3Jsc0UwK3phRzlHWHJEOFRKeFMxckdBZW14cVduUkZWOUlLMjBMWTZpUG4xVWlmMWRhK09ZVmgzbUFNWitsV2ZUN252V1cxZEZTQzA2RWNHbnhDN2VNcEhoL1YwV0lkUEdIa3ZTNjU4bGJhcEttL2tBY2doVlVhKzZDUkFoRzVnTmhBaTZ6azFubjFyTzdTMEh4SS9rdHBqSFpnMnEvRzEvVCtvL25YKytSeWhma0JCOXJpa1lnYm1UbVk4SGpmMnBvbFBLdGpEdjIrL1VVZVVMdFZIdzZmWHBaOVo0NE12dk5heU9sbmRZcFhGNkJYOEtlT2xBTThqd2JLbkgvekNQNkFFMkVqWEJ5TFV1elpVb1E5MXgreDB0WFFFdnZ3OFJRQlNvQXpybkpXMnpaQXc5R1V4OFZ6UENhQW95SUpDS0gramFJZzV2enhrSU4rOE9DNlEreERkazBBOTZsa1RQeVpmbmxlRW5QOWJyaHQ0U2RDV3U1RGUvbkZ4dlBoWGVZRlJlRDBiV1lXaGFnN3VaQUpnT2tiMTh0aS9FZDhWdEFwN2tZMUxiZnY2RzZiS0MrV2kzUlZudjRyUFhqallGY01UN0ROd3JKQlROaDZyQjVpK21oc3cveTlLSDYybXQ1U2Q4L01XOENPc3UiLCJtYWMiOiI5OGYwYTQ4MjNmNDExMTc3OWM3NTMyN2JiYWEwYjZlZGY3YTI1NzE2M2UxNzZkNjIzYzFlNGEwNWUyMGJhYTYxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:15:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjdOOTdsRlB2d2pQNG9BV3dQdXU4TGc9PSIsInZhbHVlIjoieTJWK25YbUZkNFBXVEduc3FPMy9DTE9lL0h0VDVJekg2QXNxM2tLSy9zN1pNWHZMYVNVUmIrNVgvQU5JYzBIQkNOOTNnMWtKUWNQVmFxajVOR0FpcUg4V01jZHhVeGp2STNoSnUyUUwzUTRJRldJcXNJcG5sbXd4V1JteklxSDU1L1lQb3kwb0w3aVVOR2RHWXZIOXgyKzhkY0s4ZzA3a0FuTnlqQ3ZMVG90UEdwSTVpMUpMK2hWQ3Z2R0tYbm1HR3p1V0t1ZW1HRm9xVFUxRTZrSEdhNTNiczJIaVRBb3B6N0lmVnJMZENreXUvZjBrNHNyTTduNEpZVTV5UlJmM1MwKzZWdWs4ZWJaNG1sM3k0T2hNb05nR09uczlZbGVia25Ib2tlREJnMGVEWFpPQ2hvVnR6NExoNDRnNlE1K1hPWHVRaTRsQnBsdUxQVXRyV3hDM3FRSEhJbnNCZXZQZFkrTE01UTFlanExdDYxd1pPSzRnUzFUR2huTmo4SzBTd3VGQTZZaXRUVW5uZXRjWGM2Z053TjlMeVViNVRTSG5ydWI0RjRaYmF1dnFyRk1kY3c0S0JvS21HaUFOVFZMdTF5dG5PUG1XUGVIcURxUWJMWDlQNCtQRXMxT1hRYjZPa3ZHQ1NHYVZ3bVZ1VjdBZVRQSUpBUGhnS01MWHpuN2QiLCJtYWMiOiIzNjViYWZmMTI2ODY1NWU2NDU0ODM2OGQ3NTYxNjhhMDMyNWJlOWRmZjNmNDEzNGVhMTJhMzRkYTIwYzJiMWEyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:15:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094358863\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-113279882 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-113279882\", {\"maxDepth\":0})</script>\n"}}