
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Edit Budget Planner')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('budget.index')); ?>"><?php echo e(__('Budget Planner')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Budget Edit')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('script-page'); ?>
    <script src="<?php echo e(asset('js/jquery-ui.min.js')); ?>"></script>
    <script>

        $(document).ready(function() {

            $( ".income_data" ).each(function( index ) {
                var el = $(this).parent().parent();

                var inputs = $(el.find('.income_data'));

                var totalincome = 0;
                for (var i = 0; i < inputs.length; i++) {
                    var price = $(inputs[i]).val();
                    totalincome = parseFloat(totalincome) + parseFloat(price);
                }
                el.find('.totalIncome').html(totalincome);

                // month wise total //
                var month_income = $(this).data('month');
                var month_inputs = $(el.parent().find('.' + month_income + '_income'));
                var month_totalincome = 0;
                for (var i = 0; i < month_inputs.length; i++) {
                    var month_price = $(month_inputs[i]).val();
                    month_totalincome = parseFloat(month_totalincome) + parseFloat(month_price);
                }
                var month_total_income = month_income + '_total_income';
                el.parent().find('.' + month_total_income).html(month_totalincome);

                //all total //
                var total_inputs = $(el.parent().find('.totalIncome'));

                var income = 0;
                for (var i = 0; i < total_inputs.length; i++) {
                    var price = $(total_inputs[i]).html();
                    income = parseFloat(income) + parseFloat(price);
                }
                el.parent().find('.income').html(income);

            });


            $( ".expense_data" ).each(function( index ) {
                var el = $(this).parent().parent();
                var inputs = $(el.find('.expense_data'));

                var totalexpense = 0;
                for (var i = 0; i < inputs.length; i++) {
                    var price = $(inputs[i]).val();
                    totalexpense = parseFloat(totalexpense) + parseFloat(price);
                }
                el.find('.totalExpense').html(totalexpense);

                // month wise total //
                var month_expense = $(this).data('month');
                var month_inputs = $(el.parent().find('.' + month_expense + '_expense'));
                var month_totalexpense = 0;
                for (var i = 0; i < month_inputs.length; i++) {
                    var month_price = $(month_inputs[i]).val();
                    month_totalexpense = parseFloat(month_totalexpense) + parseFloat(month_price);
                }
                var month_total_expense = month_expense + '_total_expense';
                el.parent().find('.' + month_total_expense).html(month_totalexpense);

                //all total //
                var total_inputs = $(el.parent().find('.totalExpense'));
                var expense = 0;
                for (var i = 0; i < total_inputs.length; i++) {
                    var price = $(total_inputs[i]).html();
                    expense = parseFloat(expense) + parseFloat(price);
                }
                el.parent().find('.expense').html(expense);
            })


        })


        //Income Total
        $(document).on('keyup', '.income_data', function () {
            //category wise total
            var el = $(this).parent().parent();
            var inputs = $(el.find('.income_data'));

            var totalincome = 0;
            for (var i = 0; i < inputs.length; i++) {
                var price = $(inputs[i]).val();
                totalincome = parseFloat(totalincome) + parseFloat(price);
            }
            el.find('.totalIncome').html(totalincome);

            // month wise total //
            var month_income = $(this).data('month');
            var month_inputs = $(el.parent().find('.' + month_income+'_income'));
            var month_totalincome = 0;
            for (var i = 0; i < month_inputs.length; i++) {
                var month_price = $(month_inputs[i]).val();
                month_totalincome = parseFloat(month_totalincome) + parseFloat(month_price);
            }
            var month_total_income = month_income + '_total_income';
            el.parent().find('.' + month_total_income).html(month_totalincome);

            //all total //
            var total_inputs = $(el.parent().find('.totalIncome'));
            console.log(total_inputs)
            var income = 0;
            for (var i = 0; i < total_inputs.length; i++) {
                var price = $(total_inputs[i]).html();
                income = parseFloat(income) + parseFloat(price);
            }
            el.parent().find('.income').html(income);

        })


        //Expense Total
        $(document).on('keyup', '.expense_data', function () {
            //category wise total
            var el = $(this).parent().parent();
            var inputs = $(el.find('.expense_data'));

            var totalexpense = 0;
            for (var i = 0; i < inputs.length; i++) {
                var price = $(inputs[i]).val();
                totalexpense = parseFloat(totalexpense) + parseFloat(price);
            }
            el.find('.totalExpense').html(totalexpense);

            // month wise total //
            var month_expense = $(this).data('month');
            var month_inputs = $(el.parent().find('.' + month_expense+'_expense'));
            var month_totalexpense = 0;
            for (var i = 0; i < month_inputs.length; i++) {
                var month_price = $(month_inputs[i]).val();
                month_totalexpense = parseFloat(month_totalexpense) + parseFloat(month_price);
            }
            var month_total_expense = month_expense + '_total_expense';
            el.parent().find('.' + month_total_expense).html(month_totalexpense);

            //all total //
            var total_inputs = $(el.parent().find('.totalExpense'));
            console.log(total_inputs)
            var expense = 0;
            for (var i = 0; i < total_inputs.length; i++) {
                var price = $(total_inputs[i]).html();
                expense = parseFloat(expense) + parseFloat(price);
            }
            el.parent().find('.expense').html(expense);

        })

        //Hide & Show
        $(document).on('change', '.period', function() {
            var period = $(this).val();

            $('.budget_plan').removeClass('d-block');
            $('.budget_plan').addClass('d-none');
            $('#' + period).removeClass('d-none');
            $('#' + period).addClass('d-block');
        });

        // Budget Type Selector (Income/Expense/Both)
        $(document).on('change', '.budget-type-selector', function() {
            var budgetType = $(this).val();

            if (budgetType === 'both') {
                $('.income-section, .income-row').css('display', '');
                $('.expense-section, .expense-row').css('display', '');
            } else if (budgetType === 'income') {
                $('.income-section, .income-row').css('display', '');
                $('.expense-section, .expense-row').css('display', 'none');
            } else if (budgetType === 'expense') {
                $('.income-section, .income-row').css('display', 'none');
                $('.expense-section, .expense-row').css('display', '');
            }

            // Apply category filter after budget type change
            $('.category-filter').trigger('change');
        });

        // Category Filter
        $(document).on('change', '.category-filter', function() {
            var selectedCategories = $(this).val();
            console.log('Edit page - Selected categories:', selectedCategories);
            console.log('Edit page - Selected categories type:', typeof selectedCategories);
            console.log('Edit page - Selected categories length:', selectedCategories ? selectedCategories.length : 0);

            // Update debug info in real-time
            if ($('#debug-selected-categories').length > 0) {
                var debugText = selectedCategories && selectedCategories.length > 0 ?
                    selectedCategories.join(', ') + ' (Count: ' + selectedCategories.length + ')' :
                    'None selected';
                $('#debug-selected-categories').text(debugText);
            }

            // If "All Categories" is selected, show all categories
            if (selectedCategories && selectedCategories.includes('all')) {
                $('.income-row, .expense-row').css('display', '');
                $('.income-section, .expense-section').css('display', '');
                console.log('Showing all categories');
                return;
            }

            // Hide all category rows first (using CSS visibility instead of hide())
            $('.income-row, .expense-row').css('display', 'none');
            $('.income-section, .expense-section').css('display', 'none');

            var hasIncomeCategories = false;
            var hasExpenseCategories = false;

            // Show only selected categories
            if (selectedCategories && selectedCategories.length > 0) {
                selectedCategories.forEach(function(category) {
                    if (category.startsWith('income-')) {
                        var categoryId = category.replace('income-', '');
                        $('.income-category-' + categoryId).css('display', '');
                        hasIncomeCategories = true;
                        console.log('Showing income category:', categoryId);
                    } else if (category.startsWith('expense-')) {
                        var categoryId = category.replace('expense-', '');
                        $('.expense-category-' + categoryId).css('display', '');
                        hasExpenseCategories = true;
                        console.log('Showing expense category:', categoryId);
                    }
                });
            } else {
                // إذا لم يتم اختيار أي فئة، أظهر جميع الفئات
                $('.income-row, .expense-row').css('display', '');
                $('.income-section, .expense-section').css('display', '');
                console.log('No categories selected, showing all');
            }

            // Show section headers if there are visible categories
            if (hasIncomeCategories) {
                $('.income-section').css('display', '');
                console.log('Showing income section');
            }
            if (hasExpenseCategories) {
                $('.expense-section').css('display', '');
                console.log('Showing expense section');
            }
        });

        // دالة لتطبيق الفلاتر المحفوظة
        function applyFilters() {
            console.log('Applying filters...');

            // طباعة القيم المختارة للتشخيص
            var budgetType = $('.budget-type-selector').val();
            var categoryFilter = $('.category-filter').val();
            console.log('Budget type:', budgetType);
            console.log('Category filter:', categoryFilter);

            // تطبيق فلتر نوع الميزانية
            $('.budget-type-selector').trigger('change');

            // تطبيق فلتر الفئات
            $('.category-filter').trigger('change');

            // تطبيق فلتر الفترة
            $('.period').trigger('change');
        }

        // دالة للتأكد من إرسال جميع الحقول
        function ensureAllFieldsSubmitted() {
            // إظهار جميع الحقول مؤقتاً قبل الإرسال
            $('.income-row, .expense-row').css('display', '');
            $('.income-section, .expense-section').css('display', '');

            // طباعة عدد حقول الدخل والمصروفات للتشخيص
            var incomeFields = $('input[name^="income["]').length;
            var expenseFields = $('input[name^="expense["]').length;
            console.log('Income fields count:', incomeFields);
            console.log('Expense fields count:', expenseFields);

            // طباعة بعض القيم للتشخيص
            $('input[name^="income["]').each(function(index) {
                if ($(this).val() && $(this).val() != '0') {
                    console.log('Income field:', $(this).attr('name'), '=', $(this).val());
                }
            });

            console.log('All fields made visible for form submission');
        }

        // Initialize the view
        $(document).ready(function() {
            // Initialize select2 for better multi-select experience
            if (typeof $.fn.select2 !== 'undefined') {
                $('.category-filter').select2({
                    placeholder: "<?php echo e(__('Select Categories')); ?>",
                    allowClear: true,
                    closeOnSelect: false, // Keep dropdown open for multiple selections
                    width: '100%'
                });

                // Log initial selected values
                var initialSelected = $('.category-filter').val();
                console.log('Edit page - Initial selected categories:', initialSelected);

                // Update debug info on page load
                if ($('#debug-selected-categories').length > 0) {
                    var debugText = initialSelected && initialSelected.length > 0 ?
                        initialSelected.join(', ') + ' (Count: ' + initialSelected.length + ')' :
                        'None selected';
                    $('#debug-selected-categories').text(debugText);
                }
            }

            // تطبيق الفلاتر بعد تحميل الصفحة
            setTimeout(function() {
                applyFilters();
            }, 100);

            // التأكد من إرسال جميع الحقول عند إرسال النموذج
            $('form').on('submit', function(e) {
                ensureAllFieldsSubmitted();

                // طباعة فلتر الفئات المختارة للتشخيص
                var selectedCategories = $('.category-filter').val();
                console.log('Selected categories before submit:', selectedCategories);
                console.log('Selected categories type:', typeof selectedCategories);
                console.log('Selected categories length:', selectedCategories ? selectedCategories.length : 0);

                // إضافة حقول مخفية لضمان إرسال جميع البيانات
                var form = $(this);

                // جمع جميع بيانات الدخل
                $('input[name^="income["]').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val() || '0';

                    // إضافة حقل مخفي إذا لم يكن موجوداً
                    if (form.find('input[type="hidden"][name="' + name + '"]').length === 0) {
                        form.append('<input type="hidden" name="' + name + '" value="' + value + '">');
                    }
                });

                // جمع جميع بيانات المصروفات
                $('input[name^="expense["]').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val() || '0';

                    // إضافة حقل مخفي إذا لم يكن موجوداً
                    if (form.find('input[type="hidden"][name="' + name + '"]').length === 0) {
                        form.append('<input type="hidden" name="' + name + '" value="' + value + '">');
                    }
                });

                console.log('Form submitted with all fields visible and hidden backups added');
            });
        });

    </script>
<?php $__env->stopPush(); ?>



<?php $__env->startSection('content'); ?>

    <div class="card bg-none card-box mt-3">
        <div class="card-body">
            <?php echo e(Form::model($budget, array('route' => array('budget.update', $budget->id), 'method' => 'PUT', 'class'=>'needs-validation', 'novalidate'))); ?>

            <div class="row">
                <input type="hidden" name="type" id="type" value="<?php echo e(csrf_token()); ?>">

                <div class="form-group col-md-4">
                    <?php echo e(Form::label('name', __('Name'),['class'=>'form-label'])); ?><?php if (isset($component)) { $__componentOriginalbba606fec37ea04333bc269e3e165587 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbba606fec37ea04333bc269e3e165587 = $attributes; } ?>
<?php $component = App\View\Components\Required::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('required'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Required::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $attributes = $__attributesOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__attributesOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $component = $__componentOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__componentOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
                    <?php echo e(Form::text('name', null, array('class' => 'form-control','required'=>'required', 'placeholder' => __('Enter Name')))); ?>

                </div>


                <div class="form-group col-md-4">
                    <?php echo e(Form::label('period', __('Budget Period'),['class'=>'form-label'])); ?><?php if (isset($component)) { $__componentOriginalbba606fec37ea04333bc269e3e165587 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbba606fec37ea04333bc269e3e165587 = $attributes; } ?>
<?php $component = App\View\Components\Required::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('required'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Required::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $attributes = $__attributesOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__attributesOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $component = $__componentOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__componentOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
                    <?php echo e(Form::select('period', $periods,null, array('class' => 'form-control select period','required'=>'required'))); ?>


                </div>

                <div class="form-group  col-md-4">
                    <div class="btn-box">
                        <?php echo e(Form::label('year', __('Year'),['class'=>'form-label'])); ?>

                        <?php echo e(Form::select('year',$yearList,isset($_GET['year'])?$_GET['year']:'', array('class' => 'form-control select'))); ?>

                    </div>
                </div>

            </div>

            <!-- Budget Type and Category Filters -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="budget_type" class="form-label"><?php echo e(__('Budget Type')); ?></label>
                        <select id="budget_type" class="form-control select budget-type-selector" name="display_settings[budget_type]">
                            <option value="both" <?php echo e(isset($budget->display_settings['budget_type']) && $budget->display_settings['budget_type'] == 'both' ? 'selected' : ''); ?>><?php echo e(__('Both Income & Expense')); ?></option>
                            <option value="income" <?php echo e(isset($budget->display_settings['budget_type']) && $budget->display_settings['budget_type'] == 'income' ? 'selected' : ''); ?>><?php echo e(__('Income Only')); ?></option>
                            <option value="expense" <?php echo e(isset($budget->display_settings['budget_type']) && $budget->display_settings['budget_type'] == 'expense' ? 'selected' : ''); ?>><?php echo e(__('Expense Only')); ?></option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="category_filter" class="form-label"><?php echo e(__('Filter Categories')); ?></label>
                        <select id="category_filter" class="form-control select category-filter" multiple name="display_settings[category_filter][]">
                            <?php
                                $allSelected = true;
                                if (isset($budget->display_settings['category_filter'])) {
                                    $categoryFilter = $budget->display_settings['category_filter'];

                                    // إذا كان string، فك التشفير
                                    if (is_string($categoryFilter)) {
                                        $categoryFilter = json_decode($categoryFilter, true);
                                    }

                                    // تحقق من أن النتيجة مصفوفة وليست تحتوي على 'all' فقط
                                    if (is_array($categoryFilter) && !in_array('all', $categoryFilter) && count($categoryFilter) > 0) {
                                        $allSelected = false;
                                    }
                                }
                            ?>
                            <option value="all" <?php echo e($allSelected ? 'selected' : ''); ?>><?php echo e(__('All Categories')); ?></option>
                            <optgroup label="<?php echo e(__('Income Categories')); ?>">
                                <?php $__currentLoopData = $incomeproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $isSelected = false;
                                        if (isset($budget->display_settings['category_filter'])) {
                                            $categoryFilter = $budget->display_settings['category_filter'];

                                            // إذا كان string، فك التشفير
                                            if (is_string($categoryFilter)) {
                                                $categoryFilter = json_decode($categoryFilter, true);
                                            }

                                            // تحقق من أن النتيجة مصفوفة
                                            if (is_array($categoryFilter)) {
                                                $isSelected = in_array('income-'.$productService->id, $categoryFilter);
                                            }
                                        }
                                    ?>
                                    <option value="income-<?php echo e($productService->id); ?>" <?php echo e($isSelected ? 'selected' : ''); ?>><?php echo e($productService->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </optgroup>
                            <optgroup label="<?php echo e(__('Expense Categories')); ?>">
                                <?php $__currentLoopData = $expenseproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $isSelected = false;
                                        if (isset($budget->display_settings['category_filter'])) {
                                            $categoryFilter = $budget->display_settings['category_filter'];

                                            // إذا كان string، فك التشفير
                                            if (is_string($categoryFilter)) {
                                                $categoryFilter = json_decode($categoryFilter, true);
                                            }

                                            // تحقق من أن النتيجة مصفوفة
                                            if (is_array($categoryFilter)) {
                                                $isSelected = in_array('expense-'.$productService->id, $categoryFilter);
                                            }
                                        }
                                    ?>
                                    <option value="expense-<?php echo e($productService->id); ?>" <?php echo e($isSelected ? 'selected' : ''); ?>><?php echo e($productService->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </optgroup>
                        </select>
                        <!-- Debug info -->
                        <div class="mt-2 small text-muted">
                            <p><?php echo e(__('Selected categories will be displayed in the budget view.')); ?></p>
                            <p><?php echo e(__('If you select specific categories, only those will be shown and included in the net profit calculation.')); ?></p>
                            <?php if(config('app.debug')): ?>
                                <div class="alert alert-info mt-2">
                                    <strong>Debug Info:</strong><br>
                                    Display Settings: <?php echo e(json_encode($budget->display_settings ?? [])); ?><br>
                                    <?php if(isset($budget->display_settings['category_filter'])): ?>
                                        Category Filter Type: <?php echo e(gettype($budget->display_settings['category_filter'])); ?><br>
                                        Category Filter Value: <?php echo e(is_array($budget->display_settings['category_filter']) ? json_encode($budget->display_settings['category_filter']) : $budget->display_settings['category_filter']); ?><br>
                                    <?php endif; ?>
                                    Income Products Count: <?php echo e(count($incomeproduct)); ?><br>
                                    Expense Products Count: <?php echo e(count($expenseproduct)); ?><br>
                                    <div class="mt-2">
                                        <strong>Selected Categories (Live):</strong>
                                        <div id="debug-selected-categories" class="text-primary">None selected</div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="card">

            <div class="card-body table-border-style">

                <!---Start Monthly Budget ----->
                <div class="table-responsive budget_plan d-block"  id="monthly">
                    <table class="table mb-0" id="dataTable-manual">
                        <thead>
                        <tr>
                            <th><?php echo e(__('Category')); ?></th>
                            <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="total text-dark"><?php echo e($month); ?></td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <th><?php echo e(__('Total :')); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <!------------------   Income Category ----------------------------------->
                        <tr class="income-section">
                            <th colspan="14" class="text-dark light_blue"><span><?php echo e(__('Income :')); ?></span></th>
                        </tr>

                        <?php $__currentLoopData = $incomeproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="income-row income-category-<?php echo e($productService->id); ?>">
                                <td><?php echo e($productService->name); ?></td>

                                <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                    <td>
                                        <input type="number" class="form-control pl-1 pr-1 income_data <?php echo e($month); ?>_income" data-month="<?php echo e($month); ?>" name="income[<?php echo e($productService->id); ?>][<?php echo e($month); ?>]" value="<?php echo e(!empty($budget['income_data'][$productService->id][$month])?$budget['income_data'][$productService->id][$month]:0); ?>" id="income_data_<?php echo e($month); ?>">
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <td class="totalIncome text-dark">
                                    0.00
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <tr>
                            <td class="text-dark"><?php echo e(__('Total :')); ?></td>
                            <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td>
                                    <span class="<?php echo e($month); ?>_total_income text-dark">0.00</span>
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <td>
                                <span class="income text-dark">0.00</span>
                            </td>
                        </tr>

                        <!------------------   Expense Category ----------------------------------->

                        <tr class="expense-section">
                            <th colspan="14" class="text-dark light_blue"><span><?php echo e(__('Expense :')); ?></span></th>
                        </tr>

                        <?php $__currentLoopData = $expenseproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="expense-row expense-category-<?php echo e($productService->id); ?>">
                                <td><?php echo e($productService->name); ?></td>
                                <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <td>
                                        <input type="number" class="form-control pl-1 pr-1 expense_data <?php echo e($month); ?>_expense" data-month="<?php echo e($month); ?>" name="expense[<?php echo e($productService->id); ?>][<?php echo e($month); ?>]" value="<?php echo e(!empty($budget['expense_data'][$productService->id][$month])?$budget['expense_data'][$productService->id][$month]:0); ?>" id="expense_data_<?php echo e($month); ?>">
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <td class="totalExpense text-dark">
                                    0.00
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <tr>
                            <td  class="text-dark"><?php echo e(__('Total :')); ?></span></td>
                            <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td>
                                    <span class="<?php echo e($month); ?>_total_expense text-dark">0.00</span>
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <td>
                                <span class="expense text-dark">0.00</span>
                            </td>

                        </tr>

                        </tbody>

                    </table>

                    <div class="modal-footer budget">
                        <input type="button" value="<?php echo e(__('Cancel')); ?>" onclick="location.href = '<?php echo e(route("budget.index")); ?>';" class="btn btn-secondary">
                        <input type="submit" value="<?php echo e(__('Update')); ?>" class="btn  btn-primary">
                    </div>
                </div>
                <!---End Monthly Budget ----->

                <!---- Start Quarterly Budget ----->
                <div class="table-responsive budget_plan d-none" id="quarterly">
                    <table class="table  mb-0" id="dataTable-manual">
                        <thead>
                        <tr>
                            <th><?php echo e(__('Category')); ?></th>
                            <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="total text-dark"><?php echo e($month); ?></td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <th><?php echo e(__('Total :')); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <!------------------   Income Category ----------------------------------->
                        <tr class="income-section">
                            <th colspan="14" class="text-dark light_blue"><span><?php echo e(__('Income :')); ?></span></th>
                        </tr>

                        <?php $__currentLoopData = $incomeproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="income-row income-category-<?php echo e($productService->id); ?>">
                                <td><?php echo e($productService->name); ?></td>

                                <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                    <td>
                                        <input type="number" class="form-control income_data <?php echo e($month); ?>_income" data-month="<?php echo e($month); ?>" name="income[<?php echo e($productService->id); ?>][<?php echo e($month); ?>]" value="<?php echo e(!empty($budget['income_data'][$productService->id][$month])?$budget['income_data'][$productService->id][$month]:0); ?>" id="income_data_<?php echo e($month); ?>">
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <td class="text-end totalIncome text-dark">
                                    0.00
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <tr>
                            <td class="text-dark"><?php echo e(__('Total :')); ?></td>
                            <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td>
                                    <span class="<?php echo e($month); ?>_total_income text-dark">0.00</span>
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <td class="text-end">
                                <span class="income text-dark">0.00</span>
                            </td>
                        </tr>

                        <!------------------   Expense Category ----------------------------------->

                        <tr class="expense-section">
                            <th colspan="14" class="text-dark light_blue"><span><?php echo e(__('Expense :')); ?></span></th>
                        </tr>

                        <?php $__currentLoopData = $expenseproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="expense-row expense-category-<?php echo e($productService->id); ?>">
                                <td><?php echo e($productService->name); ?></td>
                                <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <td>
                                        <input type="number" class="form-control expense_data <?php echo e($month); ?>_expense" data-month="<?php echo e($month); ?>" name="expense[<?php echo e($productService->id); ?>][<?php echo e($month); ?>]" value="<?php echo e(!empty($budget['expense_data'][$productService->id][$month])?$budget['expense_data'][$productService->id][$month]:0); ?>" id="expense_data_<?php echo e($month); ?>">
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <td class="text-end totalExpense text-dark">
                                    0.00
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <tr>
                            <td  class="text-dark"><?php echo e(__('Total :')); ?></span></td>
                            <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td>
                                    <span class="<?php echo e($month); ?>_total_expense text-dark">0.00</span>
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <td class="text-end">
                                <span class="expense text-dark">0.00</span>
                            </td>

                        </tr>

                        </tbody>

                    </table>
                    <div class="modal-footer budget">
                        <input type="button" value="<?php echo e(__('Cancel')); ?>" onclick="location.href = '<?php echo e(route("budget.index")); ?>';" class="btn btn-secondary">
                        <input type="submit" value="<?php echo e(__('Update')); ?>" class="btn  btn-primary">
                    </div>
                </div>

                <!---- End Quarterly Budget ----->



                <!---Start Half-Yearly Budget ----->
                <div class="table-responsive budget_plan d-none" id="half-yearly">
                    <table class="table  mb-0" id="dataTable-manual">
                        <thead>
                        <tr>
                            <th><?php echo e(__('Category')); ?></th>
                            <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="total text-dark"><?php echo e($month); ?></td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <th><?php echo e(__('Total :')); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <!------------------   Income Category ----------------------------------->
                        <tr class="income-section">
                            <th colspan="14" class="text-dark light_blue"><span><?php echo e(__('Income :')); ?></span></th>
                        </tr>

                        <?php $__currentLoopData = $incomeproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="income-row income-category-<?php echo e($productService->id); ?>">
                                <td><?php echo e($productService->name); ?></td>

                                <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                    <td>
                                        <input type="number" class="form-control income_data <?php echo e($month); ?>_income" data-month="<?php echo e($month); ?>" name="income[<?php echo e($productService->id); ?>][<?php echo e($month); ?>]" value="<?php echo e(!empty($budget['income_data'][$productService->id][$month])?$budget['income_data'][$productService->id][$month]:0); ?>" id="income_data_<?php echo e($month); ?>">
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <td class="text-end totalIncome text-dark">
                                    0.00
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <tr>
                            <td class="text-dark"><?php echo e(__('Total :')); ?></td>
                            <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td>
                                    <span class="<?php echo e($month); ?>_total_income text-dark">0.00</span>
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <td class="text-end">
                                <span class="income text-dark">0.00</span>
                            </td>
                        </tr>

                        <!------------------   Expense Category ----------------------------------->

                        <tr class="expense-section">
                            <th colspan="14" class="text-dark light_blue"><span><?php echo e(__('Expense :')); ?></span></th>
                        </tr>

                        <?php $__currentLoopData = $expenseproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="expense-row expense-category-<?php echo e($productService->id); ?>">
                                <td><?php echo e($productService->name); ?></td>
                                <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <td>
                                        <input type="number" class="form-control expense_data <?php echo e($month); ?>_expense" data-month="<?php echo e($month); ?>" name="expense[<?php echo e($productService->id); ?>][<?php echo e($month); ?>]" value="<?php echo e(!empty($budget['expense_data'][$productService->id][$month])?$budget['expense_data'][$productService->id][$month]:0); ?>" id="expense_data_<?php echo e($month); ?>">
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <td class="text-end totalExpense text-dark">
                                    0.00
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <tr>
                            <td  class="text-dark"><?php echo e(__('Total :')); ?></span></td>
                            <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td>
                                    <span class="<?php echo e($month); ?>_total_expense text-dark">0.00</span>
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <td class="text-end">
                                <span class="expense text-dark">0.00</span>
                            </td>

                        </tr>

                        </tbody>

                    </table>
                    <div class="modal-footer budget">
                        <input type="button" value="<?php echo e(__('Cancel')); ?>" onclick="location.href = '<?php echo e(route("budget.index")); ?>';" class="btn btn-secondary">
                        <input type="submit" value="<?php echo e(__('Update')); ?>" class="btn  btn-primary">
                    </div>
                </div>

                <!---End Half-Yearly Budget ----->


                <!---Start Yearly Budget ----->
                <div class="table-responsive budget_plan d-none" id="yearly">
                    <table class="table  mb-0" id="dataTable-manual">
                        <thead>
                        <tr>
                            <th><?php echo e(__('Category')); ?></th>
                            <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td class="total text-dark"><?php echo e($month); ?></td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <th><?php echo e(__('Total :')); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <!------------------   Income Category ----------------------------------->
                        <tr class="income-section">
                            <th colspan="14" class="text-dark light_blue"><span><?php echo e(__('Income :')); ?></span></th>
                        </tr>

                        <?php $__currentLoopData = $incomeproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="income-row income-category-<?php echo e($productService->id); ?>">
                                <td><?php echo e($productService->name); ?></td>

                                <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                    <td>
                                        <input type="number" class="form-control income_data <?php echo e($month); ?>_income" data-month="<?php echo e($month); ?>" name="income[<?php echo e($productService->id); ?>][<?php echo e($month); ?>]" value="<?php echo e(!empty($budget['income_data'][$productService->id][$month])?$budget['income_data'][$productService->id][$month]:0); ?>" id="income_data_<?php echo e($month); ?>">
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <td class="text-end totalIncome text-dark">
                                    0.00
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <tr>
                            <td class="text-dark"><?php echo e(__('Total :')); ?></td>
                            <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td>
                                    <span class="<?php echo e($month); ?>_total_income text-dark">0.00</span>
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <td class="text-end">
                                <span class="income text-dark">0.00</span>
                            </td>
                        </tr>

                        <!------------------   Expense Category ----------------------------------->

                        <tr class="expense-section">
                            <th colspan="14" class="text-dark light_blue"><span><?php echo e(__('Expense :')); ?></span></th>
                        </tr>

                        <?php $__currentLoopData = $expenseproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="expense-row expense-category-<?php echo e($productService->id); ?>">
                                <td><?php echo e($productService->name); ?></td>
                                <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <td>
                                        <input type="number" class="form-control expense_data <?php echo e($month); ?>_expense" data-month="<?php echo e($month); ?>" name="expense[<?php echo e($productService->id); ?>][<?php echo e($month); ?>]" value="<?php echo e(!empty($budget['expense_data'][$productService->id][$month])?$budget['expense_data'][$productService->id][$month]:0); ?>" id="expense_data_<?php echo e($month); ?>">
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <td class="text-end totalExpense text-dark">
                                    0.00
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <tr>
                            <td  class="text-dark"><?php echo e(__('Total :')); ?></span></td>
                            <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <td>
                                    <span class="<?php echo e($month); ?>_total_expense text-dark">0.00</span>
                                </td>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <td class="text-end">
                                <span class="expense text-dark">0.00</span>
                            </td>

                        </tr>

                        </tbody>

                    </table>
                    <div class="modal-footer budget">
                        <input type="button" value="<?php echo e(__('Cancel')); ?>" onclick="location.href = '<?php echo e(route("budget.index")); ?>';" class="btn btn-secondary me-2">
                        <input type="submit" value="<?php echo e(__('Update')); ?>" class="btn  btn-primary">
                    </div>
                </div>
                <!---End Yearly Budget ----->


            </div>
            <?php echo e(Form::close()); ?>


        </div>
    </div>
<?php $__env->stopSection(); ?>





<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\erpq24\resources\views/budget/edit.blade.php ENDPATH**/ ?>