{"__meta": {"id": "Xb812ebe6d070537c4284f9792974a5ea", "datetime": "2025-06-26 23:17:50", "utime": **********.751331, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.315173, "end": **********.751345, "duration": 0.4361720085144043, "duration_str": "436ms", "measures": [{"label": "Booting", "start": **********.315173, "relative_start": 0, "end": **********.70017, "relative_end": **********.70017, "duration": 0.3849971294403076, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.700179, "relative_start": 0.3850061893463135, "end": **********.751346, "relative_end": 1.1920928955078125e-06, "duration": 0.05116701126098633, "duration_str": "51.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029752, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029, "accumulated_duration_str": "2.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.726491, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.931}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7373939, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.931, "width_percent": 14.483}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.744289, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.414, "width_percent": 17.586}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/create?0=\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-48405337 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-48405337\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1794497248 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1794497248\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1529991469 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529991469\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1498339207 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/bill/create?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979867629%7C10%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImUxbmIvcGVPaU5IS1NZN3M3YjYrK1E9PSIsInZhbHVlIjoiR1gvSUVCRVp2U2Q5cENkMWQ4OXV0WmFjMWVTb1hZd3pRK3JUY1JYa3VzdDF2QnF3OXNMSmd0ZDV5akc5SnhwV2ZiRmEyQWhtWmxBNHpkOGFVWTBYcXFNQlhTSUdJU2x3dVZhRzNDM2xmQXk4QUZzNExzYzhBVm9pQ0RaellFaW9hdmNNOFVDL3ZVQUx6aE1wbStDVkczVHQwcE5EeEN3dGkvTXJIMEc4RHRMQVJza21QSWhZUmlrdW0vV1dzSExGbVZTeVB4c2h5V2xzR2ZUWWxqUCtYOFUvYjFLeThQY0d6d28wd0xYR00rVDBnd2EweU5xcEJZSnVYdWkweVR2cU00NjdDNlAxZWVQRU96YWp3bTZnYjZRYldaRVUrN3lyVHB2a25VQmdGaHgxeTZHdG5TOS90T01nQkhtVGpTdUhhc1FFVVBaUmsyUkRqVHZtT2p2N3FWUlhVMXUwZ2RFM1k1Rkt2TUFIWndSZmJoQlVVL1NFdW12TWU0YXhZbDZNZ0ZxNVVlWWgxaC9XTkluSjVvVXJUcDBnUFBSL1YxRlhsUjFtVW5XY0VxUTNqeFoxNDV1ZC9LeWs4Q3QyRDhvMlhlRTVOUmZSbGtGWlJoY1c5RUI3Q000N1RuejBuOEJTRW93RlJySjd2YlJYNGtNTzBUcVEyZjVwTncxeFRoaWQiLCJtYWMiOiJhMWEzNzA0MDJlZGE4YzcwOGVlODM0ZmE2MjJjNmQzZGI2MjNhMDVmOGQxNGE5MTI5MTY0YTJkMDI3NTlmNzYwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJHejl1dFhORmpFYllkdkxub0d1ZkE9PSIsInZhbHVlIjoiN3V0VjRYV0czQlZ1am5kQXpONkk1K1AvMlNmT05PNExwVVJsTU1JQTIxY2NsTFdPTkdXMXhPaE1URVZvT3JHRTlROW1tWWNHY2F2Rm1DMlYxdVVmU3A0dWFYTnZpSk1idUltckxXdjZxc2hwVUYzeC9xMXVUQTBYb1NkanZQalROTG5HZ21xSHZHTjY5UmNjT0VickdXdVc4RlgvZko1NTNjWHJ4b0JDeEYrdzRHdU1TcHlPREdXYWVxNmNmV1pJbVdzTW9nUy9Xb0JlL1B5R1JZRGxXcmt6dnRMR3BaRjg1SWgwNGQzZ2pydHowN0tGOXVQOEZnNnFFYWI1emNXVVZqUUdHT2pvYWFwaEtMMVBLT09qaGMvTGxwSzN3ekwrQWxSakkxZE1yb0p6T3d1VXBqdW0vMm1iVGxFMzQ3OFRVYzBmSWZqck8yMXJGaEFkVTMyblA1Zm9uVjFWMjVYcXExRDJzandKcko5aUpCYnVQckpocmlEMGNFb1dmZHI5d1hQZlB4NjRqSk5DMnU4ODlMK3ExRUt1MS9RWmpYK2FvYjJhQXFmblZQdllXT0sxRjZDa3pTTlducmZ3UkgxV25OV3doYWtQdmNBZUMrdjdxZTY4R29RTytyMUtEOEhRelROdDNSNFAzQVorZ0ltU2J3cEM2eG9IYXpPYmhMZjIiLCJtYWMiOiIzMmIzMDU2MjljODcyMGQzZTM4NTMxNGNmMTY3MzAxYWY5ZjcyZmY2MjIwODAzMzkzYzUzZDA0ODU5ZGZiY2RmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1498339207\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-401107535 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401107535\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-738436677 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:17:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlQvc3pjR3FOM2V5bkozTWNibWVUS2c9PSIsInZhbHVlIjoiUnJqcDJ4R0hySUJRWG0vRjZKanlrbGJoNnAzekEzNjBDeENpNTk1cUV6V2tEZkpBOEZzalAyYWwwSGZ3TmZmOUI0M3p1Y3dJOU4ybkozSFZmVHdlUzNtUTlSL0QyUEhHc01xWm9yWWdqL3JabW5jUDhiMVU1a0MwYzlTRXlRT2xZMG5kaS9pbVRsSmVTWFIrV0tlMkNVaVJ3aTFDSVNKM3FMVllLWDdzQW5nOFExeDg2YUJhN1JkVkthN1VoMzR6dDNqTjY0Rk5NNlBTaWpxQ3pnSW9iYmlsb3ZlMXJuekoyemxnLzlqM2FOUG13aFErSDlNQ1F6azZNeEJGdlp6TzNzajRtN2p5Y3dSeWRxSzY2cWdaVlcxbzZoMytDbUFzMVkrNCtMRXVLaWkzUnBJZGJ3YWdjUVV0ZW1zbUlGUGhTT0hTQW9DVVNQRy9yd0UwYlpJR0JYQ2tnUm53ays5eHpkd0FHOGdVQ1ArMlo0LzhoZ2pLR1Q3M0RGdHczL3dUZlpJL3NVTWtzeU9wNWdqY0F0MVhlcUxlNnpiRitUVFdSZm03OEZJOG1ySTZmTWs2dlIzUkQwdjhCTnFUakh2ck9IRXZGNERHOUhudmIweFdWRHh2NnZFbzlJZFRwbGx1bWR3NzIwQ3NES3hHNEZUR2IvNjJyd3doa2hPbGMvd1IiLCJtYWMiOiI5MWRhZjFlNjFjMmU4NGNjMDAzYTA3OTQxM2MyMjE1ODBmZTM4NTUwMzU5YTBjNzdlNzAwMmMzNzkxZDlhNWMwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:17:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikh3TXRGaUFFT2tqZmt5Zmc5dm5lenc9PSIsInZhbHVlIjoicXM1V1YxU3phSm5yWEloR3ZmQUxvc05jL2E1S05kZlA1Vk56RUUxTkVMWHZ2aFJjM0FQSHg1SVEvSmJVZk9qbTRYMk55ZjVUdlNGL2NKMWtTbjNEWThIUWFZbktwbkFzRnhZQ3o2Yk5VRUhsQUhud0hSbFNFR1VnTEJzSVRQcis4SlZYRVpyMVhVeU42cXJkMmpvMGQ1K0Z1Yi9weVc2ZmNxdkdKeGNyKzlNeVhqejVMWUxoMTN2YkdpaVdZRG1rTjhqWVovK0NZcXFFTVYrN3FZd2VDUzRrSEdIOFI2WjRKY3ZKeEJzaE85bTJPUWtMM0djVWJJVEZ0a1JSalBCMVNpTGRON3lUZ1FaNUxPWHBtbStsNzkxcFZZS2hRUkJPYzBrZzJ4c0FuSjRPeTRFTmdEZ3dCZCt0eVh3N1RjRkttYUw1bm1VN1dMM2NPVGxaS0pPTnJQdVhwMnBhUUl5S0RRT1NtY21uTHJJOEIyWmMzN0xFdFk1L0YvcTV4MFo2aUpnK3pnOTJVMnZVdkpFbFZXYno3dC9yTEllcDBDQTlQcGJ5SHBWRTQ3VWRsMmRIK21yRTRqMG50WHdoa1d0M2ZBWVNYNDh2aHJXZVdLMXF6OHljWTk2Q2FlR1BYK0c5aTk4YkFBS1JFeHd1MWlGV1owbmhZTFBkTlQzYUhTVzkiLCJtYWMiOiJlNGZmNjViMGU0Zjg4NzZmNDRiOTVjYTk1ZDYwOWM2OGUyYTRkMTE2NjEyODU4ZjY5MzY1NmQ0ZWNmNDBlYjE1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:17:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlQvc3pjR3FOM2V5bkozTWNibWVUS2c9PSIsInZhbHVlIjoiUnJqcDJ4R0hySUJRWG0vRjZKanlrbGJoNnAzekEzNjBDeENpNTk1cUV6V2tEZkpBOEZzalAyYWwwSGZ3TmZmOUI0M3p1Y3dJOU4ybkozSFZmVHdlUzNtUTlSL0QyUEhHc01xWm9yWWdqL3JabW5jUDhiMVU1a0MwYzlTRXlRT2xZMG5kaS9pbVRsSmVTWFIrV0tlMkNVaVJ3aTFDSVNKM3FMVllLWDdzQW5nOFExeDg2YUJhN1JkVkthN1VoMzR6dDNqTjY0Rk5NNlBTaWpxQ3pnSW9iYmlsb3ZlMXJuekoyemxnLzlqM2FOUG13aFErSDlNQ1F6azZNeEJGdlp6TzNzajRtN2p5Y3dSeWRxSzY2cWdaVlcxbzZoMytDbUFzMVkrNCtMRXVLaWkzUnBJZGJ3YWdjUVV0ZW1zbUlGUGhTT0hTQW9DVVNQRy9yd0UwYlpJR0JYQ2tnUm53ays5eHpkd0FHOGdVQ1ArMlo0LzhoZ2pLR1Q3M0RGdHczL3dUZlpJL3NVTWtzeU9wNWdqY0F0MVhlcUxlNnpiRitUVFdSZm03OEZJOG1ySTZmTWs2dlIzUkQwdjhCTnFUakh2ck9IRXZGNERHOUhudmIweFdWRHh2NnZFbzlJZFRwbGx1bWR3NzIwQ3NES3hHNEZUR2IvNjJyd3doa2hPbGMvd1IiLCJtYWMiOiI5MWRhZjFlNjFjMmU4NGNjMDAzYTA3OTQxM2MyMjE1ODBmZTM4NTUwMzU5YTBjNzdlNzAwMmMzNzkxZDlhNWMwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:17:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikh3TXRGaUFFT2tqZmt5Zmc5dm5lenc9PSIsInZhbHVlIjoicXM1V1YxU3phSm5yWEloR3ZmQUxvc05jL2E1S05kZlA1Vk56RUUxTkVMWHZ2aFJjM0FQSHg1SVEvSmJVZk9qbTRYMk55ZjVUdlNGL2NKMWtTbjNEWThIUWFZbktwbkFzRnhZQ3o2Yk5VRUhsQUhud0hSbFNFR1VnTEJzSVRQcis4SlZYRVpyMVhVeU42cXJkMmpvMGQ1K0Z1Yi9weVc2ZmNxdkdKeGNyKzlNeVhqejVMWUxoMTN2YkdpaVdZRG1rTjhqWVovK0NZcXFFTVYrN3FZd2VDUzRrSEdIOFI2WjRKY3ZKeEJzaE85bTJPUWtMM0djVWJJVEZ0a1JSalBCMVNpTGRON3lUZ1FaNUxPWHBtbStsNzkxcFZZS2hRUkJPYzBrZzJ4c0FuSjRPeTRFTmdEZ3dCZCt0eVh3N1RjRkttYUw1bm1VN1dMM2NPVGxaS0pPTnJQdVhwMnBhUUl5S0RRT1NtY21uTHJJOEIyWmMzN0xFdFk1L0YvcTV4MFo2aUpnK3pnOTJVMnZVdkpFbFZXYno3dC9yTEllcDBDQTlQcGJ5SHBWRTQ3VWRsMmRIK21yRTRqMG50WHdoa1d0M2ZBWVNYNDh2aHJXZVdLMXF6OHljWTk2Q2FlR1BYK0c5aTk4YkFBS1JFeHd1MWlGV1owbmhZTFBkTlQzYUhTVzkiLCJtYWMiOiJlNGZmNjViMGU0Zjg4NzZmNDRiOTVjYTk1ZDYwOWM2OGUyYTRkMTE2NjEyODU4ZjY5MzY1NmQ0ZWNmNDBlYjE1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:17:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738436677\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-116919515 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/bill/create?0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-116919515\", {\"maxDepth\":0})</script>\n"}}