{"__meta": {"id": "X244a76aa0bea4b1aba691532d8859926", "datetime": "2025-06-26 23:16:09", "utime": **********.846516, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.439543, "end": **********.846531, "duration": 0.40698790550231934, "duration_str": "407ms", "measures": [{"label": "Booting", "start": **********.439543, "relative_start": 0, "end": **********.797668, "relative_end": **********.797668, "duration": 0.3581249713897705, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.797676, "relative_start": 0.35813307762145996, "end": **********.846532, "relative_end": 1.1920928955078125e-06, "duration": 0.04885601997375488, "duration_str": "48.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00313, "accumulated_duration_str": "3.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.822515, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.815}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8327298, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.815, "width_percent": 19.169}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.838352, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.984, "width_percent": 15.016}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IldLOWlTYUhnYTc3MVhZTThXK0MrclE9PSIsInZhbHVlIjoiTkhYcWZGeXlkb0hYZE1iUlJOc21IQT09IiwibWFjIjoiYzczNjU1YWZmZjI3OWFjNTA4YjE0YjZmZWQ5NjYwNGUzZmM3ODI1MzZiMzA5YTIxM2ZhY2EzMzZkNWVhY2JlYiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1717966447 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1717966447\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1891613243 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1891613243\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1207230801 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207230801\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-830671278 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IldLOWlTYUhnYTc3MVhZTThXK0MrclE9PSIsInZhbHVlIjoiTkhYcWZGeXlkb0hYZE1iUlJOc21IQT09IiwibWFjIjoiYzczNjU1YWZmZjI3OWFjNTA4YjE0YjZmZWQ5NjYwNGUzZmM3ODI1MzZiMzA5YTIxM2ZhY2EzMzZkNWVhY2JlYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979766363%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndjTWxiYTNPSjdaOGxNM3Y3REdyVHc9PSIsInZhbHVlIjoiRGlpNEZyN3djU083eEp3WWtFWWFLMVc4Y0ZSQTVrVURqMkVLZk9Fc21YTWw3cFpTL0ZEMks2QXhyTmFKQ3RzSmFuTVhtVlhjeDR4bnBQeHZZMUtTNG83YWlNMnlweUZnM3hGaFlDcHV0V0J0cjZCQkN6U3B3OWg5WDhaSlQ2U0RnaDN5bFFhQVl4NElyazg2WE1xUWVFODVUS2k3dEt2ZWJjOXJSNE40N3VqM2NxRVdqdy9zN29rT0hBRmEwcm1ybk1RNC8wZ2dxL3Z6UUNmSFVtWW1xVW8zQ3FYdmVvcnpCcm1wenZ5b2JjNUNxQlFMcXhRZzk3NFRsT3E4WFQxRkk3aFRhN2JKYlRhZlpPaVRTUmtROS9rWUFVR3pMaklITWNtd2hTMjZVVjJ5dityV3pNS3hVemwxVHhmUGE5NFBLdzBYamRSQndhd3dBS3IzWUNOVTZpeTFpeFZQY2FGUXVlQUtPTVAvZk53V3ZoNUVMKzdUbDVqRW0yUWNhbHdPdGNCVkgvMXBVKzV0T0pmVG9zQldxWFpvUERkSkdkR0UyVm0vazFFbjNZQTA4RUk3ZWFRcDdaZjgycVJ2cXJSaEhTcG96U3FBc2RRSG1jb1VLMkk3Q1oxVHdHclRXOTA4ekZSblVQOGFRNitIV1RTNVBISW5FQWNtTkh1ajhLZHgiLCJtYWMiOiIyOGMxMDliNzA5MDk4NjAxNzk0NmJmYTBkNjljMGYyZGU0N2ExMjM2NWZhZWIwMDI1Y2YwOWZhZTM4MzkyMjdlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllUb3l5dFVCdXVBVjIvZE9IaG1JVkE9PSIsInZhbHVlIjoidnhmSXQ3WjVMYW9PZFdlYjdwcE5hMGU2MVNPQmNpNEtmcVF1MFhwYXllcnpXeGVEM0ZmbHpXTy85T2pqck1MVkoyM1Y5M3dPY29xMzltenF3UXFxbExlNGtNTWZ4ZTNiRGNlam9GTExsWTlkcnZQTlpYUUJEMEdUV2twM1RBd3ErMGdLTzRsZEIwZ2piNnUwSTlUMlY5SmM3am5FTDVORjlnOFRHdGxQM3M0S0g5OW5saGZaRjBKSXBHN1R0Y2tad2RqeWw1b0cwL2krVFVpYWlobWxONFJBOWllL20vRURyOEd0Q0hyTC8wcjBsb2F6OTBRdFVnelpwd2lnUEJobnFwWlBkNEllUWNJTm93a21pWllwZDYvZ0d1S05STDA2NmRacStIZEo0bzVqNzJlRFBKVHpPWHlXSDRaWURaNkVvOWZQOU9UMkhQV091bWorY2pNd2pLYmJjV3M2STlhem5MdktCQlBseW5nTDVZMWpRWndzclp1RjI4WWN4Sk1MUkdjaEhnWTUzK3J5R3I1OURGbVlQd3JUdDVCU3ZPbUF0cU1QT1JZUlZnNW0xRjE3b3FySkdmQkdiQWw0d29mUERMODZPMTdHdGRHdllZdFBLZnhKeEVRWkVQVTYrQXZIcVIyWHo1clY5T0VVcXk5K0hhRWhjd3VVNWx6U3o1bzYiLCJtYWMiOiIwZGI0NzM3MmM2MjUyYzkxMjkxMWJiNGExNzZiNWQ5YmM2NWFmMzJiMjc5YzRhN2ZkZThjMzI4MWU2YzE5NGVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-830671278\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-46781304 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46781304\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1343363215 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:16:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVLUVJIRVozNEo2T1Z4Vk1idTg3U3c9PSIsInZhbHVlIjoiVUdVY25xUG4wekVLWkUyK0dHRHkrSjhyZHI0cXN2UEtUYmJROWRKbWtaUUE4RkV2R1UvbEFlS1ZvVmJzTG9VbVJDZEZRM2VyRXlDWFlvbEtSTnVZdGlVd1d3Nkt0RHMyblRTcHpBa3hYWkdzZWtwVTZHdk5tZVZaUkdGYzNVSitvWGI4VGgreStsUE9LYTgrMzBSaDBPYklYTExNMHhkd2MyY1I0STA2T0lMbk0yMzlOYWlLeW5vaEMrNUlPVEdCMTdSeTNVN1dWV2xNU0p3K3NyRy80bm1TZ1BiUTFXWjNaQ0s2QzE3bnlNL2RZVm5XYStmd1J5ZEdzcU5rT1hsZVFML21VSGdOeGE5c2lOZERjN3ZpdnQxckdlakFzdjlwUXFweXRYcjhXeFJwbnNhbThxS0xCdlRGT0NFUEp0dEg5NlBVM3lpY3NvT3h6SHZ2MTZvYkxCemdtWHdFbW9sVzFjRnZWU3pyMk44QkFIbWcyTkhBYzZQTlF4WDg2ZDk3ZjJXNlFwOGtoTUhaT05XcC82TXIwdU9ZMnFQVzhkaXcyZ3hHVzVra2dnUjlmWE5EVW5rVEJNNHNaR3VTSVp5RE50cDAxM0dCOTU5aVRJUTNUU2JpVjFIUDF6VStEaWRNc3BndHNnbCtwVjVJL3NJcE1QY1BuTDlUZ2llelpvRmkiLCJtYWMiOiJkYjc5N2ZlZjU3OTY5MTYwNjdhOTNmNTk5OTk2MTZjNzk0ZjRlZDEyMmVhMDA0MWNiODAxNTdiMzZiYTUzNGQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:16:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im5haHh0RmhDT1pBdE1PV3FWNTQwQ2c9PSIsInZhbHVlIjoicGcxZFF2am1pcG1GYWx6dnV2TTAxNlNoMzJkTEpHRzBiMjlWRStmZ2ozZkd4dkVmcS9lSklFSVBNZW5DMDJBS0NwaXNmY1M5SU1ESXBsaHR3M25pZndGamxVeGlRZTFLYndyR0NSS0RQN2gwL21kNVpOeHlCT1RHMnI2UXozSkxRSjkwdER1amZrbUF0cnFjR2s3WTdpZHo5dFFPSnczZWxoY2piU3VZRHBwakFtdUVjTmdJbldNVGhIZEpzdC90WTJyUHlTSE1zdWtLTnJLY0hXUDNFNVM4YmlFMzgwNVgyQWNDcG9ubTBuMlg2bUNVZlVpem9OTXpzTVB0emt4VTg0M2h3bWRhcGF2N2JTUGN1M1dQNTgya3p2Z1EvUFZmdnc4RXgxSmo3ejFHVWtWSjBIR0dnYzZVQ0tZTDNaRnVFS3hKUTBTRm1zZ2RQZ2pvcFhFYnNZMFBHSUk1U2QzS2poZ3VLOUp5YVltYUdtMGRtaVgyb0JzS1V3SzVBYjFlMUM0MCs3djdvRjkza1FsUHNSeEZKVStQeXNWZmFOREdRekhQRUFmb0dvdVM2VDZ1b0tTYlQ2aHVYd204dnNiVXBWdmV3UCtkZi9lRTRBcjlUcmszWDF0S2NjcSttWEVxaldUNlVsSk5XYzBvVUNXQW5QZjVBbnM3KzZ1Nk9vK0YiLCJtYWMiOiIxNDFjYTkyYjJiNjFlY2M4NzU3NzY4ODdjNTY2NDhhNWMzMjE5NWZhZWRhZmE0NmMwY2EwY2VlYmQyYTc3NDkzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:16:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVLUVJIRVozNEo2T1Z4Vk1idTg3U3c9PSIsInZhbHVlIjoiVUdVY25xUG4wekVLWkUyK0dHRHkrSjhyZHI0cXN2UEtUYmJROWRKbWtaUUE4RkV2R1UvbEFlS1ZvVmJzTG9VbVJDZEZRM2VyRXlDWFlvbEtSTnVZdGlVd1d3Nkt0RHMyblRTcHpBa3hYWkdzZWtwVTZHdk5tZVZaUkdGYzNVSitvWGI4VGgreStsUE9LYTgrMzBSaDBPYklYTExNMHhkd2MyY1I0STA2T0lMbk0yMzlOYWlLeW5vaEMrNUlPVEdCMTdSeTNVN1dWV2xNU0p3K3NyRy80bm1TZ1BiUTFXWjNaQ0s2QzE3bnlNL2RZVm5XYStmd1J5ZEdzcU5rT1hsZVFML21VSGdOeGE5c2lOZERjN3ZpdnQxckdlakFzdjlwUXFweXRYcjhXeFJwbnNhbThxS0xCdlRGT0NFUEp0dEg5NlBVM3lpY3NvT3h6SHZ2MTZvYkxCemdtWHdFbW9sVzFjRnZWU3pyMk44QkFIbWcyTkhBYzZQTlF4WDg2ZDk3ZjJXNlFwOGtoTUhaT05XcC82TXIwdU9ZMnFQVzhkaXcyZ3hHVzVra2dnUjlmWE5EVW5rVEJNNHNaR3VTSVp5RE50cDAxM0dCOTU5aVRJUTNUU2JpVjFIUDF6VStEaWRNc3BndHNnbCtwVjVJL3NJcE1QY1BuTDlUZ2llelpvRmkiLCJtYWMiOiJkYjc5N2ZlZjU3OTY5MTYwNjdhOTNmNTk5OTk2MTZjNzk0ZjRlZDEyMmVhMDA0MWNiODAxNTdiMzZiYTUzNGQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:16:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im5haHh0RmhDT1pBdE1PV3FWNTQwQ2c9PSIsInZhbHVlIjoicGcxZFF2am1pcG1GYWx6dnV2TTAxNlNoMzJkTEpHRzBiMjlWRStmZ2ozZkd4dkVmcS9lSklFSVBNZW5DMDJBS0NwaXNmY1M5SU1ESXBsaHR3M25pZndGamxVeGlRZTFLYndyR0NSS0RQN2gwL21kNVpOeHlCT1RHMnI2UXozSkxRSjkwdER1amZrbUF0cnFjR2s3WTdpZHo5dFFPSnczZWxoY2piU3VZRHBwakFtdUVjTmdJbldNVGhIZEpzdC90WTJyUHlTSE1zdWtLTnJLY0hXUDNFNVM4YmlFMzgwNVgyQWNDcG9ubTBuMlg2bUNVZlVpem9OTXpzTVB0emt4VTg0M2h3bWRhcGF2N2JTUGN1M1dQNTgya3p2Z1EvUFZmdnc4RXgxSmo3ejFHVWtWSjBIR0dnYzZVQ0tZTDNaRnVFS3hKUTBTRm1zZ2RQZ2pvcFhFYnNZMFBHSUk1U2QzS2poZ3VLOUp5YVltYUdtMGRtaVgyb0JzS1V3SzVBYjFlMUM0MCs3djdvRjkza1FsUHNSeEZKVStQeXNWZmFOREdRekhQRUFmb0dvdVM2VDZ1b0tTYlQ2aHVYd204dnNiVXBWdmV3UCtkZi9lRTRBcjlUcmszWDF0S2NjcSttWEVxaldUNlVsSk5XYzBvVUNXQW5QZjVBbnM3KzZ1Nk9vK0YiLCJtYWMiOiIxNDFjYTkyYjJiNjFlY2M4NzU3NzY4ODdjNTY2NDhhNWMzMjE5NWZhZWRhZmE0NmMwY2EwY2VlYmQyYTc3NDkzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:16:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1343363215\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1962899642 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IldLOWlTYUhnYTc3MVhZTThXK0MrclE9PSIsInZhbHVlIjoiTkhYcWZGeXlkb0hYZE1iUlJOc21IQT09IiwibWFjIjoiYzczNjU1YWZmZjI3OWFjNTA4YjE0YjZmZWQ5NjYwNGUzZmM3ODI1MzZiMzA5YTIxM2ZhY2EzMzZkNWVhY2JlYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1962899642\", {\"maxDepth\":0})</script>\n"}}