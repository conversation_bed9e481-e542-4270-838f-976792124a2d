{"__meta": {"id": "X07b17ab43f4c123d7b57fa7880bedac0", "datetime": "2025-06-26 23:20:30", "utime": **********.126643, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980029.687087, "end": **********.126657, "duration": 0.43956995010375977, "duration_str": "440ms", "measures": [{"label": "Booting", "start": 1750980029.687087, "relative_start": 0, "end": **********.072005, "relative_end": **********.072005, "duration": 0.3849179744720459, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.072012, "relative_start": 0.38492488861083984, "end": **********.126659, "relative_end": 1.9073486328125e-06, "duration": 0.054646968841552734, "duration_str": "54.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00323, "accumulated_duration_str": "3.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.099919, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.684}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.113216, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.684, "width_percent": 9.907}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.11935, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.591, "width_percent": 16.409}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IkI3MGNuWnlFZEdMVXRJMGFPeURVWEE9PSIsInZhbHVlIjoiQUpGUEpFd1MydUJxSXJGME03Y3FBZz09IiwibWFjIjoiNjBiNWNkZDFkZmNjYWRiOWUxODI3NjBkNTk0NGQ3ODk1MGYyYzBmMDA5YTUyMGY2NzM0ODI1YWE5OWY0ZDhlOSIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1307303927 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1307303927\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-837062499 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-837062499\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-440728751 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-440728751\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-593100102 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IkI3MGNuWnlFZEdMVXRJMGFPeURVWEE9PSIsInZhbHVlIjoiQUpGUEpFd1MydUJxSXJGME03Y3FBZz09IiwibWFjIjoiNjBiNWNkZDFkZmNjYWRiOWUxODI3NjBkNTk0NGQ3ODk1MGYyYzBmMDA5YTUyMGY2NzM0ODI1YWE5OWY0ZDhlOSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980008295%7C14%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlA1TEJRVENLczJqVUlITHBHZWNCWVE9PSIsInZhbHVlIjoiWXdjeXFqT0IzaGtkUzlXREhONmExTmFLakw4NnpwVlVNR0U3T3lBRXZVQ1NtSWl1TFMvRXlnb01vemZJbTkxNG9TaFZQbi9uN1J6S1A0aFZWN2R6MmNTakxZQzVranEvOG83bU00T1A3MW01S25zTXgwV1o1b29qdFkrQWhUc0d6bkk3S0NVUWdJcWRoSkRVZm5KVUlybGVTZWVQUnhyT1lUOCsremM4REphclUwS1VxYW1qS01mTlYvbTVWY2U4clpNaHh1b0JodGF0ZVl1emx4cmlKcGlYSzB5cGcwUUVhNFpQcitPRUJQNmlLSnpkVXEvL0Y4cENwOGh4N290akk1S0xaem1JZWloUmU5eG1YVTZTODNIRW5KK2dpNTJ0VjNzVVBLMjlWazEzNDRmbk1kWjMxYzgzTUNaQXhxN0tEb1R5b0R5UVlTTGpwK3MxRXF0VHVHeEg4SGtWRVBCby9weDJ0eFYwa1hqK04yclliMm8yN29odXd5N1lvV3llc05ScVViaEV1L3V5eW1tRktXandsUG1FWWtqeHlZR04xbWhOZWIreW4xci9EbFNOT3A0MlJMTTljdG9MODVVLzRsc1pkdnZZZTFjMDRPUy9kSjVqdm13N0M3cGF2cWhSZjRqRFVWOVphRDVBWTVxbDlwbEg2TG9YQ0RTbUlEd20iLCJtYWMiOiJiOTFhYWVkMDhhNmNkNWM0YmFlYzE4NWY4ZTJkNzQ4ODg2MjIxMWUzYmUyNzRjZTI1YjQ0ZmZjMGYyZDZiMmM2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRJdGxCUmthWXRpbVRnb01rQTcveEE9PSIsInZhbHVlIjoiOEpNVlBHbStQaG1WTjBIYnhLY3BNM2JwMHQrYzJRbHFvTUZ6NlFWY2RLY3RUckg0bkRnakx5bXpwcUt0YzlIL3ZscGNycmxPTWxPdUlRMEpuay9PQVFTSjNKMTFXZVJUSTJYQUpYbU1FbzhiOGRvYlg5TVc5czNHbHVpRTZWNy8wUlI2ZlBFRHQybEptQjVUOFA3WFl1b1hQbkplaEo5SEVnOVlpczV2MDB6ZVoxYklsVmRnZU1SVVgzSHF3bW1lVEpqMXpkUXJScGlmTlYzVkJpSzQ2bmdzaVpNVFluZlJtSjIrM0k4dTlYQUJlZndpR2RQRlFIYnB0WUlsbDQwL2U1T2MxS2Mrbk9JSVBDRkxkci9PditvVWVSallTUlk4QkZrRjdXRTRNbGwrT1Z2QUEvY1lUQXpod0FaRDR5YWRKWTJSb3VTdXFuS25UZFEwaEFUQXRpMnUyQmFhV0JxeE9MR0MvenN3TDVON3VFUklYWEl6akxEOEJKS3pqUzdzNElhNHVaS3VNdkhjaEk3ZG52emI1eHNpMzVCYTdWL2VEcjU3NVZKZk91S3B5bjM4eFBlZzJvekpuenZLMXBVOWhISDVXWXZHdyt0YlV4YzMwZHI5QU1Wb0JVVmNxOFVOcnU3MWpLcXpYeTc5T0k4Y3h1bVdSSUhraFJDdStvaWciLCJtYWMiOiJlYmZmNjRkYjEwOGJjOTE5YTg4NTExMDIwNDdhYzk5ZGRkY2JiNzBmMjNjZDgyOTE1MTAzYmI5MDQzNzMxNzY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593100102\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1944513709 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944513709\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJ1dXBYcFhiSmtKZWlhVFNWdXdoRlE9PSIsInZhbHVlIjoiTE5Rd3E1WGU3NUJOMFliRm9DRnVOaUZQS0ZnVDdZeVpWM0dTaTdZWERuTFV5aFVQNERnN1daanVMbUpwZWNEVVRFdkYycnEyT2RMdUJDWmpObys1UHVWM3dMTzZ5ditVZG9EcFRMa2F6TjUzczg0dVp5aGZzSVJXMkpIaHJEMXlxbTE2L3JRbWlMYVo2dzdBbUlKVlh0aTRQTEs0SWcvSWx6Z1E1RnZNbUFqUXBhcVNueFJZSUhtMnovbjJseHNuTUVLZW9WWEp2ZXM5Q1BjcDcyVXdZKzA2WGJvQTQ5WDF3diswbFpic25QV3ZqL0hMVVBVZDJiYVltc2kzVUg3aDdTaVZ5VlAyVG96WHZVQUlaTzdkOW5zSWRJQUJMbFlLNGNjL21jek8ydDdzL1M0MVQ5VzBrd2NuSzRUR0VHazc5WnlwZlpIVUsxeDcyRDMwWXNybjJubnVaUDBTS3JWM2pRN242TUJLejYrMmNpVkxBV1dnT3RQNFZkdnpxRTJxZC9TZ0E1VlNtWmZtYkhMYnd4WkRKa0JSNFFNWUZVeVZLVXMvYk9UNzVSdUt4ZGpjTUdVZUUzT3RTZnB6WlhqL0h3UElmQ3RidGh3Wmo0YXlWY3Qzc25qSTI0T2NiMnY0Vm02T3NEUlpwRUMzM3dOZGdyNCtXOXoyRkhUYlNCSjAiLCJtYWMiOiIwZDE4YTE0YjhlZWNhMDIwZWZmYmJiMTEyOWFkZDllMDAxN2I0ZDIwYThlYmQ2YjE2NTcwZmFhMDNhZmE1NDQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImR2bk9LcUhLYWhKd0Q4ZU5TL0ZsdEE9PSIsInZhbHVlIjoidFdHVzgwd256TDZKMWxhd1hNTVQ4U3lzQi9WQ05XdmxhcXArbnhweU9CVEtJU0NSNUZ5clFJa0dHYko0R2JuSnd3dlU3TERQVDNESFdxNHhjNW1MSlBqMUR2cnpISWRhdGhyeUMrTzV1VU13ejhKTklRRVZjVHI5dnlhZ3kvcnVmSTREL2k2SW5lM1hsSTRiS1pOV1h6SjNjT1lkS21uZjBwVFg3VmRxbmd0UUJyUGlMc0pXQ3QzeDE3aEVJTVMzK0FyaHRzUDNqS0xvVUtjWUdOWW14aGhkR2l5UWkybHEvWVBWckQ4Z3UwTlpFZWpqeWtHOVVXK1c2ZlZWM083T0djeDVpR1NWTE9DczdCaThVc3BIR01kdFE2LzB6SDMycUcxSnF4Q1pDWG1pVWEyejAvUm9kWWl3eWpGMy8yMDNvTFFDNkRLZkw0c1VXQVBSaDFkTmYyb3BRRitEdHAra3JGVS9Ha1h4L3IvazlqR2piYXdPM2luNlVXOU92VTBqTkNxeEsweVVOeE1oekIzWkpWekZUblE4aEppVUlVSTNRZG9zQ3ZpVWJ2QTd3dlMxMlpwMk1XVW1XZUx2Yi8yU09WOU42VDc4MFZScWhCS2h3RmlIWDhBV0wzNHBpSVBqSGVXNGxyUFY3Wk1qQjBSSmpuNnRhd1RPUTMrRXdhcm8iLCJtYWMiOiI2OGFiNjdmY2FiMDVkODNmODYxZjE2YmRmZjIyNDcxMjc2ZGY0YWMyMjNlMzk0MGYwZWIxMDI1NTRjYWNmMzM2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJ1dXBYcFhiSmtKZWlhVFNWdXdoRlE9PSIsInZhbHVlIjoiTE5Rd3E1WGU3NUJOMFliRm9DRnVOaUZQS0ZnVDdZeVpWM0dTaTdZWERuTFV5aFVQNERnN1daanVMbUpwZWNEVVRFdkYycnEyT2RMdUJDWmpObys1UHVWM3dMTzZ5ditVZG9EcFRMa2F6TjUzczg0dVp5aGZzSVJXMkpIaHJEMXlxbTE2L3JRbWlMYVo2dzdBbUlKVlh0aTRQTEs0SWcvSWx6Z1E1RnZNbUFqUXBhcVNueFJZSUhtMnovbjJseHNuTUVLZW9WWEp2ZXM5Q1BjcDcyVXdZKzA2WGJvQTQ5WDF3diswbFpic25QV3ZqL0hMVVBVZDJiYVltc2kzVUg3aDdTaVZ5VlAyVG96WHZVQUlaTzdkOW5zSWRJQUJMbFlLNGNjL21jek8ydDdzL1M0MVQ5VzBrd2NuSzRUR0VHazc5WnlwZlpIVUsxeDcyRDMwWXNybjJubnVaUDBTS3JWM2pRN242TUJLejYrMmNpVkxBV1dnT3RQNFZkdnpxRTJxZC9TZ0E1VlNtWmZtYkhMYnd4WkRKa0JSNFFNWUZVeVZLVXMvYk9UNzVSdUt4ZGpjTUdVZUUzT3RTZnB6WlhqL0h3UElmQ3RidGh3Wmo0YXlWY3Qzc25qSTI0T2NiMnY0Vm02T3NEUlpwRUMzM3dOZGdyNCtXOXoyRkhUYlNCSjAiLCJtYWMiOiIwZDE4YTE0YjhlZWNhMDIwZWZmYmJiMTEyOWFkZDllMDAxN2I0ZDIwYThlYmQ2YjE2NTcwZmFhMDNhZmE1NDQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImR2bk9LcUhLYWhKd0Q4ZU5TL0ZsdEE9PSIsInZhbHVlIjoidFdHVzgwd256TDZKMWxhd1hNTVQ4U3lzQi9WQ05XdmxhcXArbnhweU9CVEtJU0NSNUZ5clFJa0dHYko0R2JuSnd3dlU3TERQVDNESFdxNHhjNW1MSlBqMUR2cnpISWRhdGhyeUMrTzV1VU13ejhKTklRRVZjVHI5dnlhZ3kvcnVmSTREL2k2SW5lM1hsSTRiS1pOV1h6SjNjT1lkS21uZjBwVFg3VmRxbmd0UUJyUGlMc0pXQ3QzeDE3aEVJTVMzK0FyaHRzUDNqS0xvVUtjWUdOWW14aGhkR2l5UWkybHEvWVBWckQ4Z3UwTlpFZWpqeWtHOVVXK1c2ZlZWM083T0djeDVpR1NWTE9DczdCaThVc3BIR01kdFE2LzB6SDMycUcxSnF4Q1pDWG1pVWEyejAvUm9kWWl3eWpGMy8yMDNvTFFDNkRLZkw0c1VXQVBSaDFkTmYyb3BRRitEdHAra3JGVS9Ha1h4L3IvazlqR2piYXdPM2luNlVXOU92VTBqTkNxeEsweVVOeE1oekIzWkpWekZUblE4aEppVUlVSTNRZG9zQ3ZpVWJ2QTd3dlMxMlpwMk1XVW1XZUx2Yi8yU09WOU42VDc4MFZScWhCS2h3RmlIWDhBV0wzNHBpSVBqSGVXNGxyUFY3Wk1qQjBSSmpuNnRhd1RPUTMrRXdhcm8iLCJtYWMiOiI2OGFiNjdmY2FiMDVkODNmODYxZjE2YmRmZjIyNDcxMjc2ZGY0YWMyMjNlMzk0MGYwZWIxMDI1NTRjYWNmMzM2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IkI3MGNuWnlFZEdMVXRJMGFPeURVWEE9PSIsInZhbHVlIjoiQUpGUEpFd1MydUJxSXJGME03Y3FBZz09IiwibWFjIjoiNjBiNWNkZDFkZmNjYWRiOWUxODI3NjBkNTk0NGQ3ODk1MGYyYzBmMDA5YTUyMGY2NzM0ODI1YWE5OWY0ZDhlOSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}