<?php
// This file was auto-generated from sdk-root/src/data/pca-connector-ad/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'endpointPrefix' => 'pca-connector-ad', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'PcaConnectorAd', 'serviceId' => 'Pca Connector Ad', 'signatureVersion' => 'v4', 'signingName' => 'pca-connector-ad', 'uid' => 'pca-connector-ad-2018-05-10', ], 'operations' => [ 'CreateConnector' => [ 'name' => 'CreateConnector', 'http' => [ 'method' => 'POST', 'requestUri' => '/connectors', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateConnectorRequest', ], 'output' => [ 'shape' => 'CreateConnectorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateDirectoryRegistration' => [ 'name' => 'CreateDirectoryRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/directoryRegistrations', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateDirectoryRegistrationRequest', ], 'output' => [ 'shape' => 'CreateDirectoryRegistrationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateServicePrincipalName' => [ 'name' => 'CreateServicePrincipalName', 'http' => [ 'method' => 'POST', 'requestUri' => '/directoryRegistrations/{DirectoryRegistrationArn}/servicePrincipalNames/{ConnectorArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateServicePrincipalNameRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateTemplate' => [ 'name' => 'CreateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/templates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTemplateRequest', ], 'output' => [ 'shape' => 'CreateTemplateResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateTemplateGroupAccessControlEntry' => [ 'name' => 'CreateTemplateGroupAccessControlEntry', 'http' => [ 'method' => 'POST', 'requestUri' => '/templates/{TemplateArn}/accessControlEntries', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTemplateGroupAccessControlEntryRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteConnector' => [ 'name' => 'DeleteConnector', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/connectors/{ConnectorArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteConnectorRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteDirectoryRegistration' => [ 'name' => 'DeleteDirectoryRegistration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/directoryRegistrations/{DirectoryRegistrationArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteDirectoryRegistrationRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteServicePrincipalName' => [ 'name' => 'DeleteServicePrincipalName', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/directoryRegistrations/{DirectoryRegistrationArn}/servicePrincipalNames/{ConnectorArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteServicePrincipalNameRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteTemplate' => [ 'name' => 'DeleteTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/templates/{TemplateArn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteTemplateRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteTemplateGroupAccessControlEntry' => [ 'name' => 'DeleteTemplateGroupAccessControlEntry', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/templates/{TemplateArn}/accessControlEntries/{GroupSecurityIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTemplateGroupAccessControlEntryRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'GetConnector' => [ 'name' => 'GetConnector', 'http' => [ 'method' => 'GET', 'requestUri' => '/connectors/{ConnectorArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConnectorRequest', ], 'output' => [ 'shape' => 'GetConnectorResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetDirectoryRegistration' => [ 'name' => 'GetDirectoryRegistration', 'http' => [ 'method' => 'GET', 'requestUri' => '/directoryRegistrations/{DirectoryRegistrationArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDirectoryRegistrationRequest', ], 'output' => [ 'shape' => 'GetDirectoryRegistrationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetServicePrincipalName' => [ 'name' => 'GetServicePrincipalName', 'http' => [ 'method' => 'GET', 'requestUri' => '/directoryRegistrations/{DirectoryRegistrationArn}/servicePrincipalNames/{ConnectorArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetServicePrincipalNameRequest', ], 'output' => [ 'shape' => 'GetServicePrincipalNameResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTemplate' => [ 'name' => 'GetTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/templates/{TemplateArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTemplateRequest', ], 'output' => [ 'shape' => 'GetTemplateResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTemplateGroupAccessControlEntry' => [ 'name' => 'GetTemplateGroupAccessControlEntry', 'http' => [ 'method' => 'GET', 'requestUri' => '/templates/{TemplateArn}/accessControlEntries/{GroupSecurityIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTemplateGroupAccessControlEntryRequest', ], 'output' => [ 'shape' => 'GetTemplateGroupAccessControlEntryResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListConnectors' => [ 'name' => 'ListConnectors', 'http' => [ 'method' => 'GET', 'requestUri' => '/connectors', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConnectorsRequest', ], 'output' => [ 'shape' => 'ListConnectorsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDirectoryRegistrations' => [ 'name' => 'ListDirectoryRegistrations', 'http' => [ 'method' => 'GET', 'requestUri' => '/directoryRegistrations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDirectoryRegistrationsRequest', ], 'output' => [ 'shape' => 'ListDirectoryRegistrationsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListServicePrincipalNames' => [ 'name' => 'ListServicePrincipalNames', 'http' => [ 'method' => 'GET', 'requestUri' => '/directoryRegistrations/{DirectoryRegistrationArn}/servicePrincipalNames', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListServicePrincipalNamesRequest', ], 'output' => [ 'shape' => 'ListServicePrincipalNamesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTemplateGroupAccessControlEntries' => [ 'name' => 'ListTemplateGroupAccessControlEntries', 'http' => [ 'method' => 'GET', 'requestUri' => '/templates/{TemplateArn}/accessControlEntries', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTemplateGroupAccessControlEntriesRequest', ], 'output' => [ 'shape' => 'ListTemplateGroupAccessControlEntriesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTemplates' => [ 'name' => 'ListTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/templates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTemplatesRequest', ], 'output' => [ 'shape' => 'ListTemplatesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateTemplate' => [ 'name' => 'UpdateTemplate', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/templates/{TemplateArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTemplateRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateTemplateGroupAccessControlEntry' => [ 'name' => 'UpdateTemplateGroupAccessControlEntry', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/templates/{TemplateArn}/accessControlEntries/{GroupSecurityIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTemplateGroupAccessControlEntryRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], ], 'shapes' => [ 'AccessControlEntry' => [ 'type' => 'structure', 'members' => [ 'AccessRights' => [ 'shape' => 'AccessRights', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'GroupDisplayName' => [ 'shape' => 'DisplayName', ], 'GroupSecurityIdentifier' => [ 'shape' => 'GroupSecurityIdentifier', ], 'TemplateArn' => [ 'shape' => 'TemplateArn', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'AccessControlEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessControlEntrySummary', ], ], 'AccessControlEntrySummary' => [ 'type' => 'structure', 'members' => [ 'AccessRights' => [ 'shape' => 'AccessRights', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'GroupDisplayName' => [ 'shape' => 'DisplayName', ], 'GroupSecurityIdentifier' => [ 'shape' => 'GroupSecurityIdentifier', ], 'TemplateArn' => [ 'shape' => 'TemplateArn', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccessRight' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'AccessRights' => [ 'type' => 'structure', 'members' => [ 'AutoEnroll' => [ 'shape' => 'AccessRight', ], 'Enroll' => [ 'shape' => 'AccessRight', ], ], ], 'ApplicationPolicies' => [ 'type' => 'structure', 'required' => [ 'Policies', ], 'members' => [ 'Critical' => [ 'shape' => 'Boolean', ], 'Policies' => [ 'shape' => 'ApplicationPolicyList', ], ], ], 'ApplicationPolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyObjectIdentifier' => [ 'shape' => 'CustomObjectIdentifier', ], 'PolicyType' => [ 'shape' => 'ApplicationPolicyType', ], ], 'union' => true, ], 'ApplicationPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationPolicy', ], 'max' => 100, 'min' => 1, ], 'ApplicationPolicyType' => [ 'type' => 'string', 'enum' => [ 'ALL_APPLICATION_POLICIES', 'ANY_PURPOSE', 'ATTESTATION_IDENTITY_KEY_CERTIFICATE', 'CERTIFICATE_REQUEST_AGENT', 'CLIENT_AUTHENTICATION', 'CODE_SIGNING', 'CTL_USAGE', 'DIGITAL_RIGHTS', 'DIRECTORY_SERVICE_EMAIL_REPLICATION', 'DISALLOWED_LIST', 'DNS_SERVER_TRUST', 'DOCUMENT_ENCRYPTION', 'DOCUMENT_SIGNING', 'DYNAMIC_CODE_GENERATOR', 'EARLY_LAUNCH_ANTIMALWARE_DRIVER', 'EMBEDDED_WINDOWS_SYSTEM_COMPONENT_VERIFICATION', 'ENCLAVE', 'ENCRYPTING_FILE_SYSTEM', 'ENDORSEMENT_KEY_CERTIFICATE', 'FILE_RECOVERY', 'HAL_EXTENSION', 'IP_SECURITY_END_SYSTEM', 'IP_SECURITY_IKE_INTERMEDIATE', 'IP_SECURITY_TUNNEL_TERMINATION', 'IP_SECURITY_USER', 'ISOLATED_USER_MODE', 'KDC_AUTHENTICATION', 'KERNEL_MODE_CODE_SIGNING', 'KEY_PACK_LICENSES', 'KEY_RECOVERY', 'KEY_RECOVERY_AGENT', 'LICENSE_SERVER_VERIFICATION', 'LIFETIME_SIGNING', 'MICROSOFT_PUBLISHER', 'MICROSOFT_TIME_STAMPING', 'MICROSOFT_TRUST_LIST_SIGNING', 'OCSP_SIGNING', 'OEM_WINDOWS_SYSTEM_COMPONENT_VERIFICATION', 'PLATFORM_CERTIFICATE', 'PREVIEW_BUILD_SIGNING', 'PRIVATE_KEY_ARCHIVAL', 'PROTECTED_PROCESS_LIGHT_VERIFICATION', 'PROTECTED_PROCESS_VERIFICATION', 'QUALIFIED_SUBORDINATION', 'REVOKED_LIST_SIGNER', 'ROOT_PROGRAM_AUTO_UPDATE_CA_REVOCATION', 'ROOT_PROGRAM_AUTO_UPDATE_END_REVOCATION', 'ROOT_PROGRAM_NO_OSCP_FAILOVER_TO_CRL', 'ROOT_LIST_SIGNER', 'SECURE_EMAIL', 'SERVER_AUTHENTICATION', 'SMART_CARD_LOGIN', 'SPC_ENCRYPTED_DIGEST_RETRY_COUNT', 'SPC_RELAXED_PE_MARKER_CHECK', 'TIME_STAMPING', 'WINDOWS_HARDWARE_DRIVER_ATTESTED_VERIFICATION', 'WINDOWS_HARDWARE_DRIVER_EXTENDED_VERIFICATION', 'WINDOWS_HARDWARE_DRIVER_VERIFICATION', 'WINDOWS_HELLO_RECOVERY_KEY_ENCRYPTION', 'WINDOWS_KITS_COMPONENT', 'WINDOWS_RT_VERIFICATION', 'WINDOWS_SOFTWARE_EXTENSION_VERIFICATION', 'WINDOWS_STORE', 'WINDOWS_SYSTEM_COMPONENT_VERIFICATION', 'WINDOWS_TCB_COMPONENT', 'WINDOWS_THIRD_PARTY_APPLICATION_COMPONENT', 'WINDOWS_UPDATE', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CertificateAuthorityArn' => [ 'type' => 'string', 'max' => 200, 'min' => 5, 'pattern' => '^arn:[\\w-]+:acm-pca:[\\w-]+:[0-9]+:certificate-authority\\/[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$', ], 'CertificateValidity' => [ 'type' => 'structure', 'required' => [ 'RenewalPeriod', 'ValidityPeriod', ], 'members' => [ 'RenewalPeriod' => [ 'shape' => 'ValidityPeriod', ], 'ValidityPeriod' => [ 'shape' => 'ValidityPeriod', ], ], ], 'ClientCompatibilityV2' => [ 'type' => 'string', 'enum' => [ 'WINDOWS_SERVER_2003', 'WINDOWS_SERVER_2008', 'WINDOWS_SERVER_2008_R2', 'WINDOWS_SERVER_2012', 'WINDOWS_SERVER_2012_R2', 'WINDOWS_SERVER_2016', ], ], 'ClientCompatibilityV3' => [ 'type' => 'string', 'enum' => [ 'WINDOWS_SERVER_2008', 'WINDOWS_SERVER_2008_R2', 'WINDOWS_SERVER_2012', 'WINDOWS_SERVER_2012_R2', 'WINDOWS_SERVER_2016', ], ], 'ClientCompatibilityV4' => [ 'type' => 'string', 'enum' => [ 'WINDOWS_SERVER_2012', 'WINDOWS_SERVER_2012_R2', 'WINDOWS_SERVER_2016', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[!-~]+$', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'Connector' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ConnectorArn', ], 'CertificateAuthorityArn' => [ 'shape' => 'CertificateAuthorityArn', ], 'CertificateEnrollmentPolicyServerEndpoint' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Status' => [ 'shape' => 'ConnectorStatus', ], 'StatusReason' => [ 'shape' => 'ConnectorStatusReason', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'VpcInformation' => [ 'shape' => 'VpcInformation', ], ], ], 'ConnectorArn' => [ 'type' => 'string', 'max' => 200, 'min' => 5, 'pattern' => '^arn:[\\w-]+:pca-connector-ad:[\\w-]+:[0-9]+:connector\\/[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$', ], 'ConnectorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectorSummary', ], ], 'ConnectorStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', ], ], 'ConnectorStatusReason' => [ 'type' => 'string', 'enum' => [ 'DIRECTORY_ACCESS_DENIED', 'INTERNAL_FAILURE', 'PRIVATECA_ACCESS_DENIED', 'PRIVATECA_RESOURCE_NOT_FOUND', 'SECURITY_GROUP_NOT_IN_VPC', 'VPC_ACCESS_DENIED', 'VPC_ENDPOINT_LIMIT_EXCEEDED', 'VPC_RESOURCE_NOT_FOUND', ], ], 'ConnectorSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ConnectorArn', ], 'CertificateAuthorityArn' => [ 'shape' => 'CertificateAuthorityArn', ], 'CertificateEnrollmentPolicyServerEndpoint' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Status' => [ 'shape' => 'ConnectorStatus', ], 'StatusReason' => [ 'shape' => 'ConnectorStatusReason', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'VpcInformation' => [ 'shape' => 'VpcInformation', ], ], ], 'CreateConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'CertificateAuthorityArn', 'DirectoryId', 'VpcInformation', ], 'members' => [ 'CertificateAuthorityArn' => [ 'shape' => 'CertificateAuthorityArn', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Tags' => [ 'shape' => 'Tags', ], 'VpcInformation' => [ 'shape' => 'VpcInformation', ], ], ], 'CreateConnectorResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectorArn' => [ 'shape' => 'ConnectorArn', ], ], ], 'CreateDirectoryRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDirectoryRegistrationResponse' => [ 'type' => 'structure', 'members' => [ 'DirectoryRegistrationArn' => [ 'shape' => 'DirectoryRegistrationArn', ], ], ], 'CreateServicePrincipalNameRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorArn', 'DirectoryRegistrationArn', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ConnectorArn' => [ 'shape' => 'ConnectorArn', 'location' => 'uri', 'locationName' => 'ConnectorArn', ], 'DirectoryRegistrationArn' => [ 'shape' => 'DirectoryRegistrationArn', 'location' => 'uri', 'locationName' => 'DirectoryRegistrationArn', ], ], ], 'CreateTemplateGroupAccessControlEntryRequest' => [ 'type' => 'structure', 'required' => [ 'AccessRights', 'GroupDisplayName', 'GroupSecurityIdentifier', 'TemplateArn', ], 'members' => [ 'AccessRights' => [ 'shape' => 'AccessRights', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'GroupDisplayName' => [ 'shape' => 'DisplayName', ], 'GroupSecurityIdentifier' => [ 'shape' => 'GroupSecurityIdentifier', ], 'TemplateArn' => [ 'shape' => 'TemplateArn', 'location' => 'uri', 'locationName' => 'TemplateArn', ], ], ], 'CreateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorArn', 'Definition', 'Name', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ConnectorArn' => [ 'shape' => 'ConnectorArn', ], 'Definition' => [ 'shape' => 'TemplateDefinition', ], 'Name' => [ 'shape' => 'TemplateName', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'TemplateArn' => [ 'shape' => 'TemplateArn', ], ], ], 'CryptoProvidersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CryptoProvidersListMemberString', ], 'max' => 100, 'min' => 1, ], 'CryptoProvidersListMemberString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'CustomObjectIdentifier' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^([0-2])\\.([0-9]|([0-3][0-9]))(\\.([0-9]+)){0,126}$', ], 'DeleteConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorArn', ], 'members' => [ 'ConnectorArn' => [ 'shape' => 'ConnectorArn', 'location' => 'uri', 'locationName' => 'ConnectorArn', ], ], ], 'DeleteDirectoryRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryRegistrationArn', ], 'members' => [ 'DirectoryRegistrationArn' => [ 'shape' => 'DirectoryRegistrationArn', 'location' => 'uri', 'locationName' => 'DirectoryRegistrationArn', ], ], ], 'DeleteServicePrincipalNameRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorArn', 'DirectoryRegistrationArn', ], 'members' => [ 'ConnectorArn' => [ 'shape' => 'ConnectorArn', 'location' => 'uri', 'locationName' => 'ConnectorArn', ], 'DirectoryRegistrationArn' => [ 'shape' => 'DirectoryRegistrationArn', 'location' => 'uri', 'locationName' => 'DirectoryRegistrationArn', ], ], ], 'DeleteTemplateGroupAccessControlEntryRequest' => [ 'type' => 'structure', 'required' => [ 'GroupSecurityIdentifier', 'TemplateArn', ], 'members' => [ 'GroupSecurityIdentifier' => [ 'shape' => 'GroupSecurityIdentifier', 'location' => 'uri', 'locationName' => 'GroupSecurityIdentifier', ], 'TemplateArn' => [ 'shape' => 'TemplateArn', 'location' => 'uri', 'locationName' => 'TemplateArn', ], ], ], 'DeleteTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateArn', ], 'members' => [ 'TemplateArn' => [ 'shape' => 'TemplateArn', 'location' => 'uri', 'locationName' => 'TemplateArn', ], ], ], 'DirectoryId' => [ 'type' => 'string', 'pattern' => '^d-[0-9a-f]{10}$', ], 'DirectoryRegistration' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'DirectoryRegistrationArn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Status' => [ 'shape' => 'DirectoryRegistrationStatus', ], 'StatusReason' => [ 'shape' => 'DirectoryRegistrationStatusReason', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'DirectoryRegistrationArn' => [ 'type' => 'string', 'max' => 200, 'min' => 5, 'pattern' => '^arn:[\\w-]+:pca-connector-ad:[\\w-]+:[0-9]+:directory-registration\\/d-[0-9a-f]{10}$', ], 'DirectoryRegistrationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DirectoryRegistrationSummary', ], ], 'DirectoryRegistrationStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', ], ], 'DirectoryRegistrationStatusReason' => [ 'type' => 'string', 'enum' => [ 'DIRECTORY_ACCESS_DENIED', 'DIRECTORY_RESOURCE_NOT_FOUND', 'DIRECTORY_NOT_ACTIVE', 'DIRECTORY_NOT_REACHABLE', 'DIRECTORY_TYPE_NOT_SUPPORTED', 'INTERNAL_FAILURE', ], ], 'DirectoryRegistrationSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'DirectoryRegistrationArn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Status' => [ 'shape' => 'DirectoryRegistrationStatus', ], 'StatusReason' => [ 'shape' => 'DirectoryRegistrationStatusReason', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'DisplayName' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[\\x20-\\x7E]+$', ], 'EnrollmentFlagsV2' => [ 'type' => 'structure', 'members' => [ 'EnableKeyReuseOnNtTokenKeysetStorageFull' => [ 'shape' => 'Boolean', ], 'IncludeSymmetricAlgorithms' => [ 'shape' => 'Boolean', ], 'NoSecurityExtension' => [ 'shape' => 'Boolean', ], 'RemoveInvalidCertificateFromPersonalStore' => [ 'shape' => 'Boolean', ], 'UserInteractionRequired' => [ 'shape' => 'Boolean', ], ], ], 'EnrollmentFlagsV3' => [ 'type' => 'structure', 'members' => [ 'EnableKeyReuseOnNtTokenKeysetStorageFull' => [ 'shape' => 'Boolean', ], 'IncludeSymmetricAlgorithms' => [ 'shape' => 'Boolean', ], 'NoSecurityExtension' => [ 'shape' => 'Boolean', ], 'RemoveInvalidCertificateFromPersonalStore' => [ 'shape' => 'Boolean', ], 'UserInteractionRequired' => [ 'shape' => 'Boolean', ], ], ], 'EnrollmentFlagsV4' => [ 'type' => 'structure', 'members' => [ 'EnableKeyReuseOnNtTokenKeysetStorageFull' => [ 'shape' => 'Boolean', ], 'IncludeSymmetricAlgorithms' => [ 'shape' => 'Boolean', ], 'NoSecurityExtension' => [ 'shape' => 'Boolean', ], 'RemoveInvalidCertificateFromPersonalStore' => [ 'shape' => 'Boolean', ], 'UserInteractionRequired' => [ 'shape' => 'Boolean', ], ], ], 'ExtensionsV2' => [ 'type' => 'structure', 'required' => [ 'KeyUsage', ], 'members' => [ 'ApplicationPolicies' => [ 'shape' => 'ApplicationPolicies', ], 'KeyUsage' => [ 'shape' => 'KeyUsage', ], ], ], 'ExtensionsV3' => [ 'type' => 'structure', 'required' => [ 'KeyUsage', ], 'members' => [ 'ApplicationPolicies' => [ 'shape' => 'ApplicationPolicies', ], 'KeyUsage' => [ 'shape' => 'KeyUsage', ], ], ], 'ExtensionsV4' => [ 'type' => 'structure', 'required' => [ 'KeyUsage', ], 'members' => [ 'ApplicationPolicies' => [ 'shape' => 'ApplicationPolicies', ], 'KeyUsage' => [ 'shape' => 'KeyUsage', ], ], ], 'GeneralFlagsV2' => [ 'type' => 'structure', 'members' => [ 'AutoEnrollment' => [ 'shape' => 'Boolean', ], 'MachineType' => [ 'shape' => 'Boolean', ], ], ], 'GeneralFlagsV3' => [ 'type' => 'structure', 'members' => [ 'AutoEnrollment' => [ 'shape' => 'Boolean', ], 'MachineType' => [ 'shape' => 'Boolean', ], ], ], 'GeneralFlagsV4' => [ 'type' => 'structure', 'members' => [ 'AutoEnrollment' => [ 'shape' => 'Boolean', ], 'MachineType' => [ 'shape' => 'Boolean', ], ], ], 'GetConnectorRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorArn', ], 'members' => [ 'ConnectorArn' => [ 'shape' => 'ConnectorArn', 'location' => 'uri', 'locationName' => 'ConnectorArn', ], ], ], 'GetConnectorResponse' => [ 'type' => 'structure', 'members' => [ 'Connector' => [ 'shape' => 'Connector', ], ], ], 'GetDirectoryRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryRegistrationArn', ], 'members' => [ 'DirectoryRegistrationArn' => [ 'shape' => 'DirectoryRegistrationArn', 'location' => 'uri', 'locationName' => 'DirectoryRegistrationArn', ], ], ], 'GetDirectoryRegistrationResponse' => [ 'type' => 'structure', 'members' => [ 'DirectoryRegistration' => [ 'shape' => 'DirectoryRegistration', ], ], ], 'GetServicePrincipalNameRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorArn', 'DirectoryRegistrationArn', ], 'members' => [ 'ConnectorArn' => [ 'shape' => 'ConnectorArn', 'location' => 'uri', 'locationName' => 'ConnectorArn', ], 'DirectoryRegistrationArn' => [ 'shape' => 'DirectoryRegistrationArn', 'location' => 'uri', 'locationName' => 'DirectoryRegistrationArn', ], ], ], 'GetServicePrincipalNameResponse' => [ 'type' => 'structure', 'members' => [ 'ServicePrincipalName' => [ 'shape' => 'ServicePrincipalName', ], ], ], 'GetTemplateGroupAccessControlEntryRequest' => [ 'type' => 'structure', 'required' => [ 'GroupSecurityIdentifier', 'TemplateArn', ], 'members' => [ 'GroupSecurityIdentifier' => [ 'shape' => 'GroupSecurityIdentifier', 'location' => 'uri', 'locationName' => 'GroupSecurityIdentifier', ], 'TemplateArn' => [ 'shape' => 'TemplateArn', 'location' => 'uri', 'locationName' => 'TemplateArn', ], ], ], 'GetTemplateGroupAccessControlEntryResponse' => [ 'type' => 'structure', 'members' => [ 'AccessControlEntry' => [ 'shape' => 'AccessControlEntry', ], ], ], 'GetTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateArn', ], 'members' => [ 'TemplateArn' => [ 'shape' => 'TemplateArn', 'location' => 'uri', 'locationName' => 'TemplateArn', ], ], ], 'GetTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'Template' => [ 'shape' => 'Template', ], ], ], 'GroupSecurityIdentifier' => [ 'type' => 'string', 'max' => 256, 'min' => 7, 'pattern' => '^S-[0-9]-([0-9]+-){1,14}[0-9]+$', ], 'HashAlgorithm' => [ 'type' => 'string', 'enum' => [ 'SHA256', 'SHA384', 'SHA512', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'KeySpec' => [ 'type' => 'string', 'enum' => [ 'KEY_EXCHANGE', 'SIGNATURE', ], ], 'KeyUsage' => [ 'type' => 'structure', 'required' => [ 'UsageFlags', ], 'members' => [ 'Critical' => [ 'shape' => 'Boolean', ], 'UsageFlags' => [ 'shape' => 'KeyUsageFlags', ], ], ], 'KeyUsageFlags' => [ 'type' => 'structure', 'members' => [ 'DataEncipherment' => [ 'shape' => 'Boolean', ], 'DigitalSignature' => [ 'shape' => 'Boolean', ], 'KeyAgreement' => [ 'shape' => 'Boolean', ], 'KeyEncipherment' => [ 'shape' => 'Boolean', ], 'NonRepudiation' => [ 'shape' => 'Boolean', ], ], ], 'KeyUsageProperty' => [ 'type' => 'structure', 'members' => [ 'PropertyFlags' => [ 'shape' => 'KeyUsagePropertyFlags', ], 'PropertyType' => [ 'shape' => 'KeyUsagePropertyType', ], ], 'union' => true, ], 'KeyUsagePropertyFlags' => [ 'type' => 'structure', 'members' => [ 'Decrypt' => [ 'shape' => 'Boolean', ], 'KeyAgreement' => [ 'shape' => 'Boolean', ], 'Sign' => [ 'shape' => 'Boolean', ], ], ], 'KeyUsagePropertyType' => [ 'type' => 'string', 'enum' => [ 'ALL', ], ], 'ListConnectorsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListConnectorsResponse' => [ 'type' => 'structure', 'members' => [ 'Connectors' => [ 'shape' => 'ConnectorList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDirectoryRegistrationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListDirectoryRegistrationsResponse' => [ 'type' => 'structure', 'members' => [ 'DirectoryRegistrations' => [ 'shape' => 'DirectoryRegistrationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListServicePrincipalNamesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryRegistrationArn', ], 'members' => [ 'DirectoryRegistrationArn' => [ 'shape' => 'DirectoryRegistrationArn', 'location' => 'uri', 'locationName' => 'DirectoryRegistrationArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListServicePrincipalNamesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ServicePrincipalNames' => [ 'shape' => 'ServicePrincipalNameList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'ListTemplateGroupAccessControlEntriesRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateArn', ], 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'TemplateArn' => [ 'shape' => 'TemplateArn', 'location' => 'uri', 'locationName' => 'TemplateArn', ], ], ], 'ListTemplateGroupAccessControlEntriesResponse' => [ 'type' => 'structure', 'members' => [ 'AccessControlEntries' => [ 'shape' => 'AccessControlEntryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTemplatesRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectorArn', ], 'members' => [ 'ConnectorArn' => [ 'shape' => 'ConnectorArn', 'location' => 'querystring', 'locationName' => 'ConnectorArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Templates' => [ 'shape' => 'TemplateList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^(?:[A-Za-z0-9_-]{4})*(?:[A-Za-z0-9_-]{2}==|[A-Za-z0-9_-]{3}=)?$', ], 'PrivateKeyAlgorithm' => [ 'type' => 'string', 'enum' => [ 'RSA', 'ECDH_P256', 'ECDH_P384', 'ECDH_P521', ], ], 'PrivateKeyAttributesV2' => [ 'type' => 'structure', 'required' => [ 'KeySpec', 'MinimalKeyLength', ], 'members' => [ 'CryptoProviders' => [ 'shape' => 'CryptoProvidersList', ], 'KeySpec' => [ 'shape' => 'KeySpec', ], 'MinimalKeyLength' => [ 'shape' => 'PrivateKeyAttributesV2MinimalKeyLengthInteger', ], ], ], 'PrivateKeyAttributesV2MinimalKeyLengthInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'PrivateKeyAttributesV3' => [ 'type' => 'structure', 'required' => [ 'Algorithm', 'KeySpec', 'KeyUsageProperty', 'MinimalKeyLength', ], 'members' => [ 'Algorithm' => [ 'shape' => 'PrivateKeyAlgorithm', ], 'CryptoProviders' => [ 'shape' => 'CryptoProvidersList', ], 'KeySpec' => [ 'shape' => 'KeySpec', ], 'KeyUsageProperty' => [ 'shape' => 'KeyUsageProperty', ], 'MinimalKeyLength' => [ 'shape' => 'PrivateKeyAttributesV3MinimalKeyLengthInteger', ], ], ], 'PrivateKeyAttributesV3MinimalKeyLengthInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'PrivateKeyAttributesV4' => [ 'type' => 'structure', 'required' => [ 'KeySpec', 'MinimalKeyLength', ], 'members' => [ 'Algorithm' => [ 'shape' => 'PrivateKeyAlgorithm', ], 'CryptoProviders' => [ 'shape' => 'CryptoProvidersList', ], 'KeySpec' => [ 'shape' => 'KeySpec', ], 'KeyUsageProperty' => [ 'shape' => 'KeyUsageProperty', ], 'MinimalKeyLength' => [ 'shape' => 'PrivateKeyAttributesV4MinimalKeyLengthInteger', ], ], ], 'PrivateKeyAttributesV4MinimalKeyLengthInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'PrivateKeyFlagsV2' => [ 'type' => 'structure', 'required' => [ 'ClientVersion', ], 'members' => [ 'ClientVersion' => [ 'shape' => 'ClientCompatibilityV2', ], 'ExportableKey' => [ 'shape' => 'Boolean', ], 'StrongKeyProtectionRequired' => [ 'shape' => 'Boolean', ], ], ], 'PrivateKeyFlagsV3' => [ 'type' => 'structure', 'required' => [ 'ClientVersion', ], 'members' => [ 'ClientVersion' => [ 'shape' => 'ClientCompatibilityV3', ], 'ExportableKey' => [ 'shape' => 'Boolean', ], 'RequireAlternateSignatureAlgorithm' => [ 'shape' => 'Boolean', ], 'StrongKeyProtectionRequired' => [ 'shape' => 'Boolean', ], ], ], 'PrivateKeyFlagsV4' => [ 'type' => 'structure', 'required' => [ 'ClientVersion', ], 'members' => [ 'ClientVersion' => [ 'shape' => 'ClientCompatibilityV4', ], 'ExportableKey' => [ 'shape' => 'Boolean', ], 'RequireAlternateSignatureAlgorithm' => [ 'shape' => 'Boolean', ], 'RequireSameKeyRenewal' => [ 'shape' => 'Boolean', ], 'StrongKeyProtectionRequired' => [ 'shape' => 'Boolean', ], 'UseLegacyProvider' => [ 'shape' => 'Boolean', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 20, 'min' => 11, 'pattern' => '^(?:sg-[0-9a-f]{8}|sg-[0-9a-f]{17})$', ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 4, 'min' => 1, ], 'ServicePrincipalName' => [ 'type' => 'structure', 'members' => [ 'ConnectorArn' => [ 'shape' => 'ConnectorArn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DirectoryRegistrationArn' => [ 'shape' => 'DirectoryRegistrationArn', ], 'Status' => [ 'shape' => 'ServicePrincipalNameStatus', ], 'StatusReason' => [ 'shape' => 'ServicePrincipalNameStatusReason', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ServicePrincipalNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServicePrincipalNameSummary', ], ], 'ServicePrincipalNameStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', ], ], 'ServicePrincipalNameStatusReason' => [ 'type' => 'string', 'enum' => [ 'DIRECTORY_ACCESS_DENIED', 'DIRECTORY_NOT_REACHABLE', 'DIRECTORY_RESOURCE_NOT_FOUND', 'SPN_EXISTS_ON_DIFFERENT_AD_OBJECT', 'INTERNAL_FAILURE', ], ], 'ServicePrincipalNameSummary' => [ 'type' => 'structure', 'members' => [ 'ConnectorArn' => [ 'shape' => 'ConnectorArn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'DirectoryRegistrationArn' => [ 'shape' => 'DirectoryRegistrationArn', ], 'Status' => [ 'shape' => 'ServicePrincipalNameStatus', ], 'StatusReason' => [ 'shape' => 'ServicePrincipalNameStatusReason', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', 'QuotaCode', 'ResourceId', 'ResourceType', 'ServiceCode', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'QuotaCode' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], 'ServiceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'SubjectNameFlagsV2' => [ 'type' => 'structure', 'members' => [ 'RequireCommonName' => [ 'shape' => 'Boolean', ], 'RequireDirectoryPath' => [ 'shape' => 'Boolean', ], 'RequireDnsAsCn' => [ 'shape' => 'Boolean', ], 'RequireEmail' => [ 'shape' => 'Boolean', ], 'SanRequireDirectoryGuid' => [ 'shape' => 'Boolean', ], 'SanRequireDns' => [ 'shape' => 'Boolean', ], 'SanRequireDomainDns' => [ 'shape' => 'Boolean', ], 'SanRequireEmail' => [ 'shape' => 'Boolean', ], 'SanRequireSpn' => [ 'shape' => 'Boolean', ], 'SanRequireUpn' => [ 'shape' => 'Boolean', ], ], ], 'SubjectNameFlagsV3' => [ 'type' => 'structure', 'members' => [ 'RequireCommonName' => [ 'shape' => 'Boolean', ], 'RequireDirectoryPath' => [ 'shape' => 'Boolean', ], 'RequireDnsAsCn' => [ 'shape' => 'Boolean', ], 'RequireEmail' => [ 'shape' => 'Boolean', ], 'SanRequireDirectoryGuid' => [ 'shape' => 'Boolean', ], 'SanRequireDns' => [ 'shape' => 'Boolean', ], 'SanRequireDomainDns' => [ 'shape' => 'Boolean', ], 'SanRequireEmail' => [ 'shape' => 'Boolean', ], 'SanRequireSpn' => [ 'shape' => 'Boolean', ], 'SanRequireUpn' => [ 'shape' => 'Boolean', ], ], ], 'SubjectNameFlagsV4' => [ 'type' => 'structure', 'members' => [ 'RequireCommonName' => [ 'shape' => 'Boolean', ], 'RequireDirectoryPath' => [ 'shape' => 'Boolean', ], 'RequireDnsAsCn' => [ 'shape' => 'Boolean', ], 'RequireEmail' => [ 'shape' => 'Boolean', ], 'SanRequireDirectoryGuid' => [ 'shape' => 'Boolean', ], 'SanRequireDns' => [ 'shape' => 'Boolean', ], 'SanRequireDomainDns' => [ 'shape' => 'Boolean', ], 'SanRequireEmail' => [ 'shape' => 'Boolean', ], 'SanRequireSpn' => [ 'shape' => 'Boolean', ], 'SanRequireUpn' => [ 'shape' => 'Boolean', ], ], ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Template' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'TemplateArn', ], 'ConnectorArn' => [ 'shape' => 'ConnectorArn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Definition' => [ 'shape' => 'TemplateDefinition', ], 'Name' => [ 'shape' => 'TemplateName', ], 'ObjectIdentifier' => [ 'shape' => 'CustomObjectIdentifier', ], 'PolicySchema' => [ 'shape' => 'Integer', ], 'Revision' => [ 'shape' => 'TemplateRevision', ], 'Status' => [ 'shape' => 'TemplateStatus', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'TemplateArn' => [ 'type' => 'string', 'max' => 200, 'min' => 5, 'pattern' => '^arn:[\\w-]+:pca-connector-ad:[\\w-]+:[0-9]+:connector\\/[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}\\/template\\/[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$', ], 'TemplateDefinition' => [ 'type' => 'structure', 'members' => [ 'TemplateV2' => [ 'shape' => 'TemplateV2', ], 'TemplateV3' => [ 'shape' => 'TemplateV3', ], 'TemplateV4' => [ 'shape' => 'TemplateV4', ], ], 'union' => true, ], 'TemplateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateSummary', ], ], 'TemplateName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^(?!^\\s+$)((?![\\x5c\'\\x2b,;<=>#\\x22])([\\x20-\\x7E]))+$', ], 'TemplateNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateName', ], 'max' => 100, 'min' => 1, ], 'TemplateRevision' => [ 'type' => 'structure', 'required' => [ 'MajorRevision', 'MinorRevision', ], 'members' => [ 'MajorRevision' => [ 'shape' => 'Integer', ], 'MinorRevision' => [ 'shape' => 'Integer', ], ], ], 'TemplateStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DELETING', ], ], 'TemplateSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'TemplateArn', ], 'ConnectorArn' => [ 'shape' => 'ConnectorArn', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Definition' => [ 'shape' => 'TemplateDefinition', ], 'Name' => [ 'shape' => 'TemplateName', ], 'ObjectIdentifier' => [ 'shape' => 'CustomObjectIdentifier', ], 'PolicySchema' => [ 'shape' => 'Integer', ], 'Revision' => [ 'shape' => 'TemplateRevision', ], 'Status' => [ 'shape' => 'TemplateStatus', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'TemplateV2' => [ 'type' => 'structure', 'required' => [ 'CertificateValidity', 'EnrollmentFlags', 'Extensions', 'GeneralFlags', 'PrivateKeyAttributes', 'PrivateKeyFlags', 'SubjectNameFlags', ], 'members' => [ 'CertificateValidity' => [ 'shape' => 'CertificateValidity', ], 'EnrollmentFlags' => [ 'shape' => 'EnrollmentFlagsV2', ], 'Extensions' => [ 'shape' => 'ExtensionsV2', ], 'GeneralFlags' => [ 'shape' => 'GeneralFlagsV2', ], 'PrivateKeyAttributes' => [ 'shape' => 'PrivateKeyAttributesV2', ], 'PrivateKeyFlags' => [ 'shape' => 'PrivateKeyFlagsV2', ], 'SubjectNameFlags' => [ 'shape' => 'SubjectNameFlagsV2', ], 'SupersededTemplates' => [ 'shape' => 'TemplateNameList', ], ], ], 'TemplateV3' => [ 'type' => 'structure', 'required' => [ 'CertificateValidity', 'EnrollmentFlags', 'Extensions', 'GeneralFlags', 'HashAlgorithm', 'PrivateKeyAttributes', 'PrivateKeyFlags', 'SubjectNameFlags', ], 'members' => [ 'CertificateValidity' => [ 'shape' => 'CertificateValidity', ], 'EnrollmentFlags' => [ 'shape' => 'EnrollmentFlagsV3', ], 'Extensions' => [ 'shape' => 'ExtensionsV3', ], 'GeneralFlags' => [ 'shape' => 'GeneralFlagsV3', ], 'HashAlgorithm' => [ 'shape' => 'HashAlgorithm', ], 'PrivateKeyAttributes' => [ 'shape' => 'PrivateKeyAttributesV3', ], 'PrivateKeyFlags' => [ 'shape' => 'PrivateKeyFlagsV3', ], 'SubjectNameFlags' => [ 'shape' => 'SubjectNameFlagsV3', ], 'SupersededTemplates' => [ 'shape' => 'TemplateNameList', ], ], ], 'TemplateV4' => [ 'type' => 'structure', 'required' => [ 'CertificateValidity', 'EnrollmentFlags', 'Extensions', 'GeneralFlags', 'PrivateKeyAttributes', 'PrivateKeyFlags', 'SubjectNameFlags', ], 'members' => [ 'CertificateValidity' => [ 'shape' => 'CertificateValidity', ], 'EnrollmentFlags' => [ 'shape' => 'EnrollmentFlagsV4', ], 'Extensions' => [ 'shape' => 'ExtensionsV4', ], 'GeneralFlags' => [ 'shape' => 'GeneralFlagsV4', ], 'HashAlgorithm' => [ 'shape' => 'HashAlgorithm', ], 'PrivateKeyAttributes' => [ 'shape' => 'PrivateKeyAttributesV4', ], 'PrivateKeyFlags' => [ 'shape' => 'PrivateKeyFlagsV4', ], 'SubjectNameFlags' => [ 'shape' => 'SubjectNameFlagsV4', ], 'SupersededTemplates' => [ 'shape' => 'TemplateNameList', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'QuotaCode' => [ 'shape' => 'String', ], 'ServiceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateTemplateGroupAccessControlEntryRequest' => [ 'type' => 'structure', 'required' => [ 'GroupSecurityIdentifier', 'TemplateArn', ], 'members' => [ 'AccessRights' => [ 'shape' => 'AccessRights', ], 'GroupDisplayName' => [ 'shape' => 'DisplayName', ], 'GroupSecurityIdentifier' => [ 'shape' => 'GroupSecurityIdentifier', 'location' => 'uri', 'locationName' => 'GroupSecurityIdentifier', ], 'TemplateArn' => [ 'shape' => 'TemplateArn', 'location' => 'uri', 'locationName' => 'TemplateArn', ], ], ], 'UpdateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateArn', ], 'members' => [ 'Definition' => [ 'shape' => 'TemplateDefinition', ], 'ReenrollAllCertificateHolders' => [ 'shape' => 'Boolean', ], 'TemplateArn' => [ 'shape' => 'TemplateArn', 'location' => 'uri', 'locationName' => 'TemplateArn', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'FIELD_VALIDATION_FAILED', 'INVALID_PERMISSION', 'INVALID_STATE', 'MISMATCHED_CONNECTOR', 'MISMATCHED_VPC', 'NO_CLIENT_TOKEN', 'UNKNOWN_OPERATION', 'OTHER', ], ], 'ValidityPeriod' => [ 'type' => 'structure', 'required' => [ 'Period', 'PeriodType', ], 'members' => [ 'Period' => [ 'shape' => 'ValidityPeriodPeriodLong', ], 'PeriodType' => [ 'shape' => 'ValidityPeriodType', ], ], ], 'ValidityPeriodPeriodLong' => [ 'type' => 'long', 'box' => true, 'max' => 8766000, 'min' => 1, ], 'ValidityPeriodType' => [ 'type' => 'string', 'enum' => [ 'HOURS', 'DAYS', 'WEEKS', 'MONTHS', 'YEARS', ], ], 'VpcInformation' => [ 'type' => 'structure', 'required' => [ 'SecurityGroupIds', ], 'members' => [ 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], ], ], ],];
