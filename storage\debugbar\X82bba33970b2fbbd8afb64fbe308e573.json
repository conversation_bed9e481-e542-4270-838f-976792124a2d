{"__meta": {"id": "X82bba33970b2fbbd8afb64fbe308e573", "datetime": "2025-06-26 23:22:12", "utime": **********.369527, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980131.963288, "end": **********.369541, "duration": 0.4062528610229492, "duration_str": "406ms", "measures": [{"label": "Booting", "start": 1750980131.963288, "relative_start": 0, "end": **********.320619, "relative_end": **********.320619, "duration": 0.3573310375213623, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.320628, "relative_start": 0.35733985900878906, "end": **********.369543, "relative_end": 2.1457672119140625e-06, "duration": 0.04891514778137207, "duration_str": "48.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45030352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00228, "accumulated_duration_str": "2.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3465528, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.491}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.357027, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.491, "width_percent": 15.351}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.362528, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.842, "width_percent": 13.158}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6ImFmdWw0Y3Y2R08zSUJYNU84aUR1V1E9PSIsInZhbHVlIjoiZTAyUXJrQmN2UWQ3NEJ4NHFjY3FlQT09IiwibWFjIjoiYjkzNGQ5MDMyYTIyMjY0OGM2OWJlZTllMjkyN2JiMWY1NGZiYWQ2Yzg4MTk0MTRkYzM5MTQ1MThlNzBmNDZlNyIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1841871330 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1841871330\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-61735471 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-61735471\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-616523909 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616523909\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6ImFmdWw0Y3Y2R08zSUJYNU84aUR1V1E9PSIsInZhbHVlIjoiZTAyUXJrQmN2UWQ3NEJ4NHFjY3FlQT09IiwibWFjIjoiYjkzNGQ5MDMyYTIyMjY0OGM2OWJlZTllMjkyN2JiMWY1NGZiYWQ2Yzg4MTk0MTRkYzM5MTQ1MThlNzBmNDZlNyIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980129059%7C27%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9FdGVTaHZDYVRhRDRXQjVoeUdKTHc9PSIsInZhbHVlIjoicTAzU1htY1dTVUYrbWpaL0sxeTdEUVg1UGVxS1hhQ085SzNkU0llVHc3a1M5ZURPY3JiYlFOYlhCZVRwU3FTSTRzVlBPWXU2T0NZUit0SytWUnFwS0I2WllVdFdMZXozOHNLTkdVWTA5alg3Q1I5T3M5emMwMVhrVXk2cFVJMzZwb0tmNEl0Sk9OUDNNQlJTRlpMcW5zK2FiOGVCdEZGVlA0c0FqU2EvUDNXNXF5Vk55MFpvT2gwazB2akt0N0Uyd1ZHMHRWc2ZUZDRkTm9uQitRTTZQL01naGo4S3F3aUovaS9qcjhzOW1KdnVIV2dFMW5SVTM5SnJjdFFhWkl4cVVodXE4WEdRN2RDTVpkMGd3N3pGa2tiS3lQSHFYZC9TRDhlSC9JVFM5clpzeDRFWjRIRTgxM0VkZi90czFVa0EybzhnbkZNYjhma24zcE82MDhKWXZGeFNrMDN0NGRuVE5UTlFRYjlPSVh4R2k4V1d0SVFHaElRTEFaeFBXYWlzN0ZFRk9EQ3FSR2JzRFVGME04Vm9mdS9zZjJhYVpFZjJWY0k0d0JwcFBqcVRUK29TY0U5Sm9xaldEUXltdVZocXkySW9DZTlOOUhtQUw2UVlRaGpQM0NaT0M1SXZjZmNZa1RxZzZzaFd0TTg2cUFuWnQrS0wrdGtoMk9GOGRhUjMiLCJtYWMiOiI1NmI1MWM0MjZiZjkxY2NkNTgzZmJjODFmZDdiMmVkOTQ1NTcwZjlmMzc3M2ZmNzA5ZWQ2YzM0YmQwNjc1NDU3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkkxVHpWVkxVMU9tK1c5bjhiaDBHYVE9PSIsInZhbHVlIjoiaERURVZiQmFKSVV5bTJ4SU95MlROaGxzNzFhVHVUdm9QQ1lPdkRnSURZNFdxZ0pNbG9LNjl1cGQ0Y0hVNnhsNWtBWFMzV1hTb0tYekJ2RnlGeDNQQU1ObEdrMXplWHRCY1hicEVSZk5JdFNZZ1NYTTkyODlwMFgxK0VnS0FDeHdUVXoxVGprZmhrUkh6YXpYMm9Rc1BiK0FEcU92ZVRGOVY3L2FYY2xLanlUaFdFTUtudWFhVEtHa0Z4cGJjQ0hUMVUyb3ZWd0l3Zjh0VUxwbVJ2TDVhV3FyQmsyQ280L1hHY3dIWjZuTW96OUdPaVhOcFpybTJneHlWTHVGbUZtUDJXUThjTk1sRktXeVd3OUo3eWw0QVlnWW5FeG5wT1BKYlBRc0J3bG5lL3hXODJ6RWRQN1FWUEJodTVwcVpDekg1djh5NUdieHNSYnNTTVJFd01oTkZVVWFWVjEwOEg3c2w1NzRPNTJRbjA4R0o5TFRmUTQySHRxOHlNYlhmSW9PN3ZkYlBKcjZ4WU9vcG0vVVhXMk1xc0ZBMDJaMVZqSmRuZFpRWE0xVTdRZEZQVE1sTWtYdERkQjk3TzNhR3UzQ3pGeStVNWMrY2wwK1pmQ0padS9xZkIyRVVDL1dwWG1RWXRvNGZ2MVhUUFozbkhKYm1ib3RkY0QvNkFJWGdVMnciLCJtYWMiOiI1MTdlYmY4NjJhNThmZGFmYmY0YzViOGM3OWZkZTQ5NWZjZDU1Zjk4M2QzZDgwMTJhN2U0NDM5ZDY5Y2RjOTY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-633193376 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:22:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJ6MDlOaVR3Q2ZjMlYzSVRveUxDMGc9PSIsInZhbHVlIjoiY1VsOWR2YUEvL3pyb2lhSHdQcXR0WStrTWlNOXZxd09UM0ZGc21TblAvUXNUL2hZWW5OWVpWSmFwdnRuSFhUNUNVNVZaelV1dzE0NEt5Q0pZTER4UXprYUJzcUpTRVJPYkxEVXd4LzVQaWZ4ejJTRThLd2RXT0lpRWNGaStqTGwwV3pmaGNUTnRmK01hK3NrSERiaHR3aUttSHNoYnM0L29kTzhQNU8xdXk4MnRqc0tLczZESXJYVFRBYmpxSzQ2RTFwbEJtZzVhMXpUTXNZOWxaVlhOWm1XVUJoeWswNWo0TU1kdEVTb252bFNCMUFZSXJQN3RMTUZ5bSt0NGNpUk5kZFd1cTNlSUw2cUEzSElWeVQva0o4U0RkUi9GT0lvbGRwVU9kbm1OZnNJRzVwRVNsSkEzL1pZa01HRS9XM3liMWRveDFnZVU3TnBVZ2JtMkR1RnlNUEVWaWNtRVRjQTJhclFXdkdoN1pHTEpESy94RVB2R3FFL0xYbzMrbjI2U3ZTQ1piVnpnT1c5YVV4aGwwRitHNU1ob1ZYNjg0bHV0Uit2MzR1VGNRMk5HQVhHNnI2NnhwU3Y0UXRadHBhM1AyYWkxN3MyQlJEdUdtUlhBZHVRUmN5RERibHRrRGNIbDV2NUtjMWVoYXUxN21ybStHRUtzaUVjWkhyWTVEL2UiLCJtYWMiOiJjNDE5NDg1YzI0NjQ2ZWU3NTc0ZDgwNGVjMWUwOGU2N2Q3YjBkZTI5ZjQ1ZGEzODBmYmE3ODFmNzUyZmRjNGVhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imc5R3Zta1N2UWRBTmxPN3dMVlViOEE9PSIsInZhbHVlIjoiTkZCbmduZ1hKNUI1Zk1zZDNsYmpML2hLeFFGSnlUMzRVd1c0N2VlRkQ2VjBRK0xqRVA2cnZLNTkwNWhyeVdJQjdnMGhiSTVOS2JObjB4aDNwNnQ3WVNDWmFNamIvQWw5aVN6VWMxSVRlcXZRWXZGSlRjS2wwcElJSExWbm9lK2NSV0RzaHJkRGxZdW42bVN5ekZoYmVrd0N2eVkxMVZBSTJ2YUU4NG81TTI0ZmNZUzVpSHUzY3dGazduNEJQd1JlMWd1OWJELzZjcS9nQ1JqOHRTaDBITkp0UW1yRzg2MmdqVTc2NURuRDV4WTIzYVBvcHo4UVpaa1V3U2RCeDBJelAvWGF3OU1RNW41L1pLeGlMZXlLdlVYK082azJoYjVKRGU5K1RQb1pOUHBHU3J1WHFTWFp0UjZBaXR2bS84Vmt5d1dlWTJmeTY2TXdEMi9NSzljMVFuTmlHcmUvZEN2RW01NWF4Q0Z4NDBzbi80dWZiaUkwaGpYbGt6UVZ4M1c1enlvaXhmUndNbDFjMSs4czlyWjNZQnZHWFUwRkVZN3RObythL1J5SFMrUkFERTUxa1llUVFlbkVrck5RUFl1VE5ORkJpUHc3dDZSZmtsZUU1T2h1WERaQ05pUUNDVSt0OWZIemt4Nm5VL3ZQUVBWdVkvdWMvMTZTSnR6bzdxenUiLCJtYWMiOiI1YWQwNmFkYjU0MmM0MDU2OGQ3YzJhM2QyOTMxNGY1ZGY1ZmQ1Mjc5YzkyMDU0NWI3ZmIyZDI0ZmQwNjgxMTE2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJ6MDlOaVR3Q2ZjMlYzSVRveUxDMGc9PSIsInZhbHVlIjoiY1VsOWR2YUEvL3pyb2lhSHdQcXR0WStrTWlNOXZxd09UM0ZGc21TblAvUXNUL2hZWW5OWVpWSmFwdnRuSFhUNUNVNVZaelV1dzE0NEt5Q0pZTER4UXprYUJzcUpTRVJPYkxEVXd4LzVQaWZ4ejJTRThLd2RXT0lpRWNGaStqTGwwV3pmaGNUTnRmK01hK3NrSERiaHR3aUttSHNoYnM0L29kTzhQNU8xdXk4MnRqc0tLczZESXJYVFRBYmpxSzQ2RTFwbEJtZzVhMXpUTXNZOWxaVlhOWm1XVUJoeWswNWo0TU1kdEVTb252bFNCMUFZSXJQN3RMTUZ5bSt0NGNpUk5kZFd1cTNlSUw2cUEzSElWeVQva0o4U0RkUi9GT0lvbGRwVU9kbm1OZnNJRzVwRVNsSkEzL1pZa01HRS9XM3liMWRveDFnZVU3TnBVZ2JtMkR1RnlNUEVWaWNtRVRjQTJhclFXdkdoN1pHTEpESy94RVB2R3FFL0xYbzMrbjI2U3ZTQ1piVnpnT1c5YVV4aGwwRitHNU1ob1ZYNjg0bHV0Uit2MzR1VGNRMk5HQVhHNnI2NnhwU3Y0UXRadHBhM1AyYWkxN3MyQlJEdUdtUlhBZHVRUmN5RERibHRrRGNIbDV2NUtjMWVoYXUxN21ybStHRUtzaUVjWkhyWTVEL2UiLCJtYWMiOiJjNDE5NDg1YzI0NjQ2ZWU3NTc0ZDgwNGVjMWUwOGU2N2Q3YjBkZTI5ZjQ1ZGEzODBmYmE3ODFmNzUyZmRjNGVhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imc5R3Zta1N2UWRBTmxPN3dMVlViOEE9PSIsInZhbHVlIjoiTkZCbmduZ1hKNUI1Zk1zZDNsYmpML2hLeFFGSnlUMzRVd1c0N2VlRkQ2VjBRK0xqRVA2cnZLNTkwNWhyeVdJQjdnMGhiSTVOS2JObjB4aDNwNnQ3WVNDWmFNamIvQWw5aVN6VWMxSVRlcXZRWXZGSlRjS2wwcElJSExWbm9lK2NSV0RzaHJkRGxZdW42bVN5ekZoYmVrd0N2eVkxMVZBSTJ2YUU4NG81TTI0ZmNZUzVpSHUzY3dGazduNEJQd1JlMWd1OWJELzZjcS9nQ1JqOHRTaDBITkp0UW1yRzg2MmdqVTc2NURuRDV4WTIzYVBvcHo4UVpaa1V3U2RCeDBJelAvWGF3OU1RNW41L1pLeGlMZXlLdlVYK082azJoYjVKRGU5K1RQb1pOUHBHU3J1WHFTWFp0UjZBaXR2bS84Vmt5d1dlWTJmeTY2TXdEMi9NSzljMVFuTmlHcmUvZEN2RW01NWF4Q0Z4NDBzbi80dWZiaUkwaGpYbGt6UVZ4M1c1enlvaXhmUndNbDFjMSs4czlyWjNZQnZHWFUwRkVZN3RObythL1J5SFMrUkFERTUxa1llUVFlbkVrck5RUFl1VE5ORkJpUHc3dDZSZmtsZUU1T2h1WERaQ05pUUNDVSt0OWZIemt4Nm5VL3ZQUVBWdVkvdWMvMTZTSnR6bzdxenUiLCJtYWMiOiI1YWQwNmFkYjU0MmM0MDU2OGQ3YzJhM2QyOTMxNGY1ZGY1ZmQ1Mjc5YzkyMDU0NWI3ZmIyZDI0ZmQwNjgxMTE2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633193376\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1820465589 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6ImFmdWw0Y3Y2R08zSUJYNU84aUR1V1E9PSIsInZhbHVlIjoiZTAyUXJrQmN2UWQ3NEJ4NHFjY3FlQT09IiwibWFjIjoiYjkzNGQ5MDMyYTIyMjY0OGM2OWJlZTllMjkyN2JiMWY1NGZiYWQ2Yzg4MTk0MTRkYzM5MTQ1MThlNzBmNDZlNyIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1820465589\", {\"maxDepth\":0})</script>\n"}}