{"__meta": {"id": "Xf8ec0d507871ee24c2afd6bd33f6e39f", "datetime": "2025-06-26 23:17:47", "utime": **********.79469, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.283219, "end": **********.79471, "duration": 0.5114908218383789, "duration_str": "511ms", "measures": [{"label": "Booting", "start": **********.283219, "relative_start": 0, "end": **********.705261, "relative_end": **********.705261, "duration": 0.4220418930053711, "duration_str": "422ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.705274, "relative_start": 0.4220550060272217, "end": **********.794712, "relative_end": 2.1457672119140625e-06, "duration": 0.08943796157836914, "duration_str": "89.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029728, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02778, "accumulated_duration_str": "27.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.738099, "duration": 0.02649, "duration_str": "26.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.356}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.777114, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.356, "width_percent": 2.556}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.784273, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.912, "width_percent": 2.088}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1679801928 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1679801928\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1054536976 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1054536976\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1766339041 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766339041\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1657524671 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979775999%7C9%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlMzODRjdDZLYXdONGxIdjJwOFdDc2c9PSIsInZhbHVlIjoiaEI2ZUV2b3hJVERqTTlFQjZWc1A0VGpPUytsVHIvdXBjWUZhbnkwVTI3bkx4MjB4ZVFjYkZHTE9nYnRaL2RJWktaS0orNUhpWWVjM2cxVFg0eVR1VGdDcFl1MEg4Z09veTF2NmlFd21lODZLUXVZdFNZMFpWUmtjZWtsbGxIWEpWdk1peUFNeXcrSXlzN011VjYwYVpzQVNpMWU4ZXdsdWh2ZHN4ZFpnempobmRQQmVrQXlPcTkzb0ptSnRGTTZ2U29acmhpOENNZEhFVWQzKzA5REFqMDVXY3EwOXFHcDhHUnMrYm01TFVXNEhxTU9JVnZhUXlaNTd0TFZmc2JobGVORkowMnRSWEFEeUVLWVA4WWFpOENFR3YvTG45V084MzBSSUowT3FnaThsNFJac3N5VTY3Wm9zVGxRbGVMb0pBRUNlaisvTjZuSVFvbndLdDNNQlFXYnVYbnhmazhwakYvR3AyNGlKREo4L2ZReXJtNzJmVmRITGZiNlVya2pCbDRBai9QcXBzcURXZDVaVEdpZDJiclptYVFIVkVKQ1FoSG9VSE80dDVYL3V4RHh6YW1rUmU3RkxDamd6aDBMSWdZYlRLaTQzTVJ1dGN5TzNHaFhaQ2ZrVmE4bjNNMm9SVFhkT29MZ3BIYUlNSXF3NE5YQjBGRVhRaytOZzR3RXEiLCJtYWMiOiJlYzM0NGE1MWZkMTUwMjcwM2VhZTQ3ZWE5Zjc0MTEwMmE0OTBlYmZlNDRkOWZiYWJhMjg3YTU1MGM3Mzk3NzI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdZbStzVmtVRVYyK1lDMHAxUTdKQkE9PSIsInZhbHVlIjoicUg1VWNOSXhQM1V6dkZkNk5sc2V2bEp3RnlMdjFGQVhFQ0JoV2FDQnBLYUFCMThsRE5aKzRFZmVZSXFzSTNncjdHVXVpMnJSZHBKMzVlTHlMV1hlTDRpbGl6L0VsSmQ0V2NJQ0ZuVnFWOVpESDh5YzVvQThGUHJSUTdHOWllckR1ZUVUbmd5M2UvN1RoQlRlTTdpTlQ4ajROK1VTbTZBZjllY0ZCbGdaRzdlb2JRWkVYWExSZSs5U3ozYmRzeFFpNjdTKzZYVTZiSUpNemdZdHp4MjJ0d3l5TEdjYUFwYjFKQUJQd3h0TXZVeEZPZ1hranRiQ0JMdSt6YlBST0NLRVp0a0VKVm1oSHZRakVRU2RGL2t6NjdKQzNNRXFDZnBDQTJXTEdOVndya2pPRlRyWGtCTitGQ1BUYjgvVkZEYmEyVFpCVjdkR1F0MEJERVFZWmJ5VStzVUxsV2JVVkpCQjk3QlRRbW9OZW1mOTlBVzc1VzlRczJLeUY0ZXVSaHpiQ3lJeTFscnNUK3F5YitPaC9qTzRybjhuVUptUVVyTXFkL0FpSjl2d2krNzNkY1FCWUFmZk4yWnpkVzZka1oyUS9hNnZDcUd1dXdMR0pmaHd4Q2dPVENaekczOVNvZmpEVDBsWWNiVjNiYktmK3laNkhVYWtkQVpSc1hYNVZzRGoiLCJtYWMiOiJkNTgyMDIwNmU1NTQ5MzY4YjQ0MTk2N2YxNzI3Njc1ZGFhMzgwMTMwYjBkZWQ2Yjk3YjJjMWIxYmFmMDJmZmFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657524671\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-358510939 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-358510939\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-645306821 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:17:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRCaUN4OHdlUkExeXB6S0pxQW9aV1E9PSIsInZhbHVlIjoiSUlFMXo4Vjd3N0dhUzZkTEhTUkF5UDFvM0FHQmoxTmZ0ZmRvbzBNanNCWmtqYStwanByaFhmQUY5Qml6YmhRdlFXM3VjN3RBMW9haGpsUmhNRFMzQVhyc3NRc08zRjRUejJLaW90RG1FQUNIT1JZanZ3SnE0aTBjVVFnNFJnSTc1SVVMdFpQUHk4OFVidExkSVRKS0dNZ1pGMlF2bUN1RzBRKzNoSk1ja0xaaDJkVHpRMVVHbVplb1Nacld2OUlEREpLYWZoZ054VkY0UmlxMUN2Wi9HRzMvNUM4WndkRVBaeVZ6NHE5OHRqTEsvTE52eDd0a3JMZEJuVlR5L0V5dVdRVjczcjUrUVdOaHJCK2RKU25VT1M3eUozMC9tK0xDbnQwQUZlVUtKL0xwR0V1RkhEMlBYRGg2UUJCK0pGeFVsWG04dVZxQngyQytQVmFCM1pkY2pydkxCNzNCUk80Y1FVNnVHM2pxK3oxVGtERnk3OFR0OFJhc1JkWDdiV2RCL05BSUtFK1FYclUrQ3JYUjdINjQ1TmtlWmtqQTJEZUU0TnRHekhIcTBmTTF2VE00Mi9NWlVDZzdlRVZzd0dsU01BOUdDSXZ3WmR6K3FJSFlDcFRWckN1MFNQWitPa3pTVmczU2xJazZOUTBQdVFvbVZEWXk5cndoS3N0dVA2OEYiLCJtYWMiOiIxYjIxNzg2MTU1YWQ3YWY5OTUwYTZjZTZiYTE0OWZjMzRhZTdlNTJiOGM1OWZlOTFiNGY3NzA0YTRhNDhjN2Y5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:17:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpQTW00TnlnM3MrUUR0K1BwanhiWGc9PSIsInZhbHVlIjoiak14djlFa3FzVGQ3VFV0UHhuL3l2ZWtwdmVwSmxDcmhEd0lmb21EWXZPYlpjNU5lN3NjNjhQMllMdGlWSG9QTmxFYk5icDdqQTlDSFBxUk1MdksrWGxwdGQ0dnpGOXdMNVZSWXhDK09LQ2ZMUFVtKzFKazRabVphNXBpejlZSytOVExwUklLa2RPSHpjR2UvU25qNktoQit2MDYxTW9OUDJ4ZDZVTWlPa0JaWW5XRzR6dWtUUCtSODVMQWhZQU1kSFZOWmRERjBoQ0cwVExnWHhmbGJyaVhSSVJkOXdJOUdDenBHdlZmQ1BQZjRyN1dObDRrRGI4ZlJRU2IwMDFoMHBNaE9GRis0dDRkSDNPTUxuWWlyYmNZWVMzVlRlV052OVIzUlhNNFJYSXNqcGJuNGVJVTNqNHk1cFVmK2NYTXRCbmxBYU4wb1ZUaGFJeFFmdndtRTg4Sk13dDJWRU51cnpwakNRai9DQ1B6SkxRU3hPeFhxVm1WVW9ETmFDMkM5NTd0by9CL01Ed05WaXh1QzlRTGQ2bk5WUDVqTktuRGRoQ3FmeXZhamVucCtnM29TYzRDR01FYlQwSW5GYlNVWGp6cmphbEpWWXpidlh1RC93d09za0dSZ1krNTVDTnNFR3hTVC9OSjVrMUpwbjFVbDVINFA1b0ZxTktIOTV6cSsiLCJtYWMiOiIyMmQ1NTNkYWE3NjAwMGE2NGYyMWMwNDY4ZDI5MDI4ZmQ2M2JlNTlmZGEwYmYzZTE4NDM5ODk4NWQ3MTM2NzBhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:17:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRCaUN4OHdlUkExeXB6S0pxQW9aV1E9PSIsInZhbHVlIjoiSUlFMXo4Vjd3N0dhUzZkTEhTUkF5UDFvM0FHQmoxTmZ0ZmRvbzBNanNCWmtqYStwanByaFhmQUY5Qml6YmhRdlFXM3VjN3RBMW9haGpsUmhNRFMzQVhyc3NRc08zRjRUejJLaW90RG1FQUNIT1JZanZ3SnE0aTBjVVFnNFJnSTc1SVVMdFpQUHk4OFVidExkSVRKS0dNZ1pGMlF2bUN1RzBRKzNoSk1ja0xaaDJkVHpRMVVHbVplb1Nacld2OUlEREpLYWZoZ054VkY0UmlxMUN2Wi9HRzMvNUM4WndkRVBaeVZ6NHE5OHRqTEsvTE52eDd0a3JMZEJuVlR5L0V5dVdRVjczcjUrUVdOaHJCK2RKU25VT1M3eUozMC9tK0xDbnQwQUZlVUtKL0xwR0V1RkhEMlBYRGg2UUJCK0pGeFVsWG04dVZxQngyQytQVmFCM1pkY2pydkxCNzNCUk80Y1FVNnVHM2pxK3oxVGtERnk3OFR0OFJhc1JkWDdiV2RCL05BSUtFK1FYclUrQ3JYUjdINjQ1TmtlWmtqQTJEZUU0TnRHekhIcTBmTTF2VE00Mi9NWlVDZzdlRVZzd0dsU01BOUdDSXZ3WmR6K3FJSFlDcFRWckN1MFNQWitPa3pTVmczU2xJazZOUTBQdVFvbVZEWXk5cndoS3N0dVA2OEYiLCJtYWMiOiIxYjIxNzg2MTU1YWQ3YWY5OTUwYTZjZTZiYTE0OWZjMzRhZTdlNTJiOGM1OWZlOTFiNGY3NzA0YTRhNDhjN2Y5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:17:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpQTW00TnlnM3MrUUR0K1BwanhiWGc9PSIsInZhbHVlIjoiak14djlFa3FzVGQ3VFV0UHhuL3l2ZWtwdmVwSmxDcmhEd0lmb21EWXZPYlpjNU5lN3NjNjhQMllMdGlWSG9QTmxFYk5icDdqQTlDSFBxUk1MdksrWGxwdGQ0dnpGOXdMNVZSWXhDK09LQ2ZMUFVtKzFKazRabVphNXBpejlZSytOVExwUklLa2RPSHpjR2UvU25qNktoQit2MDYxTW9OUDJ4ZDZVTWlPa0JaWW5XRzR6dWtUUCtSODVMQWhZQU1kSFZOWmRERjBoQ0cwVExnWHhmbGJyaVhSSVJkOXdJOUdDenBHdlZmQ1BQZjRyN1dObDRrRGI4ZlJRU2IwMDFoMHBNaE9GRis0dDRkSDNPTUxuWWlyYmNZWVMzVlRlV052OVIzUlhNNFJYSXNqcGJuNGVJVTNqNHk1cFVmK2NYTXRCbmxBYU4wb1ZUaGFJeFFmdndtRTg4Sk13dDJWRU51cnpwakNRai9DQ1B6SkxRU3hPeFhxVm1WVW9ETmFDMkM5NTd0by9CL01Ed05WaXh1QzlRTGQ2bk5WUDVqTktuRGRoQ3FmeXZhamVucCtnM29TYzRDR01FYlQwSW5GYlNVWGp6cmphbEpWWXpidlh1RC93d09za0dSZ1krNTVDTnNFR3hTVC9OSjVrMUpwbjFVbDVINFA1b0ZxTktIOTV6cSsiLCJtYWMiOiIyMmQ1NTNkYWE3NjAwMGE2NGYyMWMwNDY4ZDI5MDI4ZmQ2M2JlNTlmZGEwYmYzZTE4NDM5ODk4NWQ3MTM2NzBhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:17:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645306821\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-932698280 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-932698280\", {\"maxDepth\":0})</script>\n"}}