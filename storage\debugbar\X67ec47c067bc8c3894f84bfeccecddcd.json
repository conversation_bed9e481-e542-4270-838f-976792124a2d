{"__meta": {"id": "X67ec47c067bc8c3894f84bfeccecddcd", "datetime": "2025-06-26 22:43:13", "utime": **********.888526, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.424585, "end": **********.888541, "duration": 0.4639558792114258, "duration_str": "464ms", "measures": [{"label": "Booting", "start": **********.424585, "relative_start": 0, "end": **********.801358, "relative_end": **********.801358, "duration": 0.3767728805541992, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.801366, "relative_start": 0.37678098678588867, "end": **********.888542, "relative_end": 9.5367431640625e-07, "duration": 0.08717584609985352, "duration_str": "87.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043912, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02638, "accumulated_duration_str": "26.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.837832, "duration": 0.02524, "duration_str": "25.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.679}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8729908, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.679, "width_percent": 2.161}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.880008, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.839, "width_percent": 2.161}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IjdrU1g4bmx1cWNiUGdHS2lHZ2NUWEE9PSIsInZhbHVlIjoiTTNVaHl2eVFHQzhjVFAvMXFvZEVyZz09IiwibWFjIjoiYTI1NjY1ZDdmNWFlZmZhZDZlNDQwYTQzMDgxZDI3ZGI0Zjg1MjA1Y2RmMTZhMzEzNDU4NjkxMGVmNWJmZmM5YSIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-433460948 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-433460948\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1889791139 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1889791139\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1176045342 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176045342\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1525550156 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjdrU1g4bmx1cWNiUGdHS2lHZ2NUWEE9PSIsInZhbHVlIjoiTTNVaHl2eVFHQzhjVFAvMXFvZEVyZz09IiwibWFjIjoiYTI1NjY1ZDdmNWFlZmZhZDZlNDQwYTQzMDgxZDI3ZGI0Zjg1MjA1Y2RmMTZhMzEzNDU4NjkxMGVmNWJmZmM5YSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977781273%7C17%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjM5eTdOdEkwZ0V4MWZidnBUV052dFE9PSIsInZhbHVlIjoiaXJwZXdiV3JMTFM0Z2dZMitBSjY2OU9NcThudHpJb3M2VFlhNUVWcXFwV3FiNVo4M3g1Qko5dWxNSUQrUlRJL1gvQjNJa3UycVp6RlpjVDZuVnlNeTNLem1idXJoLzV1cm9xbHp6Tm9vMzZVYzNuWkVXbkVzazViNjVURTJUU3JJUG9iVGR4ZEJwMkUrS2NtdldLbm83elRVWUNmdEx4emRGR1J2cVhBUWZBK0tKbHB3K1I1UzBVNjlKTVYrWms3UENTQjFFekIrWllBZUhWNzZEUTgxelhKaGtobkdLUzhhd245V3J2aTJGeU5LUEpxQmdXMlVPZ3BtbWlxNURQZjdlaDRlVlAxaEJ1YThTUXlZaWVkMm9iMVRqK3VjdlZIcHp0R0ptb3RhdnMvdWkrRzV6TWptdUxWUGliUU5hdUxTRy9BVmoyb3JLVzY0ZTN5ZCt0cExBVkdsczlnYmQ5cVZPcklHc3RXMTV0eWlZN0xkaXpPcW1ieGVvWlRvRWg4bVJvemhNMUkyL3FHN1ZZS0ZhV0pnUEE3MkpxbTZaK2RVQ1Q2bFRhbHAvVFRxUzdoK0tJTkVBYWdKR29OamJQSERlOGpIbHdpbmJGUEtxVzZHVnlLVk4xTklIekVFekVVUGRUdzBCMG16VE4raENRSVBXcXk4S0dQdmx6K2g4dFoiLCJtYWMiOiJjZjE3MmU4ZDRhMDhmMjIwNDhmMjI3ZTkxNWUwYjNmNWFkNTQ0YzAxYTE3YTkwZjhmOTJlMjRhYjI4NjVkY2MyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlNRZk5vdTRWOCtzY2ZFV0M2OEx5L2c9PSIsInZhbHVlIjoiUHRVelNqeHRQN1BXcUxCOFN0QVZkOER6MGhWTndZQllFd3Fabll1NWJwVlNuRUVOS0NVeEp2a3M0VzY5ZmtqZGFzS2dSd2JTK2dPaEFZaDhhc1Q3UCszREdkQXpkREZIM1JuSGdBaFRCUEJUVGZuVGg3UWV1QmRVNktIVDVJQ1ZWbkNyd0cxaUVNV3ZaTjUrMmZMQWswWEZpVmdyalk4Vkk1L1BnbU93Zm44YU5yN3A0Q3Jvcm9aSTBqdDNXbk04WWI3cEJkMGZMU0J4SllVVnRHUElzRk9BN21BKzhVNGhHVy9UdHpBbm5ZSUl6dDJhcTNmenltTm9JdyswMmo1UmV6ZFRPQUxiY28vMUVNN0RleElRRmcydURPMmxHT0lDM3NYMHFJN0VZbFVZeTF2VGpuTjdXbWVHR0VncFJyZ2VndzZPeU5LL1RuSHlCeWtHeURwTkpvbXJVSzBreDRzaFptU1ZOa2lYNkIwVVdQWm53amt1YWQ3RDVpcURjK25TRjhVTExVdU0wTUs3a3hYaDFQQU5JUjRKWnJtblFpTzFiTTdiWk9tSk9pZUU3L0dqTk9iKzNVc2Y1YW9FaFFvTFVPSktNNFVDV2hOMHovaGFpRTQvZlBvRkxnQ1RpT0FKUHNxMS8zUGMyQUYrSVhnc2x4d1NOeVJVQjhocG1RK1UiLCJtYWMiOiIzYmRiZTVjYWE3YTZhZThlMjVmYWE5ZjY4NTdhMWE3ZjgyMGVmYTdjNmZhN2M5ODZmMDZmZTNmZTg4MzQyNDUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1525550156\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1142499100 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1142499100\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-646979834 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:43:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxkSUJRYjNXdzl1dXlYVml2QmNXWEE9PSIsInZhbHVlIjoiVHF4ZkRCbWQvZGpHUW1KbEhmS0ZwOS9aMlRVMndiYTV0TkI3dC9aczhWbjJLKzlhazJXcXVQaGFQVVNhUnhJY2l4MDJDeERKRVJXLzhoRDUyUGt6Qk5pOWpOeU1sdi9vMTgxLzl1Zlk1d1FLSG5oRHlTTlljQWdza2xFYU01c3FVRjZIWXJGbjRjUW1tOS8yQU5ZRVdZL3lGc2E3SHJxbnhuRDBabHBJZDRFWEE5a01CWHUyVUExakxSdVd1ZDdDOVZmc2NjdzFsM3FjNmpqK25YSHVNQVNKSXhVQ0NsRVYwNVJLTDhzN29sR2pvL2pBNjNvOFhpOFFVRTZpSzl1RUxEZGxvVzhCZWUzcU5CMkpaOTVoWjBuQWJsNHpnN2UvQmFGUU9PMDlqT3ZtWHFkSEtoaThGUkR5L1JZaHZQZlFUanNUVi9HdytCQ2VEREcwbXcrVkI3S01JTGhIcW42Q29PMHp5RmVISGVWemV4RHIrVWFBS1libkVXaUUwZWJQRXUrK1JjUTNnQ0pUUXhVZFM0SjRrZ3RLWFZveFkxU0QzM3Zxb0xVbHJWc0hJZ0pEb2Z4enN5cFNoRWQvdFRKU0QwUmpWaUdyUjc4NkZMR3BNL2RhOHd1b01qVzY4WDhsNm5VMzc4QU1YVXVveU1yR1A2U3lrS1FPZ0d6TUx2Y2wiLCJtYWMiOiJmNGZhMGM0MDRkMTM3YjY0Y2I2YTBkZGQ4ZjdmNTVmNTcxMGNmMGMzYjVlZWQwZWJlYmJkODUzZWZlZGIyMTIyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:43:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJJTGVkYnBhQ1R6NTJacG5sSWpEdnc9PSIsInZhbHVlIjoiVHZQK00raXZ4MWZTK1I3TXg0U25JT2Z0eXNZemxUalZvOFNhVG5XOGwybW4yUitiUGJQRUNBalU4YjRGV2pHZ1RlLzArbGVrZk9rVTZCbTdVMUtXd0YrUEVrdEJ5dGphNVF3cVFwVCsvYzJVQWlXOWFHWjJkd0xLSmxmSFVkTUx6WmdDMEE1V2Y4ZkdBVjdlQkdWSGcxQTlwMXJPNkFlNlhMM2I5TVhzZVROK0lBNXA3bkFhT2FubHdFaTc3NjNkVjVPV2l2VVVROFc2VXB6UHVBa1ozTXhWWUJZNUdXUlEzeHhyQlJUa2VuUk5JLzhtV2ZoY283Q001SXJGblVNbmcrNUF4QkRnRSs4bFByQ3kzRitIOHFKdU5oMWFwOE0zUitFY1ZWWjg2ZDk1UmtmeG1PTHRGZlVVUGNrNWFSdENLYWh5bEtIRjFUZkx2RjU4WUJPOWFTOGZFeFBvKzdleVJ2ZCsrRWdtY1dncHZRWHBKUnc0WThxUDVyQWRPd3IrRXl6OGR2KytkQ0U0dCs1blV4aEhkYUsyVlB3bUQ4clhRUDQxYjl0UnBKenhxUElpTi9JUEdoc0lxbW1KM2V6b3h3MVFxTjFnRFlMTDM2cVE1cVB0OE9TTk9GTkJEV05yc2hBbjhvOERqU2tRZFdhVnN5U1A4cm1RK3VXSUk2S24iLCJtYWMiOiJiZjI0M2Y2YzM0OGNlNzEwOGVhYjEzNDljYTEwNmNkYzE1OWZkN2I4NTQxMGJjMGQ5ZjNlZmE5YzIxYWFmYTU0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:43:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxkSUJRYjNXdzl1dXlYVml2QmNXWEE9PSIsInZhbHVlIjoiVHF4ZkRCbWQvZGpHUW1KbEhmS0ZwOS9aMlRVMndiYTV0TkI3dC9aczhWbjJLKzlhazJXcXVQaGFQVVNhUnhJY2l4MDJDeERKRVJXLzhoRDUyUGt6Qk5pOWpOeU1sdi9vMTgxLzl1Zlk1d1FLSG5oRHlTTlljQWdza2xFYU01c3FVRjZIWXJGbjRjUW1tOS8yQU5ZRVdZL3lGc2E3SHJxbnhuRDBabHBJZDRFWEE5a01CWHUyVUExakxSdVd1ZDdDOVZmc2NjdzFsM3FjNmpqK25YSHVNQVNKSXhVQ0NsRVYwNVJLTDhzN29sR2pvL2pBNjNvOFhpOFFVRTZpSzl1RUxEZGxvVzhCZWUzcU5CMkpaOTVoWjBuQWJsNHpnN2UvQmFGUU9PMDlqT3ZtWHFkSEtoaThGUkR5L1JZaHZQZlFUanNUVi9HdytCQ2VEREcwbXcrVkI3S01JTGhIcW42Q29PMHp5RmVISGVWemV4RHIrVWFBS1libkVXaUUwZWJQRXUrK1JjUTNnQ0pUUXhVZFM0SjRrZ3RLWFZveFkxU0QzM3Zxb0xVbHJWc0hJZ0pEb2Z4enN5cFNoRWQvdFRKU0QwUmpWaUdyUjc4NkZMR3BNL2RhOHd1b01qVzY4WDhsNm5VMzc4QU1YVXVveU1yR1A2U3lrS1FPZ0d6TUx2Y2wiLCJtYWMiOiJmNGZhMGM0MDRkMTM3YjY0Y2I2YTBkZGQ4ZjdmNTVmNTcxMGNmMGMzYjVlZWQwZWJlYmJkODUzZWZlZGIyMTIyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:43:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJJTGVkYnBhQ1R6NTJacG5sSWpEdnc9PSIsInZhbHVlIjoiVHZQK00raXZ4MWZTK1I3TXg0U25JT2Z0eXNZemxUalZvOFNhVG5XOGwybW4yUitiUGJQRUNBalU4YjRGV2pHZ1RlLzArbGVrZk9rVTZCbTdVMUtXd0YrUEVrdEJ5dGphNVF3cVFwVCsvYzJVQWlXOWFHWjJkd0xLSmxmSFVkTUx6WmdDMEE1V2Y4ZkdBVjdlQkdWSGcxQTlwMXJPNkFlNlhMM2I5TVhzZVROK0lBNXA3bkFhT2FubHdFaTc3NjNkVjVPV2l2VVVROFc2VXB6UHVBa1ozTXhWWUJZNUdXUlEzeHhyQlJUa2VuUk5JLzhtV2ZoY283Q001SXJGblVNbmcrNUF4QkRnRSs4bFByQ3kzRitIOHFKdU5oMWFwOE0zUitFY1ZWWjg2ZDk1UmtmeG1PTHRGZlVVUGNrNWFSdENLYWh5bEtIRjFUZkx2RjU4WUJPOWFTOGZFeFBvKzdleVJ2ZCsrRWdtY1dncHZRWHBKUnc0WThxUDVyQWRPd3IrRXl6OGR2KytkQ0U0dCs1blV4aEhkYUsyVlB3bUQ4clhRUDQxYjl0UnBKenhxUElpTi9JUEdoc0lxbW1KM2V6b3h3MVFxTjFnRFlMTDM2cVE1cVB0OE9TTk9GTkJEV05yc2hBbjhvOERqU2tRZFdhVnN5U1A4cm1RK3VXSUk2S24iLCJtYWMiOiJiZjI0M2Y2YzM0OGNlNzEwOGVhYjEzNDljYTEwNmNkYzE1OWZkN2I4NTQxMGJjMGQ5ZjNlZmE5YzIxYWFmYTU0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:43:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646979834\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1691618933 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjdrU1g4bmx1cWNiUGdHS2lHZ2NUWEE9PSIsInZhbHVlIjoiTTNVaHl2eVFHQzhjVFAvMXFvZEVyZz09IiwibWFjIjoiYTI1NjY1ZDdmNWFlZmZhZDZlNDQwYTQzMDgxZDI3ZGI0Zjg1MjA1Y2RmMTZhMzEzNDU4NjkxMGVmNWJmZmM5YSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691618933\", {\"maxDepth\":0})</script>\n"}}