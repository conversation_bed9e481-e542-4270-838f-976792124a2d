{"__meta": {"id": "Xc87fd1ecd2c3a9c9028b09cf479e1513", "datetime": "2025-06-26 23:20:04", "utime": **********.576152, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.102695, "end": **********.576166, "duration": 0.47347092628479004, "duration_str": "473ms", "measures": [{"label": "Booting", "start": **********.102695, "relative_start": 0, "end": **********.514475, "relative_end": **********.514475, "duration": 0.41178011894226074, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.514484, "relative_start": 0.4117889404296875, "end": **********.576168, "relative_end": 2.1457672119140625e-06, "duration": 0.06168413162231445, "duration_str": "61.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029736, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00345, "accumulated_duration_str": "3.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.546937, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.797}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.562913, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.797, "width_percent": 15.072}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.569024, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.87, "width_percent": 19.13}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill?5=\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1390318323 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1390318323\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1038895757 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1038895757\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-377940464 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377940464\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1621891928 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/bill?5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979881159%7C12%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpZWFI1VDBaT08zQmJTdnhRZ1ZnS1E9PSIsInZhbHVlIjoiYkc1UCtNTWJMUWpwcitjZ0tGNEhUOUlTWUVMbzdmYUd1R1dpU1JvRStiZXN5WEcraHRwNGRqTTliYUltV0l0ZXJ5T0lUSzZteFhueDlpU0xZK2YxOGd4Tm5rbDhUN09VMElZTllyODdlOWJ2aXNuNEZDbmQ1VVlWc3NlUXZlTzhwQXBtMlkyMnA2M2RRZFNVTnl5ejJqOTZqcVNzTEp3ODlhSzJ6QVNaa0tuNktpOWdPL2JtRkhuWlovRjhnajN5UEJEZTJiVTQ4T2g1VEt1QUtrRkx3eUVxVCtjRC8rTXVuaHBMd2xDakJtYkNtMHhPMmYrNFRITHdYOVdVTkNOYytkK0hDV003Y2V6L1ZmaUxFV1EyQmlxTUdQOUZLNFlKTlhCQXpiUW1Cc0syTlhpY0ZQT3lQM3pERDBoa0RadzY1enE0WE5Za3BOUFNFTEFkMjljbm0vNmV6YUhaWktvOEtwN1dCQW1vK2xDNUJmK3pleUJNblRyUUx6U244YklVZDhjaEhkU242cHZrQ1ZLdURIU0JvZFl6U3BQKzZjS2JFMVpmKzFkek10cFdOdGNxL2xYVW81VkY3T2srYzJPZDdhQVN6d1NXbk8rdkhKckpCMmdQM1dIU1ZtUzRxV3psVkkvK1pTeTlmOURDanBJWk9NVWNLT0dwdEpBNnJpRDAiLCJtYWMiOiJmMDBkNjRhOTVhZjg4YWZlOThmNmJkYzI3ZjYxYjVmMDY0Y2UzZjljODMwODBiZDMzMzgwYWE5MGExOTEyYTg5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlpCSXl0SDZsbXBnRXVTTzgyZy9LVmc9PSIsInZhbHVlIjoiUlR6S3RrQVZiMGx1VlE3bnBTRHlUUkVGTVcvMXUxanRCcVk1aFMrTG1YOVdWdFdwSk52Q3pRMktkOWNqdFFDRmNtd0VaUGh5MVNpeEFrTVovZ2dOcFJVcldGZGtRYXVTOFlaUWtYWERZUGd2MUkxbzB5QUVhRjNCbDczRHRYN0p4UE80QmU5WFFnQ2JvTDZVQkFBSjNRWitrTGlxODVFMElQN3d1bmdpbWlpelhrcnROa1pmWTlFTEt1a1oza2JZWGcxUUd0MFUxOUhzclQwM3ZrdzRqNk1aWDZrVlhCbVdEWEZITkNURmY2MHBwVGhpcWhOSlQrK3RnNzl1TFZicTFpZmFpTG9JQ24rVnBtZGc2MTgwSmhYVGxyZEtNRFhGZ1NMdHZaWmxrUGlKVlA1b3lzTGg2L2c1WU9leGlCMDdZR2FCUHQyOHJXWjJKK0pqQXNCdllaamhaRzNSYit3clpOK0QxakhiMHFyTENpVUNmeVpZWmZ5eGczb0hjWW0ycTRBUzVuS3oxMCtjbEJ3Y21TR2ZtcTJ0SXVBWkkzZWxmVUM1RnBmeDl6Zk1XZFZRN2E5dUdaVHFkaUtITUd0VTVpV1lTdHdHMXZlb2RPTVR2aGdmWVgvT1N6QWtqc2JEU2tLTk1LaUxiQnlDNmIwWUx2dEJtRnhWOUwxUy9FR2EiLCJtYWMiOiJhZGE0NGI5ODRiZGNhZDRkYjQ1NDg3ZGMxYmEzMzA5OTJkMDg1MzgxMTk2NmQ5YTg3MjdiN2RjYjA3NDFkMjA5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1621891928\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-514819769 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-514819769\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1145582415 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkY1YlhCbExEUytsZmQwK2dINVJkemc9PSIsInZhbHVlIjoiWjZzSXJLdzNMTGxlRkJhemlVUmxGalJWWDVHTEdoZE9MSndsc2RBVkdhOG96ekcrZzhwdzc4MXl3bmc4M1RHQ28vM1JoZlRqT3hVOFZFckUweUdpbnlqdGNEbTdsUWVlc2NBSFZlSkNlU1ZmUWg5YXVQbHE3eWlLZ1ZqM2pkWDdsWDBXUHVyVzZiVnBYeG1rYnVjMWU1MHVvMllmVktOWjNSdUtEZytweVhrWUdCZGdKZFdlcWhtOEN0bU1kRXFjNmlrTURuckZGYStnVFZIWEhNMjMrUzlwTUI0WmZqeExxM1pGVGRaWUk2cjN2SlViR3RmYXd2Z3VqRFlqQzV4Q3BYbnI2WVFuTDNuMFEydkdJcnNlcUx2ck9rbXErNmtoRmdqaFNMSlI1WS90SzNjMjhyWEwwQXpxbFo5aVJNWjl3cGRjc25Ic2lKVHlkVjZ4NlNVNDZFZ3NVMHR3QURBSGNnejRiS1NXS1UzOFZmYUhxNUNkd1o2eTAyaG1mOStQZ240YTVnczcxL2hkb2N0dk9QS2xIbEdrY25XMmdtcDZpcjFXVUVPS2tINm55a0dwV1pmL0R6bkdDY1RqVzdLVWlKY3RqNEo5U1RFby96a1oxd0cxdXBVSnl6ci84eTgwMzBzQ2ZYei8rZ1pMT2Q1dnZ5NHRzdWhKbm5oZkFsUWYiLCJtYWMiOiIwZWNhMTI2MmY1ZmY5ODZmNTFiNTg5ZWFlODc4NTEwYTk1MjM0NTM0OWM0YWY0NmFjMTViZGQwNjUwMDY1ZWUxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFjd3c0eGRtM1pwR3hXaDRoc0xMeFE9PSIsInZhbHVlIjoiblA0OWdmMXNZRWVrVFB4MEVLTmRPNGN1emtRN1daZnFTRlNZT3JUbTVSZmtDU2Q3ZUMvdFIyWkNRNFdubE5BbUxNcUdBT3VySkFqaFJ0QTMyU1lrSWxYOVJ1YUNJclJmZEt2MnRMMzU4YkZabjEzSTl4RzEvdEF5WXNuOS9QV1czUnNsSUE3emt1RU9MaVJFOWxNaENxUW9YR0FTbE80NEdkTk1ZTE1QZExhQjFINmVBS01LZmtiUFZJeVBwdnBKSVAxSENpbURRY1RPSVRYdy9QMVRBTW5VWGFyamtEcmZmSG1CL0lsMVdMRE5NSmdaZVMrL2k1TnF6aFdreXB1VFFyVnQ0bG93RlgvMVp4UHNBbENXOWc1c05jUXhxUkc4eVZSbW41c29nZmdzamlmalRBQ0J6bGVIeVk1aE85QVRKQ2I0UkFkSlA4dTVlY2hHV2hnTllBaUV4aytPd1Q1RVIrcjZGY0VNYWZkYithMGVtMUk0MW5IYm5XbzUwZzJHeXZWYjVZeEhUeUEvanZncUVOdFQ4Y0ZmV3A3Q1NzanhMTzFoOUdoZUNIb0hMS0VqMnJ0Wk1LTDdzckdsVUhjSHpTTUdpTHIrb3J3VktaWS9MMlhTWHdvcWpyTi9NVEFoYnN3Nk9ndktoWUh0bmV1NXVjSVMyekcrNWYyMGMySlAiLCJtYWMiOiJkNTIxYTgxODc0MzdkYjc5ODk3ZTJiZDIzODFjZGM0Yzk4MjRjN2ExYWM2NjBiNDJhZTRiOTExNzQxZGI1ZGMyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkY1YlhCbExEUytsZmQwK2dINVJkemc9PSIsInZhbHVlIjoiWjZzSXJLdzNMTGxlRkJhemlVUmxGalJWWDVHTEdoZE9MSndsc2RBVkdhOG96ekcrZzhwdzc4MXl3bmc4M1RHQ28vM1JoZlRqT3hVOFZFckUweUdpbnlqdGNEbTdsUWVlc2NBSFZlSkNlU1ZmUWg5YXVQbHE3eWlLZ1ZqM2pkWDdsWDBXUHVyVzZiVnBYeG1rYnVjMWU1MHVvMllmVktOWjNSdUtEZytweVhrWUdCZGdKZFdlcWhtOEN0bU1kRXFjNmlrTURuckZGYStnVFZIWEhNMjMrUzlwTUI0WmZqeExxM1pGVGRaWUk2cjN2SlViR3RmYXd2Z3VqRFlqQzV4Q3BYbnI2WVFuTDNuMFEydkdJcnNlcUx2ck9rbXErNmtoRmdqaFNMSlI1WS90SzNjMjhyWEwwQXpxbFo5aVJNWjl3cGRjc25Ic2lKVHlkVjZ4NlNVNDZFZ3NVMHR3QURBSGNnejRiS1NXS1UzOFZmYUhxNUNkd1o2eTAyaG1mOStQZ240YTVnczcxL2hkb2N0dk9QS2xIbEdrY25XMmdtcDZpcjFXVUVPS2tINm55a0dwV1pmL0R6bkdDY1RqVzdLVWlKY3RqNEo5U1RFby96a1oxd0cxdXBVSnl6ci84eTgwMzBzQ2ZYei8rZ1pMT2Q1dnZ5NHRzdWhKbm5oZkFsUWYiLCJtYWMiOiIwZWNhMTI2MmY1ZmY5ODZmNTFiNTg5ZWFlODc4NTEwYTk1MjM0NTM0OWM0YWY0NmFjMTViZGQwNjUwMDY1ZWUxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFjd3c0eGRtM1pwR3hXaDRoc0xMeFE9PSIsInZhbHVlIjoiblA0OWdmMXNZRWVrVFB4MEVLTmRPNGN1emtRN1daZnFTRlNZT3JUbTVSZmtDU2Q3ZUMvdFIyWkNRNFdubE5BbUxNcUdBT3VySkFqaFJ0QTMyU1lrSWxYOVJ1YUNJclJmZEt2MnRMMzU4YkZabjEzSTl4RzEvdEF5WXNuOS9QV1czUnNsSUE3emt1RU9MaVJFOWxNaENxUW9YR0FTbE80NEdkTk1ZTE1QZExhQjFINmVBS01LZmtiUFZJeVBwdnBKSVAxSENpbURRY1RPSVRYdy9QMVRBTW5VWGFyamtEcmZmSG1CL0lsMVdMRE5NSmdaZVMrL2k1TnF6aFdreXB1VFFyVnQ0bG93RlgvMVp4UHNBbENXOWc1c05jUXhxUkc4eVZSbW41c29nZmdzamlmalRBQ0J6bGVIeVk1aE85QVRKQ2I0UkFkSlA4dTVlY2hHV2hnTllBaUV4aytPd1Q1RVIrcjZGY0VNYWZkYithMGVtMUk0MW5IYm5XbzUwZzJHeXZWYjVZeEhUeUEvanZncUVOdFQ4Y0ZmV3A3Q1NzanhMTzFoOUdoZUNIb0hMS0VqMnJ0Wk1LTDdzckdsVUhjSHpTTUdpTHIrb3J3VktaWS9MMlhTWHdvcWpyTi9NVEFoYnN3Nk9ndktoWUh0bmV1NXVjSVMyekcrNWYyMGMySlAiLCJtYWMiOiJkNTIxYTgxODc0MzdkYjc5ODk3ZTJiZDIzODFjZGM0Yzk4MjRjN2ExYWM2NjBiNDJhZTRiOTExNzQxZGI1ZGMyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145582415\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1986525054 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">http://localhost/bill?5=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1986525054\", {\"maxDepth\":0})</script>\n"}}