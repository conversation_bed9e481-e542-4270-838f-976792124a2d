{"__meta": {"id": "X1b95b87929aaa243f0cfaced0721932a", "datetime": "2025-06-26 23:20:08", "utime": **********.309578, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980007.873858, "end": **********.309593, "duration": 0.43573498725891113, "duration_str": "436ms", "measures": [{"label": "Booting", "start": 1750980007.873858, "relative_start": 0, "end": **********.248706, "relative_end": **********.248706, "duration": 0.3748481273651123, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.248718, "relative_start": 0.3748600482940674, "end": **********.309595, "relative_end": 2.1457672119140625e-06, "duration": 0.060877084732055664, "duration_str": "60.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45030352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00315, "accumulated_duration_str": "3.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.280774, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.111}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.294734, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.111, "width_percent": 13.651}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3024502, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.762, "width_percent": 15.238}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1010164941 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1010164941\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1263401240 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1263401240\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2110362079 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110362079\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-636369626 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980004460%7C13%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFieXpIcVduL2U1c3cvZ2EwU0V3MWc9PSIsInZhbHVlIjoiRjRqTFpOMGJCWGkyd3pCNEVMaGhSaXZQYXRLUUR3OFMraE5saVZ4bWhPQkRRQTdxQXJqOU9lMURvTWp6dkQ2elUxcHR1NmZvazBrajNXMEQwTzFXL096TDJxZzd1a2lSUG5XY1ViNVQ4b3B2andwTktzdVhMVk1nbXljamdUNWNUSWoyVE5FNkp5WlV0WFgxMmp4UXB5cTBjTDUyK3NSMzBIQTRmUFU2Szh1ZVkwb2t1d2dHR3FDSExYUExVbkRybkU1MitCai9LVGFWb2FZTGNhYTRpbFFsYjVKRm16WHpEcGdJMGF6WktRK25nLzc0TkVNZitucnZ4bUlHbDBLU2lCc2E4SE9RbjNOOXJKNkVoZ2Z0WmdUeVQvdXNDeDlSODVhMjJtb0YwUC8vM1V6MWZvMlF2MmdOZVh2SkJhUjd4L1JoTG9XWkQrZlloaVd0Q3NVbHArT2VNbVcwYjlmbzFTaCtaSmJkVGVvcVhMM0h0K2cva1NPdkhxbU80dUowUzgrejFLRVhvdzJrdmtuR3ljblBJQUdzTVd0bGlTeHV4dXFYSnEwNWJJcWZmUXVLNk9DWFJmSzhkeTJhOXlnaGo1WlQ2TWNJb2JDanNsY2FkTVV5dFlCdEx4RXhrTlRvVEpRZmgxbXRoQVRqdXV2Y1RpSmxqK0pDamZodFpjOVkiLCJtYWMiOiI5OWRiNDU0NjUwY2NkOTc2MzRhYmM5M2FmYmY1NWUzODFhN2U4ZmQ2NmE1NDg2YmNjNWM3OGNmZWM2ZTFjYTU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVGTmVyQ3VPZXV4dzJ2SWVSQWZVWWc9PSIsInZhbHVlIjoiQzVWeEkwSm4za3B0Q2p0U2hpc0pUVldkMVNHRGQxTjdicUZGTjZYMFBNak9qVnZZalBQczdaLzcyZVN2Qm5TWkRlL0U3TTlVRkhVWDFwQm5JVGlJd3hhREpPSzU2dnl0OFVMb0FaYUU2VUtmRHJNWDNYUlQ2NHBUVTNXTDFyNjZxaDhGV2M2VmMzQTVob092Qk05dlQrWm91eUdseVhiZjNDOEF0dHllbmw2Z2Rvb3F3cllTVHBncHJpbjVPU3kxY01NOGdSVzdyajNJdWs1ZVh5akpUcVNUMTU3am9SaCsvZEsydkhaUUNOblVGYW96cGZRamdSdmdwZTRLK2VpM3NUQnRQUzA3Qy9MZ283MWhLMnJDN1lxcjlucUhwTmRCSm5TeFM1ZjZmUFBwaFdJWmNMcHZ5Znp5Mm5Ia0daU1FhUW8zM1FTVExLT2ZKQkhxZjVuVTFLQjFrVWpUNEZ0TFptU2xWN1hrdmFjcWJLc2t0cFc3a3RjdWYyWmZyRnFZd3phTXp0NHNuUTB6Tm8zWStZM1RJTkhWWFBicjVWT1Yyc0ZuUVRaUjVtSExLYzFhOTY3TFlMKzdpUW9DWThIZXFWNnROTVhDbGVIQVllMXRjaG1RRnpWeEU1KzlFbXZhQTRleUR2YzcvSnFjQWMrdksraHFIZnhnZDV2S0hUNjAiLCJtYWMiOiJjMGQ1NmFhMjA5NDRjYzAzYTE3N2RlY2YxYTBhMzI0OTRlMjkyOTE1MzZmZGM2ZjU2YTMyMGQ3YWI4MmNiMDJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636369626\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-206917805 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-206917805\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-703202126 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZGbjdPbU1kSFdZOEZpOGU4cTF0bkE9PSIsInZhbHVlIjoiNm9WZ25sdTlna3JqUzVtdE81VUEzWURTWksvS0E5Zm9nelpxMUtIcURlVmlDbktyMnFDMmVLN0c5L1NEdzZQeDFOTU40WUlDR2F5Y3k2Ykgyc0FubWEwWkQ4eFhUMWVGTi9JSXpaMVVJN2FpdlE3engvSVFsR0RtOWRMZGlQemF4bCtRdnpqdU1xci85TDllZmNjUG51QUlsT2Y4bWV0WmNnNHdvMk5IZVJtZVpLcVRmUDZUUFpNdS9VcjhMRmthOThLNW1laWFSNlJUNjhxT082YzYxUTV1dFZDbFEyeGVxQkliQTlIdjJtMlBJbWNoVGI0RHoxWGNZWFlpbWVIQkpjS0pBL2F4dll1emJZQm8vWncxUzNId1JlSDV4RUlGY29ZY1FjS25aaXJDam03L3BFcCsvazVlMDlZOFpMQlpXR05tZlo4cEUxVlJrbGM2WDlqRE8xRVI5aGNQaEJQaHpjZGg2TFpmVE5sU2UyUk8yVXg1OCtVVTJWdDVSSGRxTVhreXNzWVhXb25Nb2tPcCtVR0MzeHM3dTA2THNRMWxKWFFTNnRqR3EvMEFLSXFIS2xyVmZLZ2F4L2VqWklqRDh2TlhYVFg1NTFtSGR4aWg5Z0pxRFpNYUhJdjNsbm9KVkN2bU1reXVRTk1vNnBFUktXV2RmRHdxckt4UlRoQWUiLCJtYWMiOiI4N2ZhNzU5MGQ2MDI0NGZhMjgxYzMyMTRjMGIyN2MxMzdjMDg3OGI2ZDgxMDE0Zjg4MTEzN2VkMzUwNzRhMTIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpWMVpNTVNxbXNWR1c0bXBxSE1UMnc9PSIsInZhbHVlIjoiY0ltR09yV3QxODZWVXVjWnh2ODdoWEgwS0ErODBTRDd6d256MHY4Tk04MS9JdkRDMWhDYjlMc01EOTFockJRQy81VjMrby85QTM1blNwQTVpeTRBYTl0SUNVaWJLQnU4bkIreTk3cUpGM2dTZEtHM0x5cTlXUnFyeTRHcCtYUjlwdWZQUzdJSEJkWHU2VzFBZFI3YUdyOU5rRnEvT1hnSngzaDR2dW9EYVdmbFZlZTZkdWFlam1kdzNDVEZVL2xxVHBZNDR3a3AwOWNmNzdIdkRIUVVVMnJnaFkydkNVTVdVTmdOdFhiaXBPRFpLbTBMSDJ2UG9vYm5kaGVjcEhyTm1td2gyNndzOUNIZDEyeHd6SW1OOTRpMEhhV1JnbkFZZXJ2RGZOc3U0Rm54Q2txdWRtdk5zY3RRcUpONk1YbjkxOWpjdzVoMDhXMmN0dlA4UlJtOWpoc2dTYyttbFVGakFHK0VYQnNsUkl2aEZqY0RCdGk4ZUk4bkxTRW1DZXN1blo0Ukx4Ti9yOWVJcThVYTcvVFJON1Z1OU5qc1RLZ21ySFJrcFpuNUwwN2dERlFWV3V0WkdjR1hTcmRNUTJXaFFuN05TcnJUbC9TWXBpRDNhY2htVWFGdXN4NEI4MG1vRldoYVRjdUxLeHhQRXozcTNBNXBJcnNvRklQblJDWXgiLCJtYWMiOiJiZDFlM2MwZjgwOTc0NTg2OTgzYWJmMWU1YTEyOGM5ZmYwYmExNTEwODMwYTYzOGQxOTdkMDE1MDc5NDA1OGQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZGbjdPbU1kSFdZOEZpOGU4cTF0bkE9PSIsInZhbHVlIjoiNm9WZ25sdTlna3JqUzVtdE81VUEzWURTWksvS0E5Zm9nelpxMUtIcURlVmlDbktyMnFDMmVLN0c5L1NEdzZQeDFOTU40WUlDR2F5Y3k2Ykgyc0FubWEwWkQ4eFhUMWVGTi9JSXpaMVVJN2FpdlE3engvSVFsR0RtOWRMZGlQemF4bCtRdnpqdU1xci85TDllZmNjUG51QUlsT2Y4bWV0WmNnNHdvMk5IZVJtZVpLcVRmUDZUUFpNdS9VcjhMRmthOThLNW1laWFSNlJUNjhxT082YzYxUTV1dFZDbFEyeGVxQkliQTlIdjJtMlBJbWNoVGI0RHoxWGNZWFlpbWVIQkpjS0pBL2F4dll1emJZQm8vWncxUzNId1JlSDV4RUlGY29ZY1FjS25aaXJDam03L3BFcCsvazVlMDlZOFpMQlpXR05tZlo4cEUxVlJrbGM2WDlqRE8xRVI5aGNQaEJQaHpjZGg2TFpmVE5sU2UyUk8yVXg1OCtVVTJWdDVSSGRxTVhreXNzWVhXb25Nb2tPcCtVR0MzeHM3dTA2THNRMWxKWFFTNnRqR3EvMEFLSXFIS2xyVmZLZ2F4L2VqWklqRDh2TlhYVFg1NTFtSGR4aWg5Z0pxRFpNYUhJdjNsbm9KVkN2bU1reXVRTk1vNnBFUktXV2RmRHdxckt4UlRoQWUiLCJtYWMiOiI4N2ZhNzU5MGQ2MDI0NGZhMjgxYzMyMTRjMGIyN2MxMzdjMDg3OGI2ZDgxMDE0Zjg4MTEzN2VkMzUwNzRhMTIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpWMVpNTVNxbXNWR1c0bXBxSE1UMnc9PSIsInZhbHVlIjoiY0ltR09yV3QxODZWVXVjWnh2ODdoWEgwS0ErODBTRDd6d256MHY4Tk04MS9JdkRDMWhDYjlMc01EOTFockJRQy81VjMrby85QTM1blNwQTVpeTRBYTl0SUNVaWJLQnU4bkIreTk3cUpGM2dTZEtHM0x5cTlXUnFyeTRHcCtYUjlwdWZQUzdJSEJkWHU2VzFBZFI3YUdyOU5rRnEvT1hnSngzaDR2dW9EYVdmbFZlZTZkdWFlam1kdzNDVEZVL2xxVHBZNDR3a3AwOWNmNzdIdkRIUVVVMnJnaFkydkNVTVdVTmdOdFhiaXBPRFpLbTBMSDJ2UG9vYm5kaGVjcEhyTm1td2gyNndzOUNIZDEyeHd6SW1OOTRpMEhhV1JnbkFZZXJ2RGZOc3U0Rm54Q2txdWRtdk5zY3RRcUpONk1YbjkxOWpjdzVoMDhXMmN0dlA4UlJtOWpoc2dTYyttbFVGakFHK0VYQnNsUkl2aEZqY0RCdGk4ZUk4bkxTRW1DZXN1blo0Ukx4Ti9yOWVJcThVYTcvVFJON1Z1OU5qc1RLZ21ySFJrcFpuNUwwN2dERlFWV3V0WkdjR1hTcmRNUTJXaFFuN05TcnJUbC9TWXBpRDNhY2htVWFGdXN4NEI4MG1vRldoYVRjdUxLeHhQRXozcTNBNXBJcnNvRklQblJDWXgiLCJtYWMiOiJiZDFlM2MwZjgwOTc0NTg2OTgzYWJmMWU1YTEyOGM5ZmYwYmExNTEwODMwYTYzOGQxOTdkMDE1MDc5NDA1OGQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703202126\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1983087171 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983087171\", {\"maxDepth\":0})</script>\n"}}