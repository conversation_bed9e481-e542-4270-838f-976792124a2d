{"__meta": {"id": "Xaf9f321794b4f911ded24e3fd5551abb", "datetime": "2025-06-26 22:43:01", "utime": **********.36863, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750977780.937652, "end": **********.368644, "duration": 0.43099188804626465, "duration_str": "431ms", "measures": [{"label": "Booting", "start": 1750977780.937652, "relative_start": 0, "end": **********.31208, "relative_end": **********.31208, "duration": 0.37442779541015625, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.312089, "relative_start": 0.3744368553161621, "end": **********.368645, "relative_end": 9.5367431640625e-07, "duration": 0.056555986404418945, "duration_str": "56.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00284, "accumulated_duration_str": "2.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.344461, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.07}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.355155, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.07, "width_percent": 16.549}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.361197, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.62, "width_percent": 13.38}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InJYTjJ6RlpNeS9OM2FhZ1d3dDY2a3c9PSIsInZhbHVlIjoidloyT0twSm5OQ3NYM29BWUIwWnUrQT09IiwibWFjIjoiZDkyYjdkZDE5NzdkMmRiYWY4MjhkZWRiMGI1N2M2NmI3YmQyNTllM2IzY2ZlMjg2MmJjNDVhODEzYmRhNDliNiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1655057956 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1655057956\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1691658520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1691658520\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-976759412 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976759412\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-217997768 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6InJYTjJ6RlpNeS9OM2FhZ1d3dDY2a3c9PSIsInZhbHVlIjoidloyT0twSm5OQ3NYM29BWUIwWnUrQT09IiwibWFjIjoiZDkyYjdkZDE5NzdkMmRiYWY4MjhkZWRiMGI1N2M2NmI3YmQyNTllM2IzY2ZlMjg2MmJjNDVhODEzYmRhNDliNiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977773814%7C16%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkQ1dDRlNmUvNDVpczlvS0ZZTlo0V1E9PSIsInZhbHVlIjoiS2ZldUlMdjg2SEtYL2JQZHB1R2xPY0JRcUtBQkZYdTRCTnlBb3JteWpOU05ra3FsS2twSU1oKzhGeHR3KzJDMWNhaHdVeXZOZE1qZVdmYVM4N2ZOYWVzSEtiQitXQUk2Zmo2aTZ1N0doNG1SQWhwRXRKbGxRSXFWeUdpUnZ6S1NBcExmZDBKTS9sL0J3T29wQ24xU1MzRjIvWUpSYXoxeE9maUJXbFJnWXBaOUdWdGoyVVkvQWgzeVY4S0hRVG9YeXVIRlI5ZURXcTBQTGhiVXV5enFHcDRXTnEvclc0ejZialpyKzI2M1pTMGhyWlFCOVBzcVU2WXEyR05iSGNIQmtyckVxby9VNXhSQVc1WDYwYnJjMHgrU3RlaVN6MTVGUnBiU2ZmMEdmS0ZCUEF6TmhlYXdNeUZBS2tYMFlwSThxWkQza0tMQXZ4S1pac2lUU3pPOUE2TzNSL0psL3p1c1lVdFBQTTlvc1daemo4aE1SbkEwTFZkSkl4RlkzSUZrRjk1VXFYTVorOEZWamkxRU5mcUo5ZmxkM001QndLczZJOUtISVNUbzBKUk16R2QydmJwTmZsdW5HNzd3bWgzMGUwWFVQTVVLbHYwRmEraTFnUHllcTZwNk9SZ1hFR1FTYjNnWlNPeGhVTlQvelJlRXhQQndpWE96dXdUMnFKS0YiLCJtYWMiOiI4ZTgxMjc3N2ViMDEyMDRkODc3ZDk4ZWRmYWRlZjNhNTg1ZDAyZDRlOTg4YjUyZWYyYWMxOTUwYTZkYjYyOWExIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtlQjI3ZGtBaGhtaHdWRldPSDVaZnc9PSIsInZhbHVlIjoiRU5kc0pOYUpWUmxMZ2c3elh2RkpKMi9laUo3bnBDYmZ0cnVNSG82eE84RVhJNHhWNEtEVXpJWi9sVTVJT1lXaitGblljYTI2WlBOMElJeG9Dem44am15dVN6VlV2MitWcHBIMGVKK25IUzlsREdpbzh5VFcyRkZPcUxoUTJ1ZExHSHF1ZEUzRGxNUTZYRGRJaEJlMXUyaG5VMTFwaDFjZE9wYzlzZCtVVjBEQ25xOXlNTzMzWW4zbXMxamZ4aFQzSnJVUXd4bnAwcGYwZnZQQm00SkdGbU80am5STG80OTFsaE80d0xnYmNRTFowcERJQmowR1JxZ2RpTFpYU2p6bkhMQWd3cGpqY1R1Q1hMelhzdVJsRlM4YjFrQllMUy96aDlkaGtyOVZXdkNBQXFiVGJGUnE3UDRKOHJuampJaWFJK0RXZ1dyS3BIRWVQemFBQUZqNWtMMW50di8xbHFVY1J6M0svTkNHVVduSDd3dCswTmxLcmRBbjVEV1dVOEhkSVNkMTVINEY4TGtPRGdNaG84eCtpUjFVUWI3N3BHWHNNK2RhdDduMUxzQ01LNFZRdzM2c2RORXBHLzBPYmtMWHhlUndKZFVxQ2t2U0pmTURZSElUMW9WQlRUdjRqMkpxM0lSd25BcGNXRXNmaW55cDVrTDBFU3g4c2V1OEd4eDIiLCJtYWMiOiJmZmE3NDU5ODljOGZiMGE2ZmM3YjM2YzdkYWYwMWM1ZGYzZGQ4ODBlNWU4NTk3ZjMwNTQ3MDk5M2M4NGIyZTRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-217997768\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-504958568 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:43:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJGZ3daNHRyY3pJeFdHaC9nbXg4VHc9PSIsInZhbHVlIjoiZWNuTWVKUllsSW9HQXVpRXhtSTM3b2VHM05xQnF4T2dYVE9lZGRJdFcwVStrMnF2UFVKcm1rd0hlcUNOTXA2bVFTc0RQZDVKWm1keDBBV1ZoK25LZUZURmVrWTFlUVFvampFdHhHUWNSL3RiZ0pybWlIUFdKVFJveDFGSjlQcFo5VXpsbzhUb0k0ekNqMVFYUEVkaC9JTHlYQmFTNXFvR3V5M0JMQmxrb0NOSC9RajVGVVJpYWdJNndXNlR4SHNsYmR2S3pCdkNuYVpKYVp3SmQ0TE95RzBOMFNsUzhRMnhrRXEySG9aejVNZkhlQUdJb2lxaDE3aFNwdkswbHBla1JVd0FGZUM5aVliUWMzejF0aXVVZzBnTHZlVEZoZ2ZyTU95YmsxSUlLd2FsQUNraSszWkZxL2VLakNEdjdxZ2xsUDNLYStvcmM4Rm9lUUFnb3FIeFlNU25BcHBkaFBlNERBeUtYQ3EyWjdwdHZaZWcrYlNzK3JYNGd0YVZ2R1RLRGl2bEJXUWtTbW8zZHlHcTNCc0RjUGExUlRKUThvN1JjZFcrSFl4MzRuSEVNSmgyZ3M0MmFqR0Z3K01EcFlweVhnT1huai82OUZMdkNHUFpvZnlWTDFYK2hRdkFCWnI1WElkVFZzN1RiQWdVZlU5NjhjNzdpeUpDRnpGZDNGQisiLCJtYWMiOiI4Y2IyMWUzMWI3ODAxYWFmMGU0ZmFiNmQxOTMwMWFlYzU3MDliMTU1OWIwMDY0YjE5ZGJhNDVkMzYxODJjNzZiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:43:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik0zRFhOalc3M3BZUnozMmo3V3FMblE9PSIsInZhbHVlIjoiaXhRNU9ObGxDSkNFWlF5Rkl1YmFKY2IzaG1TUFVnOGdNdk5BRkh1eTcvbzcrcnV1ZjZaRk1VaFNrd0gvankwaWNLNEhlN2JnTXNpRDFmeGo4N200R1hPRlN5MVJXdDAvd3haVFF5RWtGd1dScGZPbER4S2dZOUd6ZGNBS0NXeHJKWUpaNEpuNWhDUzdVa3RLUlpJdjRkY01jQlVlemt0TDBkNEhvQkNoSy84Sng1aEt3SHRjTlgvOU9FRDh5RVdENXlHanZpWVpTUXUySXVHeTZTaHdxZndxT2N6VHltZktvNS9tNmRaQUNKdzN3SGhHOHRFYjdpZmZ1bEh0dnhISXBpRkFjQlFzazRoUVRrMzVETElOR1Q4YXJxRzBGdHdCR25PWG52UUZLR0pNSE92WWoycFlKY2xodndKMTBlYVMxeUZrelpsdmRSYWhkbUhJU3BjT1kwMUZwcVhrMG9JNVlobEYxMURHWHB0andzQlhlU2FDcDQ0U1lMZTZDVGpMUk9zdkZaSDZsNWdpZ2s3ajU0Y3prM1JRaDM0YmlaVlVxVTJEbGcvWkdYUTRLbGptWWVERUZySXliSFpUbms5S0dsWDNoNEthajZpUXpLQnU1dC95SVNiSDZ2Zlg1NmZFcFo2MFg4N05UVk5pVnZwZkpET0I3d21qL2NrSHdTMk0iLCJtYWMiOiIwMjU1NTExY2U4NTQ2Y2EzYzEwMGE1NmY1MGJjODNjOTcxZDE3MWYwZjZkMzhlMGUzZjE1Y2I3ZmU3ZmZiMmIwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:43:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJGZ3daNHRyY3pJeFdHaC9nbXg4VHc9PSIsInZhbHVlIjoiZWNuTWVKUllsSW9HQXVpRXhtSTM3b2VHM05xQnF4T2dYVE9lZGRJdFcwVStrMnF2UFVKcm1rd0hlcUNOTXA2bVFTc0RQZDVKWm1keDBBV1ZoK25LZUZURmVrWTFlUVFvampFdHhHUWNSL3RiZ0pybWlIUFdKVFJveDFGSjlQcFo5VXpsbzhUb0k0ekNqMVFYUEVkaC9JTHlYQmFTNXFvR3V5M0JMQmxrb0NOSC9RajVGVVJpYWdJNndXNlR4SHNsYmR2S3pCdkNuYVpKYVp3SmQ0TE95RzBOMFNsUzhRMnhrRXEySG9aejVNZkhlQUdJb2lxaDE3aFNwdkswbHBla1JVd0FGZUM5aVliUWMzejF0aXVVZzBnTHZlVEZoZ2ZyTU95YmsxSUlLd2FsQUNraSszWkZxL2VLakNEdjdxZ2xsUDNLYStvcmM4Rm9lUUFnb3FIeFlNU25BcHBkaFBlNERBeUtYQ3EyWjdwdHZaZWcrYlNzK3JYNGd0YVZ2R1RLRGl2bEJXUWtTbW8zZHlHcTNCc0RjUGExUlRKUThvN1JjZFcrSFl4MzRuSEVNSmgyZ3M0MmFqR0Z3K01EcFlweVhnT1huai82OUZMdkNHUFpvZnlWTDFYK2hRdkFCWnI1WElkVFZzN1RiQWdVZlU5NjhjNzdpeUpDRnpGZDNGQisiLCJtYWMiOiI4Y2IyMWUzMWI3ODAxYWFmMGU0ZmFiNmQxOTMwMWFlYzU3MDliMTU1OWIwMDY0YjE5ZGJhNDVkMzYxODJjNzZiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:43:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik0zRFhOalc3M3BZUnozMmo3V3FMblE9PSIsInZhbHVlIjoiaXhRNU9ObGxDSkNFWlF5Rkl1YmFKY2IzaG1TUFVnOGdNdk5BRkh1eTcvbzcrcnV1ZjZaRk1VaFNrd0gvankwaWNLNEhlN2JnTXNpRDFmeGo4N200R1hPRlN5MVJXdDAvd3haVFF5RWtGd1dScGZPbER4S2dZOUd6ZGNBS0NXeHJKWUpaNEpuNWhDUzdVa3RLUlpJdjRkY01jQlVlemt0TDBkNEhvQkNoSy84Sng1aEt3SHRjTlgvOU9FRDh5RVdENXlHanZpWVpTUXUySXVHeTZTaHdxZndxT2N6VHltZktvNS9tNmRaQUNKdzN3SGhHOHRFYjdpZmZ1bEh0dnhISXBpRkFjQlFzazRoUVRrMzVETElOR1Q4YXJxRzBGdHdCR25PWG52UUZLR0pNSE92WWoycFlKY2xodndKMTBlYVMxeUZrelpsdmRSYWhkbUhJU3BjT1kwMUZwcVhrMG9JNVlobEYxMURHWHB0andzQlhlU2FDcDQ0U1lMZTZDVGpMUk9zdkZaSDZsNWdpZ2s3ajU0Y3prM1JRaDM0YmlaVlVxVTJEbGcvWkdYUTRLbGptWWVERUZySXliSFpUbms5S0dsWDNoNEthajZpUXpLQnU1dC95SVNiSDZ2Zlg1NmZFcFo2MFg4N05UVk5pVnZwZkpET0I3d21qL2NrSHdTMk0iLCJtYWMiOiIwMjU1NTExY2U4NTQ2Y2EzYzEwMGE1NmY1MGJjODNjOTcxZDE3MWYwZjZkMzhlMGUzZjE1Y2I3ZmU3ZmZiMmIwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:43:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504958568\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1550570256 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6InJYTjJ6RlpNeS9OM2FhZ1d3dDY2a3c9PSIsInZhbHVlIjoidloyT0twSm5OQ3NYM29BWUIwWnUrQT09IiwibWFjIjoiZDkyYjdkZDE5NzdkMmRiYWY4MjhkZWRiMGI1N2M2NmI3YmQyNTllM2IzY2ZlMjg2MmJjNDVhODEzYmRhNDliNiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1550570256\", {\"maxDepth\":0})</script>\n"}}