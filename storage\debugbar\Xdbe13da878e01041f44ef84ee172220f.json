{"__meta": {"id": "Xdbe13da878e01041f44ef84ee172220f", "datetime": "2025-06-26 23:21:15", "utime": **********.643522, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.261881, "end": **********.643539, "duration": 0.38165783882141113, "duration_str": "382ms", "measures": [{"label": "Booting", "start": **********.261881, "relative_start": 0, "end": **********.594521, "relative_end": **********.594521, "duration": 0.3326399326324463, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.59453, "relative_start": 0.33264899253845215, "end": **********.643542, "relative_end": 3.0994415283203125e-06, "duration": 0.049011945724487305, "duration_str": "49.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028192, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0024000000000000002, "accumulated_duration_str": "2.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.621129, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.917}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.63066, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.917, "width_percent": 15.417}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6365778, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.333, "width_percent": 16.667}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget/eyJpdiI6IitUUkg3UTRnWGVYT1hkZm9Da0NPZXc9PSIsInZhbHVlIjoiZStjelcvdEROa3hBMXEzZVd5NkdKUT09IiwibWFjIjoiNzk4NWQxMmViNWIzMzdiYjg3Njg1OTI5MWExNzQ3ZjQ3MDBmODgxNDZhZWU1OWUwZmNmN2JiMWI4MTMxZmM1YyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-442133712 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-442133712\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1608168696 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1608168696\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1196600251 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196600251\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1609169365 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/budget/eyJpdiI6IitUUkg3UTRnWGVYT1hkZm9Da0NPZXc9PSIsInZhbHVlIjoiZStjelcvdEROa3hBMXEzZVd5NkdKUT09IiwibWFjIjoiNzk4NWQxMmViNWIzMzdiYjg3Njg1OTI5MWExNzQ3ZjQ3MDBmODgxNDZhZWU1OWUwZmNmN2JiMWI4MTMxZmM1YyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980072902%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRuNVQ0ays0THFlaHU5VURKSms3b1E9PSIsInZhbHVlIjoiR1AyY21wMm9lbFpsVEtFeFB2bDNlSE1KNXNGN0lkY25BdEQrSW1wZ1BhQ25aQUlURTI3OW5mZlVjd2VBUUo3ZHd0SWhIbHEvRmoyeXJ5bEkzNklHSVBwaTdHQkJsYmVyU3ZtVjhYNWFoRFJ0SkV3VHhiVThsaU56cHFXWEMwSXdib3d2TWpTYWpnRlNRckExVnNlSUNIMmlHSWVTekxIMG5QSUt0aEZBaGtjQmxrWW03L2lzT1htTXBMSm9zUFpmM0txcTdNUU5DMnF0MnRFZkpMWnVNVjRoNExQSTRNbzZQVjM4WjBhdEZGWUE2YVp4VXZnbC9rWXY5dFlORjJZNnNlWTc2SEdxSEU2aDdMUmdBdWdiQXFQalEwOUNFR1lTa2gzVnMvaDZ4a3JEWW1PYmlXcDg1ZldjV0REd1hsRUVISmY5L3dPUThVWXJzNjFJTTR6NjlyN3p5QUxyOFhJNDBEbVJMVWUxVkU3aTYzOWhodjFJRkpQcUxWQkY2dzVPTUVmVXdncDRibm55bkt5Y3hHanZqSGp5OUVHTlZCNWRyeUZNRUJySHRQZ25UU2cyK3k1ZnNxR0JWamQ1elp3eWRTRVNReVVYRUpwZGpiWkFTWTlNU1JGaE1Va2IxMXh1bS9oaVNiTFUxbGRFZjhYS0VBMERhU2ZYN2FqV0tzaHciLCJtYWMiOiJlNjU0MDEwNDI5MGIxOTJjYzM4NmFlNTc5ODgyOWRmNTc2YzkyMWE3MTYzMzQ1MmY5NGE3MTVhNDhkOGUzM2MzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhkV0grMFgvTXhOS0E4UnI5RlRLZkE9PSIsInZhbHVlIjoiU0pHTHlPTmpuNzhxVmNMZldqNjVkVXRFbWRYeWw1d2t5VFV2MWpNVjh1MzFGSGNadjhZNFI2c3BNMy9OUEx0V0R5OG1Ga0lHd1VDUDRQRmthaW9Jd3JFUUxoc0ZCUVRZUjc0NzFwTkpybVd5aDFabkF1U01aZGlYSDM4MlkxdEVRTUNoaDE3SkdkSnZlbXh2ODBIcmFmZUI4M3FralVRR2N2SVg1LzI4M1F5aVJWbEJaVVhYeEdBL2x3ZHNKZXVuYkV4TXhLZU84ZnhJZlp4VGltaXdleHJ4RVhoU2IzeGRVQWRRbFRFd3FaS1Y3QTdvRzZHOTdYWktRT3FZc1JuUHFSWk5yNFpVaDhUK0E3NCtyTGtUZVdWeVQ2QWwzTDZQc0ppQzlyM2d3b2lSMWU4OWN2a3BhTjdtZ0JaenpXOVJtbWhHbFdOc1ZPSE5QVHE3dnFsNEFMaGV0ZUxtdUQ3a2prNCtmWmg4RVMvRUVMdTY3VmpGWkNZc1hzeFZFVDZYTVZlRFBxZDFDSis5TitnRXBRNFFuOTBCeHVxcXQ1K0dFMTFpZXFrS0xyeXVjVkVOTXNCNnZtTXRXVVZnY0RzNEVZN2d6RHg2eDhlWjRFckRMMk9LOTlXdW5lRkJsRW9OMGdMSjlGdS9aa0VaRWNIdDFvNkg5Q1RlTmpQRkw5NFEiLCJtYWMiOiJjZmQyNTNjMGY3MjFjMjNjMGFlYzlmZGNkNzczNDlmZGNhNWQ1YjY2N2EzM2I2MmJjYWE5ZDA4ZmY3MGUwZDc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609169365\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1400545450 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:21:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktCcW0xWUlLMUVBdjNqVm9zcDF6REE9PSIsInZhbHVlIjoieW11aTB5L1dFQ0JJcnA4RGRCb29RVUpNbU92VlhxT1lML3UvRFhxU24wUmpCRng1d1BYS3FxY04xTlR5Z3piWldZZ1MwVkVxQ3NLL1VCUWt0OEMyRkJQc0FITTF2UlQvLzZmREJLMnR6bE51SUwwUFhDZFU1V1FEZ3psSGJkWTc2b2Q0eWRzT1dFYVVuVGwyaXl2Q1Vud3RSTXNXMUI4L2h4SVVBdEVXZ1BPLzN5UkRVYlJPRWQrQzk4ZnhrL3FaQWdRaVdzQTRmWDZVQ2FQeitmazczMjJDNzVoSEJNRWFDZ0pYNzlEVjRrNkN4V3h0QlhFRStRWDgveDFCSDRra2N3SU5JWkY5Z3RRT1FhTUFTK2w1YjJYQnlDZGhod2twZ1I4L1hTY2V6MzN1azRjdGRvTUZNWHA1elFkOWdIaU95dUI5aVllamRML3FrYTFkUGJORkx1WUd1dGFKMmJUUU02QzJNd1NoUW8xRmFzK0hYalpsa2I3MTJJM2Z4dVVRUVhSN2FYNjc4WFY3UjF1NitxVVBiRyt6Q1VDRDhHN29LMXUyaysxLzFVTW9HcThsbHhvdnlTOHcyUTc2YWplRWkxNmdlcFhDMWoxQmltbnBHTXc4ek4wYk5wVjZETUxpa0xEdW1nQVlVVUl5Uk0rWVpnbWlldyt4SXQrVEt0M2ciLCJtYWMiOiJiMjg3NjVhNjBiYTI3ODIzNGI2ZTUyMzU3NmNiZDQ5MTQ0M2MxNmU2M2EwMTNhMDVlOWVjY2YxMWEzYWJhOWJlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpuWTVWSG40blJQeTVwVjVJV01YNlE9PSIsInZhbHVlIjoicmxmd0E4dm9iNUVYNU1rZUw4MUJpL05QZzl3OURsamZyUnYweHFkRUxiamtNMUxpUnc4enlHZGZwUVkyNWVZQXgzN3g3ZUdqSUxMQkNjOWMrUEVxS1lFSTBxaEZqRTZxYlpGSk4yNk8yZDZuazRSNXNJNEl6d3RxZ29qSTJ1aURQQkg4YWxpd3Y5ZDYydnNzTGM1a1lXemViQzlDaUYwTldPVkVOMGNLVjhWNlFUYlpOVlVhU3oxUnFuYStqajM5bzFTWXl6UkRIcnpHbmI2eTNRYUx1bWRzRmpwWmRST3p6UkNlc3B1NVBJS0Z2cEJOMTUwQkNuZWgwZllDK2xrNXRrdW9FZ0hzQmFnbWJhTjRJajJPMGVUMDN1amk0K2YwOU45VE0xaFROUXNyWFJvakJ5anEzRC9OVk1UbUlLOG9ZTVJ6Wk9TOWlSVzlJN2NucnRibHQrdDlOL3RVNHltRkg4L2xnWEFWYmxCNm8yL1BlQkdKUHNlU05kczh5NkM2WXprWEU5dUN2QW9PazIvVXp3a1NXdkpWZFREZWgxd2tLbGE5ck1YRWdPTTNBZFBsRjI5SkVGMzljZGlXVlJhNjB6YllnYkZmYXhXUHl2NEtpTy80ZEJrR3VBRzE3TzREVFRRUklydVV0NWVrOWdzeHFvQ1pNY0kvejNtRVRKdEIiLCJtYWMiOiI4YjgyYmVlYzIzYTNiZThmN2I5ZjAyMTZkYzVmYTk4ODFjM2MyNGI5Y2U5M2ViYTJkN2I0Y2QwMWY2ODU1MzI3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktCcW0xWUlLMUVBdjNqVm9zcDF6REE9PSIsInZhbHVlIjoieW11aTB5L1dFQ0JJcnA4RGRCb29RVUpNbU92VlhxT1lML3UvRFhxU24wUmpCRng1d1BYS3FxY04xTlR5Z3piWldZZ1MwVkVxQ3NLL1VCUWt0OEMyRkJQc0FITTF2UlQvLzZmREJLMnR6bE51SUwwUFhDZFU1V1FEZ3psSGJkWTc2b2Q0eWRzT1dFYVVuVGwyaXl2Q1Vud3RSTXNXMUI4L2h4SVVBdEVXZ1BPLzN5UkRVYlJPRWQrQzk4ZnhrL3FaQWdRaVdzQTRmWDZVQ2FQeitmazczMjJDNzVoSEJNRWFDZ0pYNzlEVjRrNkN4V3h0QlhFRStRWDgveDFCSDRra2N3SU5JWkY5Z3RRT1FhTUFTK2w1YjJYQnlDZGhod2twZ1I4L1hTY2V6MzN1azRjdGRvTUZNWHA1elFkOWdIaU95dUI5aVllamRML3FrYTFkUGJORkx1WUd1dGFKMmJUUU02QzJNd1NoUW8xRmFzK0hYalpsa2I3MTJJM2Z4dVVRUVhSN2FYNjc4WFY3UjF1NitxVVBiRyt6Q1VDRDhHN29LMXUyaysxLzFVTW9HcThsbHhvdnlTOHcyUTc2YWplRWkxNmdlcFhDMWoxQmltbnBHTXc4ek4wYk5wVjZETUxpa0xEdW1nQVlVVUl5Uk0rWVpnbWlldyt4SXQrVEt0M2ciLCJtYWMiOiJiMjg3NjVhNjBiYTI3ODIzNGI2ZTUyMzU3NmNiZDQ5MTQ0M2MxNmU2M2EwMTNhMDVlOWVjY2YxMWEzYWJhOWJlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpuWTVWSG40blJQeTVwVjVJV01YNlE9PSIsInZhbHVlIjoicmxmd0E4dm9iNUVYNU1rZUw4MUJpL05QZzl3OURsamZyUnYweHFkRUxiamtNMUxpUnc4enlHZGZwUVkyNWVZQXgzN3g3ZUdqSUxMQkNjOWMrUEVxS1lFSTBxaEZqRTZxYlpGSk4yNk8yZDZuazRSNXNJNEl6d3RxZ29qSTJ1aURQQkg4YWxpd3Y5ZDYydnNzTGM1a1lXemViQzlDaUYwTldPVkVOMGNLVjhWNlFUYlpOVlVhU3oxUnFuYStqajM5bzFTWXl6UkRIcnpHbmI2eTNRYUx1bWRzRmpwWmRST3p6UkNlc3B1NVBJS0Z2cEJOMTUwQkNuZWgwZllDK2xrNXRrdW9FZ0hzQmFnbWJhTjRJajJPMGVUMDN1amk0K2YwOU45VE0xaFROUXNyWFJvakJ5anEzRC9OVk1UbUlLOG9ZTVJ6Wk9TOWlSVzlJN2NucnRibHQrdDlOL3RVNHltRkg4L2xnWEFWYmxCNm8yL1BlQkdKUHNlU05kczh5NkM2WXprWEU5dUN2QW9PazIvVXp3a1NXdkpWZFREZWgxd2tLbGE5ck1YRWdPTTNBZFBsRjI5SkVGMzljZGlXVlJhNjB6YllnYkZmYXhXUHl2NEtpTy80ZEJrR3VBRzE3TzREVFRRUklydVV0NWVrOWdzeHFvQ1pNY0kvejNtRVRKdEIiLCJtYWMiOiI4YjgyYmVlYzIzYTNiZThmN2I5ZjAyMTZkYzVmYTk4ODFjM2MyNGI5Y2U5M2ViYTJkN2I0Y2QwMWY2ODU1MzI3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400545450\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-311209629 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/budget/eyJpdiI6IitUUkg3UTRnWGVYT1hkZm9Da0NPZXc9PSIsInZhbHVlIjoiZStjelcvdEROa3hBMXEzZVd5NkdKUT09IiwibWFjIjoiNzk4NWQxMmViNWIzMzdiYjg3Njg1OTI5MWExNzQ3ZjQ3MDBmODgxNDZhZWU1OWUwZmNmN2JiMWI4MTMxZmM1YyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311209629\", {\"maxDepth\":0})</script>\n"}}