{"__meta": {"id": "X9e48eaf779006bda9f517ce0b7547aba", "datetime": "2025-06-26 23:14:58", "utime": **********.397969, "method": "GET", "uri": "/vender/4/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.002438, "end": **********.397982, "duration": 0.39554381370544434, "duration_str": "396ms", "measures": [{"label": "Booting", "start": **********.002438, "relative_start": 0, "end": **********.325575, "relative_end": **********.325575, "duration": 0.3231370449066162, "duration_str": "323ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.325584, "relative_start": 0.32314586639404297, "end": **********.397984, "relative_end": 2.1457672119140625e-06, "duration": 0.07240009307861328, "duration_str": "72.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51447136, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x vender.edit", "param_count": null, "params": [], "start": **********.390383, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/vender/edit.blade.phpvender.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvender%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "vender.edit"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.394602, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}, {"name": "1x components.mobile", "param_count": null, "params": [], "start": **********.395501, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/components/mobile.blade.phpcomponents.mobile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fcomponents%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.mobile"}]}, "route": {"uri": "GET vender/{vender}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "vender.edit", "controller": "App\\Http\\Controllers\\VenderController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=168\" onclick=\"\">app/Http/Controllers/VenderController.php:168-182</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.0037600000000000003, "accumulated_duration_str": "3.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3511639, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 43.351}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.36057, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 43.351, "width_percent": 10.904}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.37389, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 54.255, "width_percent": 14.362}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.3756652, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 68.617, "width_percent": 7.181}, {"sql": "select * from `venders` where `venders`.`id` = '4' limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\VenderController.php", "line": 172}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.379712, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "VenderController.php:172", "source": "app/Http/Controllers/VenderController.php:172", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=172", "ajax": false, "filename": "VenderController.php", "line": "172"}, "connection": "kdmkjkqknb", "start_percent": 75.798, "width_percent": 9.309}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'vendor' and `record_id` = 4", "type": "query", "params": [], "bindings": ["vendor", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\VenderController.php", "line": 173}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.38132, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "kdmkjkqknb", "start_percent": 85.106, "width_percent": 9.84}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'vendor'", "type": "query", "params": [], "bindings": ["15", "vendor"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\VenderController.php", "line": 174}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.382993, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "VenderController.php:174", "source": "app/Http/Controllers/VenderController.php:174", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=174", "ajax": false, "filename": "VenderController.php", "line": "174"}, "connection": "kdmkjkqknb", "start_percent": 94.947, "width_percent": 5.053}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Vender": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=1", "ajax": false, "filename": "Vender.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2053277070 data-indent-pad=\"  \"><span class=sf-dump-note>edit vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053277070\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.378854, "xdebug_link": null}]}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/vender/4/edit", "status_code": "<pre class=sf-dump id=sf-dump-1921354893 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1921354893\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1359922934 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1359922934\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-675081916 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-675081916\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1783218350 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979694284%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imt5bU9zNWZBTkJLS1FhaEphZWxjOFE9PSIsInZhbHVlIjoiY3djclIzVXhZRGdnVm5ZSmtQTEI1cFdwTnN5L3VMQ2hGUXMzV01SQUpKNW8wRFNCbkxyYmZ2WFVIcVlaV0w1c1JwWVpKazJFc1RMOVUzaEZBcExQVFFoY0ZGSWpkS3J2TlVpRHVwazNGdW93dWNPdmJkcUtvMXNZK1JzcnE5U0Z4bHh3WGo4NFVUZnQ1WWtkSkpMeURCbGxCeUpIY0xSRng2Y1Bub2syNEZzMTVZNWVWcndVNlFFVVc2T2dkOHgvNUQvUDUweE5vaHpnTnNYNTZOcnFpVHZ6dzdRSWpGWGsyaVZnY3dCMU1HYUpVK2hPa1I1MHRuclZtWGtFM3J0eGtLdlJVODdrTFpFeDRWVTVTVUxlaUxDNlpnSHEya0dDME9veGZaTlo3MmNaaTlyeTFzZUdGUzV3SHB2cGNHaTgxM0thdWxLNklZcU5zaVJGajlnMFg5a1JueXc1VWVmN1VEaTUyblFkMDBCTHRaM2lDeW9DYzYwaVRxUmpDUlJiYnIwUHpsY3FTMmlIUVYxRFVlQ2QyWGd6Y2V4RUw3VmdJZVpzam9UQmo5ZU5aemdDbFpYUUZFN3Z0UndWSDVIdlFnMEx5TWQ2KzJlUE5ZM1IyOXNQenZ3Sm5BeUVNdVFrbTRQejI3VzJvQ1JqM1BpOU9XNTBDdG5kcDMyWkl6VysiLCJtYWMiOiJlMjJiYTM3YTg1YTJiMmNkOTZiOTUyYWJhODQ3ZDQyM2ZiOGQ1YzljYWI1ZTlkYWI5Mzg5MWIwZmRjMWJlMzAxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlcvNEYveC9uRUJQdGVucDBtMHJnQ3c9PSIsInZhbHVlIjoic2xnZzdHVXA4YVdTSy80NHF0T1VPcDd3and5akRIRWYrOEZOc3NLL0xQU0VOYUhpYlNWOTZqOXkydDVpSWpMTFpocHdpUU1NamRrN1pLU2wraEdEdmNUZUVqL3VmZWt1bU5OdXpUcjBTeWxFQXN4SzAxaFBWeFpzN3FQRDV2QnMwMWhaRTF1MTB5ZFZ1RzkxQmg0b1NBend4Q0ZIUmJoamNCOThSeXZlM2Z5QlR6czFhbHFiYmsyc2I3cUk1TUdRT1RkTDhORnY2bGtTeld4bm9WVkNmNzMzSUZ0bzB4S0xaWFYvV0dHQldjQmdBS0VIc2hFejBzb3A4MTczZ0F2RkN4YnFjSmlieEh5ek1Lanh2c25PNzBwNVR1NUQxQWIxeDY3ZEhVVTNOVTBwWGoxclYxTTBQbUE3MVJWOG8yNk5xTDg5R2x2MnVGeitFM1NjeHRqb0NEQjBDS3NXT2ZCcjV0WDV5RXlzSVQ5b1QwQzNvd3RMTHd1cGtDY2NrTTlRMmRtM0NMUVNNcFdPaFdwa2NtL2EvMCt1N3lobmp2Z2h6V202dXNETWlwMGh4bVE4aTdJR0lscE4wcFhuRWM4dzJRZ05wQlpMTHF4VFhubk1DVWErbzdzWEhxQ0ZoZG9pNCtYNWF3dEtrNEFUdEFlUWxqN0ZEMnp3Y2ppemxGWmUiLCJtYWMiOiJhMzYxNzQ1ZDlhMjZiODBkMDE1NDFiOTQ3MzJmZWFlOTcyZGRmMmE5MDJlYTFkOTQ5NWQ2NWRkZjM5OGMwYjJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1783218350\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1420779084 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1420779084\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-538571840 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:14:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik84VVgrUVBzSEl5anAwZHBOK3pFZlE9PSIsInZhbHVlIjoiU0g4Wlk3MFVBdHJZeHFnN0JiaFhLT25HTTFPNXpGN1diQkNKdzRwQjRNYUt4dDFneEdlN2cwOTZMZURHZ29KS2Raais2c1FpWjBTRXl3cjNhNHowc2p4SWVGS09ZcWlpU2pld25MSWVYZkVObFNWZ0c3Wkx4aUxBcUd1bUFQNlczVXFRM0FiQ3hIR3FQRlFwNmdtK0VDeVVwSUNLOElrU0R6djB4VFRoaEQ0VFFyZ1ZRa28yOEdIUWZaaUxaWXF2Zkl1eEtWSDNzR04wY3VwQWIyVytoNnRMb1dQZUQ4djdQQ2g4V25pVk1LUmIwYXJ6YTZMcDQ4OFQza3hicXVCM2NqTkw2c1NISlFPR0poSDF5d0dvTXFNcHdtWjZzcjgzQ3RRQWwzQ2tkczlDTDU1TTllMnEwQ0FycHFqOVFLQnI5S1BtRGhTTkpEUjBGQkk2WWRPT2pqd2tRNi9sb0xwY2s4MnE5YkN1NXRUbjZQMXp3MkNMeGJrTXpCVzZmdnRNc2lKT1YwbkUrKzc2QUxUR1BZSVVIa2NsSzA3anVqa0xXMEkxQWNGSmVEYzdvR244YXJpYmZFek4yNW5MYjZXQ3QxbG9UaExieFk0SEdRZmI0UlBEUTBXWk51eUpsdG9ta0hiRmdzV2xSVVJ6eHl3NDlHbHJXT0Z1WkZEbitvMGYiLCJtYWMiOiIxMWZiODkzZTE3MzVjZGE3NGFlM2E5ZTA5MWIwZDIxNDFjMTQyZGU5MGYwZGIwNmI2ZTIzYTM4NjI1N2RkMjRkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:14:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjdvWFU3K2JPL3d2MmUzd0R5TmVreUE9PSIsInZhbHVlIjoidXM3TWdOTkwxS21YaWNpWlBtU3B3TTVWUzQ2ekdGdXJyNDBTcDUzNC82V3VtOStEa3BObFJtYm5KY2pjbWlBODFHZlk0VFM5WWJmdUNVQ0JDZW9HYTRVejlMVVluVHVSSVQxUmhYMzVmUC9IQ29tR3phU2VNNFZmd09Nd21wK2dudFlCVW5BeXVqL0JHOU41SUZsYU9VblVDdmRLdDVIVVcySUpXVStNMng0YU54WTZVT0NRNDk0SWpudld4eVBHVXgxb2UyYzdldXY1c0dpbmd3NzRzUU5oS3lvbWRTWStBTjRSR3dWY2dWck1JM1FHVGh5Y2tDYnlDbVJ4VUNEL3h5RkNodTBXdTViMmttL3NBLzBtNERWeGc1bVZDNTRRQUdZYkE0ZE03RFNVbmJzUU1tdE5uOXZCSnpXZDJvUXlVNGZoc043ejhHbXJKMlM2ZjAyVElKUUR5UVlwK3UwVGtoREFvZ29ybWdxRGZwOFRNd2k4OWxCMmxVRHZrU09VWHo1NVJZeFp0SlV0OW1OYTJSVWtNNTlMS1BnMElzVjlod3ZOblZ1ZncreFhkWWhUUnVpYlVFZ1p0UDYyOXFLaldrUzB1OXZydzRRektLU1J2dUZpdGtVRUduVERMd3E2RndYeVpxYjhkNXlUMEErWHZ6eWtSK1k2RkNIRWIvdlQiLCJtYWMiOiJlYWM4NTQzMWRiMDhlYmM4MjRjYjU0ZmI4Y2YyNDFmN2Y5MTVlMWRmZWExMDFiNTNmZGJiOTcwMGYwNTRhNmNlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:14:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik84VVgrUVBzSEl5anAwZHBOK3pFZlE9PSIsInZhbHVlIjoiU0g4Wlk3MFVBdHJZeHFnN0JiaFhLT25HTTFPNXpGN1diQkNKdzRwQjRNYUt4dDFneEdlN2cwOTZMZURHZ29KS2Raais2c1FpWjBTRXl3cjNhNHowc2p4SWVGS09ZcWlpU2pld25MSWVYZkVObFNWZ0c3Wkx4aUxBcUd1bUFQNlczVXFRM0FiQ3hIR3FQRlFwNmdtK0VDeVVwSUNLOElrU0R6djB4VFRoaEQ0VFFyZ1ZRa28yOEdIUWZaaUxaWXF2Zkl1eEtWSDNzR04wY3VwQWIyVytoNnRMb1dQZUQ4djdQQ2g4V25pVk1LUmIwYXJ6YTZMcDQ4OFQza3hicXVCM2NqTkw2c1NISlFPR0poSDF5d0dvTXFNcHdtWjZzcjgzQ3RRQWwzQ2tkczlDTDU1TTllMnEwQ0FycHFqOVFLQnI5S1BtRGhTTkpEUjBGQkk2WWRPT2pqd2tRNi9sb0xwY2s4MnE5YkN1NXRUbjZQMXp3MkNMeGJrTXpCVzZmdnRNc2lKT1YwbkUrKzc2QUxUR1BZSVVIa2NsSzA3anVqa0xXMEkxQWNGSmVEYzdvR244YXJpYmZFek4yNW5MYjZXQ3QxbG9UaExieFk0SEdRZmI0UlBEUTBXWk51eUpsdG9ta0hiRmdzV2xSVVJ6eHl3NDlHbHJXT0Z1WkZEbitvMGYiLCJtYWMiOiIxMWZiODkzZTE3MzVjZGE3NGFlM2E5ZTA5MWIwZDIxNDFjMTQyZGU5MGYwZGIwNmI2ZTIzYTM4NjI1N2RkMjRkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:14:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjdvWFU3K2JPL3d2MmUzd0R5TmVreUE9PSIsInZhbHVlIjoidXM3TWdOTkwxS21YaWNpWlBtU3B3TTVWUzQ2ekdGdXJyNDBTcDUzNC82V3VtOStEa3BObFJtYm5KY2pjbWlBODFHZlk0VFM5WWJmdUNVQ0JDZW9HYTRVejlMVVluVHVSSVQxUmhYMzVmUC9IQ29tR3phU2VNNFZmd09Nd21wK2dudFlCVW5BeXVqL0JHOU41SUZsYU9VblVDdmRLdDVIVVcySUpXVStNMng0YU54WTZVT0NRNDk0SWpudld4eVBHVXgxb2UyYzdldXY1c0dpbmd3NzRzUU5oS3lvbWRTWStBTjRSR3dWY2dWck1JM1FHVGh5Y2tDYnlDbVJ4VUNEL3h5RkNodTBXdTViMmttL3NBLzBtNERWeGc1bVZDNTRRQUdZYkE0ZE03RFNVbmJzUU1tdE5uOXZCSnpXZDJvUXlVNGZoc043ejhHbXJKMlM2ZjAyVElKUUR5UVlwK3UwVGtoREFvZ29ybWdxRGZwOFRNd2k4OWxCMmxVRHZrU09VWHo1NVJZeFp0SlV0OW1OYTJSVWtNNTlMS1BnMElzVjlod3ZOblZ1ZncreFhkWWhUUnVpYlVFZ1p0UDYyOXFLaldrUzB1OXZydzRRektLU1J2dUZpdGtVRUduVERMd3E2RndYeVpxYjhkNXlUMEErWHZ6eWtSK1k2RkNIRWIvdlQiLCJtYWMiOiJlYWM4NTQzMWRiMDhlYmM4MjRjYjU0ZmI4Y2YyNDFmN2Y5MTVlMWRmZWExMDFiNTNmZGJiOTcwMGYwNTRhNmNlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:14:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538571840\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2100299720 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100299720\", {\"maxDepth\":0})</script>\n"}}