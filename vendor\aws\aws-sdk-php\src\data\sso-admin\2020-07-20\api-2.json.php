<?php
// This file was auto-generated from sdk-root/src/data/sso-admin/2020-07-20/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-07-20', 'endpointPrefix' => 'sso', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'SSO Admin', 'serviceFullName' => 'AWS Single Sign-On Admin', 'serviceId' => 'SSO Admin', 'signatureVersion' => 'v4', 'signingName' => 'sso', 'targetPrefix' => 'SWBExternalService', 'uid' => 'sso-admin-2020-07-20', ], 'operations' => [ 'AttachCustomerManagedPolicyReferenceToPermissionSet' => [ 'name' => 'AttachCustomerManagedPolicyReferenceToPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachCustomerManagedPolicyReferenceToPermissionSetRequest', ], 'output' => [ 'shape' => 'AttachCustomerManagedPolicyReferenceToPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'AttachManagedPolicyToPermissionSet' => [ 'name' => 'AttachManagedPolicyToPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachManagedPolicyToPermissionSetRequest', ], 'output' => [ 'shape' => 'AttachManagedPolicyToPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateAccountAssignment' => [ 'name' => 'CreateAccountAssignment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAccountAssignmentRequest', ], 'output' => [ 'shape' => 'CreateAccountAssignmentResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateApplication' => [ 'name' => 'CreateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateApplicationRequest', ], 'output' => [ 'shape' => 'CreateApplicationResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateApplicationAssignment' => [ 'name' => 'CreateApplicationAssignment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateApplicationAssignmentRequest', ], 'output' => [ 'shape' => 'CreateApplicationAssignmentResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateInstance' => [ 'name' => 'CreateInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstanceRequest', ], 'output' => [ 'shape' => 'CreateInstanceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateInstanceAccessControlAttributeConfiguration' => [ 'name' => 'CreateInstanceAccessControlAttributeConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstanceAccessControlAttributeConfigurationRequest', ], 'output' => [ 'shape' => 'CreateInstanceAccessControlAttributeConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreatePermissionSet' => [ 'name' => 'CreatePermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePermissionSetRequest', ], 'output' => [ 'shape' => 'CreatePermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateTrustedTokenIssuer' => [ 'name' => 'CreateTrustedTokenIssuer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTrustedTokenIssuerRequest', ], 'output' => [ 'shape' => 'CreateTrustedTokenIssuerResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteAccountAssignment' => [ 'name' => 'DeleteAccountAssignment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAccountAssignmentRequest', ], 'output' => [ 'shape' => 'DeleteAccountAssignmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteApplication' => [ 'name' => 'DeleteApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteApplicationRequest', ], 'output' => [ 'shape' => 'DeleteApplicationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteApplicationAccessScope' => [ 'name' => 'DeleteApplicationAccessScope', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteApplicationAccessScopeRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteApplicationAssignment' => [ 'name' => 'DeleteApplicationAssignment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteApplicationAssignmentRequest', ], 'output' => [ 'shape' => 'DeleteApplicationAssignmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteApplicationAuthenticationMethod' => [ 'name' => 'DeleteApplicationAuthenticationMethod', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteApplicationAuthenticationMethodRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteApplicationGrant' => [ 'name' => 'DeleteApplicationGrant', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteApplicationGrantRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteInlinePolicyFromPermissionSet' => [ 'name' => 'DeleteInlinePolicyFromPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInlinePolicyFromPermissionSetRequest', ], 'output' => [ 'shape' => 'DeleteInlinePolicyFromPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteInstance' => [ 'name' => 'DeleteInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInstanceRequest', ], 'output' => [ 'shape' => 'DeleteInstanceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteInstanceAccessControlAttributeConfiguration' => [ 'name' => 'DeleteInstanceAccessControlAttributeConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInstanceAccessControlAttributeConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteInstanceAccessControlAttributeConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeletePermissionSet' => [ 'name' => 'DeletePermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePermissionSetRequest', ], 'output' => [ 'shape' => 'DeletePermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeletePermissionsBoundaryFromPermissionSet' => [ 'name' => 'DeletePermissionsBoundaryFromPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePermissionsBoundaryFromPermissionSetRequest', ], 'output' => [ 'shape' => 'DeletePermissionsBoundaryFromPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteTrustedTokenIssuer' => [ 'name' => 'DeleteTrustedTokenIssuer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTrustedTokenIssuerRequest', ], 'output' => [ 'shape' => 'DeleteTrustedTokenIssuerResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DescribeAccountAssignmentCreationStatus' => [ 'name' => 'DescribeAccountAssignmentCreationStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountAssignmentCreationStatusRequest', ], 'output' => [ 'shape' => 'DescribeAccountAssignmentCreationStatusResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeAccountAssignmentDeletionStatus' => [ 'name' => 'DescribeAccountAssignmentDeletionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountAssignmentDeletionStatusRequest', ], 'output' => [ 'shape' => 'DescribeAccountAssignmentDeletionStatusResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeApplication' => [ 'name' => 'DescribeApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeApplicationRequest', ], 'output' => [ 'shape' => 'DescribeApplicationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeApplicationAssignment' => [ 'name' => 'DescribeApplicationAssignment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeApplicationAssignmentRequest', ], 'output' => [ 'shape' => 'DescribeApplicationAssignmentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeApplicationProvider' => [ 'name' => 'DescribeApplicationProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeApplicationProviderRequest', ], 'output' => [ 'shape' => 'DescribeApplicationProviderResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeInstance' => [ 'name' => 'DescribeInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstanceRequest', ], 'output' => [ 'shape' => 'DescribeInstanceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeInstanceAccessControlAttributeConfiguration' => [ 'name' => 'DescribeInstanceAccessControlAttributeConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstanceAccessControlAttributeConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeInstanceAccessControlAttributeConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribePermissionSet' => [ 'name' => 'DescribePermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePermissionSetRequest', ], 'output' => [ 'shape' => 'DescribePermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribePermissionSetProvisioningStatus' => [ 'name' => 'DescribePermissionSetProvisioningStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePermissionSetProvisioningStatusRequest', ], 'output' => [ 'shape' => 'DescribePermissionSetProvisioningStatusResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeTrustedTokenIssuer' => [ 'name' => 'DescribeTrustedTokenIssuer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrustedTokenIssuerRequest', ], 'output' => [ 'shape' => 'DescribeTrustedTokenIssuerResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DetachCustomerManagedPolicyReferenceFromPermissionSet' => [ 'name' => 'DetachCustomerManagedPolicyReferenceFromPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachCustomerManagedPolicyReferenceFromPermissionSetRequest', ], 'output' => [ 'shape' => 'DetachCustomerManagedPolicyReferenceFromPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DetachManagedPolicyFromPermissionSet' => [ 'name' => 'DetachManagedPolicyFromPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachManagedPolicyFromPermissionSetRequest', ], 'output' => [ 'shape' => 'DetachManagedPolicyFromPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetApplicationAccessScope' => [ 'name' => 'GetApplicationAccessScope', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetApplicationAccessScopeRequest', ], 'output' => [ 'shape' => 'GetApplicationAccessScopeResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetApplicationAssignmentConfiguration' => [ 'name' => 'GetApplicationAssignmentConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetApplicationAssignmentConfigurationRequest', ], 'output' => [ 'shape' => 'GetApplicationAssignmentConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetApplicationAuthenticationMethod' => [ 'name' => 'GetApplicationAuthenticationMethod', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetApplicationAuthenticationMethodRequest', ], 'output' => [ 'shape' => 'GetApplicationAuthenticationMethodResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetApplicationGrant' => [ 'name' => 'GetApplicationGrant', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetApplicationGrantRequest', ], 'output' => [ 'shape' => 'GetApplicationGrantResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetInlinePolicyForPermissionSet' => [ 'name' => 'GetInlinePolicyForPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInlinePolicyForPermissionSetRequest', ], 'output' => [ 'shape' => 'GetInlinePolicyForPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetPermissionsBoundaryForPermissionSet' => [ 'name' => 'GetPermissionsBoundaryForPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPermissionsBoundaryForPermissionSetRequest', ], 'output' => [ 'shape' => 'GetPermissionsBoundaryForPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListAccountAssignmentCreationStatus' => [ 'name' => 'ListAccountAssignmentCreationStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountAssignmentCreationStatusRequest', ], 'output' => [ 'shape' => 'ListAccountAssignmentCreationStatusResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListAccountAssignmentDeletionStatus' => [ 'name' => 'ListAccountAssignmentDeletionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountAssignmentDeletionStatusRequest', ], 'output' => [ 'shape' => 'ListAccountAssignmentDeletionStatusResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListAccountAssignments' => [ 'name' => 'ListAccountAssignments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountAssignmentsRequest', ], 'output' => [ 'shape' => 'ListAccountAssignmentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListAccountAssignmentsForPrincipal' => [ 'name' => 'ListAccountAssignmentsForPrincipal', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountAssignmentsForPrincipalRequest', ], 'output' => [ 'shape' => 'ListAccountAssignmentsForPrincipalResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListAccountsForProvisionedPermissionSet' => [ 'name' => 'ListAccountsForProvisionedPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountsForProvisionedPermissionSetRequest', ], 'output' => [ 'shape' => 'ListAccountsForProvisionedPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListApplicationAccessScopes' => [ 'name' => 'ListApplicationAccessScopes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListApplicationAccessScopesRequest', ], 'output' => [ 'shape' => 'ListApplicationAccessScopesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListApplicationAssignments' => [ 'name' => 'ListApplicationAssignments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListApplicationAssignmentsRequest', ], 'output' => [ 'shape' => 'ListApplicationAssignmentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListApplicationAssignmentsForPrincipal' => [ 'name' => 'ListApplicationAssignmentsForPrincipal', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListApplicationAssignmentsForPrincipalRequest', ], 'output' => [ 'shape' => 'ListApplicationAssignmentsForPrincipalResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListApplicationAuthenticationMethods' => [ 'name' => 'ListApplicationAuthenticationMethods', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListApplicationAuthenticationMethodsRequest', ], 'output' => [ 'shape' => 'ListApplicationAuthenticationMethodsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListApplicationGrants' => [ 'name' => 'ListApplicationGrants', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListApplicationGrantsRequest', ], 'output' => [ 'shape' => 'ListApplicationGrantsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListApplicationProviders' => [ 'name' => 'ListApplicationProviders', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListApplicationProvidersRequest', ], 'output' => [ 'shape' => 'ListApplicationProvidersResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListApplications' => [ 'name' => 'ListApplications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListApplicationsRequest', ], 'output' => [ 'shape' => 'ListApplicationsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListCustomerManagedPolicyReferencesInPermissionSet' => [ 'name' => 'ListCustomerManagedPolicyReferencesInPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCustomerManagedPolicyReferencesInPermissionSetRequest', ], 'output' => [ 'shape' => 'ListCustomerManagedPolicyReferencesInPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListInstances' => [ 'name' => 'ListInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInstancesRequest', ], 'output' => [ 'shape' => 'ListInstancesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListManagedPoliciesInPermissionSet' => [ 'name' => 'ListManagedPoliciesInPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListManagedPoliciesInPermissionSetRequest', ], 'output' => [ 'shape' => 'ListManagedPoliciesInPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPermissionSetProvisioningStatus' => [ 'name' => 'ListPermissionSetProvisioningStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPermissionSetProvisioningStatusRequest', ], 'output' => [ 'shape' => 'ListPermissionSetProvisioningStatusResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPermissionSets' => [ 'name' => 'ListPermissionSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPermissionSetsRequest', ], 'output' => [ 'shape' => 'ListPermissionSetsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPermissionSetsProvisionedToAccount' => [ 'name' => 'ListPermissionSetsProvisionedToAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPermissionSetsProvisionedToAccountRequest', ], 'output' => [ 'shape' => 'ListPermissionSetsProvisionedToAccountResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTrustedTokenIssuers' => [ 'name' => 'ListTrustedTokenIssuers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTrustedTokenIssuersRequest', ], 'output' => [ 'shape' => 'ListTrustedTokenIssuersResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ProvisionPermissionSet' => [ 'name' => 'ProvisionPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ProvisionPermissionSetRequest', ], 'output' => [ 'shape' => 'ProvisionPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutApplicationAccessScope' => [ 'name' => 'PutApplicationAccessScope', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutApplicationAccessScopeRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'PutApplicationAssignmentConfiguration' => [ 'name' => 'PutApplicationAssignmentConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutApplicationAssignmentConfigurationRequest', ], 'output' => [ 'shape' => 'PutApplicationAssignmentConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'PutApplicationAuthenticationMethod' => [ 'name' => 'PutApplicationAuthenticationMethod', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutApplicationAuthenticationMethodRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'PutApplicationGrant' => [ 'name' => 'PutApplicationGrant', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutApplicationGrantRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'PutInlinePolicyToPermissionSet' => [ 'name' => 'PutInlinePolicyToPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutInlinePolicyToPermissionSetRequest', ], 'output' => [ 'shape' => 'PutInlinePolicyToPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutPermissionsBoundaryToPermissionSet' => [ 'name' => 'PutPermissionsBoundaryToPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutPermissionsBoundaryToPermissionSetRequest', ], 'output' => [ 'shape' => 'PutPermissionsBoundaryToPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateApplication' => [ 'name' => 'UpdateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateApplicationRequest', ], 'output' => [ 'shape' => 'UpdateApplicationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateInstance' => [ 'name' => 'UpdateInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateInstanceRequest', ], 'output' => [ 'shape' => 'UpdateInstanceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateInstanceAccessControlAttributeConfiguration' => [ 'name' => 'UpdateInstanceAccessControlAttributeConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateInstanceAccessControlAttributeConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateInstanceAccessControlAttributeConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdatePermissionSet' => [ 'name' => 'UpdatePermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePermissionSetRequest', ], 'output' => [ 'shape' => 'UpdatePermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateTrustedTokenIssuer' => [ 'name' => 'UpdateTrustedTokenIssuer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTrustedTokenIssuerRequest', ], 'output' => [ 'shape' => 'UpdateTrustedTokenIssuerResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], ], 'shapes' => [ 'AccessControlAttribute' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'AccessControlAttributeKey', ], 'Value' => [ 'shape' => 'AccessControlAttributeValue', ], ], ], 'AccessControlAttributeKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@]+$', ], 'AccessControlAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessControlAttribute', ], 'max' => 50, 'min' => 0, ], 'AccessControlAttributeValue' => [ 'type' => 'structure', 'required' => [ 'Source', ], 'members' => [ 'Source' => [ 'shape' => 'AccessControlAttributeValueSourceList', ], ], ], 'AccessControlAttributeValueSource' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@\\[\\]\\{\\}\\$\\\\"]*$', ], 'AccessControlAttributeValueSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessControlAttributeValueSource', ], 'max' => 1, 'min' => 1, ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'AccessDeniedExceptionMessage', ], ], 'exception' => true, ], 'AccessDeniedExceptionMessage' => [ 'type' => 'string', ], 'AccountAssignment' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], ], ], 'AccountAssignmentForPrincipal' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], ], ], 'AccountAssignmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountAssignment', ], ], 'AccountAssignmentListForPrincipal' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountAssignmentForPrincipal', ], ], 'AccountAssignmentOperationStatus' => [ 'type' => 'structure', 'members' => [ 'CreatedDate' => [ 'shape' => 'Date', ], 'FailureReason' => [ 'shape' => 'Reason', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], 'RequestId' => [ 'shape' => 'UUId', ], 'Status' => [ 'shape' => 'StatusValues', ], 'TargetId' => [ 'shape' => 'TargetId', ], 'TargetType' => [ 'shape' => 'TargetType', ], ], ], 'AccountAssignmentOperationStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountAssignmentOperationStatusMetadata', ], ], 'AccountAssignmentOperationStatusMetadata' => [ 'type' => 'structure', 'members' => [ 'CreatedDate' => [ 'shape' => 'Date', ], 'RequestId' => [ 'shape' => 'UUId', ], 'Status' => [ 'shape' => 'StatusValues', ], ], ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'AccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], ], 'ActorPolicyDocument' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'Application' => [ 'type' => 'structure', 'members' => [ 'ApplicationAccount' => [ 'shape' => 'AccountId', ], 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'ApplicationProviderArn' => [ 'shape' => 'ApplicationProviderArn', ], 'CreatedDate' => [ 'shape' => 'Date', ], 'Description' => [ 'shape' => 'Description', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'Name' => [ 'shape' => 'NameType', ], 'PortalOptions' => [ 'shape' => 'PortalOptions', ], 'Status' => [ 'shape' => 'ApplicationStatus', ], ], ], 'ApplicationArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::\\d{12}:application/(sso)?ins-[a-zA-Z0-9-.]{16}/apl-[a-zA-Z0-9]{16}$', ], 'ApplicationAssignment' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'PrincipalId', 'PrincipalType', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], ], ], 'ApplicationAssignmentForPrincipal' => [ 'type' => 'structure', 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], ], ], 'ApplicationAssignmentListForPrincipal' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationAssignmentForPrincipal', ], ], 'ApplicationAssignmentsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationAssignment', ], ], 'ApplicationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Application', ], 'max' => 50, 'min' => 0, ], 'ApplicationProvider' => [ 'type' => 'structure', 'required' => [ 'ApplicationProviderArn', ], 'members' => [ 'ApplicationProviderArn' => [ 'shape' => 'ApplicationProviderArn', ], 'DisplayData' => [ 'shape' => 'DisplayData', ], 'FederationProtocol' => [ 'shape' => 'FederationProtocol', ], 'ResourceServerConfig' => [ 'shape' => 'ResourceServerConfig', ], ], ], 'ApplicationProviderArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::aws:applicationProvider/[a-zA-Z0-9-/]+$', ], 'ApplicationProviderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationProvider', ], ], 'ApplicationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ApplicationUrl' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^http(s)?:\\/\\/[-a-zA-Z0-9+&@#\\/%?=~_|!:,.;]*[-a-zA-Z0-9+&bb@#\\/%?=~_|]$', ], 'ApplicationVisibility' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AssignmentRequired' => [ 'type' => 'boolean', 'box' => true, ], 'AttachCustomerManagedPolicyReferenceToPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'CustomerManagedPolicyReference', 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'CustomerManagedPolicyReference' => [ 'shape' => 'CustomerManagedPolicyReference', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'AttachCustomerManagedPolicyReferenceToPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'AttachManagedPolicyToPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'ManagedPolicyArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'ManagedPolicyArn' => [ 'shape' => 'ManagedPolicyArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'AttachManagedPolicyToPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'AttachedManagedPolicy' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ManagedPolicyArn', ], 'Name' => [ 'shape' => 'Name', ], ], ], 'AttachedManagedPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachedManagedPolicy', ], ], 'AuthenticationMethod' => [ 'type' => 'structure', 'members' => [ 'Iam' => [ 'shape' => 'IamAuthenticationMethod', ], ], 'union' => true, ], 'AuthenticationMethodItem' => [ 'type' => 'structure', 'members' => [ 'AuthenticationMethod' => [ 'shape' => 'AuthenticationMethod', ], 'AuthenticationMethodType' => [ 'shape' => 'AuthenticationMethodType', ], ], ], 'AuthenticationMethodType' => [ 'type' => 'string', 'enum' => [ 'IAM', ], ], 'AuthenticationMethods' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthenticationMethodItem', ], ], 'AuthorizationCodeGrant' => [ 'type' => 'structure', 'members' => [ 'RedirectUris' => [ 'shape' => 'RedirectUris', ], ], ], 'AuthorizedTokenIssuer' => [ 'type' => 'structure', 'members' => [ 'AuthorizedAudiences' => [ 'shape' => 'TokenIssuerAudiences', ], 'TrustedTokenIssuerArn' => [ 'shape' => 'TrustedTokenIssuerArn', ], ], ], 'AuthorizedTokenIssuers' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthorizedTokenIssuer', ], 'max' => 10, 'min' => 1, ], 'ClaimAttributePath' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^\\p{L}+(?:(\\.|\\_)\\p{L}+){0,2}$', ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[!-~]+$', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ConflictExceptionMessage', ], ], 'exception' => true, ], 'ConflictExceptionMessage' => [ 'type' => 'string', ], 'CreateAccountAssignmentRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', 'PrincipalId', 'PrincipalType', 'TargetId', 'TargetType', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], 'TargetId' => [ 'shape' => 'TargetId', ], 'TargetType' => [ 'shape' => 'TargetType', ], ], ], 'CreateAccountAssignmentResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentCreationStatus' => [ 'shape' => 'AccountAssignmentOperationStatus', ], ], ], 'CreateApplicationAssignmentRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'PrincipalId', 'PrincipalType', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], ], ], 'CreateApplicationAssignmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationProviderArn', 'InstanceArn', 'Name', ], 'members' => [ 'ApplicationProviderArn' => [ 'shape' => 'ApplicationProviderArn', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Description' => [ 'shape' => 'Description', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'Name' => [ 'shape' => 'NameType', ], 'PortalOptions' => [ 'shape' => 'PortalOptions', ], 'Status' => [ 'shape' => 'ApplicationStatus', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], ], ], 'CreateInstanceAccessControlAttributeConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceAccessControlAttributeConfiguration', 'InstanceArn', ], 'members' => [ 'InstanceAccessControlAttributeConfiguration' => [ 'shape' => 'InstanceAccessControlAttributeConfiguration', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], ], ], 'CreateInstanceAccessControlAttributeConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateInstanceRequest' => [ 'type' => 'structure', 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Name' => [ 'shape' => 'NameType', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], ], ], 'CreatePermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'Name', ], 'members' => [ 'Description' => [ 'shape' => 'PermissionSetDescription', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'Name' => [ 'shape' => 'PermissionSetName', ], 'RelayState' => [ 'shape' => 'RelayState', ], 'SessionDuration' => [ 'shape' => 'Duration', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreatePermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'PermissionSet' => [ 'shape' => 'PermissionSet', ], ], ], 'CreateTrustedTokenIssuerRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'Name', 'TrustedTokenIssuerConfiguration', 'TrustedTokenIssuerType', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'Name' => [ 'shape' => 'TrustedTokenIssuerName', ], 'Tags' => [ 'shape' => 'TagList', ], 'TrustedTokenIssuerConfiguration' => [ 'shape' => 'TrustedTokenIssuerConfiguration', ], 'TrustedTokenIssuerType' => [ 'shape' => 'TrustedTokenIssuerType', ], ], ], 'CreateTrustedTokenIssuerResponse' => [ 'type' => 'structure', 'members' => [ 'TrustedTokenIssuerArn' => [ 'shape' => 'TrustedTokenIssuerArn', ], ], ], 'CustomerManagedPolicyReference' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'ManagedPolicyName', ], 'Path' => [ 'shape' => 'ManagedPolicyPath', ], ], ], 'CustomerManagedPolicyReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomerManagedPolicyReference', ], ], 'Date' => [ 'type' => 'timestamp', ], 'DeleteAccountAssignmentRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', 'PrincipalId', 'PrincipalType', 'TargetId', 'TargetType', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], 'TargetId' => [ 'shape' => 'TargetId', ], 'TargetType' => [ 'shape' => 'TargetType', ], ], ], 'DeleteAccountAssignmentResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentDeletionStatus' => [ 'shape' => 'AccountAssignmentOperationStatus', ], ], ], 'DeleteApplicationAccessScopeRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'Scope', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'Scope' => [ 'shape' => 'Scope', ], ], ], 'DeleteApplicationAssignmentRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'PrincipalId', 'PrincipalType', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], ], ], 'DeleteApplicationAssignmentResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteApplicationAuthenticationMethodRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'AuthenticationMethodType', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'AuthenticationMethodType' => [ 'shape' => 'AuthenticationMethodType', ], ], ], 'DeleteApplicationGrantRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'GrantType', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'GrantType' => [ 'shape' => 'GrantType', ], ], ], 'DeleteApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], ], ], 'DeleteApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInlinePolicyFromPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'DeleteInlinePolicyFromPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInstanceAccessControlAttributeConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], ], ], 'DeleteInstanceAccessControlAttributeConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], ], ], 'DeleteInstanceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'DeletePermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePermissionsBoundaryFromPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'DeletePermissionsBoundaryFromPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTrustedTokenIssuerRequest' => [ 'type' => 'structure', 'required' => [ 'TrustedTokenIssuerArn', ], 'members' => [ 'TrustedTokenIssuerArn' => [ 'shape' => 'TrustedTokenIssuerArn', ], ], ], 'DeleteTrustedTokenIssuerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountAssignmentCreationStatusRequest' => [ 'type' => 'structure', 'required' => [ 'AccountAssignmentCreationRequestId', 'InstanceArn', ], 'members' => [ 'AccountAssignmentCreationRequestId' => [ 'shape' => 'UUId', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], ], ], 'DescribeAccountAssignmentCreationStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentCreationStatus' => [ 'shape' => 'AccountAssignmentOperationStatus', ], ], ], 'DescribeAccountAssignmentDeletionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'AccountAssignmentDeletionRequestId', 'InstanceArn', ], 'members' => [ 'AccountAssignmentDeletionRequestId' => [ 'shape' => 'UUId', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], ], ], 'DescribeAccountAssignmentDeletionStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentDeletionStatus' => [ 'shape' => 'AccountAssignmentOperationStatus', ], ], ], 'DescribeApplicationAssignmentRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'PrincipalId', 'PrincipalType', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], ], ], 'DescribeApplicationAssignmentResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], ], ], 'DescribeApplicationProviderRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationProviderArn', ], 'members' => [ 'ApplicationProviderArn' => [ 'shape' => 'ApplicationProviderArn', ], ], ], 'DescribeApplicationProviderResponse' => [ 'type' => 'structure', 'required' => [ 'ApplicationProviderArn', ], 'members' => [ 'ApplicationProviderArn' => [ 'shape' => 'ApplicationProviderArn', ], 'DisplayData' => [ 'shape' => 'DisplayData', ], 'FederationProtocol' => [ 'shape' => 'FederationProtocol', ], 'ResourceServerConfig' => [ 'shape' => 'ResourceServerConfig', ], ], ], 'DescribeApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], ], ], 'DescribeApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationAccount' => [ 'shape' => 'AccountId', ], 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'ApplicationProviderArn' => [ 'shape' => 'ApplicationProviderArn', ], 'CreatedDate' => [ 'shape' => 'Date', ], 'Description' => [ 'shape' => 'Description', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'Name' => [ 'shape' => 'NameType', ], 'PortalOptions' => [ 'shape' => 'PortalOptions', ], 'Status' => [ 'shape' => 'ApplicationStatus', ], ], ], 'DescribeInstanceAccessControlAttributeConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], ], ], 'DescribeInstanceAccessControlAttributeConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'InstanceAccessControlAttributeConfiguration' => [ 'shape' => 'InstanceAccessControlAttributeConfiguration', ], 'Status' => [ 'shape' => 'InstanceAccessControlAttributeConfigurationStatus', ], 'StatusReason' => [ 'shape' => 'InstanceAccessControlAttributeConfigurationStatusReason', ], ], ], 'DescribeInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], ], ], 'DescribeInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'CreatedDate' => [ 'shape' => 'Date', ], 'IdentityStoreId' => [ 'shape' => 'Id', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'Name' => [ 'shape' => 'NameType', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'Status' => [ 'shape' => 'InstanceStatus', ], ], ], 'DescribePermissionSetProvisioningStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'ProvisionPermissionSetRequestId', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'ProvisionPermissionSetRequestId' => [ 'shape' => 'UUId', ], ], ], 'DescribePermissionSetProvisioningStatusResponse' => [ 'type' => 'structure', 'members' => [ 'PermissionSetProvisioningStatus' => [ 'shape' => 'PermissionSetProvisioningStatus', ], ], ], 'DescribePermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'DescribePermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'PermissionSet' => [ 'shape' => 'PermissionSet', ], ], ], 'DescribeTrustedTokenIssuerRequest' => [ 'type' => 'structure', 'required' => [ 'TrustedTokenIssuerArn', ], 'members' => [ 'TrustedTokenIssuerArn' => [ 'shape' => 'TrustedTokenIssuerArn', ], ], ], 'DescribeTrustedTokenIssuerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'TrustedTokenIssuerName', ], 'TrustedTokenIssuerArn' => [ 'shape' => 'TrustedTokenIssuerArn', ], 'TrustedTokenIssuerConfiguration' => [ 'shape' => 'TrustedTokenIssuerConfiguration', ], 'TrustedTokenIssuerType' => [ 'shape' => 'TrustedTokenIssuerType', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'DetachCustomerManagedPolicyReferenceFromPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'CustomerManagedPolicyReference', 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'CustomerManagedPolicyReference' => [ 'shape' => 'CustomerManagedPolicyReference', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'DetachCustomerManagedPolicyReferenceFromPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DetachManagedPolicyFromPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'ManagedPolicyArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'ManagedPolicyArn' => [ 'shape' => 'ManagedPolicyArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'DetachManagedPolicyFromPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisplayData' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'Description', ], 'DisplayName' => [ 'shape' => 'Name', ], 'IconUrl' => [ 'shape' => 'IconUrl', ], ], ], 'Duration' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^(-?)P(?=\\d|T\\d)(?:(\\d+)Y)?(?:(\\d+)M)?(?:(\\d+)([DW]))?(?:T(?:(\\d+)H)?(?:(\\d+)M)?(?:(\\d+(?:\\.\\d+)?)S)?)?$', ], 'FederationProtocol' => [ 'type' => 'string', 'enum' => [ 'SAML', 'OAUTH', ], ], 'GetApplicationAccessScopeRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'Scope', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'Scope' => [ 'shape' => 'Scope', ], ], ], 'GetApplicationAccessScopeResponse' => [ 'type' => 'structure', 'required' => [ 'Scope', ], 'members' => [ 'AuthorizedTargets' => [ 'shape' => 'ScopeTargets', ], 'Scope' => [ 'shape' => 'Scope', ], ], ], 'GetApplicationAssignmentConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], ], ], 'GetApplicationAssignmentConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'AssignmentRequired', ], 'members' => [ 'AssignmentRequired' => [ 'shape' => 'AssignmentRequired', ], ], ], 'GetApplicationAuthenticationMethodRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'AuthenticationMethodType', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'AuthenticationMethodType' => [ 'shape' => 'AuthenticationMethodType', ], ], ], 'GetApplicationAuthenticationMethodResponse' => [ 'type' => 'structure', 'members' => [ 'AuthenticationMethod' => [ 'shape' => 'AuthenticationMethod', ], ], ], 'GetApplicationGrantRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'GrantType', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'GrantType' => [ 'shape' => 'GrantType', ], ], ], 'GetApplicationGrantResponse' => [ 'type' => 'structure', 'required' => [ 'Grant', ], 'members' => [ 'Grant' => [ 'shape' => 'Grant', ], ], ], 'GetInlinePolicyForPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'GetInlinePolicyForPermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'InlinePolicy' => [ 'shape' => 'PermissionSetPolicyDocument', ], ], ], 'GetPermissionsBoundaryForPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'GetPermissionsBoundaryForPermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'PermissionsBoundary' => [ 'shape' => 'PermissionsBoundary', ], ], ], 'Grant' => [ 'type' => 'structure', 'members' => [ 'AuthorizationCode' => [ 'shape' => 'AuthorizationCodeGrant', ], 'JwtBearer' => [ 'shape' => 'JwtBearerGrant', ], 'RefreshToken' => [ 'shape' => 'RefreshTokenGrant', ], 'TokenExchange' => [ 'shape' => 'TokenExchangeGrant', ], ], 'union' => true, ], 'GrantItem' => [ 'type' => 'structure', 'required' => [ 'Grant', 'GrantType', ], 'members' => [ 'Grant' => [ 'shape' => 'Grant', ], 'GrantType' => [ 'shape' => 'GrantType', ], ], ], 'GrantType' => [ 'type' => 'string', 'enum' => [ 'authorization_code', 'refresh_token', 'urn:ietf:params:oauth:grant-type:jwt-bearer', 'urn:ietf:params:oauth:grant-type:token-exchange', ], ], 'Grants' => [ 'type' => 'list', 'member' => [ 'shape' => 'GrantItem', ], ], 'IamAuthenticationMethod' => [ 'type' => 'structure', 'required' => [ 'ActorPolicy', ], 'members' => [ 'ActorPolicy' => [ 'shape' => 'ActorPolicyDocument', ], ], ], 'IconUrl' => [ 'type' => 'string', 'max' => 768, 'min' => 1, 'pattern' => '^(http|https):\\/\\/.*$', ], 'Id' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]*$', ], 'InstanceAccessControlAttributeConfiguration' => [ 'type' => 'structure', 'required' => [ 'AccessControlAttributes', ], 'members' => [ 'AccessControlAttributes' => [ 'shape' => 'AccessControlAttributeList', ], ], ], 'InstanceAccessControlAttributeConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'CREATION_IN_PROGRESS', 'CREATION_FAILED', ], ], 'InstanceAccessControlAttributeConfigurationStatusReason' => [ 'type' => 'string', ], 'InstanceArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::instance/(sso)?ins-[a-zA-Z0-9-.]{16}$', ], 'InstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceMetadata', ], 'max' => 10, 'min' => 0, ], 'InstanceMetadata' => [ 'type' => 'structure', 'members' => [ 'CreatedDate' => [ 'shape' => 'Date', ], 'IdentityStoreId' => [ 'shape' => 'Id', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'Name' => [ 'shape' => 'NameType', ], 'OwnerAccountId' => [ 'shape' => 'AccountId', ], 'Status' => [ 'shape' => 'InstanceStatus', ], ], ], 'InstanceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'DELETE_IN_PROGRESS', 'ACTIVE', ], ], 'InternalFailureMessage' => [ 'type' => 'string', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'InternalFailureMessage', ], ], 'exception' => true, 'fault' => true, ], 'JMESPath' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^\\p{L}+(?:\\.\\p{L}+){0,2}$', ], 'JwksRetrievalOption' => [ 'type' => 'string', 'enum' => [ 'OPEN_ID_DISCOVERY', ], ], 'JwtBearerGrant' => [ 'type' => 'structure', 'members' => [ 'AuthorizedTokenIssuers' => [ 'shape' => 'AuthorizedTokenIssuers', ], ], ], 'ListAccountAssignmentCreationStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'Filter' => [ 'shape' => 'OperationStatusFilter', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountAssignmentCreationStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentsCreationStatus' => [ 'shape' => 'AccountAssignmentOperationStatusList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountAssignmentDeletionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'Filter' => [ 'shape' => 'OperationStatusFilter', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountAssignmentDeletionStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentsDeletionStatus' => [ 'shape' => 'AccountAssignmentOperationStatusList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountAssignmentsFilter' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'ListAccountAssignmentsForPrincipalRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PrincipalId', 'PrincipalType', ], 'members' => [ 'Filter' => [ 'shape' => 'ListAccountAssignmentsFilter', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], ], ], 'ListAccountAssignmentsForPrincipalResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignments' => [ 'shape' => 'AccountAssignmentListForPrincipal', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountAssignmentsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'AccountId' => [ 'shape' => 'TargetId', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'ListAccountAssignmentsResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignments' => [ 'shape' => 'AccountAssignmentList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountsForProvisionedPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'ProvisioningStatus' => [ 'shape' => 'ProvisioningStatus', ], ], ], 'ListAccountsForProvisionedPermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'AccountIds' => [ 'shape' => 'AccountList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationAccessScopesRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'MaxResults' => [ 'shape' => 'ListApplicationAccessScopesRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationAccessScopesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'ListApplicationAccessScopesResponse' => [ 'type' => 'structure', 'required' => [ 'Scopes', ], 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'Scopes' => [ 'shape' => 'Scopes', ], ], ], 'ListApplicationAssignmentsFilter' => [ 'type' => 'structure', 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], ], ], 'ListApplicationAssignmentsForPrincipalRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PrincipalId', 'PrincipalType', ], 'members' => [ 'Filter' => [ 'shape' => 'ListApplicationAssignmentsFilter', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], ], ], 'ListApplicationAssignmentsForPrincipalResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationAssignments' => [ 'shape' => 'ApplicationAssignmentListForPrincipal', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationAssignmentsRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationAssignmentsResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationAssignments' => [ 'shape' => 'ApplicationAssignmentsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationAuthenticationMethodsRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationAuthenticationMethodsResponse' => [ 'type' => 'structure', 'members' => [ 'AuthenticationMethods' => [ 'shape' => 'AuthenticationMethods', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationGrantsRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationGrantsResponse' => [ 'type' => 'structure', 'required' => [ 'Grants', ], 'members' => [ 'Grants' => [ 'shape' => 'Grants', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationProvidersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationProvidersResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationProviders' => [ 'shape' => 'ApplicationProviderList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationsFilter' => [ 'type' => 'structure', 'members' => [ 'ApplicationAccount' => [ 'shape' => 'AccountId', ], 'ApplicationProvider' => [ 'shape' => 'ApplicationProviderArn', ], ], ], 'ListApplicationsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'Filter' => [ 'shape' => 'ListApplicationsFilter', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'Applications' => [ 'shape' => 'ApplicationList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListCustomerManagedPolicyReferencesInPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'ListCustomerManagedPolicyReferencesInPermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'CustomerManagedPolicyReferences' => [ 'shape' => 'CustomerManagedPolicyReferenceList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'Instances' => [ 'shape' => 'InstanceList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListManagedPoliciesInPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'ListManagedPoliciesInPermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'AttachedManagedPolicies' => [ 'shape' => 'AttachedManagedPolicyList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPermissionSetProvisioningStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'Filter' => [ 'shape' => 'OperationStatusFilter', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPermissionSetProvisioningStatusResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'PermissionSetsProvisioningStatus' => [ 'shape' => 'PermissionSetProvisioningStatusList', ], ], ], 'ListPermissionSetsProvisionedToAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'InstanceArn', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'ProvisioningStatus' => [ 'shape' => 'ProvisioningStatus', ], ], ], 'ListPermissionSetsProvisionedToAccountResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'PermissionSets' => [ 'shape' => 'PermissionSetList', ], ], ], 'ListPermissionSetsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPermissionSetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'PermissionSets' => [ 'shape' => 'PermissionSetList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'NextToken' => [ 'shape' => 'Token', ], 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTrustedTokenIssuersRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTrustedTokenIssuersResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'TrustedTokenIssuers' => [ 'shape' => 'TrustedTokenIssuerList', ], ], ], 'ManagedPolicyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):iam::aws:policy/[\\p{L}\\p{M}\\p{Z}\\p{S}\\p{N}\\p{P}]+$', ], 'ManagedPolicyName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[\\w+=,.@-]+$', ], 'ManagedPolicyPath' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^((/[A-Za-z0-9\\.,\\+@=_-]+)*)/$', ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'Name' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'NameType' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^[\\w+=,.@-]+$', ], 'OidcJwtConfiguration' => [ 'type' => 'structure', 'required' => [ 'ClaimAttributePath', 'IdentityStoreAttributePath', 'IssuerUrl', 'JwksRetrievalOption', ], 'members' => [ 'ClaimAttributePath' => [ 'shape' => 'ClaimAttributePath', ], 'IdentityStoreAttributePath' => [ 'shape' => 'JMESPath', ], 'IssuerUrl' => [ 'shape' => 'TrustedTokenIssuerUrl', ], 'JwksRetrievalOption' => [ 'shape' => 'JwksRetrievalOption', ], ], ], 'OidcJwtUpdateConfiguration' => [ 'type' => 'structure', 'members' => [ 'ClaimAttributePath' => [ 'shape' => 'ClaimAttributePath', ], 'IdentityStoreAttributePath' => [ 'shape' => 'JMESPath', ], 'JwksRetrievalOption' => [ 'shape' => 'JwksRetrievalOption', ], ], ], 'OperationStatusFilter' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusValues', ], ], ], 'PermissionSet' => [ 'type' => 'structure', 'members' => [ 'CreatedDate' => [ 'shape' => 'Date', ], 'Description' => [ 'shape' => 'PermissionSetDescription', ], 'Name' => [ 'shape' => 'PermissionSetName', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'RelayState' => [ 'shape' => 'RelayState', ], 'SessionDuration' => [ 'shape' => 'Duration', ], ], ], 'PermissionSetArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso:::permissionSet/(sso)?ins-[a-zA-Z0-9-.]{16}/ps-[a-zA-Z0-9-./]{16}$', ], 'PermissionSetDescription' => [ 'type' => 'string', 'max' => 700, 'min' => 1, 'pattern' => '^[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u00A1-\\u00FF]*$', ], 'PermissionSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionSetArn', ], ], 'PermissionSetName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[\\w+=,.@-]+$', ], 'PermissionSetPolicyDocument' => [ 'type' => 'string', 'max' => 32768, 'min' => 1, 'pattern' => '^[\\u0009\\u000A\\u000D\\u0020-\\u00FF]+$', ], 'PermissionSetProvisioningStatus' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'CreatedDate' => [ 'shape' => 'Date', ], 'FailureReason' => [ 'shape' => 'Reason', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'RequestId' => [ 'shape' => 'UUId', ], 'Status' => [ 'shape' => 'StatusValues', ], ], ], 'PermissionSetProvisioningStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionSetProvisioningStatusMetadata', ], ], 'PermissionSetProvisioningStatusMetadata' => [ 'type' => 'structure', 'members' => [ 'CreatedDate' => [ 'shape' => 'Date', ], 'RequestId' => [ 'shape' => 'UUId', ], 'Status' => [ 'shape' => 'StatusValues', ], ], ], 'PermissionsBoundary' => [ 'type' => 'structure', 'members' => [ 'CustomerManagedPolicyReference' => [ 'shape' => 'CustomerManagedPolicyReference', ], 'ManagedPolicyArn' => [ 'shape' => 'ManagedPolicyArn', ], ], ], 'PortalOptions' => [ 'type' => 'structure', 'members' => [ 'SignInOptions' => [ 'shape' => 'SignInOptions', ], 'Visibility' => [ 'shape' => 'ApplicationVisibility', ], ], ], 'PrincipalId' => [ 'type' => 'string', 'max' => 47, 'min' => 1, 'pattern' => '^([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}$', ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', ], ], 'ProvisionPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', 'TargetType', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'TargetId' => [ 'shape' => 'TargetId', ], 'TargetType' => [ 'shape' => 'ProvisionTargetType', ], ], ], 'ProvisionPermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'PermissionSetProvisioningStatus' => [ 'shape' => 'PermissionSetProvisioningStatus', ], ], ], 'ProvisionTargetType' => [ 'type' => 'string', 'enum' => [ 'AWS_ACCOUNT', 'ALL_PROVISIONED_ACCOUNTS', ], ], 'ProvisioningStatus' => [ 'type' => 'string', 'enum' => [ 'LATEST_PERMISSION_SET_PROVISIONED', 'LATEST_PERMISSION_SET_NOT_PROVISIONED', ], ], 'PutApplicationAccessScopeRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'Scope', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'AuthorizedTargets' => [ 'shape' => 'ScopeTargets', ], 'Scope' => [ 'shape' => 'Scope', ], ], ], 'PutApplicationAssignmentConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'AssignmentRequired', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'AssignmentRequired' => [ 'shape' => 'AssignmentRequired', ], ], ], 'PutApplicationAssignmentConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutApplicationAuthenticationMethodRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'AuthenticationMethod', 'AuthenticationMethodType', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'AuthenticationMethod' => [ 'shape' => 'AuthenticationMethod', ], 'AuthenticationMethodType' => [ 'shape' => 'AuthenticationMethodType', ], ], ], 'PutApplicationGrantRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', 'Grant', 'GrantType', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'Grant' => [ 'shape' => 'Grant', ], 'GrantType' => [ 'shape' => 'GrantType', ], ], ], 'PutInlinePolicyToPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InlinePolicy', 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InlinePolicy' => [ 'shape' => 'PermissionSetPolicyDocument', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'PutInlinePolicyToPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutPermissionsBoundaryToPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', 'PermissionsBoundary', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'PermissionsBoundary' => [ 'shape' => 'PermissionsBoundary', ], ], ], 'PutPermissionsBoundaryToPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'Reason' => [ 'type' => 'string', 'pattern' => '^[\\p{L}\\p{M}\\p{Z}\\p{S}\\p{N}\\p{P}]*$', ], 'RedirectUris' => [ 'type' => 'list', 'member' => [ 'shape' => 'URI', ], 'max' => 10, 'min' => 1, ], 'RefreshTokenGrant' => [ 'type' => 'structure', 'members' => [], ], 'RelayState' => [ 'type' => 'string', 'max' => 240, 'min' => 1, 'pattern' => '^[a-zA-Z0-9&$@#\\\\\\/%?=~\\-_\'"|!:,.;*+\\[\\]\\ \\(\\)\\{\\}]+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ResourceNotFoundMessage', ], ], 'exception' => true, ], 'ResourceNotFoundMessage' => [ 'type' => 'string', ], 'ResourceServerConfig' => [ 'type' => 'structure', 'members' => [ 'Scopes' => [ 'shape' => 'ResourceServerScopes', ], ], ], 'ResourceServerScope' => [ 'type' => 'string', 'max' => 80, 'min' => 1, 'pattern' => '^[^:=\\-\\.\\s][0-9a-zA-Z_:\\-\\.]+$', ], 'ResourceServerScopeDetails' => [ 'type' => 'structure', 'members' => [ 'DetailedTitle' => [ 'shape' => 'Description', ], 'LongDescription' => [ 'shape' => 'Description', ], ], ], 'ResourceServerScopes' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceServerScope', ], 'value' => [ 'shape' => 'ResourceServerScopeDetails', ], ], 'Scope' => [ 'type' => 'string', 'pattern' => '^([A-Za-z0-9_]{1,50})(:[A-Za-z0-9_]{1,50}){0,1}(:[A-Za-z0-9_]{1,50}){0,1}$', ], 'ScopeDetails' => [ 'type' => 'structure', 'required' => [ 'Scope', ], 'members' => [ 'AuthorizedTargets' => [ 'shape' => 'ScopeTargets', ], 'Scope' => [ 'shape' => 'Scope', ], ], ], 'ScopeTarget' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::(\\d{12}:application/(sso)?ins-[a-zA-Z0-9-.]{16}/apl-[a-zA-Z0-9]{16}|:instance/(sso)?ins-[a-zA-Z0-9-.]{16})$', ], 'ScopeTargets' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScopeTarget', ], 'max' => 10, 'min' => 1, ], 'Scopes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScopeDetails', ], 'max' => 10, 'min' => 0, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ServiceQuotaExceededMessage', ], ], 'exception' => true, ], 'ServiceQuotaExceededMessage' => [ 'type' => 'string', ], 'SignInOptions' => [ 'type' => 'structure', 'required' => [ 'Origin', ], 'members' => [ 'ApplicationUrl' => [ 'shape' => 'ApplicationUrl', ], 'Origin' => [ 'shape' => 'SignInOrigin', ], ], ], 'SignInOrigin' => [ 'type' => 'string', 'enum' => [ 'IDENTITY_CENTER', 'APPLICATION', ], ], 'StatusValues' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'FAILED', 'SUCCEEDED', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 75, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 75, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TaggableResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 10, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::((:instance/(sso)?ins-[a-zA-Z0-9-.]{16})|(:permissionSet/(sso)?ins-[a-zA-Z0-9-.]{16}/ps-[a-zA-Z0-9-./]{16})|(\\d{12}:application/(sso)?ins-[a-zA-Z0-9-.]{16}/apl-[a-zA-Z0-9]{16})|(\\d{12}:trustedTokenIssuer/(sso)?ins-[a-zA-Z0-9-.]{16}/tti-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}))$', ], 'TargetId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d{12}$', ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'AWS_ACCOUNT', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ThrottlingExceptionMessage', ], ], 'exception' => true, ], 'ThrottlingExceptionMessage' => [ 'type' => 'string', ], 'Token' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^[-a-zA-Z0-9+=/_]*$', ], 'TokenExchangeGrant' => [ 'type' => 'structure', 'members' => [], ], 'TokenIssuerAudience' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'TokenIssuerAudiences' => [ 'type' => 'list', 'member' => [ 'shape' => 'TokenIssuerAudience', ], 'max' => 10, 'min' => 1, ], 'TrustedTokenIssuerArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => '^arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):sso::\\d{12}:trustedTokenIssuer/(sso)?ins-[a-zA-Z0-9-.]{16}/tti-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', ], 'TrustedTokenIssuerConfiguration' => [ 'type' => 'structure', 'members' => [ 'OidcJwtConfiguration' => [ 'shape' => 'OidcJwtConfiguration', ], ], 'union' => true, ], 'TrustedTokenIssuerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrustedTokenIssuerMetadata', ], ], 'TrustedTokenIssuerMetadata' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'TrustedTokenIssuerName', ], 'TrustedTokenIssuerArn' => [ 'shape' => 'TrustedTokenIssuerArn', ], 'TrustedTokenIssuerType' => [ 'shape' => 'TrustedTokenIssuerType', ], ], ], 'TrustedTokenIssuerName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[\\w+=,.@-]+$', ], 'TrustedTokenIssuerType' => [ 'type' => 'string', 'enum' => [ 'OIDC_JWT', ], ], 'TrustedTokenIssuerUpdateConfiguration' => [ 'type' => 'structure', 'members' => [ 'OidcJwtConfiguration' => [ 'shape' => 'OidcJwtUpdateConfiguration', ], ], 'union' => true, ], 'TrustedTokenIssuerUrl' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^https?:\\/\\/[-a-zA-Z0-9+&@\\/%=~_|!:,.;]*[-a-zA-Z0-9+&@\\/%=~_|]$', ], 'URI' => [ 'type' => 'string', ], 'UUId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateApplicationPortalOptions' => [ 'type' => 'structure', 'members' => [ 'SignInOptions' => [ 'shape' => 'SignInOptions', ], ], ], 'UpdateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationArn', ], 'members' => [ 'ApplicationArn' => [ 'shape' => 'ApplicationArn', ], 'Description' => [ 'shape' => 'Description', ], 'Name' => [ 'shape' => 'NameType', ], 'PortalOptions' => [ 'shape' => 'UpdateApplicationPortalOptions', ], 'Status' => [ 'shape' => 'ApplicationStatus', ], ], ], 'UpdateApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateInstanceAccessControlAttributeConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceAccessControlAttributeConfiguration', 'InstanceArn', ], 'members' => [ 'InstanceAccessControlAttributeConfiguration' => [ 'shape' => 'InstanceAccessControlAttributeConfiguration', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], ], ], 'UpdateInstanceAccessControlAttributeConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'Name', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'Name' => [ 'shape' => 'NameType', ], ], ], 'UpdateInstanceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'Description' => [ 'shape' => 'PermissionSetDescription', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'RelayState' => [ 'shape' => 'RelayState', ], 'SessionDuration' => [ 'shape' => 'Duration', ], ], ], 'UpdatePermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTrustedTokenIssuerRequest' => [ 'type' => 'structure', 'required' => [ 'TrustedTokenIssuerArn', ], 'members' => [ 'Name' => [ 'shape' => 'TrustedTokenIssuerName', ], 'TrustedTokenIssuerArn' => [ 'shape' => 'TrustedTokenIssuerArn', ], 'TrustedTokenIssuerConfiguration' => [ 'shape' => 'TrustedTokenIssuerUpdateConfiguration', ], ], ], 'UpdateTrustedTokenIssuerResponse' => [ 'type' => 'structure', 'members' => [], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ValidationExceptionMessage', ], ], 'exception' => true, ], 'ValidationExceptionMessage' => [ 'type' => 'string', ], ],];
