<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Vault;

class CalendarOptions extends \Google\Collection
{
  protected $collection_key = 'responseStatuses';
  /**
   * @var string[]
   */
  public $locationQuery;
  /**
   * @var string[]
   */
  public $minusWords;
  /**
   * @var string[]
   */
  public $peopleQuery;
  /**
   * @var string[]
   */
  public $responseStatuses;
  /**
   * @var string
   */
  public $versionDate;

  /**
   * @param string[]
   */
  public function setLocationQuery($locationQuery)
  {
    $this->locationQuery = $locationQuery;
  }
  /**
   * @return string[]
   */
  public function getLocationQuery()
  {
    return $this->locationQuery;
  }
  /**
   * @param string[]
   */
  public function setMinusWords($minusWords)
  {
    $this->minusWords = $minusWords;
  }
  /**
   * @return string[]
   */
  public function getMinusWords()
  {
    return $this->minusWords;
  }
  /**
   * @param string[]
   */
  public function setPeopleQuery($peopleQuery)
  {
    $this->peopleQuery = $peopleQuery;
  }
  /**
   * @return string[]
   */
  public function getPeopleQuery()
  {
    return $this->peopleQuery;
  }
  /**
   * @param string[]
   */
  public function setResponseStatuses($responseStatuses)
  {
    $this->responseStatuses = $responseStatuses;
  }
  /**
   * @return string[]
   */
  public function getResponseStatuses()
  {
    return $this->responseStatuses;
  }
  /**
   * @param string
   */
  public function setVersionDate($versionDate)
  {
    $this->versionDate = $versionDate;
  }
  /**
   * @return string
   */
  public function getVersionDate()
  {
    return $this->versionDate;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(CalendarOptions::class, 'Google_Service_Vault_CalendarOptions');
