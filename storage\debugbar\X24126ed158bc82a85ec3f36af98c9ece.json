{"__meta": {"id": "X24126ed158bc82a85ec3f36af98c9ece", "datetime": "2025-06-26 22:24:04", "utime": **********.524857, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.009265, "end": **********.524872, "duration": 0.5156071186065674, "duration_str": "516ms", "measures": [{"label": "Booting", "start": **********.009265, "relative_start": 0, "end": **********.44719, "relative_end": **********.44719, "duration": 0.4379251003265381, "duration_str": "438ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.4472, "relative_start": 0.43793511390686035, "end": **********.524873, "relative_end": 9.5367431640625e-07, "duration": 0.07767295837402344, "duration_str": "77.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028984, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02041, "accumulated_duration_str": "20.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4810722, "duration": 0.01891, "duration_str": "18.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.651}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.510514, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.651, "width_percent": 2.891}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.516788, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.541, "width_percent": 4.459}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1433982242 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1433982242\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-118670194 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-118670194\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-690763793 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690763793\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1522877444 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2001 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750976108796%7C8%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdPb1V3dlZ2TGlCRWR3U0o3MnU5eXc9PSIsInZhbHVlIjoiRXI5MThpNm5nWWlLT0ZKTlNxMUEwNmhPUmVhYXlBckNEVzRsRWk5Vm5HN1kxQnZTRjBhbzVISDNDQTI1UHhWc3Zta0wvTU9DSElFRWNqRVduby9CYTFBajJVMkhESmdkeUtpWnRIdTMyOVJtanFaZUNONDY5MEcrWTFMTjhLdlNLU3RsT3NiT282TXB5TGdQTG1PNm4zQWRHekk1M2g2SlRkWlhzTmJEYlJTY3FwRU45UFB1NmR2ZGdoN1pmV1pFTmppZFpNL0o3YkxYL1dOVllFSVZLYnlwQmozVEJDcU1IN0JFeUk3MVl0eFJ4YWxHc21RQXRuTEJqbStpcjloSG4raThqYlRVUG93c0w4YWVqb1F0Q3BjTmVKTGI1SmZZZG9PMTlFNjRzcFdIQ1BHcDBpQ3hXT09HV2dnTVNEVEJpc0xRMlh6R3o1V2JxN1I0TFRxZWhCWWlBaW9JSmRyM09RT2hKVGx0K3JkWmNkNUpFVm83NkEzSHNUZVl2QVdXNWhVZCt1QStKTUtOSnlzekVBRGVXejlreElzZTRUY2g4NXpXblhUaW1HeXh3NW41T21WQjFaZ2k0dVVTOFVnbWkzTXhWSUNxbk85V3RTME5hWW5tVlk1NEZoK05jWmRsZUZkenlMdDZuUjBwcUlxcWwwMmQvZ0lIRTlVUTZFNlYiLCJtYWMiOiI0NDVjZjk1ZTVjMmRkYTlmYWI2NWYyNDljYzkxYTU2YWQ2NGY5NjI1YTU4YzY0NGU3NmYyYzQ4MjgwYWM4ODlkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InppSmQrekkxeVNoYnBLYVZIaFpmQnc9PSIsInZhbHVlIjoiK3VWWi9Gcm1SYk1aYzRtV3ZzcW95ditKaHVZTzI0aG1HNzNnWHFnMGZEMnJCNTF2QWx5VXZkaEVWUkVKeTBLSTBoTG1rVmdCOGJ3N2xwaTVFcS92SmF2VER6NlVQS0N3ZHlYR21VMVBLTDBjVTBjVjc5cXBFSzFYL1VNMWNnbGkwVXJXTjFLN21CSCtrODlFeXFhMVJxOW9tR2ovamEzOW5aaXppdmQ3bHdPODI5WExFYnpKdFZSdzJMUVpvVHBlR0lyUHUvdFpBbnVJK2gzcVY5TXkyZHdtaFQ1ME5VMnlPeVpGYnh6cENjdnpyOUhDQzVuWER1NDJFWC9NV2JlQVVaNGNIbEFuUktTeUpVVFB3MWdkTmlsam9rNFp2aHM3bkQ0bGMzYjBVSGF5OWlYb3RtTlowYk9LU0dzRXk2NTd5VkIvTkM4dUEwald6VXMvVDdHTThoRTJ0UE5ZdTRoT01LdXJEakJOU2Q1bEdNdERNZzFVcUY0N2ZpWGJpTjdBR0NWZHpuRkNnTE8ybFNqWHJodlM5RXhhaE1sMHRFNlQ3d2FTVG51NmFpMGs1L291bnNBeG5KRGczdmt2YnVZaFpNVnRTT3hTNGtiWklZV3hQU1piSmFUWW9hK3JmTS9jRFhwNUI0aEJBbWV0cXBVRnBaYXpHZmw3SkJ4Q2loZHYiLCJtYWMiOiIwYzUzZDAwNmMwMTZjNDk5Mjk0MTYzNzkyMjlhNWYyYTAyYTk0NmVhMWI2OWNjMzIyMzViODY5MjdjZGIyYWVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522877444\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-577535371 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-577535371\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1020912714 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:24:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRZVTQ0MjRxcDM5U1IzV3NOTXV1SHc9PSIsInZhbHVlIjoib3Nta0lXUHlaN0hxbVlBSDJqTlZwTFRsYmx3eWpvTWJLRzdNbFBCUGpsdTRzZDFsL05pKytyNm9rd3J5NjMwNlVWUVBNWDg0WEhscTBXd1BFOWw5QTV1TzQyV0ZrSmJuOFhTdi9xZWFrbDJIaVFieHNyVC9YQkdRQXNUem9hOUcxVisxR2VBN1FUYnJPK0YrTkJRUi9kRlJhSVdNWldWMzNaSkNRa1pqTUVEUGJNYndEK2xFMU95Yk9EUjBFMDR5cHNkdzQ0cUt6SGs5OWUySFVtZTh6eHV4cDlnaEc0RUIvRFA0VUNoNmVldFlTMlpsTkVLSU02WEwrSXVjcDVpNnY4clhpeTVicjNDazc1MlpXMUI1c1hDWDdrRHQvdnRPMWtsZjNHa29pL0FrUW9qSUVkQ2taYis2UmE2RlBhM1NsTGd5VS9xUHNrQU1UbWpqYjEyaElGaFB3bEt6U0xZZnlFSWl4Y2xRSkFoYm0wUXpVVDA0c2ZSUG5qU1FId1Z0TXE2RnlKVmM2ZVM1dGUwZGYwWElVYnUvVDMwQ1pTL0k1WStBUDRBc2ZRRFQzUlpCc1dnK1BUb2ZYbUtKNVhZejROeTBGeTU0VGpQRDBJNUplVkN2THJOY1dZYk1iTVBVdmtXM1V3TzV0RDd0L285bXlnM1Z1MEhrZDZENGhpRHoiLCJtYWMiOiIwOWQ3Mzk5ZTI1NDhhZjc5NWY0OWZiZjkyOWY4ZDdlNWQwYjM3ZTY5YzQ4MGFmNmNkM2UzMmZiZDExNThhNTI4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:24:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkN0YklaS1F0Y2YrWGNOVjIzZE93ZlE9PSIsInZhbHVlIjoiM0RiTEtYQVJLeWMvQmtFa2w3aDdSM0VUMy92TXVHRHBzSTkvQlI0N3RVd3BldElSamhsRldDc214dHIwWXI4SThNSmtEOVFoRXNDWDlKckExUGV6cDRMcnJGT0YvazViMjNDS2xnTHI4ZU00RkQ3QTQyNzgyNGducGtaNmJaR2RERUg4K3V6KzZjeUlQWHdoT0RDb0xUanNSQmRTdEJadHZudEhhSXFNYW96ZTRJU3dQN3kwRExmZmYyQUNKSXFMaHYrNmh1aHN3ZDhLWEgzQVFMbEVZaEZqM0h4M2lQRGh4ckdib3NZZzJpbVJTb1YwZUNwZTQ5WmFDM2xuUjN0WklhNTVtTW9hQUpXazdscWYwZDFuYlpkQ0NGMVo0VmYwVVpGU0dWQU54K1RISWF2MW5ZSnRzZjFtRko0SlhlZTJ4TzJuV2FnMmhWMUlUQmdXa0E3QXM0dndUQVV0emtSRTZtaGtlUVdlQWlZUzhIZDBpTlV5cU1DbkFRMHc4S1hGd3B3R2dyYU1FVWdrNHJmaWRZWXdKS3hnZCtQdENLQXUzVkhIbnk0MTJZQnRlSXd6WUhrMll4OUVSUW1qUmJIdjJFdlBVUXdkSEYrQU5pY0gvS0wvK0hodjZYb3VXN3lLZXNnQ0ZyN1VIT2FJZjZiQXovY2VMaGpZKytBYlpibDQiLCJtYWMiOiIwMDIyOTc2ODdmMjE1MWU1N2VlMGM5MzM3MzJmNTg1YWQ1MjZlZWRkYTA5ZTkyZDIwYjhlMDBlNTRiZmVmODE2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:24:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRZVTQ0MjRxcDM5U1IzV3NOTXV1SHc9PSIsInZhbHVlIjoib3Nta0lXUHlaN0hxbVlBSDJqTlZwTFRsYmx3eWpvTWJLRzdNbFBCUGpsdTRzZDFsL05pKytyNm9rd3J5NjMwNlVWUVBNWDg0WEhscTBXd1BFOWw5QTV1TzQyV0ZrSmJuOFhTdi9xZWFrbDJIaVFieHNyVC9YQkdRQXNUem9hOUcxVisxR2VBN1FUYnJPK0YrTkJRUi9kRlJhSVdNWldWMzNaSkNRa1pqTUVEUGJNYndEK2xFMU95Yk9EUjBFMDR5cHNkdzQ0cUt6SGs5OWUySFVtZTh6eHV4cDlnaEc0RUIvRFA0VUNoNmVldFlTMlpsTkVLSU02WEwrSXVjcDVpNnY4clhpeTVicjNDazc1MlpXMUI1c1hDWDdrRHQvdnRPMWtsZjNHa29pL0FrUW9qSUVkQ2taYis2UmE2RlBhM1NsTGd5VS9xUHNrQU1UbWpqYjEyaElGaFB3bEt6U0xZZnlFSWl4Y2xRSkFoYm0wUXpVVDA0c2ZSUG5qU1FId1Z0TXE2RnlKVmM2ZVM1dGUwZGYwWElVYnUvVDMwQ1pTL0k1WStBUDRBc2ZRRFQzUlpCc1dnK1BUb2ZYbUtKNVhZejROeTBGeTU0VGpQRDBJNUplVkN2THJOY1dZYk1iTVBVdmtXM1V3TzV0RDd0L285bXlnM1Z1MEhrZDZENGhpRHoiLCJtYWMiOiIwOWQ3Mzk5ZTI1NDhhZjc5NWY0OWZiZjkyOWY4ZDdlNWQwYjM3ZTY5YzQ4MGFmNmNkM2UzMmZiZDExNThhNTI4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:24:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkN0YklaS1F0Y2YrWGNOVjIzZE93ZlE9PSIsInZhbHVlIjoiM0RiTEtYQVJLeWMvQmtFa2w3aDdSM0VUMy92TXVHRHBzSTkvQlI0N3RVd3BldElSamhsRldDc214dHIwWXI4SThNSmtEOVFoRXNDWDlKckExUGV6cDRMcnJGT0YvazViMjNDS2xnTHI4ZU00RkQ3QTQyNzgyNGducGtaNmJaR2RERUg4K3V6KzZjeUlQWHdoT0RDb0xUanNSQmRTdEJadHZudEhhSXFNYW96ZTRJU3dQN3kwRExmZmYyQUNKSXFMaHYrNmh1aHN3ZDhLWEgzQVFMbEVZaEZqM0h4M2lQRGh4ckdib3NZZzJpbVJTb1YwZUNwZTQ5WmFDM2xuUjN0WklhNTVtTW9hQUpXazdscWYwZDFuYlpkQ0NGMVo0VmYwVVpGU0dWQU54K1RISWF2MW5ZSnRzZjFtRko0SlhlZTJ4TzJuV2FnMmhWMUlUQmdXa0E3QXM0dndUQVV0emtSRTZtaGtlUVdlQWlZUzhIZDBpTlV5cU1DbkFRMHc4S1hGd3B3R2dyYU1FVWdrNHJmaWRZWXdKS3hnZCtQdENLQXUzVkhIbnk0MTJZQnRlSXd6WUhrMll4OUVSUW1qUmJIdjJFdlBVUXdkSEYrQU5pY0gvS0wvK0hodjZYb3VXN3lLZXNnQ0ZyN1VIT2FJZjZiQXovY2VMaGpZKytBYlpibDQiLCJtYWMiOiIwMDIyOTc2ODdmMjE1MWU1N2VlMGM5MzM3MzJmNTg1YWQ1MjZlZWRkYTA5ZTkyZDIwYjhlMDBlNTRiZmVmODE2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:24:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020912714\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-683302482 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-683302482\", {\"maxDepth\":0})</script>\n"}}