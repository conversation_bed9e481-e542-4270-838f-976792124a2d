<?php
// This file was auto-generated from sdk-root/src/data/license-manager/2018-08-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-08-01', 'endpointPrefix' => 'license-manager', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'AWS License Manager', 'serviceId' => 'License Manager', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSLicenseManager', 'uid' => 'license-manager-2018-08-01', ], 'operations' => [ 'AcceptGrant' => [ 'name' => 'AcceptGrant', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AcceptGrantRequest', ], 'output' => [ 'shape' => 'AcceptGrantResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'CheckInLicense' => [ 'name' => 'CheckInLicense', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CheckInLicenseRequest', ], 'output' => [ 'shape' => 'CheckInLicenseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'CheckoutBorrowLicense' => [ 'name' => 'CheckoutBorrowLicense', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CheckoutBorrowLicenseRequest', ], 'output' => [ 'shape' => 'CheckoutBorrowLicenseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NoEntitlementsAllowedException', ], [ 'shape' => 'EntitlementNotAllowedException', ], [ 'shape' => 'UnsupportedDigitalSignatureMethodException', ], [ 'shape' => 'RedirectException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'CheckoutLicense' => [ 'name' => 'CheckoutLicense', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CheckoutLicenseRequest', ], 'output' => [ 'shape' => 'CheckoutLicenseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NoEntitlementsAllowedException', ], [ 'shape' => 'UnsupportedDigitalSignatureMethodException', ], [ 'shape' => 'RedirectException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'CreateGrant' => [ 'name' => 'CreateGrant', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGrantRequest', ], 'output' => [ 'shape' => 'CreateGrantResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateGrantVersion' => [ 'name' => 'CreateGrantVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGrantVersionRequest', ], 'output' => [ 'shape' => 'CreateGrantVersionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'CreateLicense' => [ 'name' => 'CreateLicense', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLicenseRequest', ], 'output' => [ 'shape' => 'CreateLicenseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'RedirectException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'CreateLicenseConfiguration' => [ 'name' => 'CreateLicenseConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLicenseConfigurationRequest', ], 'output' => [ 'shape' => 'CreateLicenseConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'CreateLicenseConversionTaskForResource' => [ 'name' => 'CreateLicenseConversionTaskForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLicenseConversionTaskForResourceRequest', ], 'output' => [ 'shape' => 'CreateLicenseConversionTaskForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'CreateLicenseManagerReportGenerator' => [ 'name' => 'CreateLicenseManagerReportGenerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLicenseManagerReportGeneratorRequest', ], 'output' => [ 'shape' => 'CreateLicenseManagerReportGeneratorResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateLicenseVersion' => [ 'name' => 'CreateLicenseVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLicenseVersionRequest', ], 'output' => [ 'shape' => 'CreateLicenseVersionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'RedirectException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'CreateToken' => [ 'name' => 'CreateToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTokenRequest', ], 'output' => [ 'shape' => 'CreateTokenResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'RedirectException', ], ], ], 'DeleteGrant' => [ 'name' => 'DeleteGrant', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGrantRequest', ], 'output' => [ 'shape' => 'DeleteGrantResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'DeleteLicense' => [ 'name' => 'DeleteLicense', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLicenseRequest', ], 'output' => [ 'shape' => 'DeleteLicenseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'RedirectException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'DeleteLicenseConfiguration' => [ 'name' => 'DeleteLicenseConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLicenseConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteLicenseConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'DeleteLicenseManagerReportGenerator' => [ 'name' => 'DeleteLicenseManagerReportGenerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLicenseManagerReportGeneratorRequest', ], 'output' => [ 'shape' => 'DeleteLicenseManagerReportGeneratorResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteToken' => [ 'name' => 'DeleteToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTokenRequest', ], 'output' => [ 'shape' => 'DeleteTokenResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'RedirectException', ], ], ], 'ExtendLicenseConsumption' => [ 'name' => 'ExtendLicenseConsumption', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExtendLicenseConsumptionRequest', ], 'output' => [ 'shape' => 'ExtendLicenseConsumptionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAccessToken' => [ 'name' => 'GetAccessToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAccessTokenRequest', ], 'output' => [ 'shape' => 'GetAccessTokenResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'GetGrant' => [ 'name' => 'GetGrant', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetGrantRequest', ], 'output' => [ 'shape' => 'GetGrantResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'GetLicense' => [ 'name' => 'GetLicense', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLicenseRequest', ], 'output' => [ 'shape' => 'GetLicenseResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'GetLicenseConfiguration' => [ 'name' => 'GetLicenseConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLicenseConfigurationRequest', ], 'output' => [ 'shape' => 'GetLicenseConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'GetLicenseConversionTask' => [ 'name' => 'GetLicenseConversionTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLicenseConversionTaskRequest', ], 'output' => [ 'shape' => 'GetLicenseConversionTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'GetLicenseManagerReportGenerator' => [ 'name' => 'GetLicenseManagerReportGenerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLicenseManagerReportGeneratorRequest', ], 'output' => [ 'shape' => 'GetLicenseManagerReportGeneratorResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetLicenseUsage' => [ 'name' => 'GetLicenseUsage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLicenseUsageRequest', ], 'output' => [ 'shape' => 'GetLicenseUsageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'GetServiceSettings' => [ 'name' => 'GetServiceSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetServiceSettingsRequest', ], 'output' => [ 'shape' => 'GetServiceSettingsResponse', ], 'errors' => [ [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListAssociationsForLicenseConfiguration' => [ 'name' => 'ListAssociationsForLicenseConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAssociationsForLicenseConfigurationRequest', ], 'output' => [ 'shape' => 'ListAssociationsForLicenseConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'FilterLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListDistributedGrants' => [ 'name' => 'ListDistributedGrants', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDistributedGrantsRequest', ], 'output' => [ 'shape' => 'ListDistributedGrantsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListFailuresForLicenseConfigurationOperations' => [ 'name' => 'ListFailuresForLicenseConfigurationOperations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFailuresForLicenseConfigurationOperationsRequest', ], 'output' => [ 'shape' => 'ListFailuresForLicenseConfigurationOperationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListLicenseConfigurations' => [ 'name' => 'ListLicenseConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLicenseConfigurationsRequest', ], 'output' => [ 'shape' => 'ListLicenseConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'FilterLimitExceededException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListLicenseConversionTasks' => [ 'name' => 'ListLicenseConversionTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLicenseConversionTasksRequest', ], 'output' => [ 'shape' => 'ListLicenseConversionTasksResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListLicenseManagerReportGenerators' => [ 'name' => 'ListLicenseManagerReportGenerators', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLicenseManagerReportGeneratorsRequest', ], 'output' => [ 'shape' => 'ListLicenseManagerReportGeneratorsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListLicenseSpecificationsForResource' => [ 'name' => 'ListLicenseSpecificationsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLicenseSpecificationsForResourceRequest', ], 'output' => [ 'shape' => 'ListLicenseSpecificationsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListLicenseVersions' => [ 'name' => 'ListLicenseVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLicenseVersionsRequest', ], 'output' => [ 'shape' => 'ListLicenseVersionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'ListLicenses' => [ 'name' => 'ListLicenses', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLicensesRequest', ], 'output' => [ 'shape' => 'ListLicensesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'ListReceivedGrants' => [ 'name' => 'ListReceivedGrants', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListReceivedGrantsRequest', ], 'output' => [ 'shape' => 'ListReceivedGrantsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListReceivedGrantsForOrganization' => [ 'name' => 'ListReceivedGrantsForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListReceivedGrantsForOrganizationRequest', ], 'output' => [ 'shape' => 'ListReceivedGrantsForOrganizationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListReceivedLicenses' => [ 'name' => 'ListReceivedLicenses', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListReceivedLicensesRequest', ], 'output' => [ 'shape' => 'ListReceivedLicensesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListReceivedLicensesForOrganization' => [ 'name' => 'ListReceivedLicensesForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListReceivedLicensesForOrganizationRequest', ], 'output' => [ 'shape' => 'ListReceivedLicensesForOrganizationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListResourceInventory' => [ 'name' => 'ListResourceInventory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceInventoryRequest', ], 'output' => [ 'shape' => 'ListResourceInventoryResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'FilterLimitExceededException', ], [ 'shape' => 'FailedDependencyException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'ListTokens' => [ 'name' => 'ListTokens', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTokensRequest', ], 'output' => [ 'shape' => 'ListTokensResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'ListUsageForLicenseConfiguration' => [ 'name' => 'ListUsageForLicenseConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUsageForLicenseConfigurationRequest', ], 'output' => [ 'shape' => 'ListUsageForLicenseConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'FilterLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'RejectGrant' => [ 'name' => 'RejectGrant', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RejectGrantRequest', ], 'output' => [ 'shape' => 'RejectGrantResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'UpdateLicenseConfiguration' => [ 'name' => 'UpdateLicenseConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLicenseConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateLicenseConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ResourceLimitExceededException', ], ], ], 'UpdateLicenseManagerReportGenerator' => [ 'name' => 'UpdateLicenseManagerReportGenerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLicenseManagerReportGeneratorRequest', ], 'output' => [ 'shape' => 'UpdateLicenseManagerReportGeneratorResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'RateLimitExceededException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceLimitExceededException', ], ], ], 'UpdateLicenseSpecificationsForResource' => [ 'name' => 'UpdateLicenseSpecificationsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLicenseSpecificationsForResourceRequest', ], 'output' => [ 'shape' => 'UpdateLicenseSpecificationsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidResourceStateException', ], [ 'shape' => 'LicenseUsageException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], 'UpdateServiceSettings' => [ 'name' => 'UpdateServiceSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateServiceSettingsRequest', ], 'output' => [ 'shape' => 'UpdateServiceSettingsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ServerInternalException', ], [ 'shape' => 'AuthorizationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'RateLimitExceededException', ], ], ], ], 'shapes' => [ 'AcceptGrantRequest' => [ 'type' => 'structure', 'required' => [ 'GrantArn', ], 'members' => [ 'GrantArn' => [ 'shape' => 'Arn', ], ], ], 'AcceptGrantResponse' => [ 'type' => 'structure', 'members' => [ 'GrantArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'GrantStatus', ], 'Version' => [ 'shape' => 'String', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'ActivationOverrideBehavior' => [ 'type' => 'string', 'enum' => [ 'DISTRIBUTED_GRANTS_ONLY', 'ALL_GRANTS_PERMITTED_BY_ISSUER', ], ], 'AllowedOperation' => [ 'type' => 'string', 'enum' => [ 'CreateGrant', 'CheckoutLicense', 'CheckoutBorrowLicense', 'CheckInLicense', 'ExtendConsumptionLicense', 'ListPurchasedLicenses', 'CreateToken', ], ], 'AllowedOperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AllowedOperation', ], 'max' => 7, 'min' => 1, ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '^arn:aws(-(cn|us-gov|iso-b|iso-c|iso-d))?:[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}$', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'AuthorizationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'AutomatedDiscoveryInformation' => [ 'type' => 'structure', 'members' => [ 'LastRunTime' => [ 'shape' => 'DateTime', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BorrowConfiguration' => [ 'type' => 'structure', 'required' => [ 'AllowEarlyCheckIn', 'MaxTimeToLiveInMinutes', ], 'members' => [ 'AllowEarlyCheckIn' => [ 'shape' => 'BoxBoolean', ], 'MaxTimeToLiveInMinutes' => [ 'shape' => 'BoxInteger', ], ], ], 'BoxBoolean' => [ 'type' => 'boolean', ], 'BoxInteger' => [ 'type' => 'integer', ], 'BoxLong' => [ 'type' => 'long', ], 'CheckInLicenseRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseConsumptionToken', ], 'members' => [ 'LicenseConsumptionToken' => [ 'shape' => 'String', ], 'Beneficiary' => [ 'shape' => 'String', ], ], ], 'CheckInLicenseResponse' => [ 'type' => 'structure', 'members' => [], ], 'CheckoutBorrowLicenseRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseArn', 'Entitlements', 'DigitalSignatureMethod', 'ClientToken', ], 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'Entitlements' => [ 'shape' => 'EntitlementDataList', ], 'DigitalSignatureMethod' => [ 'shape' => 'DigitalSignatureMethod', ], 'NodeId' => [ 'shape' => 'String', ], 'CheckoutMetadata' => [ 'shape' => 'MetadataList', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'CheckoutBorrowLicenseResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'LicenseConsumptionToken' => [ 'shape' => 'String', ], 'EntitlementsAllowed' => [ 'shape' => 'EntitlementDataList', ], 'NodeId' => [ 'shape' => 'String', ], 'SignedToken' => [ 'shape' => 'SignedToken', ], 'IssuedAt' => [ 'shape' => 'ISO8601DateTime', ], 'Expiration' => [ 'shape' => 'ISO8601DateTime', ], 'CheckoutMetadata' => [ 'shape' => 'MetadataList', ], ], ], 'CheckoutLicenseRequest' => [ 'type' => 'structure', 'required' => [ 'ProductSKU', 'CheckoutType', 'KeyFingerprint', 'Entitlements', 'ClientToken', ], 'members' => [ 'ProductSKU' => [ 'shape' => 'String', ], 'CheckoutType' => [ 'shape' => 'CheckoutType', ], 'KeyFingerprint' => [ 'shape' => 'String', ], 'Entitlements' => [ 'shape' => 'EntitlementDataList', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'Beneficiary' => [ 'shape' => 'String', ], 'NodeId' => [ 'shape' => 'String', ], ], ], 'CheckoutLicenseResponse' => [ 'type' => 'structure', 'members' => [ 'CheckoutType' => [ 'shape' => 'CheckoutType', ], 'LicenseConsumptionToken' => [ 'shape' => 'String', ], 'EntitlementsAllowed' => [ 'shape' => 'EntitlementDataList', ], 'SignedToken' => [ 'shape' => 'SignedToken', ], 'NodeId' => [ 'shape' => 'String', ], 'IssuedAt' => [ 'shape' => 'ISO8601DateTime', ], 'Expiration' => [ 'shape' => 'ISO8601DateTime', ], 'LicenseArn' => [ 'shape' => 'String', ], ], ], 'CheckoutType' => [ 'type' => 'string', 'enum' => [ 'PROVISIONAL', 'PERPETUAL', ], ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 36, 'min' => 1, ], 'ClientToken' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '\\S+', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'ConsumedLicenseSummary' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ConsumedLicenses' => [ 'shape' => 'BoxLong', ], ], ], 'ConsumedLicenseSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConsumedLicenseSummary', ], ], 'ConsumptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'RenewType' => [ 'shape' => 'RenewType', ], 'ProvisionalConfiguration' => [ 'shape' => 'ProvisionalConfiguration', ], 'BorrowConfiguration' => [ 'shape' => 'BorrowConfiguration', ], ], ], 'CreateGrantRequest' => [ 'type' => 'structure', 'required' => [ 'ClientToken', 'GrantName', 'LicenseArn', 'Principals', 'HomeRegion', 'AllowedOperations', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', ], 'GrantName' => [ 'shape' => 'String', ], 'LicenseArn' => [ 'shape' => 'Arn', ], 'Principals' => [ 'shape' => 'PrincipalArnList', ], 'HomeRegion' => [ 'shape' => 'String', ], 'AllowedOperations' => [ 'shape' => 'AllowedOperationList', ], ], ], 'CreateGrantResponse' => [ 'type' => 'structure', 'members' => [ 'GrantArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'GrantStatus', ], 'Version' => [ 'shape' => 'String', ], ], ], 'CreateGrantVersionRequest' => [ 'type' => 'structure', 'required' => [ 'ClientToken', 'GrantArn', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', ], 'GrantArn' => [ 'shape' => 'Arn', ], 'GrantName' => [ 'shape' => 'String', ], 'AllowedOperations' => [ 'shape' => 'AllowedOperationList', ], 'Status' => [ 'shape' => 'GrantStatus', ], 'StatusReason' => [ 'shape' => 'StatusReasonMessage', ], 'SourceVersion' => [ 'shape' => 'String', ], 'Options' => [ 'shape' => 'Options', ], ], ], 'CreateGrantVersionResponse' => [ 'type' => 'structure', 'members' => [ 'GrantArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'GrantStatus', ], 'Version' => [ 'shape' => 'String', ], ], ], 'CreateLicenseConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'LicenseCountingType', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'LicenseCountingType' => [ 'shape' => 'LicenseCountingType', ], 'LicenseCount' => [ 'shape' => 'BoxLong', ], 'LicenseCountHardLimit' => [ 'shape' => 'BoxBoolean', ], 'LicenseRules' => [ 'shape' => 'StringList', ], 'Tags' => [ 'shape' => 'TagList', ], 'DisassociateWhenNotFound' => [ 'shape' => 'BoxBoolean', ], 'ProductInformationList' => [ 'shape' => 'ProductInformationList', ], ], ], 'CreateLicenseConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseConfigurationArn' => [ 'shape' => 'String', ], ], ], 'CreateLicenseConversionTaskForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'SourceLicenseContext', 'DestinationLicenseContext', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'SourceLicenseContext' => [ 'shape' => 'LicenseConversionContext', ], 'DestinationLicenseContext' => [ 'shape' => 'LicenseConversionContext', ], ], ], 'CreateLicenseConversionTaskForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseConversionTaskId' => [ 'shape' => 'LicenseConversionTaskId', ], ], ], 'CreateLicenseManagerReportGeneratorRequest' => [ 'type' => 'structure', 'required' => [ 'ReportGeneratorName', 'Type', 'ReportContext', 'ReportFrequency', 'ClientToken', ], 'members' => [ 'ReportGeneratorName' => [ 'shape' => 'ReportGeneratorName', ], 'Type' => [ 'shape' => 'ReportTypeList', ], 'ReportContext' => [ 'shape' => 'ReportContext', ], 'ReportFrequency' => [ 'shape' => 'ReportFrequency', ], 'ClientToken' => [ 'shape' => 'ClientRequestToken', ], 'Description' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateLicenseManagerReportGeneratorResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseManagerReportGeneratorArn' => [ 'shape' => 'String', ], ], ], 'CreateLicenseRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseName', 'ProductName', 'ProductSKU', 'Issuer', 'HomeRegion', 'Validity', 'Entitlements', 'Beneficiary', 'ConsumptionConfiguration', 'ClientToken', ], 'members' => [ 'LicenseName' => [ 'shape' => 'String', ], 'ProductName' => [ 'shape' => 'String', ], 'ProductSKU' => [ 'shape' => 'String', ], 'Issuer' => [ 'shape' => 'Issuer', ], 'HomeRegion' => [ 'shape' => 'String', ], 'Validity' => [ 'shape' => 'DatetimeRange', ], 'Entitlements' => [ 'shape' => 'EntitlementList', ], 'Beneficiary' => [ 'shape' => 'String', ], 'ConsumptionConfiguration' => [ 'shape' => 'ConsumptionConfiguration', ], 'LicenseMetadata' => [ 'shape' => 'MetadataList', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'CreateLicenseResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'LicenseStatus', ], 'Version' => [ 'shape' => 'String', ], ], ], 'CreateLicenseVersionRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseArn', 'LicenseName', 'ProductName', 'Issuer', 'HomeRegion', 'Validity', 'Entitlements', 'ConsumptionConfiguration', 'Status', 'ClientToken', ], 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'LicenseName' => [ 'shape' => 'String', ], 'ProductName' => [ 'shape' => 'String', ], 'Issuer' => [ 'shape' => 'Issuer', ], 'HomeRegion' => [ 'shape' => 'String', ], 'Validity' => [ 'shape' => 'DatetimeRange', ], 'LicenseMetadata' => [ 'shape' => 'MetadataList', ], 'Entitlements' => [ 'shape' => 'EntitlementList', ], 'ConsumptionConfiguration' => [ 'shape' => 'ConsumptionConfiguration', ], 'Status' => [ 'shape' => 'LicenseStatus', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'SourceVersion' => [ 'shape' => 'String', ], ], ], 'CreateLicenseVersionResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'Version' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'LicenseStatus', ], ], ], 'CreateTokenRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseArn', 'ClientToken', ], 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'RoleArns' => [ 'shape' => 'ArnList', ], 'ExpirationInDays' => [ 'shape' => 'Integer', ], 'TokenProperties' => [ 'shape' => 'MaxSize3StringList', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'CreateTokenResponse' => [ 'type' => 'structure', 'members' => [ 'TokenId' => [ 'shape' => 'String', ], 'TokenType' => [ 'shape' => 'TokenType', ], 'Token' => [ 'shape' => 'TokenString', ], ], ], 'DateTime' => [ 'type' => 'timestamp', ], 'DatetimeRange' => [ 'type' => 'structure', 'required' => [ 'Begin', ], 'members' => [ 'Begin' => [ 'shape' => 'ISO8601DateTime', ], 'End' => [ 'shape' => 'ISO8601DateTime', ], ], ], 'DeleteGrantRequest' => [ 'type' => 'structure', 'required' => [ 'GrantArn', 'Version', ], 'members' => [ 'GrantArn' => [ 'shape' => 'Arn', ], 'StatusReason' => [ 'shape' => 'StatusReasonMessage', ], 'Version' => [ 'shape' => 'String', ], ], ], 'DeleteGrantResponse' => [ 'type' => 'structure', 'members' => [ 'GrantArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'GrantStatus', ], 'Version' => [ 'shape' => 'String', ], ], ], 'DeleteLicenseConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseConfigurationArn', ], 'members' => [ 'LicenseConfigurationArn' => [ 'shape' => 'String', ], ], ], 'DeleteLicenseConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLicenseManagerReportGeneratorRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseManagerReportGeneratorArn', ], 'members' => [ 'LicenseManagerReportGeneratorArn' => [ 'shape' => 'String', ], ], ], 'DeleteLicenseManagerReportGeneratorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLicenseRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseArn', 'SourceVersion', ], 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'SourceVersion' => [ 'shape' => 'String', ], ], ], 'DeleteLicenseResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'LicenseDeletionStatus', ], 'DeletionDate' => [ 'shape' => 'ISO8601DateTime', ], ], ], 'DeleteTokenRequest' => [ 'type' => 'structure', 'required' => [ 'TokenId', ], 'members' => [ 'TokenId' => [ 'shape' => 'String', ], ], ], 'DeleteTokenResponse' => [ 'type' => 'structure', 'members' => [], ], 'DigitalSignatureMethod' => [ 'type' => 'string', 'enum' => [ 'JWT_PS384', ], ], 'Entitlement' => [ 'type' => 'structure', 'required' => [ 'Name', 'Unit', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], 'MaxCount' => [ 'shape' => 'Long', ], 'Overage' => [ 'shape' => 'BoxBoolean', ], 'Unit' => [ 'shape' => 'EntitlementUnit', ], 'AllowCheckIn' => [ 'shape' => 'BoxBoolean', ], ], ], 'EntitlementData' => [ 'type' => 'structure', 'required' => [ 'Name', 'Unit', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], 'Unit' => [ 'shape' => 'EntitlementDataUnit', ], ], ], 'EntitlementDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntitlementData', ], ], 'EntitlementDataUnit' => [ 'type' => 'string', 'enum' => [ 'Count', 'None', 'Seconds', 'Microseconds', 'Milliseconds', 'Bytes', 'Kilobytes', 'Megabytes', 'Gigabytes', 'Terabytes', 'Bits', 'Kilobits', 'Megabits', 'Gigabits', 'Terabits', 'Percent', 'Bytes/Second', 'Kilobytes/Second', 'Megabytes/Second', 'Gigabytes/Second', 'Terabytes/Second', 'Bits/Second', 'Kilobits/Second', 'Megabits/Second', 'Gigabits/Second', 'Terabits/Second', 'Count/Second', ], ], 'EntitlementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Entitlement', ], ], 'EntitlementNotAllowedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'EntitlementUnit' => [ 'type' => 'string', 'enum' => [ 'Count', 'None', 'Seconds', 'Microseconds', 'Milliseconds', 'Bytes', 'Kilobytes', 'Megabytes', 'Gigabytes', 'Terabytes', 'Bits', 'Kilobits', 'Megabits', 'Gigabits', 'Terabits', 'Percent', 'Bytes/Second', 'Kilobytes/Second', 'Megabytes/Second', 'Gigabytes/Second', 'Terabytes/Second', 'Bits/Second', 'Kilobits/Second', 'Megabits/Second', 'Gigabits/Second', 'Terabits/Second', 'Count/Second', ], ], 'EntitlementUsage' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConsumedValue', 'Unit', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'ConsumedValue' => [ 'shape' => 'String', ], 'MaxCount' => [ 'shape' => 'String', ], 'Unit' => [ 'shape' => 'EntitlementDataUnit', ], ], ], 'EntitlementUsageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntitlementUsage', ], ], 'ExtendLicenseConsumptionRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseConsumptionToken', ], 'members' => [ 'LicenseConsumptionToken' => [ 'shape' => 'String', ], 'DryRun' => [ 'shape' => 'Boolean', ], ], ], 'ExtendLicenseConsumptionResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseConsumptionToken' => [ 'shape' => 'String', ], 'Expiration' => [ 'shape' => 'ISO8601DateTime', ], ], ], 'FailedDependencyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], 'ErrorCode' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'FilterName', ], 'Values' => [ 'shape' => 'FilterValues', ], ], ], 'FilterLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'FilterName' => [ 'type' => 'string', ], 'FilterValue' => [ 'type' => 'string', ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'GetAccessTokenRequest' => [ 'type' => 'structure', 'required' => [ 'Token', ], 'members' => [ 'Token' => [ 'shape' => 'TokenString', ], 'TokenProperties' => [ 'shape' => 'MaxSize3StringList', ], ], ], 'GetAccessTokenResponse' => [ 'type' => 'structure', 'members' => [ 'AccessToken' => [ 'shape' => 'TokenString', ], ], ], 'GetGrantRequest' => [ 'type' => 'structure', 'required' => [ 'GrantArn', ], 'members' => [ 'GrantArn' => [ 'shape' => 'Arn', ], 'Version' => [ 'shape' => 'String', ], ], ], 'GetGrantResponse' => [ 'type' => 'structure', 'members' => [ 'Grant' => [ 'shape' => 'Grant', ], ], ], 'GetLicenseConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseConfigurationArn', ], 'members' => [ 'LicenseConfigurationArn' => [ 'shape' => 'String', ], ], ], 'GetLicenseConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseConfigurationId' => [ 'shape' => 'String', ], 'LicenseConfigurationArn' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'LicenseCountingType' => [ 'shape' => 'LicenseCountingType', ], 'LicenseRules' => [ 'shape' => 'StringList', ], 'LicenseCount' => [ 'shape' => 'BoxLong', ], 'LicenseCountHardLimit' => [ 'shape' => 'BoxBoolean', ], 'ConsumedLicenses' => [ 'shape' => 'BoxLong', ], 'Status' => [ 'shape' => 'String', ], 'OwnerAccountId' => [ 'shape' => 'String', ], 'ConsumedLicenseSummaryList' => [ 'shape' => 'ConsumedLicenseSummaryList', ], 'ManagedResourceSummaryList' => [ 'shape' => 'ManagedResourceSummaryList', ], 'Tags' => [ 'shape' => 'TagList', ], 'ProductInformationList' => [ 'shape' => 'ProductInformationList', ], 'AutomatedDiscoveryInformation' => [ 'shape' => 'AutomatedDiscoveryInformation', ], 'DisassociateWhenNotFound' => [ 'shape' => 'BoxBoolean', ], ], ], 'GetLicenseConversionTaskRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseConversionTaskId', ], 'members' => [ 'LicenseConversionTaskId' => [ 'shape' => 'LicenseConversionTaskId', ], ], ], 'GetLicenseConversionTaskResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseConversionTaskId' => [ 'shape' => 'LicenseConversionTaskId', ], 'ResourceArn' => [ 'shape' => 'String', ], 'SourceLicenseContext' => [ 'shape' => 'LicenseConversionContext', ], 'DestinationLicenseContext' => [ 'shape' => 'LicenseConversionContext', ], 'StatusMessage' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'LicenseConversionTaskStatus', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'LicenseConversionTime' => [ 'shape' => 'DateTime', ], 'EndTime' => [ 'shape' => 'DateTime', ], ], ], 'GetLicenseManagerReportGeneratorRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseManagerReportGeneratorArn', ], 'members' => [ 'LicenseManagerReportGeneratorArn' => [ 'shape' => 'String', ], ], ], 'GetLicenseManagerReportGeneratorResponse' => [ 'type' => 'structure', 'members' => [ 'ReportGenerator' => [ 'shape' => 'ReportGenerator', ], ], ], 'GetLicenseRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseArn', ], 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'Version' => [ 'shape' => 'String', ], ], ], 'GetLicenseResponse' => [ 'type' => 'structure', 'members' => [ 'License' => [ 'shape' => 'License', ], ], ], 'GetLicenseUsageRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseArn', ], 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], ], ], 'GetLicenseUsageResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseUsage' => [ 'shape' => 'LicenseUsage', ], ], ], 'GetServiceSettingsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetServiceSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'S3BucketArn' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'OrganizationConfiguration' => [ 'shape' => 'OrganizationConfiguration', ], 'EnableCrossAccountsDiscovery' => [ 'shape' => 'BoxBoolean', ], 'LicenseManagerResourceShareArn' => [ 'shape' => 'String', ], ], ], 'Grant' => [ 'type' => 'structure', 'required' => [ 'GrantArn', 'GrantName', 'ParentArn', 'LicenseArn', 'GranteePrincipalArn', 'HomeRegion', 'GrantStatus', 'Version', 'GrantedOperations', ], 'members' => [ 'GrantArn' => [ 'shape' => 'Arn', ], 'GrantName' => [ 'shape' => 'String', ], 'ParentArn' => [ 'shape' => 'Arn', ], 'LicenseArn' => [ 'shape' => 'Arn', ], 'GranteePrincipalArn' => [ 'shape' => 'Arn', ], 'HomeRegion' => [ 'shape' => 'String', ], 'GrantStatus' => [ 'shape' => 'GrantStatus', ], 'StatusReason' => [ 'shape' => 'StatusReasonMessage', ], 'Version' => [ 'shape' => 'String', ], 'GrantedOperations' => [ 'shape' => 'AllowedOperationList', ], 'Options' => [ 'shape' => 'Options', ], ], ], 'GrantList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Grant', ], ], 'GrantStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_WORKFLOW', 'PENDING_ACCEPT', 'REJECTED', 'ACTIVE', 'FAILED_WORKFLOW', 'DELETED', 'PENDING_DELETE', 'DISABLED', 'WORKFLOW_COMPLETED', ], ], 'GrantedLicense' => [ 'type' => 'structure', 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'LicenseName' => [ 'shape' => 'String', ], 'ProductName' => [ 'shape' => 'String', ], 'ProductSKU' => [ 'shape' => 'String', ], 'Issuer' => [ 'shape' => 'IssuerDetails', ], 'HomeRegion' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'LicenseStatus', ], 'Validity' => [ 'shape' => 'DatetimeRange', ], 'Beneficiary' => [ 'shape' => 'String', ], 'Entitlements' => [ 'shape' => 'EntitlementList', ], 'ConsumptionConfiguration' => [ 'shape' => 'ConsumptionConfiguration', ], 'LicenseMetadata' => [ 'shape' => 'MetadataList', ], 'CreateTime' => [ 'shape' => 'ISO8601DateTime', ], 'Version' => [ 'shape' => 'String', ], 'ReceivedMetadata' => [ 'shape' => 'ReceivedMetadata', ], ], ], 'GrantedLicenseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GrantedLicense', ], ], 'ISO8601DateTime' => [ 'type' => 'string', 'max' => 50, 'pattern' => '^(-?(?:[1-9][0-9]*)?[0-9]{4})-(1[0-2]|0[1-9])-(3[0-1]|0[1-9]|[1-2][0-9])T(2[0-3]|[0-1][0-9]):([0-5][0-9]):([0-5][0-9])(\\.[0-9]+)?(Z|[+-](?:2[ 0-3]|[0-1][0-9]):[0-5][0-9])+$', ], 'Integer' => [ 'type' => 'integer', ], 'InvalidParameterValueException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, 'synthetic' => true, ], 'InvalidResourceStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'InventoryFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Condition', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Condition' => [ 'shape' => 'InventoryFilterCondition', ], 'Value' => [ 'shape' => 'String', ], ], ], 'InventoryFilterCondition' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', 'BEGINS_WITH', 'CONTAINS', ], ], 'InventoryFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InventoryFilter', ], ], 'Issuer' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'SignKey' => [ 'shape' => 'String', ], ], ], 'IssuerDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'SignKey' => [ 'shape' => 'String', ], 'KeyFingerprint' => [ 'shape' => 'String', ], ], ], 'License' => [ 'type' => 'structure', 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'LicenseName' => [ 'shape' => 'String', ], 'ProductName' => [ 'shape' => 'String', ], 'ProductSKU' => [ 'shape' => 'String', ], 'Issuer' => [ 'shape' => 'IssuerDetails', ], 'HomeRegion' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'LicenseStatus', ], 'Validity' => [ 'shape' => 'DatetimeRange', ], 'Beneficiary' => [ 'shape' => 'String', ], 'Entitlements' => [ 'shape' => 'EntitlementList', ], 'ConsumptionConfiguration' => [ 'shape' => 'ConsumptionConfiguration', ], 'LicenseMetadata' => [ 'shape' => 'MetadataList', ], 'CreateTime' => [ 'shape' => 'ISO8601DateTime', ], 'Version' => [ 'shape' => 'String', ], ], ], 'LicenseConfiguration' => [ 'type' => 'structure', 'members' => [ 'LicenseConfigurationId' => [ 'shape' => 'String', ], 'LicenseConfigurationArn' => [ 'shape' => 'String', ], 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'LicenseCountingType' => [ 'shape' => 'LicenseCountingType', ], 'LicenseRules' => [ 'shape' => 'StringList', ], 'LicenseCount' => [ 'shape' => 'BoxLong', ], 'LicenseCountHardLimit' => [ 'shape' => 'BoxBoolean', ], 'DisassociateWhenNotFound' => [ 'shape' => 'BoxBoolean', ], 'ConsumedLicenses' => [ 'shape' => 'BoxLong', ], 'Status' => [ 'shape' => 'String', ], 'OwnerAccountId' => [ 'shape' => 'String', ], 'ConsumedLicenseSummaryList' => [ 'shape' => 'ConsumedLicenseSummaryList', ], 'ManagedResourceSummaryList' => [ 'shape' => 'ManagedResourceSummaryList', ], 'ProductInformationList' => [ 'shape' => 'ProductInformationList', ], 'AutomatedDiscoveryInformation' => [ 'shape' => 'AutomatedDiscoveryInformation', ], ], ], 'LicenseConfigurationAssociation' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceOwnerId' => [ 'shape' => 'String', ], 'AssociationTime' => [ 'shape' => 'DateTime', ], 'AmiAssociationScope' => [ 'shape' => 'String', ], ], ], 'LicenseConfigurationAssociations' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseConfigurationAssociation', ], ], 'LicenseConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'DISABLED', ], ], 'LicenseConfigurationUsage' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceStatus' => [ 'shape' => 'String', ], 'ResourceOwnerId' => [ 'shape' => 'String', ], 'AssociationTime' => [ 'shape' => 'DateTime', ], 'ConsumedLicenses' => [ 'shape' => 'BoxLong', ], ], ], 'LicenseConfigurationUsageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseConfigurationUsage', ], ], 'LicenseConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseConfiguration', ], ], 'LicenseConversionContext' => [ 'type' => 'structure', 'members' => [ 'UsageOperation' => [ 'shape' => 'UsageOperation', ], ], ], 'LicenseConversionTask' => [ 'type' => 'structure', 'members' => [ 'LicenseConversionTaskId' => [ 'shape' => 'LicenseConversionTaskId', ], 'ResourceArn' => [ 'shape' => 'String', ], 'SourceLicenseContext' => [ 'shape' => 'LicenseConversionContext', ], 'DestinationLicenseContext' => [ 'shape' => 'LicenseConversionContext', ], 'Status' => [ 'shape' => 'LicenseConversionTaskStatus', ], 'StatusMessage' => [ 'shape' => 'String', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'LicenseConversionTime' => [ 'shape' => 'DateTime', ], 'EndTime' => [ 'shape' => 'DateTime', ], ], ], 'LicenseConversionTaskId' => [ 'type' => 'string', 'max' => 50, 'pattern' => '^lct-[a-zA-Z0-9]*', ], 'LicenseConversionTaskStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', ], ], 'LicenseConversionTasks' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseConversionTask', ], ], 'LicenseCountingType' => [ 'type' => 'string', 'enum' => [ 'vCPU', 'Instance', 'Core', 'Socket', ], ], 'LicenseDeletionStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_DELETE', 'DELETED', ], ], 'LicenseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'License', ], ], 'LicenseOperationFailure' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'FailureTime' => [ 'shape' => 'DateTime', ], 'OperationName' => [ 'shape' => 'String', ], 'ResourceOwnerId' => [ 'shape' => 'String', ], 'OperationRequestedBy' => [ 'shape' => 'String', ], 'MetadataList' => [ 'shape' => 'MetadataList', ], ], ], 'LicenseOperationFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseOperationFailure', ], ], 'LicenseSpecification' => [ 'type' => 'structure', 'required' => [ 'LicenseConfigurationArn', ], 'members' => [ 'LicenseConfigurationArn' => [ 'shape' => 'String', ], 'AmiAssociationScope' => [ 'shape' => 'String', ], ], ], 'LicenseSpecifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseSpecification', ], ], 'LicenseStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'PENDING_AVAILABLE', 'DEACTIVATED', 'SUSPENDED', 'EXPIRED', 'PENDING_DELETE', 'DELETED', ], ], 'LicenseUsage' => [ 'type' => 'structure', 'members' => [ 'EntitlementUsages' => [ 'shape' => 'EntitlementUsageList', ], ], ], 'LicenseUsageException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'ListAssociationsForLicenseConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseConfigurationArn', ], 'members' => [ 'LicenseConfigurationArn' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'BoxInteger', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListAssociationsForLicenseConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseConfigurationAssociations' => [ 'shape' => 'LicenseConfigurationAssociations', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListDistributedGrantsRequest' => [ 'type' => 'structure', 'members' => [ 'GrantArns' => [ 'shape' => 'ArnList', ], 'Filters' => [ 'shape' => 'FilterList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxSize100', ], ], ], 'ListDistributedGrantsResponse' => [ 'type' => 'structure', 'members' => [ 'Grants' => [ 'shape' => 'GrantList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListFailuresForLicenseConfigurationOperationsRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseConfigurationArn', ], 'members' => [ 'LicenseConfigurationArn' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'BoxInteger', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListFailuresForLicenseConfigurationOperationsResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseOperationFailureList' => [ 'shape' => 'LicenseOperationFailureList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListLicenseConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'LicenseConfigurationArns' => [ 'shape' => 'StringList', ], 'MaxResults' => [ 'shape' => 'BoxInteger', ], 'NextToken' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'ListLicenseConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseConfigurations' => [ 'shape' => 'LicenseConfigurations', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListLicenseConversionTasksRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'BoxInteger', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'ListLicenseConversionTasksResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseConversionTasks' => [ 'shape' => 'LicenseConversionTasks', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListLicenseManagerReportGeneratorsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxSize100', ], ], ], 'ListLicenseManagerReportGeneratorsResponse' => [ 'type' => 'structure', 'members' => [ 'ReportGenerators' => [ 'shape' => 'ReportGeneratorList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListLicenseSpecificationsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'BoxInteger', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListLicenseSpecificationsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseSpecifications' => [ 'shape' => 'LicenseSpecifications', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListLicenseVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseArn', ], 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxSize100', ], ], ], 'ListLicenseVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'Licenses' => [ 'shape' => 'LicenseList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListLicensesRequest' => [ 'type' => 'structure', 'members' => [ 'LicenseArns' => [ 'shape' => 'ArnList', ], 'Filters' => [ 'shape' => 'FilterList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxSize100', ], ], ], 'ListLicensesResponse' => [ 'type' => 'structure', 'members' => [ 'Licenses' => [ 'shape' => 'LicenseList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListReceivedGrantsForOrganizationRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseArn', ], 'members' => [ 'LicenseArn' => [ 'shape' => 'Arn', ], 'Filters' => [ 'shape' => 'FilterList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxSize100', ], ], ], 'ListReceivedGrantsForOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'Grants' => [ 'shape' => 'GrantList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListReceivedGrantsRequest' => [ 'type' => 'structure', 'members' => [ 'GrantArns' => [ 'shape' => 'ArnList', ], 'Filters' => [ 'shape' => 'FilterList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxSize100', ], ], ], 'ListReceivedGrantsResponse' => [ 'type' => 'structure', 'members' => [ 'Grants' => [ 'shape' => 'GrantList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListReceivedLicensesForOrganizationRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxSize100', ], ], ], 'ListReceivedLicensesForOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'Licenses' => [ 'shape' => 'GrantedLicenseList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListReceivedLicensesRequest' => [ 'type' => 'structure', 'members' => [ 'LicenseArns' => [ 'shape' => 'ArnList', ], 'Filters' => [ 'shape' => 'FilterList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxSize100', ], ], ], 'ListReceivedLicensesResponse' => [ 'type' => 'structure', 'members' => [ 'Licenses' => [ 'shape' => 'GrantedLicenseList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListResourceInventoryRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'BoxInteger', ], 'NextToken' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'InventoryFilterList', ], ], ], 'ListResourceInventoryResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceInventoryList' => [ 'shape' => 'ResourceInventoryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTokensRequest' => [ 'type' => 'structure', 'members' => [ 'TokenIds' => [ 'shape' => 'StringList', ], 'Filters' => [ 'shape' => 'FilterList', ], 'NextToken' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'MaxSize100', ], ], ], 'ListTokensResponse' => [ 'type' => 'structure', 'members' => [ 'Tokens' => [ 'shape' => 'TokenList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListUsageForLicenseConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseConfigurationArn', ], 'members' => [ 'LicenseConfigurationArn' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'BoxInteger', ], 'NextToken' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'ListUsageForLicenseConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LicenseConfigurationUsageList' => [ 'shape' => 'LicenseConfigurationUsageList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'Location' => [ 'type' => 'string', ], 'Long' => [ 'type' => 'long', ], 'ManagedResourceSummary' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'AssociationCount' => [ 'shape' => 'BoxLong', ], ], ], 'ManagedResourceSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedResourceSummary', ], ], 'MaxSize100' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxSize3StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 3, ], 'Message' => [ 'type' => 'string', ], 'Metadata' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'MetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Metadata', ], ], 'NoEntitlementsAllowedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'Options' => [ 'type' => 'structure', 'members' => [ 'ActivationOverrideBehavior' => [ 'shape' => 'ActivationOverrideBehavior', ], ], ], 'OrganizationConfiguration' => [ 'type' => 'structure', 'required' => [ 'EnableIntegration', ], 'members' => [ 'EnableIntegration' => [ 'shape' => 'Boolean', ], ], ], 'PrincipalArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], 'max' => 1, 'min' => 1, ], 'ProductInformation' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'ProductInformationFilterList', ], 'members' => [ 'ResourceType' => [ 'shape' => 'String', ], 'ProductInformationFilterList' => [ 'shape' => 'ProductInformationFilterList', ], ], ], 'ProductInformationFilter' => [ 'type' => 'structure', 'required' => [ 'ProductInformationFilterName', 'ProductInformationFilterComparator', ], 'members' => [ 'ProductInformationFilterName' => [ 'shape' => 'String', ], 'ProductInformationFilterValue' => [ 'shape' => 'StringList', ], 'ProductInformationFilterComparator' => [ 'shape' => 'String', ], ], ], 'ProductInformationFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProductInformationFilter', ], ], 'ProductInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProductInformation', ], ], 'ProvisionalConfiguration' => [ 'type' => 'structure', 'required' => [ 'MaxTimeToLiveInMinutes', ], 'members' => [ 'MaxTimeToLiveInMinutes' => [ 'shape' => 'BoxInteger', ], ], ], 'RateLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'ReceivedMetadata' => [ 'type' => 'structure', 'members' => [ 'ReceivedStatus' => [ 'shape' => 'ReceivedStatus', ], 'ReceivedStatusReason' => [ 'shape' => 'StatusReasonMessage', ], 'AllowedOperations' => [ 'shape' => 'AllowedOperationList', ], ], ], 'ReceivedStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_WORKFLOW', 'PENDING_ACCEPT', 'REJECTED', 'ACTIVE', 'FAILED_WORKFLOW', 'DELETED', 'DISABLED', 'WORKFLOW_COMPLETED', ], ], 'RedirectException' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'Location', ], 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'RejectGrantRequest' => [ 'type' => 'structure', 'required' => [ 'GrantArn', ], 'members' => [ 'GrantArn' => [ 'shape' => 'Arn', ], ], ], 'RejectGrantResponse' => [ 'type' => 'structure', 'members' => [ 'GrantArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'GrantStatus', ], 'Version' => [ 'shape' => 'String', ], ], ], 'RenewType' => [ 'type' => 'string', 'enum' => [ 'None', 'Weekly', 'Monthly', ], ], 'ReportContext' => [ 'type' => 'structure', 'required' => [ 'licenseConfigurationArns', ], 'members' => [ 'licenseConfigurationArns' => [ 'shape' => 'ArnList', ], ], ], 'ReportFrequency' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => 'Integer', ], 'period' => [ 'shape' => 'ReportFrequencyType', ], ], ], 'ReportFrequencyType' => [ 'type' => 'string', 'enum' => [ 'DAY', 'WEEK', 'MONTH', ], ], 'ReportGenerator' => [ 'type' => 'structure', 'members' => [ 'ReportGeneratorName' => [ 'shape' => 'String', ], 'ReportType' => [ 'shape' => 'ReportTypeList', ], 'ReportContext' => [ 'shape' => 'ReportContext', ], 'ReportFrequency' => [ 'shape' => 'ReportFrequency', ], 'LicenseManagerReportGeneratorArn' => [ 'shape' => 'String', ], 'LastRunStatus' => [ 'shape' => 'String', ], 'LastRunFailureReason' => [ 'shape' => 'String', ], 'LastReportGenerationTime' => [ 'shape' => 'String', ], 'ReportCreatorAccount' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'S3Location' => [ 'shape' => 'S3Location', ], 'CreateTime' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ReportGeneratorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReportGenerator', ], ], 'ReportGeneratorName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ReportType' => [ 'type' => 'string', 'enum' => [ 'LicenseConfigurationSummaryReport', 'LicenseConfigurationUsageReport', ], ], 'ReportTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReportType', ], ], 'ResourceInventory' => [ 'type' => 'structure', 'members' => [ 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceArn' => [ 'shape' => 'String', ], 'Platform' => [ 'shape' => 'String', ], 'PlatformVersion' => [ 'shape' => 'String', ], 'ResourceOwningAccountId' => [ 'shape' => 'String', ], ], ], 'ResourceInventoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceInventory', ], ], 'ResourceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'EC2_INSTANCE', 'EC2_HOST', 'EC2_AMI', 'RDS', 'SYSTEMS_MANAGER_MANAGED_INSTANCE', ], ], 'S3Location' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'String', ], 'keyPrefix' => [ 'shape' => 'String', ], ], ], 'ServerInternalException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, 'fault' => true, ], 'SignedToken' => [ 'type' => 'string', 'min' => 4096, ], 'StatusReasonMessage' => [ 'type' => 'string', 'max' => 400, 'pattern' => '[\\s\\S]+', ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TokenData' => [ 'type' => 'structure', 'members' => [ 'TokenId' => [ 'shape' => 'String', ], 'TokenType' => [ 'shape' => 'String', ], 'LicenseArn' => [ 'shape' => 'String', ], 'ExpirationTime' => [ 'shape' => 'ISO8601DateTime', ], 'TokenProperties' => [ 'shape' => 'MaxSize3StringList', ], 'RoleArns' => [ 'shape' => 'ArnList', ], 'Status' => [ 'shape' => 'String', ], ], ], 'TokenList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TokenData', ], ], 'TokenString' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '\\S+', ], 'TokenType' => [ 'type' => 'string', 'enum' => [ 'REFRESH_TOKEN', ], ], 'UnsupportedDigitalSignatureMethodException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLicenseConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseConfigurationArn', ], 'members' => [ 'LicenseConfigurationArn' => [ 'shape' => 'String', ], 'LicenseConfigurationStatus' => [ 'shape' => 'LicenseConfigurationStatus', ], 'LicenseRules' => [ 'shape' => 'StringList', ], 'LicenseCount' => [ 'shape' => 'BoxLong', ], 'LicenseCountHardLimit' => [ 'shape' => 'BoxBoolean', ], 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'ProductInformationList' => [ 'shape' => 'ProductInformationList', ], 'DisassociateWhenNotFound' => [ 'shape' => 'BoxBoolean', ], ], ], 'UpdateLicenseConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLicenseManagerReportGeneratorRequest' => [ 'type' => 'structure', 'required' => [ 'LicenseManagerReportGeneratorArn', 'ReportGeneratorName', 'Type', 'ReportContext', 'ReportFrequency', 'ClientToken', ], 'members' => [ 'LicenseManagerReportGeneratorArn' => [ 'shape' => 'String', ], 'ReportGeneratorName' => [ 'shape' => 'ReportGeneratorName', ], 'Type' => [ 'shape' => 'ReportTypeList', ], 'ReportContext' => [ 'shape' => 'ReportContext', ], 'ReportFrequency' => [ 'shape' => 'ReportFrequency', ], 'ClientToken' => [ 'shape' => 'ClientRequestToken', ], 'Description' => [ 'shape' => 'String', ], ], ], 'UpdateLicenseManagerReportGeneratorResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateLicenseSpecificationsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'AddLicenseSpecifications' => [ 'shape' => 'LicenseSpecifications', ], 'RemoveLicenseSpecifications' => [ 'shape' => 'LicenseSpecifications', ], ], ], 'UpdateLicenseSpecificationsForResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateServiceSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'S3BucketArn' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'OrganizationConfiguration' => [ 'shape' => 'OrganizationConfiguration', ], 'EnableCrossAccountsDiscovery' => [ 'shape' => 'BoxBoolean', ], ], ], 'UpdateServiceSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UsageOperation' => [ 'type' => 'string', 'max' => 50, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'exception' => true, ], ],];
