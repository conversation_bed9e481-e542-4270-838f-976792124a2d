{"__meta": {"id": "X213d7496512de4fc4e610a315a4664ac", "datetime": "2025-06-26 23:20:41", "utime": **********.309563, "method": "GET", "uri": "/budget/eyJpdiI6IlVLU3pDQ0FXT05GSVJUaDFlS0wrZ3c9PSIsInZhbHVlIjoiZGxUbHd6R2RaaDR4bzhuTHZSeEY4dz09IiwibWFjIjoiZTFiMDZlMzAzYWQ1ZDhlMDliOTc1MzRkYjRkZGE3ZmUzMzRkMGI2YjQ1ZDI1YzdmZDQ4Mjg2ZmJkYjk0NTUwZiIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 69, "messages": [{"message": "[23:20:40] LOG.info: Datos de ingresos del presupuesto: {\"32\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"630000\"},\"33\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"104040\"},\"40\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"0\"},\"41\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"9000\"},\"42\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"17000\"},\"43\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"135000\"}}", "message_html": null, "is_string": false, "label": "info", "time": **********.011281, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Budget Total Arrays: [{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"630000\"},{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"104040\"},{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"0\"},{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"9000\"},{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"17000\"},{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"135000\"}]", "message_html": null, "is_string": false, "label": "info", "time": **********.011418, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Budget Total: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":895040}", "message_html": null, "is_string": false, "label": "info", "time": **********.011514, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: فاتورة بيع مباشرة (فترة) - الفئة: مبيعات الروابي, رقم الفاتورة: 1, المبلغ: 1750", "message_html": null, "is_string": false, "label": "info", "time": **********.022139, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Período: yearly, Categoría: مبيعات الروابي, Mes: Jan-Dec, Monto: 1750", "message_html": null, "is_string": false, "label": "info", "time": **********.027124, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Período: yearly, Categoría: مبيعات المصيف, Mes: Jan-Dec, Monto: 0", "message_html": null, "is_string": false, "label": "info", "time": **********.039718, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Período: yearly, Categoría: سلف للموظفين, Mes: Jan-Dec, Monto: 0", "message_html": null, "is_string": false, "label": "info", "time": **********.048699, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Período: yearly, Categoría: احتياطي الانماء, Mes: Jan-Dec, Monto: 0", "message_html": null, "is_string": false, "label": "info", "time": **********.058535, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Período: yearly, Categoría: احتياطي بنك ساب, Mes: Jan-Dec, Monto: 0", "message_html": null, "is_string": false, "label": "info", "time": **********.0677, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Período: yearly, Categoría: استثمارات قطاع التمويل, Mes: Jan-Dec, Monto: 0", "message_html": null, "is_string": false, "label": "info", "time": **********.077286, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Selected categories: [\"income-33\",\"expense-37\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.099703, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.099836, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including expense category: حساب مشتريات مشتركة (ID: 37)", "message_html": null, "is_string": false, "label": "info", "time": **********.099974, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.100071, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered expense totals: []", "message_html": null, "is_string": false, "label": "info", "time": **********.100153, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.100227, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":102000}", "message_html": null, "is_string": false, "label": "info", "time": **********.100292, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Selected categories: [\"income-33\",\"expense-37\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.119389, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.119553, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including expense category: حساب مشتريات مشتركة (ID: 37)", "message_html": null, "is_string": false, "label": "info", "time": **********.119692, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.11979, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered expense totals: []", "message_html": null, "is_string": false, "label": "info", "time": **********.119857, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.119922, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":102000}", "message_html": null, "is_string": false, "label": "info", "time": **********.119986, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Selected categories: [\"income-33\",\"expense-37\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.138853, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.139018, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including expense category: حساب مشتريات مشتركة (ID: 37)", "message_html": null, "is_string": false, "label": "info", "time": **********.139204, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.139329, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered expense totals: []", "message_html": null, "is_string": false, "label": "info", "time": **********.139428, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.139516, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":102000}", "message_html": null, "is_string": false, "label": "info", "time": **********.139613, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Selected categories: [\"income-33\",\"expense-37\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.1585, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.15862, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including expense category: حساب مشتريات مشتركة (ID: 37)", "message_html": null, "is_string": false, "label": "info", "time": **********.158751, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.158852, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered expense totals: []", "message_html": null, "is_string": false, "label": "info", "time": **********.15893, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.159, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":102000}", "message_html": null, "is_string": false, "label": "info", "time": **********.159065, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Selected categories: [\"income-33\",\"expense-37\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.176354, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.176478, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including expense category: حساب مشتريات مشتركة (ID: 37)", "message_html": null, "is_string": false, "label": "info", "time": **********.176606, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.176702, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered expense totals: []", "message_html": null, "is_string": false, "label": "info", "time": **********.176769, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.176834, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":102000}", "message_html": null, "is_string": false, "label": "info", "time": **********.176899, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Selected categories: [\"income-33\",\"expense-37\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.193911, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.194038, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including expense category: حساب مشتريات مشتركة (ID: 37)", "message_html": null, "is_string": false, "label": "info", "time": **********.194176, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.194275, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered expense totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.194343, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.194411, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":102000}", "message_html": null, "is_string": false, "label": "info", "time": **********.194475, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Selected categories: [\"income-33\",\"expense-37\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.21182, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.211955, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including expense category: حساب مشتريات مشتركة (ID: 37)", "message_html": null, "is_string": false, "label": "info", "time": **********.212094, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.2122, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered expense totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.212267, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.212332, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":102000}", "message_html": null, "is_string": false, "label": "info", "time": **********.212397, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Selected categories: [\"income-33\",\"expense-37\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.228452, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.22858, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Including expense category: حساب مشتريات مشتركة (ID: 37)", "message_html": null, "is_string": false, "label": "info", "time": **********.22871, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.228808, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered expense totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.228874, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.228939, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":102000}", "message_html": null, "is_string": false, "label": "info", "time": **********.229003, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Período: yearly", "message_html": null, "is_string": false, "label": "info", "time": **********.232203, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: Categorías de ingresos: مبيعات الروابي, مبيعات المصيف, سلف للموظفين, احتياطي الانماء, احتياطي بنك ساب, استثمارات قطاع التمويل", "message_html": null, "is_string": false, "label": "info", "time": **********.232331, "xdebug_link": null, "collector": "log"}, {"message": "[23:20:40] LOG.info: <PERSON><PERSON> de ingresos: {\"32\":{\"Jan-Dec\":1750},\"33\":{\"Jan-Dec\":0},\"40\":{\"Jan-Dec\":0},\"41\":{\"Jan-Dec\":0},\"42\":{\"Jan-Dec\":0},\"43\":{\"Jan-Dec\":0}}", "message_html": null, "is_string": false, "label": "info", "time": **********.232406, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.596156, "end": **********.309654, "duration": 1.7134981155395508, "duration_str": "1.71s", "measures": [{"label": "Booting", "start": **********.596156, "relative_start": 0, "end": **********.951023, "relative_end": **********.951023, "duration": 0.35486721992492676, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.951033, "relative_start": 0.354877233505249, "end": **********.309657, "relative_end": 3.0994415283203125e-06, "duration": 1.35862398147583, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54888504, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x budget.show", "param_count": null, "params": [], "start": **********.239755, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/budget/show.blade.phpbudget.show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fbudget%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 1, "name_original": "budget.show"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.244706, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.248554, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.28878, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.299517, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.301798, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.302194, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET budget/{budget}", "middleware": "web, verified, auth, XSS, revalidate", "as": "budget.show", "controller": "App\\Http\\Controllers\\BudgetController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=209\" onclick=\"\">app/Http/Controllers/BudgetController.php:209-826</a>"}, "queries": {"nb_statements": 160, "nb_failed_statements": 0, "accumulated_duration": 0.04817999999999998, "accumulated_duration_str": "48.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.978767, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 3.778}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9885378, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 3.778, "width_percent": 0.872}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.003088, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 4.649, "width_percent": 1.266}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.0049648, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 5.915, "width_percent": 0.83}, {"sql": "select * from `budgets` where `budgets`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 221}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.009253, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:221", "source": "app/Http/Controllers/BudgetController.php:221", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=221", "ajax": false, "filename": "BudgetController.php", "line": "221"}, "connection": "kdmkjkqknb", "start_percent": 6.746, "width_percent": 0.747}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'income'", "type": "query", "params": [], "bindings": ["15", "income"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 301}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.012076, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:301", "source": "app/Http/Controllers/BudgetController.php:301", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=301", "ajax": false, "filename": "BudgetController.php", "line": "301"}, "connection": "kdmkjkqknb", "start_percent": 7.493, "width_percent": 0.809}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 32 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "32", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0142841, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 8.302, "width_percent": 1.432}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 429}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0185208, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 9.734, "width_percent": 1.183}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 99}, {"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 429}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.020435, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 10.917, "width_percent": 0.726}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 32 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "32", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.022376, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 11.644, "width_percent": 0.913}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0240989, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 12.557, "width_percent": 0.768}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.025697, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 13.325, "width_percent": 0.726}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 33 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "33", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.027318, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 14.051, "width_percent": 0.726}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 33 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "33", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.028906, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 14.778, "width_percent": 0.706}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.030467, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 15.484, "width_percent": 0.623}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.033585, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 16.106, "width_percent": 1.328}, {"sql": "select * from `product_services` where `product_services`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/InvoiceProduct.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\InvoiceProduct.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 455}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.035222, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "InvoiceProduct.php:30", "source": "app/Models/InvoiceProduct.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=30", "ajax": false, "filename": "InvoiceProduct.php", "line": "30"}, "connection": "kdmkjkqknb", "start_percent": 17.435, "width_percent": 0.664}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.036614, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 18.099, "width_percent": 0.747}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.038189, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 18.846, "width_percent": 0.664}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 40 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "40", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0399148, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 19.51, "width_percent": 0.457}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 40 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "40", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.041323, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 19.967, "width_percent": 0.457}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.042725, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 20.423, "width_percent": 0.374}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.043912, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 20.797, "width_percent": 0.436}, {"sql": "select * from `product_services` where `product_services`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/InvoiceProduct.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\InvoiceProduct.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 455}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.044976, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "InvoiceProduct.php:30", "source": "app/Models/InvoiceProduct.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=30", "ajax": false, "filename": "InvoiceProduct.php", "line": "30"}, "connection": "kdmkjkqknb", "start_percent": 21.233, "width_percent": 0.332}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0461318, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 21.565, "width_percent": 0.436}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.047497, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 22.001, "width_percent": 0.353}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 41 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "41", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.048881, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 22.354, "width_percent": 0.602}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 41 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "41", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.050548, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 22.956, "width_percent": 0.643}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.052128, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 23.599, "width_percent": 0.415}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.053423, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 24.014, "width_percent": 0.374}, {"sql": "select * from `product_services` where `product_services`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/InvoiceProduct.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\InvoiceProduct.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 455}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.054473, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "InvoiceProduct.php:30", "source": "app/Models/InvoiceProduct.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=30", "ajax": false, "filename": "InvoiceProduct.php", "line": "30"}, "connection": "kdmkjkqknb", "start_percent": 24.388, "width_percent": 0.311}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.055698, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 24.699, "width_percent": 0.54}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0572379, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 25.239, "width_percent": 0.457}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 42 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "42", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0587149, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 25.695, "width_percent": 0.436}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 42 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "42", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.060127, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 26.131, "width_percent": 0.415}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.061539, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 26.546, "width_percent": 0.353}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.062731, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 26.899, "width_percent": 0.353}, {"sql": "select * from `product_services` where `product_services`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/InvoiceProduct.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\InvoiceProduct.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 455}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.063779, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "InvoiceProduct.php:30", "source": "app/Models/InvoiceProduct.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=30", "ajax": false, "filename": "InvoiceProduct.php", "line": "30"}, "connection": "kdmkjkqknb", "start_percent": 27.252, "width_percent": 0.311}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.064931, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 27.563, "width_percent": 0.415}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.066334, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 27.978, "width_percent": 0.56}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 43 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "43", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0678918, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 28.539, "width_percent": 0.519}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 43 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "43", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0693588, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 29.058, "width_percent": 0.477}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.070789, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 29.535, "width_percent": 0.394}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0723329, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 29.929, "width_percent": 0.457}, {"sql": "select * from `product_services` where `product_services`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/InvoiceProduct.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\InvoiceProduct.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 455}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.073441, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "InvoiceProduct.php:30", "source": "app/Models/InvoiceProduct.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=30", "ajax": false, "filename": "InvoiceProduct.php", "line": "30"}, "connection": "kdmkjkqknb", "start_percent": 30.386, "width_percent": 0.374}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.074652, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 30.76, "width_percent": 0.477}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.076053, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 31.237, "width_percent": 0.394}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'expense'", "type": "query", "params": [], "bindings": ["15", "expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 500}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0774488, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:500", "source": "app/Http/Controllers/BudgetController.php:500", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=500", "ajax": false, "filename": "BudgetController.php", "line": "500"}, "connection": "kdmkjkqknb", "start_percent": 31.631, "width_percent": 0.477}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 30 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "30", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.079234, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 32.109, "width_percent": 0.726}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.080719, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 32.835, "width_percent": 0.394}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 30 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "30", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.082427, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 33.23, "width_percent": 0.768}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 629}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0842602, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 33.998, "width_percent": 0.56}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 629}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0857909, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 34.558, "width_percent": 0.477}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.087087, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 35.035, "width_percent": 0.457}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.088593, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 35.492, "width_percent": 0.519}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.089864, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 36.011, "width_percent": 0.477}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.091121, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 36.488, "width_percent": 0.394}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.09235, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 36.883, "width_percent": 0.353}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.093573, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 37.235, "width_percent": 0.332}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.094754, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 37.567, "width_percent": 0.311}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 30 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "30", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.096528, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 37.879, "width_percent": 1.162}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0982919, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 39.041, "width_percent": 0.415}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 31 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "31", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1004932, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 39.456, "width_percent": 0.56}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.101939, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 40.017, "width_percent": 0.394}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 31 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "31", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1032732, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 40.411, "width_percent": 0.623}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 629}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.104846, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 41.034, "width_percent": 0.581}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 629}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1061869, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 41.615, "width_percent": 0.394}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.107411, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 42.009, "width_percent": 0.457}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.108803, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 42.466, "width_percent": 0.332}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.109987, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 42.798, "width_percent": 0.477}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.111233, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 43.275, "width_percent": 0.519}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.112485, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 43.794, "width_percent": 0.415}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.113702, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 44.209, "width_percent": 0.374}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.114869, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 44.583, "width_percent": 0.353}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 31 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "31", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.11611, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 44.936, "width_percent": 0.602}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.117811, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 45.538, "width_percent": 0.602}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 34 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "34", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.12018, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 46.139, "width_percent": 0.54}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1217198, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 46.679, "width_percent": 0.457}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 34 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "34", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1231859, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 47.136, "width_percent": 0.768}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1248112, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 47.904, "width_percent": 0.623}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1263158, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 48.526, "width_percent": 0.54}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.128076, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 49.066, "width_percent": 0.623}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1295168, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 49.689, "width_percent": 0.477}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.130793, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 50.166, "width_percent": 0.581}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1326141, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 50.747, "width_percent": 0.623}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.134104, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 51.37, "width_percent": 0.498}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 34 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "34", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1358252, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 51.868, "width_percent": 0.664}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.137385, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 52.532, "width_percent": 0.498}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 35 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "35", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.139835, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 53.03, "width_percent": 0.498}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.141254, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 53.528, "width_percent": 0.498}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 35 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "35", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1431391, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 54.027, "width_percent": 0.643}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1446638, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 54.67, "width_percent": 0.519}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.146136, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 55.189, "width_percent": 0.457}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.147545, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 55.645, "width_percent": 0.747}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1490161, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 56.393, "width_percent": 1.017}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1508682, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 57.41, "width_percent": 0.955}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.152686, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 58.364, "width_percent": 1.017}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1542869, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 59.381, "width_percent": 0.477}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 35 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "35", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.155603, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 59.859, "width_percent": 0.54}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1570768, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 60.399, "width_percent": 0.436}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 36 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "36", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.159239, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 60.834, "width_percent": 0.415}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.160573, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 61.249, "width_percent": 0.374}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 36 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "36", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1618729, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 61.623, "width_percent": 0.415}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.163259, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 62.038, "width_percent": 0.602}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.164777, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 62.64, "width_percent": 0.457}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.166136, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 63.097, "width_percent": 0.602}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.167753, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 63.699, "width_percent": 1.121}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.169409, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 64.819, "width_percent": 0.747}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.170831, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 65.567, "width_percent": 0.664}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.172266, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 66.231, "width_percent": 0.602}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 36 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "36", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1736128, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 66.833, "width_percent": 0.457}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.17499, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 67.289, "width_percent": 0.374}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 37 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "37", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.177083, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 67.663, "width_percent": 0.519}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1784759, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 68.182, "width_percent": 0.457}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 37 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "37", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.179866, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 68.638, "width_percent": 0.602}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.181379, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 69.24, "width_percent": 0.581}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.183095, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 69.822, "width_percent": 0.83}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.184703, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 70.652, "width_percent": 0.56}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.186045, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 71.212, "width_percent": 0.394}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.187268, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 71.606, "width_percent": 0.436}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.188578, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 72.042, "width_percent": 0.498}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.189861, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 72.54, "width_percent": 0.415}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 37 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "37", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.191113, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 72.956, "width_percent": 0.457}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.192489, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 73.412, "width_percent": 0.394}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 38 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "38", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.194651, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 73.807, "width_percent": 0.415}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.195997, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 74.222, "width_percent": 0.477}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 38 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "38", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1973581, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 74.699, "width_percent": 0.56}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.198987, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 75.259, "width_percent": 0.747}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.20114, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 76.007, "width_percent": 0.685}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.202558, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 76.692, "width_percent": 0.436}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2038598, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 77.127, "width_percent": 0.498}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.205136, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 77.626, "width_percent": 0.394}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.206337, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 78.02, "width_percent": 0.394}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2075648, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 78.414, "width_percent": 0.498}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 38 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "38", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2088811, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 78.912, "width_percent": 0.664}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.210378, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 79.577, "width_percent": 0.415}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 39 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "39", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.212574, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 79.992, "width_percent": 0.519}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.21397, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 80.511, "width_percent": 0.374}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 39 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "39", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.215384, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 80.884, "width_percent": 0.498}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2168598, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 81.382, "width_percent": 0.54}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.218301, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 81.922, "width_percent": 0.415}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2195258, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 82.337, "width_percent": 0.477}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.220803, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 82.814, "width_percent": 0.457}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.222033, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 83.271, "width_percent": 0.374}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.223228, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 83.645, "width_percent": 0.374}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.224422, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 84.018, "width_percent": 0.353}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 39 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "39", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.225615, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 84.371, "width_percent": 0.436}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2270088, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 84.807, "width_percent": 0.498}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'income'", "type": "query", "params": [], "bindings": ["15", "income"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 791}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2291498, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:791", "source": "app/Http/Controllers/BudgetController.php:791", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=791", "ajax": false, "filename": "BudgetController.php", "line": "791"}, "connection": "kdmkjkqknb", "start_percent": 85.305, "width_percent": 0.477}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'expense'", "type": "query", "params": [], "bindings": ["15", "expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 792}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.230639, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:792", "source": "app/Http/Controllers/BudgetController.php:792", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=792", "ajax": false, "filename": "BudgetController.php", "line": "792"}, "connection": "kdmkjkqknb", "start_percent": 85.782, "width_percent": 0.685}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.245212, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 86.467, "width_percent": 1.121}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.24698, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 87.588, "width_percent": 0.643}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.251235, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 88.232, "width_percent": 0.706}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.25298, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 88.937, "width_percent": 0.768}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.254672, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "kdmkjkqknb", "start_percent": 89.705, "width_percent": 0.685}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.289268, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 90.39, "width_percent": 1.37}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.29106, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 91.76, "width_percent": 6.642}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.295667, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 98.402, "width_percent": 0.498}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 15 and `seen` = 0", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.297431, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:18", "source": "view::partials.admin.header:18", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=18", "ajax": false, "filename": "header.blade.php", "line": "18"}, "connection": "kdmkjkqknb", "start_percent": 98.9, "width_percent": 0.498}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 6233}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.300023, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 99.398, "width_percent": 0.602}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\Bill": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=1", "ajax": false, "filename": "Bill.php", "line": "?"}}, "App\\Models\\BillProduct": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillProduct.php&line=1", "ajax": false, "filename": "BillProduct.php", "line": "?"}}, "App\\Models\\BillAccount": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillAccount.php&line=1", "ajax": false, "filename": "BillAccount.php", "line": "?"}}, "App\\Models\\Invoice": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\InvoiceProduct": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=1", "ajax": false, "filename": "InvoiceProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Budget": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBudget.php&line=1", "ajax": false, "filename": "Budget.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 135, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 70, "messages": [{"message": "[ability => view budget plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-387745790 data-indent-pad=\"  \"><span class=sf-dump-note>view budget plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view budget plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387745790\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.008475, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-914888496 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-914888496\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.256725, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.257238, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.257454, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.25762, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.257904, "xdebug_link": null}, {"message": "[ability => statement report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-669702372 data-indent-pad=\"  \"><span class=sf-dump-note>statement report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-669702372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.258206, "xdebug_link": null}, {"message": "[ability => invoice report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1670276796 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1670276796\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.258507, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1060593172 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1060593172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.25885, "xdebug_link": null}, {"message": "[ability => stock report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1980090391 data-indent-pad=\"  \"><span class=sf-dump-note>stock report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980090391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.259178, "xdebug_link": null}, {"message": "[ability => loss & profit report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1666178488 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1666178488\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.259554, "xdebug_link": null}, {"message": "[ability => manage transaction, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-475718568 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475718568\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.259873, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1609988362 data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609988362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.260154, "xdebug_link": null}, {"message": "[ability => expense report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1489354703 data-indent-pad=\"  \"><span class=sf-dump-note>expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1489354703\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.260422, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.260696, "xdebug_link": null}, {"message": "[ability => tax report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>tax report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.260976, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1196894518 data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196894518\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.261171, "xdebug_link": null}, {"message": "[ability => manage report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-613510224 data-indent-pad=\"  \"><span class=sf-dump-note>manage report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613510224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.262005, "xdebug_link": null}, {"message": "[ability => show pos dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1086368552 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1086368552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.262263, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1215807211 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1215807211\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.263199, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-207983907 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-207983907\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.263748, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1865719516 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1865719516\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.264211, "xdebug_link": null}, {"message": "[ability => manage pay slip, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2025817863 data-indent-pad=\"  \"><span class=sf-dump-note>manage pay slip</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025817863\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.264748, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1469625883 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469625883\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.265384, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-245740000 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-245740000\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.26614, "xdebug_link": null}, {"message": "[ability => manage attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-356480632 data-indent-pad=\"  \"><span class=sf-dump-note>manage attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-356480632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.267224, "xdebug_link": null}, {"message": "[ability => create attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-650377367 data-indent-pad=\"  \"><span class=sf-dump-note>create attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-650377367\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.268105, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-425749248 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-425749248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.268627, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1991144874 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1991144874\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.269109, "xdebug_link": null}, {"message": "[ability => manage transfer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2073924783 data-indent-pad=\"  \"><span class=sf-dump-note>manage transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073924783\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.269689, "xdebug_link": null}, {"message": "[ability => manage resignation, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage resignation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage resignation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.270179, "xdebug_link": null}, {"message": "[ability => manage travel, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage travel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage travel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.270681, "xdebug_link": null}, {"message": "[ability => manage promotion, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1209378591 data-indent-pad=\"  \"><span class=sf-dump-note>manage promotion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage promotion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209378591\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.271189, "xdebug_link": null}, {"message": "[ability => manage complaint, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1347549907 data-indent-pad=\"  \"><span class=sf-dump-note>manage complaint</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage complaint</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347549907\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.2717, "xdebug_link": null}, {"message": "[ability => manage warning, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-489650367 data-indent-pad=\"  \"><span class=sf-dump-note>manage warning</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage warning</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-489650367\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.272205, "xdebug_link": null}, {"message": "[ability => manage termination, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1434878517 data-indent-pad=\"  \"><span class=sf-dump-note>manage termination</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage termination</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434878517\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.27271, "xdebug_link": null}, {"message": "[ability => manage announcement, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-535815807 data-indent-pad=\"  \"><span class=sf-dump-note>manage announcement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage announcement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535815807\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.273292, "xdebug_link": null}, {"message": "[ability => manage holiday, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-254295877 data-indent-pad=\"  \"><span class=sf-dump-note>manage holiday</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage holiday</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-254295877\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.27388, "xdebug_link": null}, {"message": "[ability => manage document, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage document</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage document</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.274288, "xdebug_link": null}, {"message": "[ability => manage company policy, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company policy</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage company policy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.274735, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.27501, "xdebug_link": null}, {"message": "[ability => manage bank account, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.275281, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.275557, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2094020321 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2094020321\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.275806, "xdebug_link": null}, {"message": "[ability => manage proposal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-43427591 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43427591\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.276099, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1096689363 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096689363\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.276457, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.276724, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.277114, "xdebug_link": null}, {"message": "[ability => manage goal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.277562, "xdebug_link": null}, {"message": "[ability => manage constant tax, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1855109426 data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855109426\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.277765, "xdebug_link": null}, {"message": "[ability => manage print settings, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2054371889 data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054371889\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.277948, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-110211707 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-110211707\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.278112, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1730820042 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730820042\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.278257, "xdebug_link": null}, {"message": "[ability => manage role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1639896714 data-indent-pad=\"  \"><span class=sf-dump-note>manage role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639896714\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.278419, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1039389612 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039389612\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.278723, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1055743318 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055743318\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.278944, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1734461091 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1734461091\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.279154, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1723854375 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723854375\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.279373, "xdebug_link": null}, {"message": "[ability => show warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-31469895 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31469895\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.280066, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2138348017 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138348017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.280768, "xdebug_link": null}, {"message": "[ability => show pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1200372216 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1200372216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.281582, "xdebug_link": null}, {"message": "[ability => create barcode, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1105153780 data-indent-pad=\"  \"><span class=sf-dump-note>create barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105153780\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.282365, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1102057371 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102057371\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.283784, "xdebug_link": null}, {"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-134261587 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134261587\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.284698, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-877936441 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877936441\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.285638, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.286417, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1715169656 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1715169656\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.287207, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.287451, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.288188, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1043442758 data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043442758\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.288499, "xdebug_link": null}]}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget/eyJpdiI6IlVLU3pDQ0FXT05GSVJUaDFlS0wrZ3c9PSIsInZhbHVlIjoiZGxUbHd6R2RaaDR4bzhuTHZSeEY4dz09IiwibWFjIjoiZTFiMDZlMzAzYWQ1ZDhlMDliOTc1MzRkYjRkZGE3ZmUzMzRkMGI2YjQ1ZDI1YzdmZDQ4Mjg2ZmJkYjk0NTUwZiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/budget/eyJpdiI6IlVLU3pDQ0FXT05GSVJUaDFlS0wrZ3c9PSIsInZhbHVlIjoiZGxUbHd6R2RaaDR4bzhuTHZSeEY4dz09IiwibWFjIjoiZTFiMDZlMzAzYWQ1ZDhlMDliOTc1MzRkYjRkZGE3ZmUzMzRkMGI2YjQ1ZDI1YzdmZDQ4Mjg2ZmJkYjk0NTUwZiIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-1363607386 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1363607386\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-826085754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-826085754\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-761379543 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-761379543\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1687989365 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980037648%7C17%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRTaWFLcUVFNzJXTWZtNjlkQTZjN2c9PSIsInZhbHVlIjoiMjBWdXh6WXBIR0hxcXZoUmtkdFJqZmVzZXYzQnN2NlprSHhmQ0hPUXhPWXBkTERvRHlZQnRkbnlNM0hwWWZHR3ZmS1MzL2NJcTBSR1I0S1RzdjBjVlVDM2VFcTlQUnk3VlI0bElFbUl2S1BxUkl5aWJ0YjgwTjFodEszNTlLVmZHVm16dUhZU0RrUjl5RWFmVzJrak9DdXNyWGVSTXpoWGJQalBtRnJycFF3OHFtdlJtWGlUT3hPK0g0Y1FMdXVrT1g0WDhockxxcWx1bG11cjhiZUtFdGVTTDN2RzI3bzFWaG44M3RONklxRFkyVkJOcitlVVVha1FlTFQ3RFUwRTlBYmlWRFpaUWRHOVZzLzhpaG41NE5wMWNpUWkxNVhTZVpIUXBVbVREL2hEblhsYVduQjFZbVg2TVR4djRhamZZQzArM1NqTHJtT0RjM1pneFBTWWZtKzZTcS92ekgrMHdCZFFUemx6QnkyN21laFFNUGVjRTZEZld4SncxZ0xNTy9WdEJtdE5qTENybXRkTy9SS2dZUVp6NllGVmhQZEFocDFjT25iL3FRUWpBV1o3Rm1GL2FGWllkbC8rdHpnZG5aTEhvc1U5RnE3UzNrVlF0clZWK0JXUktMdTdZNnBKaHFDeGpVS0JEVG45OGQ1RmMyNFU2bXRaNGRqSFNWbXEiLCJtYWMiOiJmYjA0ZGIzNWFlZDRjOWE3ZDljZTdkZTU2NmIyMjhlOTUxZjlhODkxZTcyZmJhOThlMGU5MmM1NTAzOWRkNGY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZvQUdlTjk2VkdXb0lSL25jWURuMkE9PSIsInZhbHVlIjoiWjdndFYvSFdHUHM1NTlWMVh6bi9EMGd6MktQSUc1NlEwdXVzUVdlMzNBa3JDTVd0L1N5UTJxSEw5SHhyNE02VWZjakZkd0hvZTljQW9XbnVLUzluVTJiS3FkNUZUQTAyZ2lGQ29KelU4OU16bUFNaUo2c240c3VwVy9Rc1pTZWdKQ3RMRTRnRGNqYlpFOU8zUXBUZEhhNHdYdDBrOXR2K1JTWERwTGRjTWhNRmJwREt6WVd2bldFUFBvRlN1dzBUZjJTVGxEYmg5a2t4dEw3NzV5bTh6c0dRZ2c5VzhYTENmQTd1SHRxOUxiR3U4WEdVM0dRdEJvRmt0TGFXNTR4VDR6MDJhQW5EWnJKSjNBcVIvZEtRL0lCSGsrY1hmejU1QXRmRzRLTHJrVlZTckR0TzZIMGxKVjFRa0tnR2FWT29tN3VsazVPbU1sV2tIaUNYVk14QWRMYjFlVXVsb21rMHdGVFp5Tk1zVFBXaVZCN1IwTUREM0t3SU5sajM1U1pkd2tyVkk2ZlRVUTAyV2g5OUk5Ynk0czVsUDZUNHBzam9FbE5NTy9VTUNmemI4T0NUSFVRTGU5cHVyektCT1hTNWUrT1dPdzRkd0NpVzA1cmd1cGttR2JnOFdwTHdyRSsxNFhWVnBzUjRKWHU2cGRzaFBaMVVzTlpKcEFmZ3NiVXIiLCJtYWMiOiI4ZGYyOTQ4ZWZiYTAyNTJlYTFjODJjODQ4ODNjNDZhOTA5OWM2ODJlM2U5NWRjMjQ4MjkxY2RmN2NiNGRkMzA5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687989365\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-988062472 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-988062472\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-135896003 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im0vWmo4ZWFsWWxnSjQ5R3k2TVJYZ1E9PSIsInZhbHVlIjoiUVhZOXlMM2JOQjQ4U0NKTjJWOFNOMWFvblhuSmpicFFsOUYwcXNkRFdRdXZrNjBvVEM2aGF1Y215QjEwQjE0YUJKTDIrUHRhWG9takMybHpOQ2NFRUxkRzFCbHpWNVhvTjcyeUo4amZYYnRFUGNzWXNENHl6TThad1ROOG1yNko5dXNJbHM1aEUydjM0YUJpUUVUN05DbFR3ajJGTkJBcmRqQkd4djg2YWJGVitjVUUzcFpCcVI0WFJHWi8yakw0Sk1WTWVXWjJWNHp2eHRTOEdPeHRZbzhla2VWd2NZR2lsYVNNRmRYbm9Nd0tJSVBJeVpaMXlDUGNManE2M2VhNWV0QkJVRXh3MFJ6TFdQYWNIc0VaZitCV2VETk1zSzNabWlqYkVsTkQvYUxxbWI1eUpmZW1hVE5WWUI0KzF0c3Fra1dNV2xvYnJqQXlsRmkwajhMNmQrMXJOYW53SUhYanRaM2YyN0lhUTJYcHVhM3R5Qi9lRUpRWFJBU3pXMCtMZU1oOVIrbWpnajNaVHhBUCt5bWRTcnJPblZTQUdCU3dTczgyL0J6b1BRQno1cXV4dy9VakhYVFBJWXkwRjhRdDJhUFl4eDRScmFzZmcvNWNDWjFlSUNsbS9RSGswa3pXWjZubVY2Z3NJcGV4SUJ6bVhjTEUrMHNjOTlYcnhHTEEiLCJtYWMiOiI3ZTk4MTYyOTFjMDgzMzBmZGZlZjFmYmU0NGUwMGJhYTJjZDUyMTFkZDMxMWEzNGM4YjQ4ZTgzZWE2ZGE2OWI4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFqTS9KejF0d1NXTllNeUdTbENQdmc9PSIsInZhbHVlIjoiSkt0dXZBNlphZVpWUkljWVNUb1ZUSkFoaFgrUnJ1aWg1Y2JJWlRpb2Z2MFhUVHdsenV2VnB3d2daVHFTWjRjeWxCSmUwMXFrUFM2dVBsZmFTYldZQ2JhMHEzamFPZWtydXFHZ3pCcWtTRGI1aUVzTlUwSU1YMkNIKzVqQnF3dU9VVVcvN0NCaFBmR3oxV1A2RE55aUNZRXN1WnZtV1pwQnlIbS9RalhNVnhheFpidjJ6YjhHdFo5T0ZXbG10TTU0RzJpWVVQWjFwYkJ0ZDVuaUo1bmVUdVlFcElGanRNa3dCUGpRS0ZKbXJuNENCZmpYcGc3azB3M085SXJacWplaE1EL3RaNEp6b3hDODlHcTVBRjNjdWtvVFRYUWFDc1hCcjRWWldyd0hYZmNPNndUODM0alJNZjJIN3BObjJUTVJjdVVzUTZabCt4K2tUU1ZoL1hGL2czaTVpczVUWll3aDFScEhzTEFtWlJaUUl4MlhMcFdSQVc3ZkhrY2FHS1NWRTIzUHMweEVnY2Y5QUZSZFJiN3ZsRm9JbVNyR0hFOGhDR2I0TzZvb1d1VzlQNjBWZ3hNTUluUU5XRGMrQkEvRmpvSXY2RHhhWUsyQTdWQWp2eHhOWm5CWVNiNGFlNUNxemZzMkJMbnY4MXdrVHlUUGZnRXQxQjhNOGtpV3J3OE0iLCJtYWMiOiI0Y2Q3ZTI4MzUzYjY0MTM3NTFlNzFhZTk2MDIxYmYxNzFmODFjMjNlY2NhYjY1OTk3NGUwYmY1NTA0ZDRjMGM0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im0vWmo4ZWFsWWxnSjQ5R3k2TVJYZ1E9PSIsInZhbHVlIjoiUVhZOXlMM2JOQjQ4U0NKTjJWOFNOMWFvblhuSmpicFFsOUYwcXNkRFdRdXZrNjBvVEM2aGF1Y215QjEwQjE0YUJKTDIrUHRhWG9takMybHpOQ2NFRUxkRzFCbHpWNVhvTjcyeUo4amZYYnRFUGNzWXNENHl6TThad1ROOG1yNko5dXNJbHM1aEUydjM0YUJpUUVUN05DbFR3ajJGTkJBcmRqQkd4djg2YWJGVitjVUUzcFpCcVI0WFJHWi8yakw0Sk1WTWVXWjJWNHp2eHRTOEdPeHRZbzhla2VWd2NZR2lsYVNNRmRYbm9Nd0tJSVBJeVpaMXlDUGNManE2M2VhNWV0QkJVRXh3MFJ6TFdQYWNIc0VaZitCV2VETk1zSzNabWlqYkVsTkQvYUxxbWI1eUpmZW1hVE5WWUI0KzF0c3Fra1dNV2xvYnJqQXlsRmkwajhMNmQrMXJOYW53SUhYanRaM2YyN0lhUTJYcHVhM3R5Qi9lRUpRWFJBU3pXMCtMZU1oOVIrbWpnajNaVHhBUCt5bWRTcnJPblZTQUdCU3dTczgyL0J6b1BRQno1cXV4dy9VakhYVFBJWXkwRjhRdDJhUFl4eDRScmFzZmcvNWNDWjFlSUNsbS9RSGswa3pXWjZubVY2Z3NJcGV4SUJ6bVhjTEUrMHNjOTlYcnhHTEEiLCJtYWMiOiI3ZTk4MTYyOTFjMDgzMzBmZGZlZjFmYmU0NGUwMGJhYTJjZDUyMTFkZDMxMWEzNGM4YjQ4ZTgzZWE2ZGE2OWI4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFqTS9KejF0d1NXTllNeUdTbENQdmc9PSIsInZhbHVlIjoiSkt0dXZBNlphZVpWUkljWVNUb1ZUSkFoaFgrUnJ1aWg1Y2JJWlRpb2Z2MFhUVHdsenV2VnB3d2daVHFTWjRjeWxCSmUwMXFrUFM2dVBsZmFTYldZQ2JhMHEzamFPZWtydXFHZ3pCcWtTRGI1aUVzTlUwSU1YMkNIKzVqQnF3dU9VVVcvN0NCaFBmR3oxV1A2RE55aUNZRXN1WnZtV1pwQnlIbS9RalhNVnhheFpidjJ6YjhHdFo5T0ZXbG10TTU0RzJpWVVQWjFwYkJ0ZDVuaUo1bmVUdVlFcElGanRNa3dCUGpRS0ZKbXJuNENCZmpYcGc3azB3M085SXJacWplaE1EL3RaNEp6b3hDODlHcTVBRjNjdWtvVFRYUWFDc1hCcjRWWldyd0hYZmNPNndUODM0alJNZjJIN3BObjJUTVJjdVVzUTZabCt4K2tUU1ZoL1hGL2czaTVpczVUWll3aDFScEhzTEFtWlJaUUl4MlhMcFdSQVc3ZkhrY2FHS1NWRTIzUHMweEVnY2Y5QUZSZFJiN3ZsRm9JbVNyR0hFOGhDR2I0TzZvb1d1VzlQNjBWZ3hNTUluUU5XRGMrQkEvRmpvSXY2RHhhWUsyQTdWQWp2eHhOWm5CWVNiNGFlNUNxemZzMkJMbnY4MXdrVHlUUGZnRXQxQjhNOGtpV3J3OE0iLCJtYWMiOiI0Y2Q3ZTI4MzUzYjY0MTM3NTFlNzFhZTk2MDIxYmYxNzFmODFjMjNlY2NhYjY1OTk3NGUwYmY1NTA0ZDRjMGM0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-135896003\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-827980951 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/budget/eyJpdiI6IlVLU3pDQ0FXT05GSVJUaDFlS0wrZ3c9PSIsInZhbHVlIjoiZGxUbHd6R2RaaDR4bzhuTHZSeEY4dz09IiwibWFjIjoiZTFiMDZlMzAzYWQ1ZDhlMDliOTc1MzRkYjRkZGE3ZmUzMzRkMGI2YjQ1ZDI1YzdmZDQ4Mjg2ZmJkYjk0NTUwZiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827980951\", {\"maxDepth\":0})</script>\n"}}