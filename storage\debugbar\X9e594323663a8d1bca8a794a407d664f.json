{"__meta": {"id": "X9e594323663a8d1bca8a794a407d664f", "datetime": "2025-06-26 23:15:07", "utime": **********.033568, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750979706.608152, "end": **********.033582, "duration": 0.4254300594329834, "duration_str": "425ms", "measures": [{"label": "Booting", "start": 1750979706.608152, "relative_start": 0, "end": 1750979706.970537, "relative_end": 1750979706.970537, "duration": 0.3623850345611572, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750979706.970546, "relative_start": 0.3623940944671631, "end": **********.033584, "relative_end": 2.1457672119140625e-06, "duration": 0.06303811073303223, "duration_str": "63.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01439, "accumulated_duration_str": "14.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0000482, "duration": 0.01366, "duration_str": "13.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.927}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.021741, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.927, "width_percent": 2.988}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.027059, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.915, "width_percent": 2.085}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Ill0cTluN3NDaXFHejRvT2ljRGNVWXc9PSIsInZhbHVlIjoiR2d5a2VkWmZGMFY1QzBOZHcxNTUxdz09IiwibWFjIjoiYmQ5MDQ4NmMwZWVhZmQwY2Q2OWI0ZjgzNWIzNzk4MmZmMWZjNDQ1MmEzZGQyOWY1OTdhNDliNjVjYmQ4MzZiNiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1515834379 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1515834379\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2120552374 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2120552374\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-344010014 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344010014\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1818826876 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6Ill0cTluN3NDaXFHejRvT2ljRGNVWXc9PSIsInZhbHVlIjoiR2d5a2VkWmZGMFY1QzBOZHcxNTUxdz09IiwibWFjIjoiYmQ5MDQ4NmMwZWVhZmQwY2Q2OWI0ZjgzNWIzNzk4MmZmMWZjNDQ1MmEzZGQyOWY1OTdhNDliNjVjYmQ4MzZiNiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979702323%7C5%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1ndEdROEM2Rlc0b2g5QTFZNDV3Nnc9PSIsInZhbHVlIjoiN2RBSWQ4L2JRMTBSRHptajVYMEhyY0lhMzN3VzFmQ0RLeXhMZXFzU3hTQ1lLQ0FDTDVJenRCb01iMU5OQ09NOGIrN2tuUzg2SytOdjZRdEtzSmRwMWwrTWxkNThhdHBIM1Y2SlBSV1BhYmZPTU4xRzk0eEI2RHNvS3BxSVMyTFhWL0l5VGJYcXY0Zy9MazRRZ0JhNE1CNThFWEJTTjdNTHZlTFlIbWtic3VTSDh5TlMxd3RXcS9Uelo5Y1FydkFLb20zaUxuOGNDRXIzMEFONnBmQ2YrbXkrZzgvMzNIVk1oSHByV2tRc1RyU3Y1WC9NRjF1RGthaHhRZnBQc2RtamJrU242UCtKVUYrTWMvTGhGZzZDeTZuNzMyV3NBdm01aWRxbmhoakFCWFlWbXRvTTZmMUE2YkEzU0pBdEgvclRaUm0zZlVMallWRHVUL1h0bnRydG9lN2RXSElaSVdCdjJxbnZaTFFmNUx4RzRIaTc4TGRCbWNsakJZenN5cWppbEYxUFVBVklNSWp1S21NWDJQQ1hJK2VsRW9KVWx2eDl5aFdjMDVaLzVwVTFYLytzTXB2cENtR0VYUG9lNVZiZ3ZzVHpCTVpVQVdpZ21DVnVuV21nS09FdnR2QjdLQlRHVENRd0VVR2ZTWmtlZUZhdTRpVTNYc0l4OTZ1MUtaZ24iLCJtYWMiOiI2ZDMxZjI5MDkxOWZkYWYxZjAyODk3ODk2NTQwM2Q2YWRlOThlNzU3Y2ViOTA0ODBhNWQ1MjY5YmVkZjNjYTFiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IithaTN6dTNSazNwcFVobG1uTlJReWc9PSIsInZhbHVlIjoibnh3TWk4dGZRLzVFclZwOXBELzZDMG0zWWo5VFc0QVJsUm1rd0lVMmkxVTRHUDRwUjEweDhjZURjbmZDQmNPell5Q3lnVHA5MGZ2WE5hQzdoQlFqTEdoQzh0YnpRRmwvWTBnTmxqRXBubkFpWHRpTHE5ektXYUdLWXRqcmdMVjFVdzFIcGU3Z0Y3VVVoZTYzYkFGWDNOMjVVVmhTZjVac2NMcWpVQ2YxWlZqeUs2enI1MDBhcldkd0xnenRlTFdSYVJGUlJhc2toRHYwY0o2aWE1ZTQ3dzJPMnFndUgrWGJvZHA1UldmVS9Wc25hVk5oR2hRVjZTY0FUUzRwNE5OdmJkUUJ2OE13bTNuVStTSzFpdXlzMzhSTys0YWRDaG9VM3VmVnFiRU9zWXJVOTFYd2wxbGh3UHJ2SEx2emUvd29yZVdwNFJka0M5ZlprTWV6ZUkrWjVPdGVxbllKMXNKb3ZLWjRFK0dPODhld2YxNG5vQU9lRytQaStTUStkV0l4TmFraGF5Y2VwRzU1aXVTTzNZUW1LTnZUdjUyeHpDNk5RT1hKVCtHOEFtaFZ2NWovZyt4eVpMV2pnK3c4SXQyWm9jWlFGUS82a29YMnF2RmxCakd4NUwzaDJYcm8wRFdWWEhoaFZrTDNvNWRRRjE1andGdlUrTE80Vlh2NXJQM08iLCJtYWMiOiJiMjAyNmNmNTNhODQ0OWY3MzE2ZjJjYWY4NWU3ZWVmOTgxZDRhY2MxZjhiYTA2MTg0YWE1ZDc2YzQ3OTU2YzFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818826876\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1898018031 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898018031\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-105088681 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:15:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9tNFRBZk0zcmF5S0dPSFV5UU9zQnc9PSIsInZhbHVlIjoiN3dpTEw1ZXVQUUs4MmdSVkM0SWdtR3NvdFM5QTJ1MURLZmRDb000cFlRaXdnOTMzc0lYSUF0NkN2RlFNamE0aDVlQ05yQjVHN2NjQXVaeXZ6SFp6cWlMWDNDU0NZcXNMeUlQVTRHVGtPR211SWVybDMyQ25VNnFkYXdmQzJIbDIrOU5WUW9PWXVyL2k1aFpzNk9ZRVhXc083R0VVN1RqQnlLUDNCWWNrWDIwMjluS3JnMG9QcE9Lc0RSamJLVTBZQUJRdDJFRVE4TzRNdElnQlBTTk5QYkVpclZrTUIrUjMwR2JVN1g0c3NMR2JqMkgwZWl2UFZOaS81Wjg0TWl6aWt6R2U5b1pTWW5GN2xGamtYTXZ5Zm5SUnJpa2ZwSkJwOTVHdXc4V2trT0xhRDUwa3dBNUsrK0VnM1UzeTU5ZDJaZnNibU5IM3Z5ZHBqMnlvZStGV1dORmRibGVDdHA3OERwRG9JdFc5T1B5ZkRQRGV6ZHhPaE1HTlF0NExNTGZ2eEVqelBqWE05VmQvZWFOODhjWXFrMGlpK1pOcU91OU5ldjZXQmVDcWhvVUtFWFNUZUFGRllZUytpVmNaTFNwQVJZYVErN1VsVHlUTUxWVHQ4a3h2QVJ3SUtNUXBzNlJISWtrY0dOUGxSK0F2dy9hYk1VSURXZzZKMmY1K085SjkiLCJtYWMiOiI1NjhjYjRhNDI3NmQ4MDNmMDNjYjg3Yzg4NGI3ZWI2NDU5YTExNDU4ODQ5NTFiNzg0MWZlYWU3ODk2MTY4ZmE1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:15:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlY0RXVjVndzeXRoTC9qWnVxTDBMMEE9PSIsInZhbHVlIjoiU3BKL0dnOVo0WTB3RVRsVUdXT2JmYXVGa3NxRjJvVGR5a1A4UktNSUM1eDhuTmZiUXJGZXp0eWlLdWozQTBKUjdCYWo5YjYrUGJsSFdCVmhDV2lDbzdKRDZDU21xSnJCL3NWaTQ0RG5UU0hocWx0KzVJbXhHMTlWbDBHd3VUNDJxUklPbnFtY3JhMGplVWdDL2dpeXVSenJhVlZVUzlOTGNCWlh0Z29yK1Z1UG5WcTJYUllJQ2xYemNNNnYva1ExMFJJL1k2ZnZydEVMRzdzejBJSmZ3aExMRitVeTJpVFViZEZScDdKSlBXUDJrWnl1bGVhNkVwUVRUZnVVZEZmSlJxZXRwcFA5eDNDVmVmbm1IRGUvbGZoczk4elFRK2Y3cVFlQ0RncWdwTXRFa24vdXc2Z1RyMkZ0VHdCNU9GMkFBQWFyVURWVXhtUmJCZ2NxdkR1aVNKaEI1b3VrYTI1dEZEUDBwUzRlNERiRUsvMmVtTlIxWFEzcVllSTltNDNCL24rdHNZK1pRdStNRnZJTnhHYnpNU3ZJY0g2K2M2SFlmcnMwRFI3eWNaWUUxc05OSXZoNEVDdWh6Sm13eS96RFFhMVdINkxaUFlwUUdqd0hWaG92ZUlhekNEVDRtUHVBZUQrdTgvWXg4bE5EdVdSSWJiNWtwWDBoOGdGbGZIWTciLCJtYWMiOiJmNDU0Y2ZhYjU4OWViYjA1OTk1NDNhYmVhNjVlMWU4ODdhMWE1MWE1MDZhNmZjZjMyNTI3MDllNjFkNTkxNjk2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:15:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9tNFRBZk0zcmF5S0dPSFV5UU9zQnc9PSIsInZhbHVlIjoiN3dpTEw1ZXVQUUs4MmdSVkM0SWdtR3NvdFM5QTJ1MURLZmRDb000cFlRaXdnOTMzc0lYSUF0NkN2RlFNamE0aDVlQ05yQjVHN2NjQXVaeXZ6SFp6cWlMWDNDU0NZcXNMeUlQVTRHVGtPR211SWVybDMyQ25VNnFkYXdmQzJIbDIrOU5WUW9PWXVyL2k1aFpzNk9ZRVhXc083R0VVN1RqQnlLUDNCWWNrWDIwMjluS3JnMG9QcE9Lc0RSamJLVTBZQUJRdDJFRVE4TzRNdElnQlBTTk5QYkVpclZrTUIrUjMwR2JVN1g0c3NMR2JqMkgwZWl2UFZOaS81Wjg0TWl6aWt6R2U5b1pTWW5GN2xGamtYTXZ5Zm5SUnJpa2ZwSkJwOTVHdXc4V2trT0xhRDUwa3dBNUsrK0VnM1UzeTU5ZDJaZnNibU5IM3Z5ZHBqMnlvZStGV1dORmRibGVDdHA3OERwRG9JdFc5T1B5ZkRQRGV6ZHhPaE1HTlF0NExNTGZ2eEVqelBqWE05VmQvZWFOODhjWXFrMGlpK1pOcU91OU5ldjZXQmVDcWhvVUtFWFNUZUFGRllZUytpVmNaTFNwQVJZYVErN1VsVHlUTUxWVHQ4a3h2QVJ3SUtNUXBzNlJISWtrY0dOUGxSK0F2dy9hYk1VSURXZzZKMmY1K085SjkiLCJtYWMiOiI1NjhjYjRhNDI3NmQ4MDNmMDNjYjg3Yzg4NGI3ZWI2NDU5YTExNDU4ODQ5NTFiNzg0MWZlYWU3ODk2MTY4ZmE1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:15:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlY0RXVjVndzeXRoTC9qWnVxTDBMMEE9PSIsInZhbHVlIjoiU3BKL0dnOVo0WTB3RVRsVUdXT2JmYXVGa3NxRjJvVGR5a1A4UktNSUM1eDhuTmZiUXJGZXp0eWlLdWozQTBKUjdCYWo5YjYrUGJsSFdCVmhDV2lDbzdKRDZDU21xSnJCL3NWaTQ0RG5UU0hocWx0KzVJbXhHMTlWbDBHd3VUNDJxUklPbnFtY3JhMGplVWdDL2dpeXVSenJhVlZVUzlOTGNCWlh0Z29yK1Z1UG5WcTJYUllJQ2xYemNNNnYva1ExMFJJL1k2ZnZydEVMRzdzejBJSmZ3aExMRitVeTJpVFViZEZScDdKSlBXUDJrWnl1bGVhNkVwUVRUZnVVZEZmSlJxZXRwcFA5eDNDVmVmbm1IRGUvbGZoczk4elFRK2Y3cVFlQ0RncWdwTXRFa24vdXc2Z1RyMkZ0VHdCNU9GMkFBQWFyVURWVXhtUmJCZ2NxdkR1aVNKaEI1b3VrYTI1dEZEUDBwUzRlNERiRUsvMmVtTlIxWFEzcVllSTltNDNCL24rdHNZK1pRdStNRnZJTnhHYnpNU3ZJY0g2K2M2SFlmcnMwRFI3eWNaWUUxc05OSXZoNEVDdWh6Sm13eS96RFFhMVdINkxaUFlwUUdqd0hWaG92ZUlhekNEVDRtUHVBZUQrdTgvWXg4bE5EdVdSSWJiNWtwWDBoOGdGbGZIWTciLCJtYWMiOiJmNDU0Y2ZhYjU4OWViYjA1OTk1NDNhYmVhNjVlMWU4ODdhMWE1MWE1MDZhNmZjZjMyNTI3MDllNjFkNTkxNjk2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:15:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105088681\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-688373855 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6Ill0cTluN3NDaXFHejRvT2ljRGNVWXc9PSIsInZhbHVlIjoiR2d5a2VkWmZGMFY1QzBOZHcxNTUxdz09IiwibWFjIjoiYmQ5MDQ4NmMwZWVhZmQwY2Q2OWI0ZjgzNWIzNzk4MmZmMWZjNDQ1MmEzZGQyOWY1OTdhNDliNjVjYmQ4MzZiNiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-688373855\", {\"maxDepth\":0})</script>\n"}}