
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Budget Vs Actual')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('budget.index')); ?>"><?php echo e(__('Budget Planner')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e($budget->name); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('script-page'); ?>
    <script src="<?php echo e(asset('js/jquery-ui.min.js')); ?>"></script>
    <script>
        //Income Total
        $(document).on('keyup', '.income_data', function () {
            //category wise total
            var el = $(this).parent().parent();
            var inputs = $(el.find('.income_data'));

            var totalincome = 0;
            for (var i = 0; i < inputs.length; i++) {
                var price = $(inputs[i]).val();
                totalincome = parseFloat(totalincome) + parseFloat(price);
            }
            el.find('.totalIncome').html(totalincome);

            // month wise total //
            var month_income = $(this).data('month');
            var month_inputs = $(el.parent().find('.' + month_income + '_income'));
            var month_totalincome = 0;
            for (var i = 0; i < month_inputs.length; i++) {
                var month_price = $(month_inputs[i]).val();
                month_totalincome = parseFloat(month_totalincome) + parseFloat(month_price);
            }
            var month_total_income = month_income + '_total_income';
            el.parent().find('.' + month_total_income).html(month_totalincome);

            //all total //
            var total_inputs = $(el.parent().find('.totalIncome'));
            console.log(total_inputs)
            var income = 0;
            for (var i = 0; i < total_inputs.length; i++) {
                var price = $(total_inputs[i]).html();
                income = parseFloat(income) + parseFloat(price);
            }
            el.parent().find('.income').html(income);

        })


        //Expense Total
        $(document).on('keyup', '.expense_data', function () {
            //category wise total
            var el = $(this).parent().parent();
            var inputs = $(el.find('.expense_data'));

            var totalexpense = 0;
            for (var i = 0; i < inputs.length; i++) {
                var price = $(inputs[i]).val();
                totalexpense = parseFloat(totalexpense) + parseFloat(price);
            }
            el.find('.totalExpense').html(totalexpense);

            // month wise total //
            var month_expense = $(this).data('month');
            var month_inputs = $(el.parent().find('.' + month_expense + '_expense'));
            var month_totalexpense = 0;
            for (var i = 0; i < month_inputs.length; i++) {
                var month_price = $(month_inputs[i]).val();
                month_totalexpense = parseFloat(month_totalexpense) + parseFloat(month_price);
            }
            var month_total_expense = month_expense + '_total_expense';
            el.parent().find('.' + month_total_expense).html(month_totalexpense);

            //all total //
            var total_inputs = $(el.parent().find('.totalExpense'));
            console.log(total_inputs)
            var expense = 0;
            for (var i = 0; i < total_inputs.length; i++) {
                var price = $(total_inputs[i]).html();
                expense = parseFloat(expense) + parseFloat(price);
            }
            el.parent().find('.expense').html(expense);

        })

        //Hide & Show
        $(document).on('change', '.period', function () {
            var period = $(this).val();

            $('.budget_plan').removeClass('d-block');
            $('.budget_plan').addClass('d-none');
            $('#' + period).removeClass('d-none');
            $('#' + period).addClass('d-block');
        });

        // Budget Type Selector (Income/Expense/Both)
        $(document).on('change', '.budget-type-selector', function() {
            var budgetType = $(this).val();
            console.log('Show page - Budget type changed to:', budgetType);

            if (budgetType === 'both') {
                $('.income-section, .income-row').show();
                $('.expense-section, .expense-row').show();
            } else if (budgetType === 'income') {
                $('.income-section, .income-row').show();
                $('.expense-section, .expense-row').hide();
            } else if (budgetType === 'expense') {
                $('.income-section, .income-row').hide();
                $('.expense-section, .expense-row').show();
            }

            // Apply category filter after budget type change
            $('.category-filter').trigger('change');
        });

        // Category Filter
        $(document).on('change', '.category-filter', function() {
            var selectedCategories = $(this).val();
            console.log('Show page - Selected categories:', selectedCategories);

            // If "All Categories" is selected or no categories selected, show all categories
            if (!selectedCategories || selectedCategories.length === 0 || selectedCategories.includes('all')) {
                $('.income-row, .expense-row').show();
                $('.income-section, .expense-section').show();
                updateTotalsDisplay();
                updateFilterStatus(selectedCategories);
                console.log('Show page - Showing all categories');
                return;
            }

            // Hide all category rows first
            $('.income-row, .expense-row').hide();
            $('.income-section, .expense-section').hide();

            var hasIncomeCategories = false;
            var hasExpenseCategories = false;

            // Show only selected categories
            selectedCategories.forEach(function(category) {
                if (category.startsWith('income-')) {
                    var categoryId = category.replace('income-', '');
                    $('.income-category-' + categoryId).show();
                    hasIncomeCategories = true;
                    console.log('Show page - Showing income category:', categoryId);
                } else if (category.startsWith('expense-')) {
                    var categoryId = category.replace('expense-', '');
                    $('.expense-category-' + categoryId).show();
                    hasExpenseCategories = true;
                    console.log('Show page - Showing expense category:', categoryId);
                }
            });

            // Show section headers and totals if there are visible categories
            if (hasIncomeCategories) {
                $('.income-section').show();
                console.log('Show page - Showing income section');
            }
            if (hasExpenseCategories) {
                $('.expense-section').show();
                console.log('Show page - Showing expense section');
            }

            // Update totals display for visible categories only
            updateTotalsDisplay();

            // Update filter status display
            updateFilterStatus(selectedCategories);
        });

        function updateFilterStatus(selectedCategories) {
            var filterStatus = $('#filter-status');
            var filterText = $('#filter-text');

            if (selectedCategories.includes('all') || selectedCategories.length === 0) {
                filterStatus.hide();
            } else {
                var categoryNames = [];
                selectedCategories.forEach(function(category) {
                    var optionText = $('.category-filter option[value="' + category + '"]').text();
                    if (optionText) {
                        categoryNames.push(optionText);
                    }
                });

                if (categoryNames.length > 0) {
                    filterText.text('<?php echo e(__("Filtered by:")); ?> ' + categoryNames.join(', '));
                    filterStatus.show();
                } else {
                    filterStatus.hide();
                }
            }
        }

        // Function to update totals display based on visible categories
        function updateTotalsDisplay() {
            // Update income totals
            updateIncomeTotals();

            // Update expense totals
            updateExpenseTotals();

            // Update net profit
            updateNetProfit();
        }

        function updateIncomeTotals() {
            var monthTotals = {};

            // Get number of months from header
            var monthCount = $('.table thead tr:first th[colspan="3"]').length;

            // Initialize month totals
            for (var i = 0; i < monthCount; i++) {
                monthTotals[i] = {budget: 0, actual: 0, overBudget: 0};
            }

            // Calculate totals from visible income rows
            $('.income-row:visible').each(function() {
                var cells = $(this).find('td');
                var monthIndex = 0;

                for (var i = 1; i < cells.length; i += 3) {
                    if (monthIndex < monthCount) {
                        // Budget column
                        var budgetText = $(cells[i]).text().replace(/[^\d.-]/g, '');
                        var budgetValue = parseFloat(budgetText) || 0;
                        monthTotals[monthIndex].budget += budgetValue;

                        // Actual column
                        if (cells[i + 1]) {
                            var actualText = $(cells[i + 1]).text().split('\n')[0].replace(/[^\d.-]/g, '');
                            var actualValue = parseFloat(actualText) || 0;
                            monthTotals[monthIndex].actual += actualValue;
                        }

                        // Over Budget column
                        if (cells[i + 2]) {
                            var overBudgetText = $(cells[i + 2]).text().split('\n')[0].replace(/[^\d.-]/g, '');
                            var overBudgetValue = parseFloat(overBudgetText) || 0;
                            monthTotals[monthIndex].overBudget += overBudgetValue;
                        }

                        monthIndex++;
                    }
                }
            });

            // Update income total row
            var incomeTotalRow = $('.income-section.total');
            if (incomeTotalRow.length > 0) {
                var totalCells = incomeTotalRow.find('td');
                var cellIndex = 1;

                for (var monthIndex = 0; monthIndex < monthCount; monthIndex++) {
                    if (totalCells[cellIndex]) {
                        $(totalCells[cellIndex]).html('<strong>' + formatNumber(monthTotals[monthIndex].budget) + '</strong>');
                    }
                    if (totalCells[cellIndex + 1]) {
                        $(totalCells[cellIndex + 1]).html('<strong>' + formatNumber(monthTotals[monthIndex].actual) + '</strong>');
                    }
                    if (totalCells[cellIndex + 2]) {
                        $(totalCells[cellIndex + 2]).html('<strong>' + formatNumber(monthTotals[monthIndex].overBudget) + '</strong>');
                    }
                    cellIndex += 3;
                }
            }
        }

        function updateExpenseTotals() {
            var monthTotals = {};

            // Get number of months from header
            var monthCount = $('.table thead tr:first th[colspan="3"]').length;

            // Initialize month totals
            for (var i = 0; i < monthCount; i++) {
                monthTotals[i] = {budget: 0, actual: 0, overBudget: 0};
            }

            // Calculate totals from visible expense rows
            $('.expense-row:visible').each(function() {
                var cells = $(this).find('td');
                var monthIndex = 0;

                for (var i = 1; i < cells.length; i += 3) {
                    if (monthIndex < monthCount) {
                        // Budget column
                        var budgetText = $(cells[i]).text().replace(/[^\d.-]/g, '');
                        var budgetValue = parseFloat(budgetText) || 0;
                        monthTotals[monthIndex].budget += budgetValue;

                        // Actual column
                        if (cells[i + 1]) {
                            var actualText = $(cells[i + 1]).text().split('\n')[0].replace(/[^\d.-]/g, '');
                            var actualValue = parseFloat(actualText) || 0;
                            monthTotals[monthIndex].actual += actualValue;
                        }

                        // Over Budget column
                        if (cells[i + 2]) {
                            var overBudgetText = $(cells[i + 2]).text().split('\n')[0].replace(/[^\d.-]/g, '');
                            var overBudgetValue = parseFloat(overBudgetText) || 0;
                            monthTotals[monthIndex].overBudget += overBudgetValue;
                        }

                        monthIndex++;
                    }
                }
            });

            // Update expense total row
            var expenseTotalRow = $('.expense-section.total');
            if (expenseTotalRow.length > 0) {
                var totalCells = expenseTotalRow.find('td');
                var cellIndex = 1;

                for (var monthIndex = 0; monthIndex < monthCount; monthIndex++) {
                    if (totalCells[cellIndex]) {
                        $(totalCells[cellIndex]).html('<strong>' + formatNumber(monthTotals[monthIndex].budget) + '</strong>');
                    }
                    if (totalCells[cellIndex + 1]) {
                        $(totalCells[cellIndex + 1]).html('<strong>' + formatNumber(monthTotals[monthIndex].actual) + '</strong>');
                    }
                    if (totalCells[cellIndex + 2]) {
                        $(totalCells[cellIndex + 2]).html('<strong>' + formatNumber(monthTotals[monthIndex].overBudget) + '</strong>');
                    }
                    cellIndex += 3;
                }
            }
        }

        function updateNetProfit() {
            // This function can be enhanced to update net profit calculations
            // based on the filtered income and expense totals
        }

        function formatNumber(amount) {
            return new Intl.NumberFormat('ar-SA', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }

        // Initialize the view
        $(document).ready(function() {
            // Initialize select2 for better multi-select experience
            if (typeof $.fn.select2 !== 'undefined') {
                $('.category-filter').select2({
                    placeholder: "<?php echo e(__('Select Categories')); ?>",
                    allowClear: true
                });
            }

            // Trigger budget type change to set initial view
            $('.budget-type-selector').trigger('change');

            // Trigger category filter to apply saved settings
            $('.category-filter').trigger('change');

            // trigger period change
            $('.period').trigger('change');

            // Initial totals calculation
            setTimeout(function() {
                updateTotalsDisplay();
            }, 500);
        });

        // Add event listener for when the page is fully loaded
        $(window).on('load', function() {
            // Ensure totals are calculated after all content is loaded
            updateTotalsDisplay();
        });

    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('css-page'); ?>
<style>
    .budget-filter-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .category-filter-info {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 4px;
        padding: 10px;
        margin-top: 10px;
        font-size: 12px;
        color: #1976d2;
    }

    .total-highlight {
        background-color: #fff3cd !important;
        border: 2px solid #ffc107 !important;
    }

    .income-section.total td,
    .expense-section.total td {
        background-color: #f8f9fa !important;
        font-weight: bold !important;
    }

    .filtered-view .income-section,
    .filtered-view .expense-section {
        border-left: 4px solid #007bff;
    }

    .budget-summary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .budget-summary h6 {
        color: white;
        margin-bottom: 0;
    }
</style>
<?php $__env->stopPush(); ?>


<?php $__env->startSection('content'); ?>
    <div class="col-12 mt-4">
        <div class="card budget-summary">
            <h6 class="report-text mb-0 text-center"><?php echo e(__('Year :')); ?> <?php echo e($budget->from); ?></h6>
            <div id="filter-status" class="text-center mt-2" style="display: none;">
                <small><i class="ti ti-filter"></i> <span id="filter-text"></span></small>
            </div>
        </div>
    </div>

    <!-- Budget Type and Category Filters -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="form-group">
                        <label for="budget_type" class="form-label"><?php echo e(__('Budget Type')); ?></label>
                        <select id="budget_type" class="form-control select budget-type-selector">
                            <option value="both" <?php echo e(isset($budget['display_settings']['budget_type']) && $budget['display_settings']['budget_type'] == 'both' ? 'selected' : ''); ?>><?php echo e(__('Both Income & Expense')); ?></option>
                            <option value="income" <?php echo e(isset($budget['display_settings']['budget_type']) && $budget['display_settings']['budget_type'] == 'income' ? 'selected' : ''); ?>><?php echo e(__('Income Only')); ?></option>
                            <option value="expense" <?php echo e(isset($budget['display_settings']['budget_type']) && $budget['display_settings']['budget_type'] == 'expense' ? 'selected' : ''); ?>><?php echo e(__('Expense Only')); ?></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="form-group">
                        <label for="category_filter" class="form-label"><?php echo e(__('Filter Categories')); ?></label>
                        <select id="category_filter" class="form-control select category-filter" multiple>
                            <?php
                                $allSelected = true;
                                if (isset($budget['display_settings']['category_filter'])) {
                                    $categoryFilter = $budget['display_settings']['category_filter'];

                                    // إذا كان string، فك التشفير
                                    if (is_string($categoryFilter)) {
                                        $categoryFilter = json_decode($categoryFilter, true);
                                    }

                                    // تحقق من أن النتيجة مصفوفة وليست تحتوي على 'all' فقط
                                    if (is_array($categoryFilter) && !in_array('all', $categoryFilter) && count($categoryFilter) > 0) {
                                        $allSelected = false;
                                    }
                                }
                            ?>
                            <option value="all" <?php echo e($allSelected ? 'selected' : ''); ?>><?php echo e(__('All Categories')); ?></option>
                            <optgroup label="<?php echo e(__('Income Categories')); ?>">
                                <?php $__currentLoopData = $allIncomeproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $isSelected = false;
                                        if (isset($budget['display_settings']['category_filter'])) {
                                            $categoryFilter = $budget['display_settings']['category_filter'];

                                            // إذا كان string، فك التشفير
                                            if (is_string($categoryFilter)) {
                                                $categoryFilter = json_decode($categoryFilter, true);
                                            }

                                            // تحقق من أن النتيجة مصفوفة
                                            if (is_array($categoryFilter)) {
                                                $isSelected = in_array('income-'.$productService->id, $categoryFilter);
                                            }
                                        }
                                    ?>
                                    <option value="income-<?php echo e($productService->id); ?>" <?php echo e($isSelected ? 'selected' : ''); ?>><?php echo e($productService->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </optgroup>
                            <optgroup label="<?php echo e(__('Expense Categories')); ?>">
                                <?php $__currentLoopData = $allExpenseproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $isSelected = false;
                                        if (isset($budget['display_settings']['category_filter'])) {
                                            $categoryFilter = $budget['display_settings']['category_filter'];

                                            // إذا كان string، فك التشفير
                                            if (is_string($categoryFilter)) {
                                                $categoryFilter = json_decode($categoryFilter, true);
                                            }

                                            // تحقق من أن النتيجة مصفوفة
                                            if (is_array($categoryFilter)) {
                                                $isSelected = in_array('expense-'.$productService->id, $categoryFilter);
                                            }
                                        }
                                    ?>
                                    <option value="expense-<?php echo e($productService->id); ?>" <?php echo e($isSelected ? 'selected' : ''); ?>><?php echo e($productService->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </optgroup>
                        </select>
                    </div>
                    <div class="category-filter-info">
                        <i class="ti ti-info-circle"></i>
                        <?php echo e(__('Select specific categories to view filtered budget data. Totals will be recalculated automatically for selected categories only.')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body" style="overflow-x: auto">


                    
                    <?php if($budget->period == 'monthly'): ?>
                        <table class="table table-bordered table-item data">
                            <thead>
                            <tr>
                                <td rowspan="2"></td>
                                <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <th colspan="3" scope="colgroup" class="text-center br-1px"><?php echo e($month); ?></th>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>
                            <tr>
                                <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <th scope="col" class="br-1px">Budget</th>
                                    <th scope="col" class="br-1px">Actual</th>
                                    <th scope="col" class="br-1px">Over Budget</th>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>
                            </thead>
                            <!----INCOME Category ---------------------->

                            <tr class="income-section">
                                <th colspan="37" class="text-dark light_blue"><span><?php echo e(__('Income :')); ?></span></th>
                            </tr>

                            <?php
                                $overBudgetTotal=[];
                            ?>

                            <?php $__currentLoopData = $incomeproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $showCategory = true;
                                    // Check if we need to filter categories based on display settings
                                    if (isset($budget['display_settings']['category_filter'])) {
                                        $selectedCategories = [];

                                        // Handle different formats of category_filter
                                        if (is_string($budget['display_settings']['category_filter'])) {
                                            // Try to decode JSON string
                                            $decodedCategories = json_decode($budget['display_settings']['category_filter'], true);
                                            if (is_array($decodedCategories)) {
                                                $selectedCategories = $decodedCategories;
                                            } else {
                                                // If not valid JSON, treat as single value
                                                $selectedCategories = [$budget['display_settings']['category_filter']];
                                            }
                                        } else if (is_array($budget['display_settings']['category_filter'])) {
                                            $selectedCategories = $budget['display_settings']['category_filter'];
                                        }

                                        // Check if this category should be shown
                                        if (!in_array('all', $selectedCategories) && !in_array('income-'.$productService->id, $selectedCategories)) {
                                            $showCategory = false;
                                        }
                                    }
                                ?>

                                <?php if($showCategory): ?>
                                <tr class="income-row income-category-<?php echo e($productService->id); ?>">
                                    <td class="text-dark"><?php echo e($productService->name); ?></td>
                                    <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            // Verificar si existe el dato de presupuesto para esta categoría y mes
                                            if (isset($budget['income_data'][$productService->id][$month])) {
                                                $budgetAmount = $budget['income_data'][$productService->id][$month];
                                            } else {
                                                $budgetAmount = 0;
                                            }

                                            // Log para depuración
                                            \Log::info("Presupuesto de ingresos - Categoría: {$productService->name}, ID: {$productService->id}, Mes: {$month}, Monto: {$budgetAmount}");

                                            $actualAmount = isset($incomeArr[$productService->id][$month]) ? $incomeArr[$productService->id][$month] : 0;
                                            $overBudgetAmount = $actualAmount - $budgetAmount;
                                            $overBudgetTotal[$productService->id][$month] = $overBudgetAmount;
                                        ?>
                                        <td class="income_data <?php echo e($month); ?>_income"><?php echo e(\Auth::user()->priceFormat($budgetAmount)); ?></td>
                                        <td><?php echo e(\Auth::user()->priceFormat($actualAmount)); ?>

                                            <p><?php echo e((isset($budget['income_data'][$productService->id][$month]) && $budget['income_data'][$productService->id][$month] !=0)? (\App\Models\Budget::percentage($budget['income_data'][$productService->id][$month],$actualAmount)!=0) ? '('.(\App\Models\Budget::percentage($budget['income_data'][$productService->id][$month],$actualAmount).'%)') :'':''); ?></p>
                                        </td>
                                        <td><?php echo e(\Auth::user()->priceFormat($overBudgetAmount)); ?>

                                            <p class="<?php echo e((isset($budget['income_data'][$productService->id][$month]) && $budget['income_data'][$productService->id][$month] < $overBudgetAmount)? 'green-text':''); ?> <?php echo e((isset($budget['income_data'][$productService->id][$month]) && $budget['income_data'][$productService->id][$month] > $overBudgetAmount)? 'red-text':''); ?>" ><?php echo e((isset($budget['income_data'][$productService->id][$month]) && $budget['income_data'][$productService->id][$month] !=0)? (\App\Models\Budget::percentage($budget['income_data'][$productService->id][$month],$overBudgetAmount) !=0) ?'('.(\App\Models\Budget::percentage($budget['income_data'][$productService->id][$month],$overBudgetAmount).'%)') :'':''); ?></p>
                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tr>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <?php
                                $overBudgetTotalArr = array();
                                  foreach($overBudgetTotal as $overBudget)
                                  {
                                      foreach($overBudget as $k => $value)
                                      {
                                          $overBudgetTotalArr[$k] = (isset($overBudgetTotalArr[$k]) ? $overBudgetTotalArr[$k] + $value : $value);
                                      }
                                  }
                            ?>
                            <tr class="total income-section">
                                <td class="text-dark"><span></span><strong><?php echo e(__('Total :')); ?></strong></td>
                                <?php if(!empty($budgetTotal) ): ?>
                                    <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                        <td class="text-dark <?php echo e($month); ?>_total_income"><strong><?php echo e(\Auth::user()->priceFormat($budgetTotal[$month])); ?></strong></td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($incomeTotalArr[$month])); ?></strong>
                                            <p><?php echo e(($budgetTotal[$month] !=0)? (\App\Models\Budget::percentage($budgetTotal[$month],$incomeTotalArr[$month])!=0) ? '('.(\App\Models\Budget::percentage($budgetTotal[$month],$incomeTotalArr[$month]).'%)') :'':''); ?></p>

                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overBudgetTotalArr[$month])); ?></strong>
                                            <p class="<?php echo e(($budgetTotal[$month] < $overBudgetTotalArr[$month])? 'green-text':''); ?> <?php echo e(($budgetTotal[$month] > $overBudgetTotalArr[$month])? 'red-text':''); ?>"><?php echo e(($budgetTotal[$month] !=0)? (\App\Models\Budget::percentage($budgetTotal[$month],$overBudgetTotalArr[$month])!=0) ?'('.(\App\Models\Budget::percentage($budgetTotal[$month],$overBudgetTotalArr[$month]).'%)') :'':''); ?></p>

                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>

                            </tr>


                            <!------------ EXPENSE Category ---------------------->

                            <tr class="expense-section">
                                <th colspan="37" class="text-dark light_blue"><span><?php echo e(__('Expense :')); ?></span></th>
                            </tr>
                            <?php
                                $overExpenseBudgetTotal=[];
                            ?>

                            <?php $__currentLoopData = $expenseproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $showCategory = true;
                                    // Check if we need to filter categories based on display settings
                                    if (isset($budget['display_settings']['category_filter'])) {
                                        $selectedCategories = [];

                                        // Handle different formats of category_filter
                                        if (is_string($budget['display_settings']['category_filter'])) {
                                            // Try to decode JSON string
                                            $decodedCategories = json_decode($budget['display_settings']['category_filter'], true);
                                            if (is_array($decodedCategories)) {
                                                $selectedCategories = $decodedCategories;
                                            } else {
                                                // If not valid JSON, treat as single value
                                                $selectedCategories = [$budget['display_settings']['category_filter']];
                                            }
                                        } else if (is_array($budget['display_settings']['category_filter'])) {
                                            $selectedCategories = $budget['display_settings']['category_filter'];
                                        }

                                        // Check if this category should be shown
                                        if (!in_array('all', $selectedCategories) && !in_array('expense-'.$productService->id, $selectedCategories)) {
                                            $showCategory = false;
                                        }
                                    }
                                ?>

                                <?php if($showCategory): ?>
                                <tr class="expense-row expense-category-<?php echo e($productService->id); ?>">
                                    <td class="text-dark"><?php echo e($productService->name); ?></td>
                                    <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            // Verificar si existe el dato de presupuesto para esta categoría y mes
                                            if (isset($budget['expense_data'][$productService->id][$month])) {
                                                $budgetAmount = $budget['expense_data'][$productService->id][$month];
                                            } else {
                                                $budgetAmount = 0;
                                            }

                                            // Log para depuración
                                            \Log::info("Presupuesto de gastos - Categoría: {$productService->name}, ID: {$productService->id}, Mes: {$month}, Monto: {$budgetAmount}");

                                            $actualAmount = isset($expenseArr[$productService->id][$month]) ? $expenseArr[$productService->id][$month] : 0;
                                            $overBudgetAmount = $actualAmount - $budgetAmount;
                                            $overExpenseBudgetTotal[$productService->id][$month] = $overBudgetAmount;
                                        ?>
                                        <td class="expense_data <?php echo e($month); ?>_expense"><?php echo e(\Auth::user()->priceFormat($budgetAmount)); ?></td>
                                        <td><?php echo e(\Auth::user()->priceFormat($actualAmount)); ?>

                                            <p><?php echo e((isset($budget['expense_data'][$productService->id][$month]) && $budget['expense_data'][$productService->id][$month] !=0)? (\App\Models\Budget::percentage($budget['expense_data'][$productService->id][$month],$actualAmount)!=0) ? '('.(\App\Models\Budget::percentage($budget['expense_data'][$productService->id][$month],$actualAmount).'%)') :'':''); ?></p>
                                        </td>
                                        <td><?php echo e(\Auth::user()->priceFormat($overBudgetAmount)); ?>

                                            <p class="<?php echo e((isset($budget['expense_data'][$productService->id][$month]) && $budget['expense_data'][$productService->id][$month] < $overBudgetAmount)? 'green-text':''); ?> <?php echo e((isset($budget['expense_data'][$productService->id][$month]) && $budget['expense_data'][$productService->id][$month] > $overBudgetAmount)? 'red-text':''); ?>" ><?php echo e((isset($budget['expense_data'][$productService->id][$month]) && $budget['expense_data'][$productService->id][$month] !=0)? (\App\Models\Budget::percentage($budget['expense_data'][$productService->id][$month],$overBudgetAmount) !=0) ?'('.(\App\Models\Budget::percentage($budget['expense_data'][$productService->id][$month],$overBudgetAmount).'%)') :'':''); ?></p>
                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tr>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <?php
                                $overExpenseBudgetTotalArr = array();
                                  foreach($overExpenseBudgetTotal as $overExpenseBudget)
                                  {
                                      foreach($overExpenseBudget as $k => $value)
                                      {
                                          $overExpenseBudgetTotalArr[$k] = (isset($overExpenseBudgetTotalArr[$k]) ? $overExpenseBudgetTotalArr[$k] + $value : $value);
                                      }
                                  }
                            ?>

                            <tr class="total expense-section">
                                <td class="text-dark"><span></span><strong><?php echo e(__('Total :')); ?></strong></td>
                                <?php if(!empty($budgetExpenseTotal) ): ?>
                                    <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <td class="text-dark <?php echo e($month); ?>_total_expense"><strong><?php echo e(\Auth::user()->priceFormat($budgetExpenseTotal[$month])); ?></strong></td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($expenseTotalArr[$month])); ?></strong>
                                            <p><?php echo e(($budgetExpenseTotal[$month] !=0)? (\App\Models\Budget::percentage($budgetExpenseTotal[$month],$expenseTotalArr[$month])!=0) ? '('.(\App\Models\Budget::percentage($budgetExpenseTotal[$month],$expenseTotalArr[$month]).'%)') :'':''); ?></p>
                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overExpenseBudgetTotalArr[$month])); ?></strong>
                                            <p class="<?php echo e(($budgetExpenseTotal[$month] < $overExpenseBudgetTotalArr[$month])? 'green-text':''); ?> <?php echo e(($budgetExpenseTotal[$month] >$overExpenseBudgetTotalArr[$month])? 'red-text':''); ?>"><?php echo e(($budgetExpenseTotal[$month] !=0)? (\App\Models\Budget::percentage($budgetExpenseTotal[$month],$overExpenseBudgetTotalArr[$month])!=0) ? '('.(\App\Models\Budget::percentage($budgetExpenseTotal[$month],$overExpenseBudgetTotalArr[$month]).'%)') :'':''); ?></p>

                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>

                            </tr>

                            <td></td>

                            <tfoot>
                            <tr class="total">
                                <td class="text-dark"><span></span><strong><?php echo e(__('NET PROFIT :')); ?></strong></td>
                                <?php
                                    // NET PROFIT OF OVER BUDGET
                                     $overbudgetprofit = [];
                                     $keys   = array_keys($overBudgetTotalArr + $overExpenseBudgetTotalArr);
                                     foreach($keys as $v)
                                     {
                                         $overbudgetprofit[$v] = (empty($overBudgetTotalArr[$v]) ? 0 : $overBudgetTotalArr[$v]) - (empty($overExpenseBudgetTotalArr[$v]) ? 0 : $overExpenseBudgetTotalArr[$v]);
                                     }
                                     $data['overbudgetprofit'] = $overbudgetprofit;
                                ?>

                                <?php if(!empty($budgetprofit) ): ?>
                                    <?php $__currentLoopData = $monthList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($budgetprofit[$month])); ?></strong></td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($actualprofit[$month])); ?></strong>
                                            <p><?php echo e(($budgetprofit[$month] !=0)? (\App\Models\Budget::percentage($budgetprofit[$month],$actualprofit[$month])!=0) ?'('.(\App\Models\Budget::percentage($budgetprofit[$month],$actualprofit[$month]).'%)') :'':''); ?></p>
                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overbudgetprofit[$month])); ?></strong>
                                            <p class="<?php echo e(($budgetprofit[$month] < $overbudgetprofit[$month])? 'green-text':''); ?> <?php echo e(($budgetprofit[$month] > $overbudgetprofit[$month])? 'red-text':''); ?>"><?php echo e(($budgetprofit[$month] !=0)? (\App\Models\Budget::percentage($budgetprofit[$month],$overbudgetprofit[$month])!=0) ? '('.(\App\Models\Budget::percentage($budgetprofit[$month],$overbudgetprofit[$month]).'%)') :'':''); ?></p>
                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>

                            </tr>
                            </tfoot>


                        </table>

                        

                    <?php elseif($budget->period == 'quarterly'): ?>
                        <table class="table table-bordered table-item data">
                            <thead>
                            <tr>
                                <td rowspan="2"></td> <!-- merge two rows -->
                                <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <th colspan="3" scope="colgroup" class="text-center br-1px"><?php echo e($month); ?></th>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>
                            <tr>
                                <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <th scope="col" class="br-1px">Budget</th>
                                    <th scope="col" class="br-1px">Actual</th>
                                    <th scope="col" class="br-1px">Over Budget</th>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>
                            </thead>

                            <!----INCOME Category ---------------------->

                            <tr>
                                <th colspan="37" class="text-dark light_blue"><span><?php echo e(__('Income :')); ?></span></th>
                            </tr>

                            <?php
                                $overBudgetTotal=[];
                            ?>

                            <?php $__currentLoopData = $incomeproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $showCategory = true;
                                    // Check if we need to filter categories based on display settings
                                    if (isset($budget['display_settings']['category_filter'])) {
                                        $selectedCategories = [];

                                        // Handle different formats of category_filter
                                        if (is_string($budget['display_settings']['category_filter'])) {
                                            // Try to decode JSON string
                                            $decodedCategories = json_decode($budget['display_settings']['category_filter'], true);
                                            if (is_array($decodedCategories)) {
                                                $selectedCategories = $decodedCategories;
                                            } else {
                                                // If not valid JSON, treat as single value
                                                $selectedCategories = [$budget['display_settings']['category_filter']];
                                            }
                                        } else if (is_array($budget['display_settings']['category_filter'])) {
                                            $selectedCategories = $budget['display_settings']['category_filter'];
                                        }

                                        // Check if this category should be shown
                                        if (!in_array('all', $selectedCategories) && !in_array('income-'.$productService->id, $selectedCategories)) {
                                            $showCategory = false;
                                        }
                                    }
                                ?>

                                <?php if($showCategory): ?>
                                <tr>
                                    <td class="text-dark"><?php echo e($productService->name); ?></td>
                                    <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            // Verificar si existe el dato de presupuesto para esta categoría y mes
                                            if (isset($budget['income_data'][$productService->id][$month])) {
                                                $budgetAmount = $budget['income_data'][$productService->id][$month];
                                            } else {
                                                $budgetAmount = 0;
                                            }

                                            // Log para depuración
                                            \Log::info("Presupuesto de ingresos (trimestral) - Categoría: {$productService->name}, ID: {$productService->id}, Mes: {$month}, Monto: {$budgetAmount}");

                                            // Check if the actual amount exists for this month
                                            if (isset($incomeArr[$productService->id][$month])) {
                                                $actualAmount = $incomeArr[$productService->id][$month];
                                            } else {
                                                $actualAmount = 0;
                                                // Log para depuración
                                                \Log::info("Categoría no encontrada: {$productService->name}, Mes: {$month}");
                                            }

                                            $overBudgetAmount = $actualAmount - $budgetAmount;
                                            $overBudgetTotal[$productService->id][$month] = $overBudgetAmount;
                                        ?>

                                        <td class="income_data <?php echo e($month); ?>_income"><?php echo e(\Auth::user()->priceFormat($budgetAmount)); ?></td>
                                        <td><?php echo e(\Auth::user()->priceFormat($actualAmount)); ?>

                                            <p><?php echo e(($budgetAmount !=0)? (\App\Models\Budget::percentage($budgetAmount,$actualAmount)!=0) ? '('.(\App\Models\Budget::percentage($budgetAmount,$actualAmount).'%)') :'':''); ?></p>
                                        </td>
                                        <td><?php echo e(\Auth::user()->priceFormat($overBudgetAmount)); ?>

                                            <p class="<?php echo e(($budgetAmount < $overBudgetAmount)? 'green-text':''); ?> <?php echo e(($budgetAmount > $overBudgetAmount)? 'red-text':''); ?>"><?php echo e(($budgetAmount !=0)? (\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount)!=0) ? '('.(\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount).'%)') :'':''); ?></p>
                                        </td>

                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tr>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $overBudgetTotalArr = array();
                                  foreach($overBudgetTotal as $overBudget)
                                  {
                                      foreach($overBudget as $k => $value)
                                      {
                                          $overBudgetTotalArr[$k] = (isset($overBudgetTotalArr[$k]) ? $overBudgetTotalArr[$k] + $value : $value);
                                      }
                                  }
                            ?>
                            <tr class="total">
                                <td class="text-dark"><strong><?php echo e(__('Total :')); ?></strong></td>
                                <?php if(!empty($budgetTotal) ): ?>

                                    <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                        <td class="text-dark <?php echo e($month); ?>_total_income">
                                            <strong>
                                                <?php
                                                    // Verificar si existe el valor en el array
                                                    if (isset($budgetTotal[$month])) {
                                                        $totalBudget = $budgetTotal[$month];
                                                    } else {
                                                        $totalBudget = 0;
                                                        // Log para depuración
                                                        \Log::warning("No se encontró el valor para el mes {$month} en budgetTotal");
                                                    }
                                                    // Log para depuración
                                                    \Log::info("Total presupuesto para {$month}: {$totalBudget}");
                                                ?>
                                                <?php echo e(\Auth::user()->priceFormat($totalBudget)); ?>

                                            </strong>
                                        </td>
                                        <td><strong><?php echo e(\Auth::user()->priceFormat($incomeTotalArr[$month])); ?></strong>
                                            <p><?php echo e(($totalBudget !=0)? (\App\Models\Budget::percentage($totalBudget,$incomeTotalArr[$month]) !=0)?'('.(\App\Models\Budget::percentage($totalBudget,$incomeTotalArr[$month]).'%)') :'':''); ?></p>
                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overBudgetTotalArr[$month])); ?></strong>
                                            <p class="<?php echo e(($totalBudget < $overBudgetTotalArr[$month])? 'green-text':''); ?> <?php echo e(($totalBudget > $overBudgetTotalArr[$month])? 'red-text':''); ?>"><?php echo e(($totalBudget !=0)? '('.(\App\Models\Budget::percentage($totalBudget,$overBudgetTotalArr[$month]).'%)') :''); ?></p>
                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>

                            </tr>


                            <!------------ EXPENSE Category ---------------------->

                            <tr>
                                <th colspan="37" class="text-dark light_blue"><span><?php echo e(__('Expense :')); ?></span></th>
                            </tr>

                            <?php
                                $overExpenseBudgetTotal=[];
                            ?>

                            <?php $__currentLoopData = $expenseproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $showCategory = true;
                                    // Check if we need to filter categories based on display settings
                                    if (isset($budget['display_settings']['category_filter'])) {
                                        $selectedCategories = [];

                                        // Handle different formats of category_filter
                                        if (is_string($budget['display_settings']['category_filter'])) {
                                            // Try to decode JSON string
                                            $decodedCategories = json_decode($budget['display_settings']['category_filter'], true);
                                            if (is_array($decodedCategories)) {
                                                $selectedCategories = $decodedCategories;
                                            } else {
                                                // If not valid JSON, treat as single value
                                                $selectedCategories = [$budget['display_settings']['category_filter']];
                                            }
                                        } else if (is_array($budget['display_settings']['category_filter'])) {
                                            $selectedCategories = $budget['display_settings']['category_filter'];
                                        }

                                        // Check if this category should be shown
                                        if (!in_array('all', $selectedCategories) && !in_array('expense-'.$productService->id, $selectedCategories)) {
                                            $showCategory = false;
                                        }
                                    }
                                ?>

                                <?php if($showCategory): ?>
                                <tr>
                                    <td class="text-dark"><?php echo e($productService->name); ?></td>
                                    <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            // Verificar si existe el dato de presupuesto para esta categoría y mes
                                            if (isset($budget['expense_data'][$productService->id][$month])) {
                                                $budgetAmount = $budget['expense_data'][$productService->id][$month];
                                            } else {
                                                $budgetAmount = 0;
                                            }

                                            // Log para depuración
                                            \Log::info("Presupuesto de gastos (trimestral) - Categoría: {$productService->name}, ID: {$productService->id}, Mes: {$month}, Monto: {$budgetAmount}");

                                            // Check if the actual amount exists for this month
                                            if (isset($expenseArr[$productService->id][$month])) {
                                                $actualAmount = $expenseArr[$productService->id][$month];
                                            } else {
                                                $actualAmount = 0;
                                            }

                                            $overBudgetAmount = $actualAmount - $budgetAmount;
                                            $overExpenseBudgetTotal[$productService->id][$month] = $overBudgetAmount;
                                        ?>
                                        <td class="expense_data <?php echo e($month); ?>_expense"><?php echo e(\Auth::user()->priceFormat($budgetAmount)); ?></td>
                                        <td><?php echo e(\Auth::user()->priceFormat($actualAmount)); ?>

                                            <p><?php echo e(($budgetAmount !=0)? (\App\Models\Budget::percentage($budgetAmount,$actualAmount) !=0) ?'('.(\App\Models\Budget::percentage($budgetAmount,$actualAmount).'%)') :'':''); ?></p>
                                        </td>
                                        <td><?php echo e(\Auth::user()->priceFormat($overBudgetAmount)); ?>

                                            <p class="<?php echo e(($budgetAmount < $overBudgetAmount)? 'green-text':''); ?> <?php echo e(($budgetAmount > $overBudgetAmount)? 'red-text':''); ?>"><?php echo e(($budgetAmount !=0)? (\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount)!=0) ? '('.(\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount).'%)') :'':''); ?></p>
                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(!empty($budgetExpenseTotal) ): ?>
                                        <?php
                                            $overExpenseBudgetTotalArr = array();
                                              foreach($overExpenseBudgetTotal as $overExpenseBudget)
                                              {
                                                  foreach($overExpenseBudget as $k => $value)
                                                  {
                                                      $overExpenseBudgetTotalArr[$k] = (isset($overExpenseBudgetTotalArr[$k]) ? $overExpenseBudgetTotalArr[$k] + $value : $value);
                                                  }
                                              }
                                        ?>
                                    <?php endif; ?>
                                </tr>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <tr class="total">
                                <td class="text-dark"><span></span><strong><?php echo e(__('Total :')); ?></strong></td>

                                <?php if(!empty($budgetExpenseTotal) ): ?>

                                    <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                        <td class="text-dark <?php echo e($month); ?>_total_expense">
                                            <strong>
                                                <?php
                                                    // Verificar si existe el valor en el array
                                                    if (isset($budgetExpenseTotal[$month])) {
                                                        $totalExpenseBudget = $budgetExpenseTotal[$month];
                                                    } else {
                                                        $totalExpenseBudget = 0;
                                                        // Log para depuración
                                                        \Log::warning("No se encontró el valor para el mes {$month} en budgetExpenseTotal");
                                                    }
                                                    // Log para depuración
                                                    \Log::info("Total presupuesto de gastos para {$month}: {$totalExpenseBudget}");
                                                ?>
                                                <?php echo e(\Auth::user()->priceFormat($totalExpenseBudget)); ?>

                                            </strong>
                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($expenseTotalArr[$month])); ?></strong>
                                            <p><?php echo e(($totalExpenseBudget !=0)? (\App\Models\Budget::percentage($totalExpenseBudget,$expenseTotalArr[$month])!=0) ? '('.(\App\Models\Budget::percentage($totalExpenseBudget,$expenseTotalArr[$month]).'%)') :'':''); ?></p>

                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overExpenseBudgetTotalArr[$month])); ?></strong>
                                            <p class="<?php echo e(($totalExpenseBudget < $overExpenseBudgetTotalArr[$month])? 'green-text':''); ?> <?php echo e(($totalExpenseBudget > $overExpenseBudgetTotalArr[$month])? 'red-text':''); ?>"><?php echo e(($totalExpenseBudget !=0)? (\App\Models\Budget::percentage($totalExpenseBudget,$overExpenseBudgetTotalArr[$month])!=0) ?'('.(\App\Models\Budget::percentage($totalExpenseBudget,$overExpenseBudgetTotalArr[$month]).'%)') :'':''); ?></p>

                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>

                            </tr>

                            <td></td>

                            <tfoot>
                            <tr class="total">
                                <td class="text-dark"><span></span><strong><?php echo e(__('NET PROFIT :')); ?></strong></td>
                                <?php
                                    // NET PROFIT OF OVER BUDGET
                                    $overbudgetprofit = [];
                                    $keys = array_keys($overBudgetTotalArr + $overExpenseBudgetTotalArr);
                                    foreach($keys as $v)
                                    {
                                        $overbudgetprofit[$v] = (empty($overBudgetTotalArr[$v]) ? 0 : $overBudgetTotalArr[$v]) - (empty($overExpenseBudgetTotalArr[$v]) ? 0 : $overExpenseBudgetTotalArr[$v]);
                                    }
                                    $data['overbudgetprofit'] = $overbudgetprofit;
                                ?>

                                <?php if(!empty($budgetprofit) ): ?>
                                    <?php $__currentLoopData = $quarterly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($budgetprofit[$month])); ?></strong></td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($actualprofit[$month])); ?></strong>
                                            <p><?php echo e(($budgetprofit[$month] !=0)? (\App\Models\Budget::percentage($budgetprofit[$month],$actualprofit[$month])!=0) ?'('.(\App\Models\Budget::percentage($budgetprofit[$month],$actualprofit[$month]).'%)') :'':''); ?></p>
                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overbudgetprofit[$month])); ?></strong>
                                            <p class="<?php echo e(($budgetprofit[$month] < $overbudgetprofit[$month])? 'green-text':''); ?> <?php echo e(($budgetprofit[$month] > $overbudgetprofit[$month])? 'red-text':''); ?>"><?php echo e(($budgetprofit[$month] !=0)? (\App\Models\Budget::percentage($budgetprofit[$month],$overbudgetprofit[$month])!=0) ? '('.(\App\Models\Budget::percentage($budgetprofit[$month],$overbudgetprofit[$month]).'%)') :'':''); ?></p>
                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>

                            </tr>
                            </tfoot>


                        </table>

                        

                    <?php elseif($budget->period == 'half-yearly'): ?>
                        <table class="table table-bordered table-item data">
                            <thead>
                            <tr>
                                <td rowspan="2"></td>
                                <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <th colspan="3" scope="colgroup" class="text-center br-1px"><?php echo e($month); ?></th>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>
                            <tr>
                                <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <th scope="col" class="br-1px">Budget</th>
                                    <th scope="col" class="br-1px">Actual</th>
                                    <th scope="col" class="br-1px">Over Budget</th>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>
                            </thead>

                            <!----INCOME Category ---------------------->

                            <tr>
                                <th colspan="37" class="text-dark light_blue"><span><?php echo e(__('Income :')); ?></span></th>
                            </tr>

                            <?php
                                $overBudgetTotal=[];
                            ?>

                            <?php $__currentLoopData = $incomeproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $showCategory = true;
                                    // Check if we need to filter categories based on display settings
                                    if (isset($budget['display_settings']['category_filter'])) {
                                        $selectedCategories = [];

                                        // Handle different formats of category_filter
                                        if (is_string($budget['display_settings']['category_filter'])) {
                                            // Try to decode JSON string
                                            $decodedCategories = json_decode($budget['display_settings']['category_filter'], true);
                                            if (is_array($decodedCategories)) {
                                                $selectedCategories = $decodedCategories;
                                            } else {
                                                // If not valid JSON, treat as single value
                                                $selectedCategories = [$budget['display_settings']['category_filter']];
                                            }
                                        } else if (is_array($budget['display_settings']['category_filter'])) {
                                            $selectedCategories = $budget['display_settings']['category_filter'];
                                        }

                                        // Check if this category should be shown
                                        if (!in_array('all', $selectedCategories) && !in_array('income-'.$productService->id, $selectedCategories)) {
                                            $showCategory = false;
                                        }
                                    }
                                ?>

                                <?php if($showCategory): ?>
                                <tr>
                                    <td class="text-dark"><?php echo e($productService->name); ?></td>
                                    <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            // Check if the data exists for this month
                                            if (isset($budget['income_data'][$productService->id][$month])) {
                                                $budgetAmount = $budget['income_data'][$productService->id][$month] ? $budget['income_data'][$productService->id][$month] : 0;
                                            } else {
                                                $budgetAmount = 0;
                                            }

                                            // Check if the actual amount exists for this month
                                            if (isset($incomeArr[$productService->id][$month])) {
                                                $actualAmount = $incomeArr[$productService->id][$month];
                                            } else {
                                                $actualAmount = 0;
                                            }

                                            $overBudgetAmount = $actualAmount - $budgetAmount;
                                            $overBudgetTotal[$productService->id][$month] = $overBudgetAmount;

                                        ?>

                                        <td class="income_data <?php echo e($month); ?>_income"><?php echo e(\Auth::user()->priceFormat($budgetAmount)); ?></td>
                                        <td><?php echo e(\Auth::user()->priceFormat($actualAmount)); ?>

                                            <p><?php echo e(($budgetAmount !=0)? (\App\Models\Budget::percentage($budgetAmount,$actualAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetAmount,$actualAmount).'%)') :'':''); ?></p>

                                        </td>
                                        <td><?php echo e(\Auth::user()->priceFormat($overBudgetAmount)); ?>

                                            <p class="<?php echo e(($budgetAmount < $overBudgetAmount)? 'green-text':''); ?> <?php echo e(($budgetAmount > $overBudgetAmount)? 'red-text':''); ?>"><?php echo e(($budgetAmount !=0)? (\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount).'%)') :'':''); ?></p>

                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tr>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $overBudgetTotalArr = array();
                                  foreach($overBudgetTotal as $overBudget)
                                  {
                                      foreach($overBudget as $k => $value)
                                      {
                                          $overBudgetTotalArr[$k] = (isset($overBudgetTotalArr[$k]) ? $overBudgetTotalArr[$k] + $value : $value);
                                      }
                                  }
                            ?>

                            <tr class="total">
                                <td class="text-dark"><span></span><strong><?php echo e(__('Total :')); ?></strong></td>
                                <?php if(!empty($budgetTotal) ): ?>
                                    <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                        <td class="text-dark <?php echo e($month); ?>_total_income">
                                            <strong>
                                                <?php
                                                    // Verificar si existe el valor en el array
                                                    if (isset($budgetTotal[$month])) {
                                                        $totalBudget = $budgetTotal[$month];
                                                    } else {
                                                        $totalBudget = 0;
                                                        // Log para depuración
                                                        \Log::warning("No se encontró el valor para el mes {$month} en budgetTotal (semestral)");
                                                    }
                                                    // Log para depuración
                                                    \Log::info("Total presupuesto para {$month} (semestral): {$totalBudget}");
                                                ?>
                                                <?php echo e(\Auth::user()->priceFormat($totalBudget)); ?>

                                            </strong>
                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($incomeTotalArr[$month])); ?></strong>
                                            <p><?php echo e(($totalBudget !=0)? (\App\Models\Budget::percentage($totalBudget,$incomeTotalArr[$month])!=0) ?'('.(\App\Models\Budget::percentage($totalBudget,$incomeTotalArr[$month]).'%)') :'':''); ?></p>
                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overBudgetTotalArr[$month])); ?></strong>
                                            <p class="<?php echo e(($totalBudget < $overBudgetTotalArr[$month])? 'green-text':''); ?> <?php echo e(($totalBudget > $overBudgetTotalArr[$month])? 'red-text':''); ?>"><?php echo e(($totalBudget !=0)? (\App\Models\Budget::percentage($totalBudget,$overBudgetTotalArr[$month])!=0) ?'('.(\App\Models\Budget::percentage($totalBudget,$overBudgetTotalArr[$month]).'%)') :'':''); ?></p>

                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>

                            </tr>


                            <!------------ EXPENSE Category ---------------------->

                            <tr>
                                <th colspan="37" class="text-dark light_blue"><span><?php echo e(__('Expense :')); ?></span></th>
                            </tr>

                            <?php
                                $overExpenseBudgetTotal=[];
                            ?>

                            <?php $__currentLoopData = $expenseproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $showCategory = true;
                                    // Check if we need to filter categories based on display settings
                                    if (isset($budget['display_settings']['category_filter'])) {
                                        $selectedCategories = [];

                                        // Handle different formats of category_filter
                                        if (is_string($budget['display_settings']['category_filter'])) {
                                            // Try to decode JSON string
                                            $decodedCategories = json_decode($budget['display_settings']['category_filter'], true);
                                            if (is_array($decodedCategories)) {
                                                $selectedCategories = $decodedCategories;
                                            } else {
                                                // If not valid JSON, treat as single value
                                                $selectedCategories = [$budget['display_settings']['category_filter']];
                                            }
                                        } else if (is_array($budget['display_settings']['category_filter'])) {
                                            $selectedCategories = $budget['display_settings']['category_filter'];
                                        }

                                        // Check if this category should be shown
                                        if (!in_array('all', $selectedCategories) && !in_array('expense-'.$productService->id, $selectedCategories)) {
                                            $showCategory = false;
                                        }
                                    }
                                ?>

                                <?php if($showCategory): ?>
                                <tr>
                                    <td class="text-dark"><?php echo e($productService->name); ?></td>
                                    <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            // Check if the data exists for this month
                                            if (isset($budget['expense_data'][$productService->id][$month])) {
                                                $budgetAmount = $budget['expense_data'][$productService->id][$month] ? $budget['expense_data'][$productService->id][$month] : 0;
                                            } else {
                                                $budgetAmount = 0;
                                            }

                                            // Check if the actual amount exists for this month
                                            if (isset($expenseArr[$productService->id][$month])) {
                                                $actualAmount = $expenseArr[$productService->id][$month];
                                            } else {
                                                $actualAmount = 0;
                                            }

                                            $overBudgetAmount = $actualAmount - $budgetAmount;
                                            $overExpenseBudgetTotal[$productService->id][$month] = $overBudgetAmount;

                                        ?>
                                        <td class="expense_data <?php echo e($month); ?>_expense"><?php echo e(\Auth::user()->priceFormat($budgetAmount)); ?></td>
                                        <td><?php echo e(\Auth::user()->priceFormat($actualAmount)); ?>

                                            <p><?php echo e(($budgetAmount !=0)? (\App\Models\Budget::percentage($budgetAmount,$actualAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetAmount,$actualAmount).'%)') :'':''); ?></p>

                                        </td>
                                        <td><?php echo e(\Auth::user()->priceFormat($overBudgetAmount)); ?>

                                            <p class="<?php echo e(($budgetAmount < $overBudgetAmount)? 'green-text':''); ?> <?php echo e(($budgetAmount > $overBudgetAmount)? 'red-text':''); ?>"><?php echo e(($budgetAmount !=0)? (\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount).'%)') :'':''); ?></p>
                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tr>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $overExpenseBudgetTotalArr = array();
                                  foreach($overExpenseBudgetTotal as $overExpenseBudget)
                                  {
                                      foreach($overExpenseBudget as $k => $value)
                                      {
                                          $overExpenseBudgetTotalArr[$k] = (isset($overExpenseBudgetTotalArr[$k]) ? $overExpenseBudgetTotalArr[$k] + $value : $value);
                                      }
                                  }
                            ?>


                            <tr class="total">
                                <td class="text-dark"><span></span><strong><?php echo e(__('Total :')); ?></strong></td>
                                <?php if(!empty($budgetExpenseTotal) ): ?>
                                    <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>


                                        <td class="text-dark <?php echo e($month); ?>_total_expense">
                                            <strong>
                                                <?php
                                                    // Verificar si existe el valor en el array
                                                    if (isset($budgetExpenseTotal[$month])) {
                                                        $totalExpenseBudget = $budgetExpenseTotal[$month];
                                                    } else {
                                                        $totalExpenseBudget = 0;
                                                        // Log para depuración
                                                        \Log::warning("No se encontró el valor para el mes {$month} en budgetExpenseTotal (semestral)");
                                                    }
                                                    // Log para depuración
                                                    \Log::info("Total presupuesto de gastos para {$month} (semestral): {$totalExpenseBudget}");
                                                ?>
                                                <?php echo e(\Auth::user()->priceFormat($totalExpenseBudget)); ?>

                                            </strong>
                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($expenseTotalArr[$month])); ?></strong>
                                            <p><?php echo e(($totalExpenseBudget !=0)? (\App\Models\Budget::percentage($totalExpenseBudget,$expenseTotalArr[$month])!=0) ?'('.(\App\Models\Budget::percentage($totalExpenseBudget,$expenseTotalArr[$month]).'%)') :'':''); ?></p>

                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overExpenseBudgetTotalArr[$month])); ?></strong>
                                            <p class="<?php echo e(($totalExpenseBudget < $overExpenseBudgetTotalArr[$month])? 'green-text':''); ?> <?php echo e(($totalExpenseBudget > $overExpenseBudgetTotalArr[$month])? 'red-text':''); ?>"><?php echo e(($totalExpenseBudget !=0)? (\App\Models\Budget::percentage($totalExpenseBudget,$overExpenseBudgetTotalArr[$month])!=0) ? '('.(\App\Models\Budget::percentage($totalExpenseBudget,$overExpenseBudgetTotalArr[$month]).'%)') :'':''); ?></p>

                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>

                            </tr>
                            <td></td>
                            <tfoot>
                            <tr class="total">
                                <td class="text-dark"><span></span><strong><?php echo e(__('NET PROFIT :')); ?></strong></td>
                                <?php
                                    // NET PROFIT OF OVER BUDGET
                                     $overbudgetprofit = [];
                                     $keys   = array_keys($overBudgetTotalArr + $overExpenseBudgetTotalArr);
                                     foreach($keys as $v)
                                     {
                                         $overbudgetprofit[$v] = (empty($overBudgetTotalArr[$v]) ? 0 : $overBudgetTotalArr[$v]) - (empty($overExpenseBudgetTotalArr[$v]) ? 0 : $overExpenseBudgetTotalArr[$v]);
                                     }
                                     $data['overbudgetprofit'] = $overbudgetprofit;
                                ?>
                                <?php if(!empty($budgetprofit) ): ?>
                                    <?php $__currentLoopData = $half_yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($budgetprofit[$month])); ?></strong></td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($actualprofit[$month])); ?></strong>
                                            <p><?php echo e(($budgetprofit[$month] !=0)? (\App\Models\Budget::percentage($budgetprofit[$month],$actualprofit[$month])!=0) ? '('.(\App\Models\Budget::percentage($budgetprofit[$month],$actualprofit[$month]).'%)') :'':''); ?></p>
                                        </td>
                                        <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overbudgetprofit[$month])); ?></strong>
                                            <p class="<?php echo e(($budgetprofit[$month] < $overbudgetprofit[$month])? 'green-text':''); ?> <?php echo e(($budgetprofit[$month] > $overbudgetprofit[$month])? 'red-text':''); ?>"><?php echo e(($budgetprofit[$month] !=0)? (\App\Models\Budget::percentage($budgetprofit[$month],$overbudgetprofit[$month])!=0) ?'('.(\App\Models\Budget::percentage($budgetprofit[$month],$overbudgetprofit[$month]).'%)') :'':''); ?></p>
                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>

                            </tr>
                            </tfoot>
                        </table>

                        
                    <?php else: ?>
                        <table class="table table-bordered table-item data">
                            <thead>
                            <tr>
                                <td rowspan="2"></td> <!-- merge two rows -->
                                <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <th colspan="3" scope="colgroup" class="text-center br-1px"><?php echo e($month); ?></th>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>`
                            </tr>
                            <tr>
                                <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <th scope="col" class="br-1px">Budget</th>
                                    <th scope="col" class="br-1px">Actual</th>
                                    <th scope="col" class="br-1px">Over Budget</th>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>
                            </thead>

                            <!----INCOME Category ---------------------->

                            <tr>
                                <th colspan="37" class="text-dark light_blue"><span><?php echo e(__('Income :')); ?></span></th>
                            </tr>

                            <?php
                                $overBudgetTotal=[];
                            ?>

                            <?php $__currentLoopData = $incomeproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $showCategory = true;
                                    // Check if we need to filter categories based on display settings
                                    if (isset($budget['display_settings']['category_filter'])) {
                                        $selectedCategories = [];

                                        // Handle different formats of category_filter
                                        if (is_string($budget['display_settings']['category_filter'])) {
                                            // Try to decode JSON string
                                            $decodedCategories = json_decode($budget['display_settings']['category_filter'], true);
                                            if (is_array($decodedCategories)) {
                                                $selectedCategories = $decodedCategories;
                                            } else {
                                                // If not valid JSON, treat as single value
                                                $selectedCategories = [$budget['display_settings']['category_filter']];
                                            }
                                        } else if (is_array($budget['display_settings']['category_filter'])) {
                                            $selectedCategories = $budget['display_settings']['category_filter'];
                                        }

                                        // Check if this category should be shown
                                        if (!in_array('all', $selectedCategories) && !in_array('income-'.$productService->id, $selectedCategories)) {
                                            $showCategory = false;
                                        }
                                    }
                                ?>

                                <?php if($showCategory): ?>
                                <tr>
                                    <td class="text-dark"><?php echo e($productService->name); ?></td>
                                    <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            // Check if the data exists for this month
                                            if (isset($budget['income_data'][$productService->id][$month])) {
                                                $budgetAmount = $budget['income_data'][$productService->id][$month] ? $budget['income_data'][$productService->id][$month] : 0;
                                            } else {
                                                $budgetAmount = 0;
                                            }

                                            // Check if the actual amount exists for this month
                                            if (isset($incomeArr[$productService->id][$month])) {
                                                $actualAmount = $incomeArr[$productService->id][$month];
                                            } else {
                                                $actualAmount = 0;
                                            }

                                            $overBudgetAmount = $actualAmount - $budgetAmount;
                                            $overBudgetTotal[$productService->id][$month] = $overBudgetAmount;
                                        ?>

                                        <td class="income_data <?php echo e($month); ?>_income"><?php echo e(\Auth::user()->priceFormat($budgetAmount)); ?></td>
                                        <td><?php echo e(\Auth::user()->priceFormat($actualAmount)); ?>

                                            <p><?php echo e(($budgetAmount !=0 && $actualAmount != 0)? (\App\Models\Budget::percentage($budgetAmount,$actualAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetAmount,$actualAmount).'%)') :'':''); ?></p>
                                        </td>
                                        <td><?php echo e(\Auth::user()->priceFormat($overBudgetAmount)); ?>

                                            <p class="<?php echo e(($budgetAmount < $overBudgetAmount)? 'green-text':''); ?> <?php echo e(($budgetAmount > $overBudgetAmount)? 'red-text':''); ?>"><?php echo e(($budgetAmount !=0 && $overBudgetAmount != 0)? (\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount).'%)') :'':''); ?></p>
                                        </td>

                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                </tr>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $overBudgetTotalArr = array();
                                  foreach($overBudgetTotal as $overBudget)
                                  {
                                      foreach($overBudget as $k => $value)
                                      {
                                          $overBudgetTotalArr[$k] = (isset($overBudgetTotalArr[$k]) ? $overBudgetTotalArr[$k] + $value : $value);
                                      }
                                  }
                            ?>


                            <tr class="total text-dark">
                                <td class=""><span></span><strong><?php echo e(__('Total :')); ?></strong></td>
                                <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        // Ensure budget total exists
                                        $budgetTotalAmount = isset($budgetTotal[$month]) ? $budgetTotal[$month] : 0;
                                        // Ensure income total exists
                                        $incomeTotalAmount = isset($incomeTotalArr[$month]) ? $incomeTotalArr[$month] : 0;
                                        // Ensure over budget total exists
                                        $overBudgetTotalAmount = isset($overBudgetTotalArr[$month]) ? $overBudgetTotalArr[$month] : 0;
                                    ?>
                                    <td class="text-dark <?php echo e($month); ?>_total_income"><strong><?php echo e(\Auth::user()->priceFormat($budgetTotalAmount)); ?></strong></td>
                                    <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($incomeTotalAmount)); ?></strong>
                                        <p><?php echo e(($budgetTotalAmount !=0 && $incomeTotalAmount != 0)? (\App\Models\Budget::percentage($budgetTotalAmount,$incomeTotalAmount)!=0)?'('.(\App\Models\Budget::percentage($budgetTotalAmount,$incomeTotalAmount).'%)') :'':''); ?></p>
                                    </td>
                                    <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overBudgetTotalAmount)); ?></strong>
                                        <p class="<?php echo e(($budgetTotalAmount < $overBudgetTotalAmount)? 'green-text':''); ?> <?php echo e(($budgetTotalAmount > $overBudgetTotalAmount)? 'red-text':''); ?>"><?php echo e(($budgetTotalAmount !=0 && $overBudgetTotalAmount != 0)? (\App\Models\Budget::percentage($budgetTotalAmount,$overBudgetTotalAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetTotalAmount,$overBudgetTotalAmount).'%)') :'':''); ?></p>
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>


                            <!------------ EXPENSE Category ---------------------->

                            <tr>
                                <th colspan="37" class="text-dark light_blue"><span><?php echo e(__('Expense :')); ?></span></th>
                            </tr>
                            <?php
                                $overExpenseBudgetTotal=[];
                            ?>
                            <?php $__currentLoopData = $expenseproduct; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productService): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $showCategory = true;
                                    // Check if we need to filter categories based on display settings
                                    if (isset($budget['display_settings']['category_filter'])) {
                                        $selectedCategories = [];

                                        // Handle different formats of category_filter
                                        if (is_string($budget['display_settings']['category_filter'])) {
                                            // Try to decode JSON string
                                            $decodedCategories = json_decode($budget['display_settings']['category_filter'], true);
                                            if (is_array($decodedCategories)) {
                                                $selectedCategories = $decodedCategories;
                                            } else {
                                                // If not valid JSON, treat as single value
                                                $selectedCategories = [$budget['display_settings']['category_filter']];
                                            }
                                        } else if (is_array($budget['display_settings']['category_filter'])) {
                                            $selectedCategories = $budget['display_settings']['category_filter'];
                                        }

                                        // Check if this category should be shown
                                        if (!in_array('all', $selectedCategories) && !in_array('expense-'.$productService->id, $selectedCategories)) {
                                            $showCategory = false;
                                        }
                                    }
                                ?>

                                <?php if($showCategory): ?>
                                <tr>
                                    <td class="text-dark"><?php echo e($productService->name); ?></td>
                                    <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            // Check if the data exists for this month
                                            if (isset($budget['expense_data'][$productService->id][$month])) {
                                                $budgetAmount = $budget['expense_data'][$productService->id][$month] ? $budget['expense_data'][$productService->id][$month] : 0;
                                            } else {
                                                $budgetAmount = 0;
                                            }

                                            // Check if the actual amount exists for this month
                                            if (isset($expenseArr[$productService->id][$month])) {
                                                $actualAmount = $expenseArr[$productService->id][$month];
                                            } else {
                                                $actualAmount = 0;
                                            }

                                            $overBudgetAmount = $actualAmount - $budgetAmount;
                                            $overExpenseBudgetTotal[$productService->id][$month] = $overBudgetAmount;
                                        ?>
                                        <td class="expense_data <?php echo e($month); ?>_expense"><?php echo e(\Auth::user()->priceFormat($budgetAmount)); ?></td>
                                        <td><?php echo e(\Auth::user()->priceFormat($actualAmount)); ?>

                                            <p><?php echo e(($budgetAmount !=0 && $actualAmount != 0)? (\App\Models\Budget::percentage($budgetAmount,$actualAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetAmount,$actualAmount).'%)') :'':''); ?></p>
                                        </td>
                                        <td><?php echo e(\Auth::user()->priceFormat($overBudgetAmount)); ?>

                                            <p class="<?php echo e(($budgetAmount < $overBudgetAmount)? 'green-text':''); ?> <?php echo e(($budgetAmount > $overBudgetAmount)? 'red-text':''); ?>"><?php echo e(($budgetAmount !=0 && $overBudgetAmount != 0)? (\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetAmount,$overBudgetAmount).'%)') :'':''); ?></p>
                                        </td>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tr>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <?php
                                $overExpenseBudgetTotalArr = array();
                                  foreach($overExpenseBudgetTotal as $overExpenseBudget)
                                  {
                                      foreach($overExpenseBudget as $k => $value)
                                      {
                                          $overExpenseBudgetTotalArr[$k] = (isset($overExpenseBudgetTotalArr[$k]) ? $overExpenseBudgetTotalArr[$k] + $value : $value);
                                      }
                                  }
                            ?>

                            <tr class="total">
                                <td class="text-dark"><span></span><strong><?php echo e(__('Total :')); ?></strong></td>
                                <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        // Ensure budget expense total exists
                                        $budgetExpenseTotalAmount = isset($budgetExpenseTotal[$month]) ? $budgetExpenseTotal[$month] : 0;
                                        // Ensure expense total exists
                                        $expenseTotalAmount = isset($expenseTotalArr[$month]) ? $expenseTotalArr[$month] : 0;
                                        // Ensure over budget expense total exists
                                        $overExpenseBudgetTotalAmount = isset($overExpenseBudgetTotalArr[$month]) ? $overExpenseBudgetTotalArr[$month] : 0;
                                    ?>
                                    <td class="text-dark <?php echo e($month); ?>_total_expense"><strong><?php echo e(\Auth::user()->priceFormat($budgetExpenseTotalAmount)); ?></strong></td>
                                    <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($expenseTotalAmount)); ?></strong>
                                        <p><?php echo e(($budgetExpenseTotalAmount !=0 && $expenseTotalAmount != 0)? (\App\Models\Budget::percentage($budgetExpenseTotalAmount,$expenseTotalAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetExpenseTotalAmount,$expenseTotalAmount).'%)') :'':''); ?></p>
                                    </td>
                                    <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overExpenseBudgetTotalAmount)); ?></strong>
                                        <p class="<?php echo e(($budgetExpenseTotalAmount < $overExpenseBudgetTotalAmount)? 'green-text':''); ?> <?php echo e(($budgetExpenseTotalAmount > $overExpenseBudgetTotalAmount)? 'red-text':''); ?>"><?php echo e(($budgetExpenseTotalAmount !=0 && $overExpenseBudgetTotalAmount != 0)? (\App\Models\Budget::percentage($budgetExpenseTotalAmount,$overExpenseBudgetTotalAmount)!=0) ? '('.(\App\Models\Budget::percentage($budgetExpenseTotalAmount,$overExpenseBudgetTotalAmount).'%)') :'':''); ?></p>
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            </tr>
                            <td></td>
                            <tfoot>
                            <tr class="total">
                                <td class="text-dark"><span></span><strong><?php echo e(__('NET PROFIT :')); ?></strong></td>
                                <?php
                                    // NET PROFIT OF OVER BUDGET
                                     $overbudgetprofit = [];
                                     $keys   = array_keys($overBudgetTotalArr + $overExpenseBudgetTotalArr);
                                     foreach($keys as $v)
                                     {
                                         $overbudgetprofit[$v] = (empty($overBudgetTotalArr[$v]) ? 0 : $overBudgetTotalArr[$v]) - (empty($overExpenseBudgetTotalArr[$v]) ? 0 : $overExpenseBudgetTotalArr[$v]);
                                     }
                                     $data['overbudgetprofit'] = $overbudgetprofit;
                                ?>

                                <?php $__currentLoopData = $yearly_monthlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        // Ensure budget profit exists
                                        $budgetProfitAmount = isset($budgetprofit[$month]) ? $budgetprofit[$month] : 0;
                                        // Ensure actual profit exists
                                        $actualProfitAmount = isset($actualprofit[$month]) ? $actualprofit[$month] : 0;
                                        // Ensure over budget profit exists
                                        $overBudgetProfitAmount = isset($overbudgetprofit[$month]) ? $overbudgetprofit[$month] : 0;
                                    ?>
                                    <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($budgetProfitAmount)); ?></strong></td>
                                    <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($actualProfitAmount)); ?></strong>
                                        <p><?php echo e(($budgetProfitAmount !=0 && $actualProfitAmount != 0)? (\App\Models\Budget::percentage($budgetProfitAmount,$actualProfitAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetProfitAmount,$actualProfitAmount).'%)') :'':''); ?></p>
                                    </td>
                                    <td class="text-dark"><strong><?php echo e(\Auth::user()->priceFormat($overBudgetProfitAmount)); ?></strong>
                                        <p class="<?php echo e(($budgetProfitAmount < $overBudgetProfitAmount)? 'green-text':''); ?> <?php echo e(($budgetProfitAmount > $overBudgetProfitAmount)? 'red-text':''); ?>"><?php echo e(($budgetProfitAmount !=0 && $overBudgetProfitAmount != 0)? (\App\Models\Budget::percentage($budgetProfitAmount,$overBudgetProfitAmount)!=0) ?'('.(\App\Models\Budget::percentage($budgetProfitAmount,$overBudgetProfitAmount).'%)') :'':''); ?></p>
                                    </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            </tr>
                            </tfoot>

                        </table>

                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>





<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\erpq24\resources\views/budget/show.blade.php ENDPATH**/ ?>