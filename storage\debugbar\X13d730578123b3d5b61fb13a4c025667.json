{"__meta": {"id": "X13d730578123b3d5b61fb13a4c025667", "datetime": "2025-06-26 23:20:37", "utime": **********.357048, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980036.910354, "end": **********.357062, "duration": 0.44670820236206055, "duration_str": "447ms", "measures": [{"label": "Booting", "start": 1750980036.910354, "relative_start": 0, "end": **********.309254, "relative_end": **********.309254, "duration": 0.39890003204345703, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.309263, "relative_start": 0.3989090919494629, "end": **********.357064, "relative_end": 1.9073486328125e-06, "duration": 0.04780101776123047, "duration_str": "47.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029728, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00312, "accumulated_duration_str": "3.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3349729, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.231}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3449762, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.231, "width_percent": 14.423}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.35036, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.654, "width_percent": 16.346}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-735353088 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-735353088\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1902814465 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1902814465\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1600695494 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1600695494\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1232404978 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980034007%7C16%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlE1aTBaL1ptY1dJdU9QNVQ5eGRCZUE9PSIsInZhbHVlIjoicTFXOWJ3RVVEUGNxbWViclJZUHBNc2U1bzEvQ0lhd3F3RkFVSmxEbEtWbGFJNGY1U3lYTFFSMzI1OVQrWk5TQVJMSFRkWUNLQllldm8vU2FBMU5uaVJteVVLdVV6bUIzcVI0dXJZVGlLaE5BRjJSY204dkowR3lmbFEwUnJzMWQvK2JQb2EwZ0lERXpQbU5FYmkyWmZkZUxvdUtuSHVyRDRpK0pSbGd3K1FiSzNBTThSQU1Ea3ZxbXNFdFI0aEdCRUNlV2Y1eDRQVDNlRm8zU0NBcWpRdUVMdko5aURyVHViaWVOSkNWOVN4STJId2VZaDQrWDVwTTZWcEhaU3k4NlFMcE81a3liQnVoWnpRODh5ckYwYVFsbGpEUU1kZ0UreXQ3bzg5djVIM2JZQTEzdEx5SktTMXBkRE1FUGNkVjhZQ2Jnd3NzYWkybW9JYXlCZjlWaWdUNW00SytiR3V2eWJzSlRNVm96V1NqenlMSHBpSFN3OWZRWWxVR0RZYWtYR25rT1o0b2o0UEp1N21TTzhJSFZwVjBLZS9pTHFzRGxEWTdqRC9PYXRmd3N2WGhVNHhhNkhFNTRrNTFuVFNObTBnTHo0cmExSGUrK2xMVGVvMFZueXZuMUVQa1V6aG5BM1Zic2xBd1NGWnBOcVA2MnNBdVgyYThpWU1ERTE0ZjAiLCJtYWMiOiI3ZjYzZWYzMzMxZjNjMTVmMGRhOWY3OWYzMGFlMWI5MTEwNzZiOGNhYWFhMDgwYjQ2NGU4NTMzM2Q2MzA2NzRhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im96UTJiVEIwN1hXNEE1cUlPUDRsUlE9PSIsInZhbHVlIjoiRnI0U1hpb0dpWDlVYlJJK054cjV2UFc4bThRT3dmMHNwQkt5ZGNIZTRFMDhpU1FPWUZtQW9kL2h2clVHa0EwM1lJQ1YxMkZXaGowNTgzalBaVUNtb0dScEZLQ01ROHhZV1p3R2lha3BhZDN2d2xFK3JGZGFaRWpESGc2cTU0ajg2NHVxanZSMEVNdkZsdWNubDZDTldneDJpdWFDOTVROUR4a2doWEVHMHp4aW4rMWRRbHg2YjZ4dE1sb3RQRUtySnBwRGtBckpvZmZhMkV2VGdkcE9lODh6MjVYaTU2SWpTVkUyZTNJOS92bk1iMmduMWk3bHNRRzBBNWJva0dldDJlMVVFMXpRaTBFTDV6dVVZcFVKenBleEllc1N6Z1NqdStCb3krMGhTZTMyZHlRSFBSTWNzQXREVTJOV2Yzc1Y0enU5TFAvRTVmUE9rMnBhV2xBdG9rWnN6NVlmMEdIVUhqS2lBWm1Yb3lxaEk2SzY1UGtqMHd4cXAxcjlURkZ5cHBiUlhUYTNHekIxQ3hhcklsRXArcWdDMTBWSG9yMHVCRVVNRXZscnV0TTNsZHB5YjRkK3g3Wit2UzkxR1VVNzdvZERad3RJci96WlJWRVFsMmRFajJ1dEtEcU5iVi85UE1qMlgrT05SVzdrUGN2UnJuME5aM2lpMzBreGlnM24iLCJtYWMiOiIzZTk1ZDU5YjZjNzI4MTg0Njk5NDhmNzNiZDY3MDU1NzZjNmFmZjQ1ZjA0MTk2ZWM0MTUxNmI1YjMxZDdlMmVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232404978\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1262653300 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262653300\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndyRmsyU3hMWWo4dEo0aXRtQ2FKeGc9PSIsInZhbHVlIjoiL1lVQWxWY25UbUxoQlJnZHVUM1gwSG9yQmRGakl2S0x3V21rVGFOV2hUUUZaRDYwUHR2ZG14Z0pKT2kyWnNFVTc4N3ZjcEdDaGpJNWFlM3orRFh2MUE2d0hObXBKTm1nc2ZOa2FXZkRGWEp2SDhBdXRTMXlMWGoranhSNDV5Y1g4TENQTU9pamNwczJoYVgzQWozam9OTGtucUVCUFZJaGdIa2lDellid2RDdXAyS0lJYzRZNGVwTFZXZE9UenNNYXN5VW1vR0JMeUM5SHdnbUNMZW9ONDdiczF4UUJneXZXd3NXWStyYmwvaGFkVFpCZktFY0xDM0NoNVlIOTU4NGtOdDdMbFpWNitnSnF1aVpUVEVBWm5RS3ZMMkhwSjM4YmdKb1RDRVNpQWYwSzBYNHdBQnpwTlZzays5T2VxWDdHUGs0SjdxaGZXWHpDQVRDRFFoMUtXRndIa29hbythZUx3SDRiaVRVc1kyM3EyM3djWEVTNTJ6cVUyTWQrWDJrUFdzYWVpQkRYMi82OURqd2swWGRKaGJxS3VpekFEbnZpQ2h4NWIyek5yalVqS3I3SUJHVHRKODM4Wjk4UDd6cmpvSEFMUVI5d0pNV2Jldk9TcDhQK2pTelNsbS84b0ZPNHhyQys1ek8zSFp2WHhtbWNVM0g1RGdYS2dxYTdRMlMiLCJtYWMiOiIwYmM5Y2M4ZTY5NTcxZGY3YjZkZWEyODQxNzRhMDU1MTVkODM2ZTY0Yzc0MWJmMjhiNTUwYzFhZjEzNTJhMWQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVIN2dIMnVnRXF5M0RiSXZvU09XVXc9PSIsInZhbHVlIjoiT2g4b3pPYk5KZ2lrbCtKZWlXQjhYL3BZOTEwWFh2TWRWcUp4M1VKcGRnQklleWI2WTBSbkpFeHVjMWdEQTllZ1ZHbTg4QU1GZ1BpLy95QTRkbS9ieXp5MVA4YjlTQ3pVRVVNdGkrTkVNaUxPTmh6UlNHck42ZkN0blNvQ3FSVFJJVThMQXVBa3BhQUhMK1k3L291UTd3RU8ycmZJcFlHWWNWOG51SEF3WTNhTEVzczcrOU5sOENTUFQvajBHYXFScXhacFBaOURPYmdIYi8vR00yRmxaemN0WjUzY3pacDN3TEJqa3lGQnlqQmNHSnBsZWo5ck1YdkR2U1BRaUpPd1U3WnhyQmF3c2htamRTSW1kdWN6bUJiVTZsL0dWU2t6M3hnRzB1QVRWRWxNVmxsSVUwODBLYjUxaGJ0U0VkVUJjd1VWMGpzbVplM3VQcXJjV3NoejBkSUZLMWFWOXBoY3kxdFlQa3Q3QXJiTUlBTW83a3dZd2IrenMvQnpLWjBmMXBpd2Vkb3hBcHRjMEhTSnEyUFVPMkh5MnZUYkhxYm1udnRycHlPTEM5ZE93N0d5NXMzdTBWY2VFY2dhV1JvUFJJYkUzQ3FSczFXcnZlc1F3dmFiS3lTZVNLZ3dKRitTV1dnZHBUSUdETzJBVFBuT1gvMkVNUlVNUnJXOTdUN2oiLCJtYWMiOiI0OTk0Yzg3MzQzMjRjNzVjZWRkNTMwNzZjZGNmZjJmNjhhNWFlM2NmMTUxNTBjNDU3NDI2ODhiYTE1MGZkYzQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndyRmsyU3hMWWo4dEo0aXRtQ2FKeGc9PSIsInZhbHVlIjoiL1lVQWxWY25UbUxoQlJnZHVUM1gwSG9yQmRGakl2S0x3V21rVGFOV2hUUUZaRDYwUHR2ZG14Z0pKT2kyWnNFVTc4N3ZjcEdDaGpJNWFlM3orRFh2MUE2d0hObXBKTm1nc2ZOa2FXZkRGWEp2SDhBdXRTMXlMWGoranhSNDV5Y1g4TENQTU9pamNwczJoYVgzQWozam9OTGtucUVCUFZJaGdIa2lDellid2RDdXAyS0lJYzRZNGVwTFZXZE9UenNNYXN5VW1vR0JMeUM5SHdnbUNMZW9ONDdiczF4UUJneXZXd3NXWStyYmwvaGFkVFpCZktFY0xDM0NoNVlIOTU4NGtOdDdMbFpWNitnSnF1aVpUVEVBWm5RS3ZMMkhwSjM4YmdKb1RDRVNpQWYwSzBYNHdBQnpwTlZzays5T2VxWDdHUGs0SjdxaGZXWHpDQVRDRFFoMUtXRndIa29hbythZUx3SDRiaVRVc1kyM3EyM3djWEVTNTJ6cVUyTWQrWDJrUFdzYWVpQkRYMi82OURqd2swWGRKaGJxS3VpekFEbnZpQ2h4NWIyek5yalVqS3I3SUJHVHRKODM4Wjk4UDd6cmpvSEFMUVI5d0pNV2Jldk9TcDhQK2pTelNsbS84b0ZPNHhyQys1ek8zSFp2WHhtbWNVM0g1RGdYS2dxYTdRMlMiLCJtYWMiOiIwYmM5Y2M4ZTY5NTcxZGY3YjZkZWEyODQxNzRhMDU1MTVkODM2ZTY0Yzc0MWJmMjhiNTUwYzFhZjEzNTJhMWQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVIN2dIMnVnRXF5M0RiSXZvU09XVXc9PSIsInZhbHVlIjoiT2g4b3pPYk5KZ2lrbCtKZWlXQjhYL3BZOTEwWFh2TWRWcUp4M1VKcGRnQklleWI2WTBSbkpFeHVjMWdEQTllZ1ZHbTg4QU1GZ1BpLy95QTRkbS9ieXp5MVA4YjlTQ3pVRVVNdGkrTkVNaUxPTmh6UlNHck42ZkN0blNvQ3FSVFJJVThMQXVBa3BhQUhMK1k3L291UTd3RU8ycmZJcFlHWWNWOG51SEF3WTNhTEVzczcrOU5sOENTUFQvajBHYXFScXhacFBaOURPYmdIYi8vR00yRmxaemN0WjUzY3pacDN3TEJqa3lGQnlqQmNHSnBsZWo5ck1YdkR2U1BRaUpPd1U3WnhyQmF3c2htamRTSW1kdWN6bUJiVTZsL0dWU2t6M3hnRzB1QVRWRWxNVmxsSVUwODBLYjUxaGJ0U0VkVUJjd1VWMGpzbVplM3VQcXJjV3NoejBkSUZLMWFWOXBoY3kxdFlQa3Q3QXJiTUlBTW83a3dZd2IrenMvQnpLWjBmMXBpd2Vkb3hBcHRjMEhTSnEyUFVPMkh5MnZUYkhxYm1udnRycHlPTEM5ZE93N0d5NXMzdTBWY2VFY2dhV1JvUFJJYkUzQ3FSczFXcnZlc1F3dmFiS3lTZVNLZ3dKRitTV1dnZHBUSUdETzJBVFBuT1gvMkVNUlVNUnJXOTdUN2oiLCJtYWMiOiI0OTk0Yzg3MzQzMjRjNzVjZWRkNTMwNzZjZGNmZjJmNjhhNWFlM2NmMTUxNTBjNDU3NDI2ODhiYTE1MGZkYzQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-172345474 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-172345474\", {\"maxDepth\":0})</script>\n"}}