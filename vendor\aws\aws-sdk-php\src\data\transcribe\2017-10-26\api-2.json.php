<?php
// This file was auto-generated from sdk-root/src/data/transcribe/2017-10-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-10-26', 'endpointPrefix' => 'transcribe', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Amazon Transcribe Service', 'serviceId' => 'Transcribe', 'signatureVersion' => 'v4', 'signingName' => 'transcribe', 'targetPrefix' => 'Transcribe', 'uid' => 'transcribe-2017-10-26', ], 'operations' => [ 'CreateCallAnalyticsCategory' => [ 'name' => 'CreateCallAnalyticsCategory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCallAnalyticsCategoryRequest', ], 'output' => [ 'shape' => 'CreateCallAnalyticsCategoryResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateLanguageModel' => [ 'name' => 'CreateLanguageModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLanguageModelRequest', ], 'output' => [ 'shape' => 'CreateLanguageModelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateMedicalVocabulary' => [ 'name' => 'CreateMedicalVocabulary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMedicalVocabularyRequest', ], 'output' => [ 'shape' => 'CreateMedicalVocabularyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateVocabulary' => [ 'name' => 'CreateVocabulary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateVocabularyRequest', ], 'output' => [ 'shape' => 'CreateVocabularyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateVocabularyFilter' => [ 'name' => 'CreateVocabularyFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateVocabularyFilterRequest', ], 'output' => [ 'shape' => 'CreateVocabularyFilterResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteCallAnalyticsCategory' => [ 'name' => 'DeleteCallAnalyticsCategory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCallAnalyticsCategoryRequest', ], 'output' => [ 'shape' => 'DeleteCallAnalyticsCategoryResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteCallAnalyticsJob' => [ 'name' => 'DeleteCallAnalyticsJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCallAnalyticsJobRequest', ], 'output' => [ 'shape' => 'DeleteCallAnalyticsJobResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteLanguageModel' => [ 'name' => 'DeleteLanguageModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLanguageModelRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteMedicalScribeJob' => [ 'name' => 'DeleteMedicalScribeJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMedicalScribeJobRequest', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteMedicalTranscriptionJob' => [ 'name' => 'DeleteMedicalTranscriptionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMedicalTranscriptionJobRequest', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteMedicalVocabulary' => [ 'name' => 'DeleteMedicalVocabulary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMedicalVocabularyRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteTranscriptionJob' => [ 'name' => 'DeleteTranscriptionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTranscriptionJobRequest', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteVocabulary' => [ 'name' => 'DeleteVocabulary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVocabularyRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DeleteVocabularyFilter' => [ 'name' => 'DeleteVocabularyFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVocabularyFilterRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'DescribeLanguageModel' => [ 'name' => 'DescribeLanguageModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLanguageModelRequest', ], 'output' => [ 'shape' => 'DescribeLanguageModelResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetCallAnalyticsCategory' => [ 'name' => 'GetCallAnalyticsCategory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCallAnalyticsCategoryRequest', ], 'output' => [ 'shape' => 'GetCallAnalyticsCategoryResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetCallAnalyticsJob' => [ 'name' => 'GetCallAnalyticsJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCallAnalyticsJobRequest', ], 'output' => [ 'shape' => 'GetCallAnalyticsJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetMedicalScribeJob' => [ 'name' => 'GetMedicalScribeJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMedicalScribeJobRequest', ], 'output' => [ 'shape' => 'GetMedicalScribeJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetMedicalTranscriptionJob' => [ 'name' => 'GetMedicalTranscriptionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMedicalTranscriptionJobRequest', ], 'output' => [ 'shape' => 'GetMedicalTranscriptionJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetMedicalVocabulary' => [ 'name' => 'GetMedicalVocabulary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMedicalVocabularyRequest', ], 'output' => [ 'shape' => 'GetMedicalVocabularyResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetTranscriptionJob' => [ 'name' => 'GetTranscriptionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTranscriptionJobRequest', ], 'output' => [ 'shape' => 'GetTranscriptionJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetVocabulary' => [ 'name' => 'GetVocabulary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetVocabularyRequest', ], 'output' => [ 'shape' => 'GetVocabularyResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetVocabularyFilter' => [ 'name' => 'GetVocabularyFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetVocabularyFilterRequest', ], 'output' => [ 'shape' => 'GetVocabularyFilterResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListCallAnalyticsCategories' => [ 'name' => 'ListCallAnalyticsCategories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCallAnalyticsCategoriesRequest', ], 'output' => [ 'shape' => 'ListCallAnalyticsCategoriesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListCallAnalyticsJobs' => [ 'name' => 'ListCallAnalyticsJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCallAnalyticsJobsRequest', ], 'output' => [ 'shape' => 'ListCallAnalyticsJobsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListLanguageModels' => [ 'name' => 'ListLanguageModels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListLanguageModelsRequest', ], 'output' => [ 'shape' => 'ListLanguageModelsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListMedicalScribeJobs' => [ 'name' => 'ListMedicalScribeJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMedicalScribeJobsRequest', ], 'output' => [ 'shape' => 'ListMedicalScribeJobsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListMedicalTranscriptionJobs' => [ 'name' => 'ListMedicalTranscriptionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMedicalTranscriptionJobsRequest', ], 'output' => [ 'shape' => 'ListMedicalTranscriptionJobsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListMedicalVocabularies' => [ 'name' => 'ListMedicalVocabularies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMedicalVocabulariesRequest', ], 'output' => [ 'shape' => 'ListMedicalVocabulariesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListTranscriptionJobs' => [ 'name' => 'ListTranscriptionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTranscriptionJobsRequest', ], 'output' => [ 'shape' => 'ListTranscriptionJobsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListVocabularies' => [ 'name' => 'ListVocabularies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListVocabulariesRequest', ], 'output' => [ 'shape' => 'ListVocabulariesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'ListVocabularyFilters' => [ 'name' => 'ListVocabularyFilters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListVocabularyFiltersRequest', ], 'output' => [ 'shape' => 'ListVocabularyFiltersResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'StartCallAnalyticsJob' => [ 'name' => 'StartCallAnalyticsJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartCallAnalyticsJobRequest', ], 'output' => [ 'shape' => 'StartCallAnalyticsJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictException', ], ], ], 'StartMedicalScribeJob' => [ 'name' => 'StartMedicalScribeJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMedicalScribeJobRequest', ], 'output' => [ 'shape' => 'StartMedicalScribeJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictException', ], ], ], 'StartMedicalTranscriptionJob' => [ 'name' => 'StartMedicalTranscriptionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMedicalTranscriptionJobRequest', ], 'output' => [ 'shape' => 'StartMedicalTranscriptionJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictException', ], ], ], 'StartTranscriptionJob' => [ 'name' => 'StartTranscriptionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartTranscriptionJobRequest', ], 'output' => [ 'shape' => 'StartTranscriptionJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InternalFailureException', ], ], ], 'UpdateCallAnalyticsCategory' => [ 'name' => 'UpdateCallAnalyticsCategory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCallAnalyticsCategoryRequest', ], 'output' => [ 'shape' => 'UpdateCallAnalyticsCategoryResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateMedicalVocabulary' => [ 'name' => 'UpdateMedicalVocabulary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMedicalVocabularyRequest', ], 'output' => [ 'shape' => 'UpdateMedicalVocabularyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateVocabulary' => [ 'name' => 'UpdateVocabulary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateVocabularyRequest', ], 'output' => [ 'shape' => 'UpdateVocabularyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateVocabularyFilter' => [ 'name' => 'UpdateVocabularyFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateVocabularyFilterRequest', ], 'output' => [ 'shape' => 'UpdateVocabularyFilterResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'NotFoundException', ], ], ], ], 'shapes' => [ 'AbsoluteTimeRange' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'TimestampMilliseconds', ], 'EndTime' => [ 'shape' => 'TimestampMilliseconds', ], 'First' => [ 'shape' => 'TimestampMilliseconds', ], 'Last' => [ 'shape' => 'TimestampMilliseconds', ], ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'FailureReason', ], ], 'exception' => true, ], 'BaseModelName' => [ 'type' => 'string', 'enum' => [ 'NarrowBand', 'WideBand', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'CLMLanguageCode' => [ 'type' => 'string', 'enum' => [ 'en-US', 'hi-IN', 'es-US', 'en-GB', 'en-AU', 'de-DE', 'ja-JP', ], ], 'CallAnalyticsFeature' => [ 'type' => 'string', 'enum' => [ 'GENERATIVE_SUMMARIZATION', ], ], 'CallAnalyticsJob' => [ 'type' => 'structure', 'members' => [ 'CallAnalyticsJobName' => [ 'shape' => 'CallAnalyticsJobName', ], 'CallAnalyticsJobStatus' => [ 'shape' => 'CallAnalyticsJobStatus', ], 'CallAnalyticsJobDetails' => [ 'shape' => 'CallAnalyticsJobDetails', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'MediaSampleRateHertz' => [ 'shape' => 'MediaSampleRateHertz', ], 'MediaFormat' => [ 'shape' => 'MediaFormat', ], 'Media' => [ 'shape' => 'Media', ], 'Transcript' => [ 'shape' => 'Transcript', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'CreationTime' => [ 'shape' => 'DateTime', ], 'CompletionTime' => [ 'shape' => 'DateTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'DataAccessRoleArn' => [ 'shape' => 'DataAccessRoleArn', ], 'IdentifiedLanguageScore' => [ 'shape' => 'IdentifiedLanguageScore', ], 'Settings' => [ 'shape' => 'CallAnalyticsJobSettings', ], 'ChannelDefinitions' => [ 'shape' => 'ChannelDefinitions', ], ], ], 'CallAnalyticsJobDetails' => [ 'type' => 'structure', 'members' => [ 'Skipped' => [ 'shape' => 'CallAnalyticsSkippedFeatureList', ], ], ], 'CallAnalyticsJobName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'CallAnalyticsJobSettings' => [ 'type' => 'structure', 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'VocabularyFilterMethod' => [ 'shape' => 'VocabularyFilterMethod', ], 'LanguageModelName' => [ 'shape' => 'ModelName', ], 'ContentRedaction' => [ 'shape' => 'ContentRedaction', ], 'LanguageOptions' => [ 'shape' => 'LanguageOptions', ], 'LanguageIdSettings' => [ 'shape' => 'LanguageIdSettingsMap', ], 'Summarization' => [ 'shape' => 'Summarization', ], ], ], 'CallAnalyticsJobStatus' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'IN_PROGRESS', 'FAILED', 'COMPLETED', ], ], 'CallAnalyticsJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'CallAnalyticsJobSummary', ], ], 'CallAnalyticsJobSummary' => [ 'type' => 'structure', 'members' => [ 'CallAnalyticsJobName' => [ 'shape' => 'CallAnalyticsJobName', ], 'CreationTime' => [ 'shape' => 'DateTime', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'CompletionTime' => [ 'shape' => 'DateTime', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'CallAnalyticsJobStatus' => [ 'shape' => 'CallAnalyticsJobStatus', ], 'CallAnalyticsJobDetails' => [ 'shape' => 'CallAnalyticsJobDetails', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'CallAnalyticsSkippedFeature' => [ 'type' => 'structure', 'members' => [ 'Feature' => [ 'shape' => 'CallAnalyticsFeature', ], 'ReasonCode' => [ 'shape' => 'CallAnalyticsSkippedReasonCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'CallAnalyticsSkippedFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CallAnalyticsSkippedFeature', ], ], 'CallAnalyticsSkippedReasonCode' => [ 'type' => 'string', 'enum' => [ 'INSUFFICIENT_CONVERSATION_CONTENT', 'FAILED_SAFETY_GUIDELINES', ], ], 'CategoryName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'CategoryProperties' => [ 'type' => 'structure', 'members' => [ 'CategoryName' => [ 'shape' => 'CategoryName', ], 'Rules' => [ 'shape' => 'RuleList', ], 'CreateTime' => [ 'shape' => 'DateTime', ], 'LastUpdateTime' => [ 'shape' => 'DateTime', ], 'InputType' => [ 'shape' => 'InputType', ], ], ], 'CategoryPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CategoryProperties', ], ], 'ChannelDefinition' => [ 'type' => 'structure', 'members' => [ 'ChannelId' => [ 'shape' => 'ChannelId', ], 'ParticipantRole' => [ 'shape' => 'ParticipantRole', ], ], ], 'ChannelDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelDefinition', ], 'max' => 2, 'min' => 2, ], 'ChannelId' => [ 'type' => 'integer', 'max' => 1, 'min' => 0, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ContentRedaction' => [ 'type' => 'structure', 'required' => [ 'RedactionType', 'RedactionOutput', ], 'members' => [ 'RedactionType' => [ 'shape' => 'RedactionType', ], 'RedactionOutput' => [ 'shape' => 'RedactionOutput', ], 'PiiEntityTypes' => [ 'shape' => 'PiiEntityTypes', ], ], ], 'CreateCallAnalyticsCategoryRequest' => [ 'type' => 'structure', 'required' => [ 'CategoryName', 'Rules', ], 'members' => [ 'CategoryName' => [ 'shape' => 'CategoryName', ], 'Rules' => [ 'shape' => 'RuleList', ], 'InputType' => [ 'shape' => 'InputType', ], ], ], 'CreateCallAnalyticsCategoryResponse' => [ 'type' => 'structure', 'members' => [ 'CategoryProperties' => [ 'shape' => 'CategoryProperties', ], ], ], 'CreateLanguageModelRequest' => [ 'type' => 'structure', 'required' => [ 'LanguageCode', 'BaseModelName', 'ModelName', 'InputDataConfig', ], 'members' => [ 'LanguageCode' => [ 'shape' => 'CLMLanguageCode', ], 'BaseModelName' => [ 'shape' => 'BaseModelName', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateLanguageModelResponse' => [ 'type' => 'structure', 'members' => [ 'LanguageCode' => [ 'shape' => 'CLMLanguageCode', ], 'BaseModelName' => [ 'shape' => 'BaseModelName', ], 'ModelName' => [ 'shape' => 'ModelName', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'ModelStatus' => [ 'shape' => 'ModelStatus', ], ], ], 'CreateMedicalVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyName', 'LanguageCode', 'VocabularyFileUri', ], 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'VocabularyFileUri' => [ 'shape' => 'Uri', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMedicalVocabularyResponse' => [ 'type' => 'structure', 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'VocabularyState' => [ 'shape' => 'VocabularyState', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'CreateVocabularyFilterRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyFilterName', 'LanguageCode', ], 'members' => [ 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'Words' => [ 'shape' => 'Words', ], 'VocabularyFilterFileUri' => [ 'shape' => 'Uri', ], 'Tags' => [ 'shape' => 'TagList', ], 'DataAccessRoleArn' => [ 'shape' => 'DataAccessRoleArn', ], ], ], 'CreateVocabularyFilterResponse' => [ 'type' => 'structure', 'members' => [ 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], ], ], 'CreateVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyName', 'LanguageCode', ], 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'Phrases' => [ 'shape' => 'Phrases', ], 'VocabularyFileUri' => [ 'shape' => 'Uri', ], 'Tags' => [ 'shape' => 'TagList', ], 'DataAccessRoleArn' => [ 'shape' => 'DataAccessRoleArn', ], ], ], 'CreateVocabularyResponse' => [ 'type' => 'structure', 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'VocabularyState' => [ 'shape' => 'VocabularyState', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'DataAccessRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:(aws|aws-cn|aws-us-gov|aws-iso-{0,1}[a-z]{0,1}):iam::[0-9]{0,63}:role/[A-Za-z0-9:_/+=,@.-]{0,1024}$', ], 'DateTime' => [ 'type' => 'timestamp', ], 'DeleteCallAnalyticsCategoryRequest' => [ 'type' => 'structure', 'required' => [ 'CategoryName', ], 'members' => [ 'CategoryName' => [ 'shape' => 'CategoryName', ], ], ], 'DeleteCallAnalyticsCategoryResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCallAnalyticsJobRequest' => [ 'type' => 'structure', 'required' => [ 'CallAnalyticsJobName', ], 'members' => [ 'CallAnalyticsJobName' => [ 'shape' => 'CallAnalyticsJobName', ], ], ], 'DeleteCallAnalyticsJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLanguageModelRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], ], ], 'DeleteMedicalScribeJobRequest' => [ 'type' => 'structure', 'required' => [ 'MedicalScribeJobName', ], 'members' => [ 'MedicalScribeJobName' => [ 'shape' => 'TranscriptionJobName', ], ], ], 'DeleteMedicalTranscriptionJobRequest' => [ 'type' => 'structure', 'required' => [ 'MedicalTranscriptionJobName', ], 'members' => [ 'MedicalTranscriptionJobName' => [ 'shape' => 'TranscriptionJobName', ], ], ], 'DeleteMedicalVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyName', ], 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], ], ], 'DeleteTranscriptionJobRequest' => [ 'type' => 'structure', 'required' => [ 'TranscriptionJobName', ], 'members' => [ 'TranscriptionJobName' => [ 'shape' => 'TranscriptionJobName', ], ], ], 'DeleteVocabularyFilterRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyFilterName', ], 'members' => [ 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], ], ], 'DeleteVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyName', ], 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], ], ], 'DescribeLanguageModelRequest' => [ 'type' => 'structure', 'required' => [ 'ModelName', ], 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], ], ], 'DescribeLanguageModelResponse' => [ 'type' => 'structure', 'members' => [ 'LanguageModel' => [ 'shape' => 'LanguageModel', ], ], ], 'DurationInSeconds' => [ 'type' => 'float', ], 'FailureReason' => [ 'type' => 'string', ], 'GetCallAnalyticsCategoryRequest' => [ 'type' => 'structure', 'required' => [ 'CategoryName', ], 'members' => [ 'CategoryName' => [ 'shape' => 'CategoryName', ], ], ], 'GetCallAnalyticsCategoryResponse' => [ 'type' => 'structure', 'members' => [ 'CategoryProperties' => [ 'shape' => 'CategoryProperties', ], ], ], 'GetCallAnalyticsJobRequest' => [ 'type' => 'structure', 'required' => [ 'CallAnalyticsJobName', ], 'members' => [ 'CallAnalyticsJobName' => [ 'shape' => 'CallAnalyticsJobName', ], ], ], 'GetCallAnalyticsJobResponse' => [ 'type' => 'structure', 'members' => [ 'CallAnalyticsJob' => [ 'shape' => 'CallAnalyticsJob', ], ], ], 'GetMedicalScribeJobRequest' => [ 'type' => 'structure', 'required' => [ 'MedicalScribeJobName', ], 'members' => [ 'MedicalScribeJobName' => [ 'shape' => 'TranscriptionJobName', ], ], ], 'GetMedicalScribeJobResponse' => [ 'type' => 'structure', 'members' => [ 'MedicalScribeJob' => [ 'shape' => 'MedicalScribeJob', ], ], ], 'GetMedicalTranscriptionJobRequest' => [ 'type' => 'structure', 'required' => [ 'MedicalTranscriptionJobName', ], 'members' => [ 'MedicalTranscriptionJobName' => [ 'shape' => 'TranscriptionJobName', ], ], ], 'GetMedicalTranscriptionJobResponse' => [ 'type' => 'structure', 'members' => [ 'MedicalTranscriptionJob' => [ 'shape' => 'MedicalTranscriptionJob', ], ], ], 'GetMedicalVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyName', ], 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], ], ], 'GetMedicalVocabularyResponse' => [ 'type' => 'structure', 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'VocabularyState' => [ 'shape' => 'VocabularyState', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'DownloadUri' => [ 'shape' => 'Uri', ], ], ], 'GetTranscriptionJobRequest' => [ 'type' => 'structure', 'required' => [ 'TranscriptionJobName', ], 'members' => [ 'TranscriptionJobName' => [ 'shape' => 'TranscriptionJobName', ], ], ], 'GetTranscriptionJobResponse' => [ 'type' => 'structure', 'members' => [ 'TranscriptionJob' => [ 'shape' => 'TranscriptionJob', ], ], ], 'GetVocabularyFilterRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyFilterName', ], 'members' => [ 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], ], ], 'GetVocabularyFilterResponse' => [ 'type' => 'structure', 'members' => [ 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], 'DownloadUri' => [ 'shape' => 'Uri', ], ], ], 'GetVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyName', ], 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], ], ], 'GetVocabularyResponse' => [ 'type' => 'structure', 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'VocabularyState' => [ 'shape' => 'VocabularyState', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'DownloadUri' => [ 'shape' => 'Uri', ], ], ], 'IdentifiedLanguageScore' => [ 'type' => 'float', ], 'InputDataConfig' => [ 'type' => 'structure', 'required' => [ 'S3Uri', 'DataAccessRoleArn', ], 'members' => [ 'S3Uri' => [ 'shape' => 'Uri', ], 'TuningDataS3Uri' => [ 'shape' => 'Uri', ], 'DataAccessRoleArn' => [ 'shape' => 'DataAccessRoleArn', ], ], ], 'InputType' => [ 'type' => 'string', 'enum' => [ 'REAL_TIME', 'POST_CALL', ], ], 'InternalFailureException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'InterruptionFilter' => [ 'type' => 'structure', 'members' => [ 'Threshold' => [ 'shape' => 'TimestampMilliseconds', ], 'ParticipantRole' => [ 'shape' => 'ParticipantRole', ], 'AbsoluteTimeRange' => [ 'shape' => 'AbsoluteTimeRange', ], 'RelativeTimeRange' => [ 'shape' => 'RelativeTimeRange', ], 'Negate' => [ 'shape' => 'Boolean', ], ], ], 'JobExecutionSettings' => [ 'type' => 'structure', 'members' => [ 'AllowDeferredExecution' => [ 'shape' => 'Boolean', ], 'DataAccessRoleArn' => [ 'shape' => 'DataAccessRoleArn', ], ], ], 'KMSEncryptionContextMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], 'max' => 10, 'min' => 1, ], 'KMSKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,2048}$', ], 'LanguageCode' => [ 'type' => 'string', 'enum' => [ 'af-ZA', 'ar-AE', 'ar-SA', 'da-DK', 'de-CH', 'de-DE', 'en-AB', 'en-AU', 'en-GB', 'en-IE', 'en-IN', 'en-US', 'en-WL', 'es-ES', 'es-US', 'fa-IR', 'fr-CA', 'fr-FR', 'he-IL', 'hi-IN', 'id-ID', 'it-IT', 'ja-JP', 'ko-KR', 'ms-MY', 'nl-NL', 'pt-BR', 'pt-PT', 'ru-RU', 'ta-IN', 'te-IN', 'tr-TR', 'zh-CN', 'zh-TW', 'th-TH', 'en-ZA', 'en-NZ', 'vi-VN', 'sv-SE', 'ab-GE', 'ast-ES', 'az-AZ', 'ba-RU', 'be-BY', 'bg-BG', 'bn-IN', 'bs-BA', 'ca-ES', 'ckb-IQ', 'ckb-IR', 'cs-CZ', 'cy-WL', 'el-GR', 'et-ET', 'eu-ES', 'fi-FI', 'gl-ES', 'gu-IN', 'ha-NG', 'hr-HR', 'hu-HU', 'hy-AM', 'is-IS', 'ka-GE', 'kab-DZ', 'kk-KZ', 'kn-IN', 'ky-KG', 'lg-IN', 'lt-LT', 'lv-LV', 'mhr-RU', 'mi-NZ', 'mk-MK', 'ml-IN', 'mn-MN', 'mr-IN', 'mt-MT', 'no-NO', 'or-IN', 'pa-IN', 'pl-PL', 'ps-AF', 'ro-RO', 'rw-RW', 'si-LK', 'sk-SK', 'sl-SI', 'so-SO', 'sr-RS', 'su-ID', 'sw-BI', 'sw-KE', 'sw-RW', 'sw-TZ', 'sw-UG', 'tl-PH', 'tt-RU', 'ug-CN', 'uk-UA', 'uz-UZ', 'wo-SN', 'zu-ZA', ], ], 'LanguageCodeItem' => [ 'type' => 'structure', 'members' => [ 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'DurationInSeconds' => [ 'shape' => 'DurationInSeconds', ], ], ], 'LanguageCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LanguageCodeItem', ], ], 'LanguageIdSettings' => [ 'type' => 'structure', 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'LanguageModelName' => [ 'shape' => 'ModelName', ], ], ], 'LanguageIdSettingsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'LanguageCode', ], 'value' => [ 'shape' => 'LanguageIdSettings', ], 'max' => 5, 'min' => 1, ], 'LanguageModel' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'CreateTime' => [ 'shape' => 'DateTime', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], 'LanguageCode' => [ 'shape' => 'CLMLanguageCode', ], 'BaseModelName' => [ 'shape' => 'BaseModelName', ], 'ModelStatus' => [ 'shape' => 'ModelStatus', ], 'UpgradeAvailability' => [ 'shape' => 'Boolean', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], ], ], 'LanguageOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'LanguageCode', ], 'min' => 1, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ListCallAnalyticsCategoriesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListCallAnalyticsCategoriesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Categories' => [ 'shape' => 'CategoryPropertiesList', ], ], ], 'ListCallAnalyticsJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'CallAnalyticsJobStatus', ], 'JobNameContains' => [ 'shape' => 'CallAnalyticsJobName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListCallAnalyticsJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'CallAnalyticsJobStatus', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'CallAnalyticsJobSummaries' => [ 'shape' => 'CallAnalyticsJobSummaries', ], ], ], 'ListLanguageModelsRequest' => [ 'type' => 'structure', 'members' => [ 'StatusEquals' => [ 'shape' => 'ModelStatus', ], 'NameContains' => [ 'shape' => 'ModelName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListLanguageModelsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Models' => [ 'shape' => 'Models', ], ], ], 'ListMedicalScribeJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'MedicalScribeJobStatus', ], 'JobNameContains' => [ 'shape' => 'TranscriptionJobName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListMedicalScribeJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'MedicalScribeJobStatus', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MedicalScribeJobSummaries' => [ 'shape' => 'MedicalScribeJobSummaries', ], ], ], 'ListMedicalTranscriptionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'TranscriptionJobStatus', ], 'JobNameContains' => [ 'shape' => 'TranscriptionJobName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListMedicalTranscriptionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'TranscriptionJobStatus', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MedicalTranscriptionJobSummaries' => [ 'shape' => 'MedicalTranscriptionJobSummaries', ], ], ], 'ListMedicalVocabulariesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'StateEquals' => [ 'shape' => 'VocabularyState', ], 'NameContains' => [ 'shape' => 'VocabularyName', ], ], ], 'ListMedicalVocabulariesResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'VocabularyState', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Vocabularies' => [ 'shape' => 'Vocabularies', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TranscribeArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'TranscribeArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTranscriptionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'TranscriptionJobStatus', ], 'JobNameContains' => [ 'shape' => 'TranscriptionJobName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListTranscriptionJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'TranscriptionJobStatus', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TranscriptionJobSummaries' => [ 'shape' => 'TranscriptionJobSummaries', ], ], ], 'ListVocabulariesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'StateEquals' => [ 'shape' => 'VocabularyState', ], 'NameContains' => [ 'shape' => 'VocabularyName', ], ], ], 'ListVocabulariesResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'VocabularyState', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Vocabularies' => [ 'shape' => 'Vocabularies', ], ], ], 'ListVocabularyFiltersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NameContains' => [ 'shape' => 'VocabularyFilterName', ], ], ], 'ListVocabularyFiltersResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'VocabularyFilters' => [ 'shape' => 'VocabularyFilters', ], ], ], 'MaxAlternatives' => [ 'type' => 'integer', 'max' => 10, 'min' => 2, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxSpeakers' => [ 'type' => 'integer', 'max' => 30, 'min' => 2, ], 'Media' => [ 'type' => 'structure', 'members' => [ 'MediaFileUri' => [ 'shape' => 'Uri', ], 'RedactedMediaFileUri' => [ 'shape' => 'Uri', ], ], ], 'MediaFormat' => [ 'type' => 'string', 'enum' => [ 'mp3', 'mp4', 'wav', 'flac', 'ogg', 'amr', 'webm', 'm4a', ], ], 'MediaSampleRateHertz' => [ 'type' => 'integer', 'max' => 48000, 'min' => 8000, ], 'MedicalContentIdentificationType' => [ 'type' => 'string', 'enum' => [ 'PHI', ], ], 'MedicalMediaSampleRateHertz' => [ 'type' => 'integer', 'max' => 48000, 'min' => 16000, ], 'MedicalScribeChannelDefinition' => [ 'type' => 'structure', 'required' => [ 'ChannelId', 'ParticipantRole', ], 'members' => [ 'ChannelId' => [ 'shape' => 'MedicalScribeChannelId', ], 'ParticipantRole' => [ 'shape' => 'MedicalScribeParticipantRole', ], ], ], 'MedicalScribeChannelDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'MedicalScribeChannelDefinition', ], 'max' => 2, 'min' => 2, ], 'MedicalScribeChannelId' => [ 'type' => 'integer', 'max' => 1, 'min' => 0, ], 'MedicalScribeJob' => [ 'type' => 'structure', 'members' => [ 'MedicalScribeJobName' => [ 'shape' => 'TranscriptionJobName', ], 'MedicalScribeJobStatus' => [ 'shape' => 'MedicalScribeJobStatus', ], 'LanguageCode' => [ 'shape' => 'MedicalScribeLanguageCode', ], 'Media' => [ 'shape' => 'Media', ], 'MedicalScribeOutput' => [ 'shape' => 'MedicalScribeOutput', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'CreationTime' => [ 'shape' => 'DateTime', ], 'CompletionTime' => [ 'shape' => 'DateTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'Settings' => [ 'shape' => 'MedicalScribeSettings', ], 'DataAccessRoleArn' => [ 'shape' => 'DataAccessRoleArn', ], 'ChannelDefinitions' => [ 'shape' => 'MedicalScribeChannelDefinitions', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'MedicalScribeJobStatus' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'IN_PROGRESS', 'FAILED', 'COMPLETED', ], ], 'MedicalScribeJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'MedicalScribeJobSummary', ], ], 'MedicalScribeJobSummary' => [ 'type' => 'structure', 'members' => [ 'MedicalScribeJobName' => [ 'shape' => 'TranscriptionJobName', ], 'CreationTime' => [ 'shape' => 'DateTime', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'CompletionTime' => [ 'shape' => 'DateTime', ], 'LanguageCode' => [ 'shape' => 'MedicalScribeLanguageCode', ], 'MedicalScribeJobStatus' => [ 'shape' => 'MedicalScribeJobStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], ], ], 'MedicalScribeLanguageCode' => [ 'type' => 'string', 'enum' => [ 'en-US', ], ], 'MedicalScribeOutput' => [ 'type' => 'structure', 'required' => [ 'TranscriptFileUri', 'ClinicalDocumentUri', ], 'members' => [ 'TranscriptFileUri' => [ 'shape' => 'Uri', ], 'ClinicalDocumentUri' => [ 'shape' => 'Uri', ], ], ], 'MedicalScribeParticipantRole' => [ 'type' => 'string', 'enum' => [ 'PATIENT', 'CLINICIAN', ], ], 'MedicalScribeSettings' => [ 'type' => 'structure', 'members' => [ 'ShowSpeakerLabels' => [ 'shape' => 'Boolean', ], 'MaxSpeakerLabels' => [ 'shape' => 'MaxSpeakers', ], 'ChannelIdentification' => [ 'shape' => 'Boolean', ], 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'VocabularyFilterMethod' => [ 'shape' => 'VocabularyFilterMethod', ], ], ], 'MedicalTranscript' => [ 'type' => 'structure', 'members' => [ 'TranscriptFileUri' => [ 'shape' => 'Uri', ], ], ], 'MedicalTranscriptionJob' => [ 'type' => 'structure', 'members' => [ 'MedicalTranscriptionJobName' => [ 'shape' => 'TranscriptionJobName', ], 'TranscriptionJobStatus' => [ 'shape' => 'TranscriptionJobStatus', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'MediaSampleRateHertz' => [ 'shape' => 'MedicalMediaSampleRateHertz', ], 'MediaFormat' => [ 'shape' => 'MediaFormat', ], 'Media' => [ 'shape' => 'Media', ], 'Transcript' => [ 'shape' => 'MedicalTranscript', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'CreationTime' => [ 'shape' => 'DateTime', ], 'CompletionTime' => [ 'shape' => 'DateTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'Settings' => [ 'shape' => 'MedicalTranscriptionSetting', ], 'ContentIdentificationType' => [ 'shape' => 'MedicalContentIdentificationType', ], 'Specialty' => [ 'shape' => 'Specialty', ], 'Type' => [ 'shape' => 'Type', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'MedicalTranscriptionJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'MedicalTranscriptionJobSummary', ], ], 'MedicalTranscriptionJobSummary' => [ 'type' => 'structure', 'members' => [ 'MedicalTranscriptionJobName' => [ 'shape' => 'TranscriptionJobName', ], 'CreationTime' => [ 'shape' => 'DateTime', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'CompletionTime' => [ 'shape' => 'DateTime', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'TranscriptionJobStatus' => [ 'shape' => 'TranscriptionJobStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'OutputLocationType' => [ 'shape' => 'OutputLocationType', ], 'Specialty' => [ 'shape' => 'Specialty', ], 'ContentIdentificationType' => [ 'shape' => 'MedicalContentIdentificationType', ], 'Type' => [ 'shape' => 'Type', ], ], ], 'MedicalTranscriptionSetting' => [ 'type' => 'structure', 'members' => [ 'ShowSpeakerLabels' => [ 'shape' => 'Boolean', ], 'MaxSpeakerLabels' => [ 'shape' => 'MaxSpeakers', ], 'ChannelIdentification' => [ 'shape' => 'Boolean', ], 'ShowAlternatives' => [ 'shape' => 'Boolean', ], 'MaxAlternatives' => [ 'shape' => 'MaxAlternatives', ], 'VocabularyName' => [ 'shape' => 'VocabularyName', ], ], ], 'ModelName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'ModelSettings' => [ 'type' => 'structure', 'members' => [ 'LanguageModelName' => [ 'shape' => 'ModelName', ], ], ], 'ModelStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'FAILED', 'COMPLETED', ], ], 'Models' => [ 'type' => 'list', 'member' => [ 'shape' => 'LanguageModel', ], ], 'NextToken' => [ 'type' => 'string', 'max' => 8192, 'pattern' => '.+', ], 'NonEmptyString' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'NonTalkTimeFilter' => [ 'type' => 'structure', 'members' => [ 'Threshold' => [ 'shape' => 'TimestampMilliseconds', ], 'AbsoluteTimeRange' => [ 'shape' => 'AbsoluteTimeRange', ], 'RelativeTimeRange' => [ 'shape' => 'RelativeTimeRange', ], 'Negate' => [ 'shape' => 'Boolean', ], ], ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'OutputBucketName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]', ], 'OutputKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_.!*\'()/]{1,1024}$', ], 'OutputLocationType' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER_BUCKET', 'SERVICE_BUCKET', ], ], 'ParticipantRole' => [ 'type' => 'string', 'enum' => [ 'AGENT', 'CUSTOMER', ], ], 'Percentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'Phrase' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.+', ], 'Phrases' => [ 'type' => 'list', 'member' => [ 'shape' => 'Phrase', ], ], 'PiiEntityType' => [ 'type' => 'string', 'enum' => [ 'BANK_ACCOUNT_NUMBER', 'BANK_ROUTING', 'CREDIT_DEBIT_NUMBER', 'CREDIT_DEBIT_CVV', 'CREDIT_DEBIT_EXPIRY', 'PIN', 'EMAIL', 'ADDRESS', 'NAME', 'PHONE', 'SSN', 'ALL', ], ], 'PiiEntityTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'PiiEntityType', ], 'max' => 11, 'min' => 0, ], 'RedactionOutput' => [ 'type' => 'string', 'enum' => [ 'redacted', 'redacted_and_unredacted', ], ], 'RedactionType' => [ 'type' => 'string', 'enum' => [ 'PII', ], ], 'RelativeTimeRange' => [ 'type' => 'structure', 'members' => [ 'StartPercentage' => [ 'shape' => 'Percentage', ], 'EndPercentage' => [ 'shape' => 'Percentage', ], 'First' => [ 'shape' => 'Percentage', ], 'Last' => [ 'shape' => 'Percentage', ], ], ], 'Rule' => [ 'type' => 'structure', 'members' => [ 'NonTalkTimeFilter' => [ 'shape' => 'NonTalkTimeFilter', ], 'InterruptionFilter' => [ 'shape' => 'InterruptionFilter', ], 'TranscriptFilter' => [ 'shape' => 'TranscriptFilter', ], 'SentimentFilter' => [ 'shape' => 'SentimentFilter', ], ], 'union' => true, ], 'RuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'max' => 20, 'min' => 1, ], 'SentimentFilter' => [ 'type' => 'structure', 'required' => [ 'Sentiments', ], 'members' => [ 'Sentiments' => [ 'shape' => 'SentimentValueList', ], 'AbsoluteTimeRange' => [ 'shape' => 'AbsoluteTimeRange', ], 'RelativeTimeRange' => [ 'shape' => 'RelativeTimeRange', ], 'ParticipantRole' => [ 'shape' => 'ParticipantRole', ], 'Negate' => [ 'shape' => 'Boolean', ], ], ], 'SentimentValue' => [ 'type' => 'string', 'enum' => [ 'POSITIVE', 'NEGATIVE', 'NEUTRAL', 'MIXED', ], ], 'SentimentValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SentimentValue', ], 'max' => 1, 'min' => 1, ], 'Settings' => [ 'type' => 'structure', 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'ShowSpeakerLabels' => [ 'shape' => 'Boolean', ], 'MaxSpeakerLabels' => [ 'shape' => 'MaxSpeakers', ], 'ChannelIdentification' => [ 'shape' => 'Boolean', ], 'ShowAlternatives' => [ 'shape' => 'Boolean', ], 'MaxAlternatives' => [ 'shape' => 'MaxAlternatives', ], 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'VocabularyFilterMethod' => [ 'shape' => 'VocabularyFilterMethod', ], ], ], 'Specialty' => [ 'type' => 'string', 'enum' => [ 'PRIMARYCARE', ], ], 'StartCallAnalyticsJobRequest' => [ 'type' => 'structure', 'required' => [ 'CallAnalyticsJobName', 'Media', ], 'members' => [ 'CallAnalyticsJobName' => [ 'shape' => 'CallAnalyticsJobName', ], 'Media' => [ 'shape' => 'Media', ], 'OutputLocation' => [ 'shape' => 'Uri', ], 'OutputEncryptionKMSKeyId' => [ 'shape' => 'KMSKeyId', ], 'DataAccessRoleArn' => [ 'shape' => 'DataAccessRoleArn', ], 'Settings' => [ 'shape' => 'CallAnalyticsJobSettings', ], 'ChannelDefinitions' => [ 'shape' => 'ChannelDefinitions', ], ], ], 'StartCallAnalyticsJobResponse' => [ 'type' => 'structure', 'members' => [ 'CallAnalyticsJob' => [ 'shape' => 'CallAnalyticsJob', ], ], ], 'StartMedicalScribeJobRequest' => [ 'type' => 'structure', 'required' => [ 'MedicalScribeJobName', 'Media', 'OutputBucketName', 'DataAccessRoleArn', 'Settings', ], 'members' => [ 'MedicalScribeJobName' => [ 'shape' => 'TranscriptionJobName', ], 'Media' => [ 'shape' => 'Media', ], 'OutputBucketName' => [ 'shape' => 'OutputBucketName', ], 'OutputEncryptionKMSKeyId' => [ 'shape' => 'KMSKeyId', ], 'KMSEncryptionContext' => [ 'shape' => 'KMSEncryptionContextMap', ], 'DataAccessRoleArn' => [ 'shape' => 'DataAccessRoleArn', ], 'Settings' => [ 'shape' => 'MedicalScribeSettings', ], 'ChannelDefinitions' => [ 'shape' => 'MedicalScribeChannelDefinitions', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartMedicalScribeJobResponse' => [ 'type' => 'structure', 'members' => [ 'MedicalScribeJob' => [ 'shape' => 'MedicalScribeJob', ], ], ], 'StartMedicalTranscriptionJobRequest' => [ 'type' => 'structure', 'required' => [ 'MedicalTranscriptionJobName', 'LanguageCode', 'Media', 'OutputBucketName', 'Specialty', 'Type', ], 'members' => [ 'MedicalTranscriptionJobName' => [ 'shape' => 'TranscriptionJobName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'MediaSampleRateHertz' => [ 'shape' => 'MedicalMediaSampleRateHertz', ], 'MediaFormat' => [ 'shape' => 'MediaFormat', ], 'Media' => [ 'shape' => 'Media', ], 'OutputBucketName' => [ 'shape' => 'OutputBucketName', ], 'OutputKey' => [ 'shape' => 'OutputKey', ], 'OutputEncryptionKMSKeyId' => [ 'shape' => 'KMSKeyId', ], 'KMSEncryptionContext' => [ 'shape' => 'KMSEncryptionContextMap', ], 'Settings' => [ 'shape' => 'MedicalTranscriptionSetting', ], 'ContentIdentificationType' => [ 'shape' => 'MedicalContentIdentificationType', ], 'Specialty' => [ 'shape' => 'Specialty', ], 'Type' => [ 'shape' => 'Type', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartMedicalTranscriptionJobResponse' => [ 'type' => 'structure', 'members' => [ 'MedicalTranscriptionJob' => [ 'shape' => 'MedicalTranscriptionJob', ], ], ], 'StartTranscriptionJobRequest' => [ 'type' => 'structure', 'required' => [ 'TranscriptionJobName', 'Media', ], 'members' => [ 'TranscriptionJobName' => [ 'shape' => 'TranscriptionJobName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'MediaSampleRateHertz' => [ 'shape' => 'MediaSampleRateHertz', ], 'MediaFormat' => [ 'shape' => 'MediaFormat', ], 'Media' => [ 'shape' => 'Media', ], 'OutputBucketName' => [ 'shape' => 'OutputBucketName', ], 'OutputKey' => [ 'shape' => 'OutputKey', ], 'OutputEncryptionKMSKeyId' => [ 'shape' => 'KMSKeyId', ], 'KMSEncryptionContext' => [ 'shape' => 'KMSEncryptionContextMap', ], 'Settings' => [ 'shape' => 'Settings', ], 'ModelSettings' => [ 'shape' => 'ModelSettings', ], 'JobExecutionSettings' => [ 'shape' => 'JobExecutionSettings', ], 'ContentRedaction' => [ 'shape' => 'ContentRedaction', ], 'IdentifyLanguage' => [ 'shape' => 'Boolean', ], 'IdentifyMultipleLanguages' => [ 'shape' => 'Boolean', ], 'LanguageOptions' => [ 'shape' => 'LanguageOptions', ], 'Subtitles' => [ 'shape' => 'Subtitles', ], 'Tags' => [ 'shape' => 'TagList', ], 'LanguageIdSettings' => [ 'shape' => 'LanguageIdSettingsMap', ], 'ToxicityDetection' => [ 'shape' => 'ToxicityDetection', ], ], ], 'StartTranscriptionJobResponse' => [ 'type' => 'structure', 'members' => [ 'TranscriptionJob' => [ 'shape' => 'TranscriptionJob', ], ], ], 'String' => [ 'type' => 'string', ], 'StringTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'min' => 1, ], 'SubtitleFileUris' => [ 'type' => 'list', 'member' => [ 'shape' => 'Uri', ], ], 'SubtitleFormat' => [ 'type' => 'string', 'enum' => [ 'vtt', 'srt', ], ], 'SubtitleFormats' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubtitleFormat', ], ], 'SubtitleOutputStartIndex' => [ 'type' => 'integer', 'max' => 1, 'min' => 0, ], 'Subtitles' => [ 'type' => 'structure', 'members' => [ 'Formats' => [ 'shape' => 'SubtitleFormats', ], 'OutputStartIndex' => [ 'shape' => 'SubtitleOutputStartIndex', ], ], ], 'SubtitlesOutput' => [ 'type' => 'structure', 'members' => [ 'Formats' => [ 'shape' => 'SubtitleFormats', ], 'SubtitleFileUris' => [ 'shape' => 'SubtitleFileUris', ], 'OutputStartIndex' => [ 'shape' => 'SubtitleOutputStartIndex', ], ], ], 'Summarization' => [ 'type' => 'structure', 'required' => [ 'GenerateAbstractiveSummary', ], 'members' => [ 'GenerateAbstractiveSummary' => [ 'shape' => 'Boolean', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TranscribeArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TimestampMilliseconds' => [ 'type' => 'long', 'max' => 14400000, 'min' => 0, ], 'ToxicityCategories' => [ 'type' => 'list', 'member' => [ 'shape' => 'ToxicityCategory', ], 'max' => 1, 'min' => 1, ], 'ToxicityCategory' => [ 'type' => 'string', 'enum' => [ 'ALL', ], ], 'ToxicityDetection' => [ 'type' => 'list', 'member' => [ 'shape' => 'ToxicityDetectionSettings', ], 'max' => 1, 'min' => 1, ], 'ToxicityDetectionSettings' => [ 'type' => 'structure', 'required' => [ 'ToxicityCategories', ], 'members' => [ 'ToxicityCategories' => [ 'shape' => 'ToxicityCategories', ], ], ], 'TranscribeArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws(-[^:]+)?:transcribe:[a-zA-Z0-9-]*:[0-9]{12}:[a-zA-Z-]*/[0-9a-zA-Z._-]+', ], 'Transcript' => [ 'type' => 'structure', 'members' => [ 'TranscriptFileUri' => [ 'shape' => 'Uri', ], 'RedactedTranscriptFileUri' => [ 'shape' => 'Uri', ], ], ], 'TranscriptFilter' => [ 'type' => 'structure', 'required' => [ 'TranscriptFilterType', 'Targets', ], 'members' => [ 'TranscriptFilterType' => [ 'shape' => 'TranscriptFilterType', ], 'AbsoluteTimeRange' => [ 'shape' => 'AbsoluteTimeRange', ], 'RelativeTimeRange' => [ 'shape' => 'RelativeTimeRange', ], 'ParticipantRole' => [ 'shape' => 'ParticipantRole', ], 'Negate' => [ 'shape' => 'Boolean', ], 'Targets' => [ 'shape' => 'StringTargetList', ], ], ], 'TranscriptFilterType' => [ 'type' => 'string', 'enum' => [ 'EXACT', ], ], 'TranscriptionJob' => [ 'type' => 'structure', 'members' => [ 'TranscriptionJobName' => [ 'shape' => 'TranscriptionJobName', ], 'TranscriptionJobStatus' => [ 'shape' => 'TranscriptionJobStatus', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'MediaSampleRateHertz' => [ 'shape' => 'MediaSampleRateHertz', ], 'MediaFormat' => [ 'shape' => 'MediaFormat', ], 'Media' => [ 'shape' => 'Media', ], 'Transcript' => [ 'shape' => 'Transcript', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'CreationTime' => [ 'shape' => 'DateTime', ], 'CompletionTime' => [ 'shape' => 'DateTime', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'Settings' => [ 'shape' => 'Settings', ], 'ModelSettings' => [ 'shape' => 'ModelSettings', ], 'JobExecutionSettings' => [ 'shape' => 'JobExecutionSettings', ], 'ContentRedaction' => [ 'shape' => 'ContentRedaction', ], 'IdentifyLanguage' => [ 'shape' => 'Boolean', ], 'IdentifyMultipleLanguages' => [ 'shape' => 'Boolean', ], 'LanguageOptions' => [ 'shape' => 'LanguageOptions', ], 'IdentifiedLanguageScore' => [ 'shape' => 'IdentifiedLanguageScore', ], 'LanguageCodes' => [ 'shape' => 'LanguageCodeList', ], 'Tags' => [ 'shape' => 'TagList', ], 'Subtitles' => [ 'shape' => 'SubtitlesOutput', ], 'LanguageIdSettings' => [ 'shape' => 'LanguageIdSettingsMap', ], 'ToxicityDetection' => [ 'shape' => 'ToxicityDetection', ], ], ], 'TranscriptionJobName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'TranscriptionJobStatus' => [ 'type' => 'string', 'enum' => [ 'QUEUED', 'IN_PROGRESS', 'FAILED', 'COMPLETED', ], ], 'TranscriptionJobSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TranscriptionJobSummary', ], ], 'TranscriptionJobSummary' => [ 'type' => 'structure', 'members' => [ 'TranscriptionJobName' => [ 'shape' => 'TranscriptionJobName', ], 'CreationTime' => [ 'shape' => 'DateTime', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'CompletionTime' => [ 'shape' => 'DateTime', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'TranscriptionJobStatus' => [ 'shape' => 'TranscriptionJobStatus', ], 'FailureReason' => [ 'shape' => 'FailureReason', ], 'OutputLocationType' => [ 'shape' => 'OutputLocationType', ], 'ContentRedaction' => [ 'shape' => 'ContentRedaction', ], 'ModelSettings' => [ 'shape' => 'ModelSettings', ], 'IdentifyLanguage' => [ 'shape' => 'Boolean', ], 'IdentifyMultipleLanguages' => [ 'shape' => 'Boolean', ], 'IdentifiedLanguageScore' => [ 'shape' => 'IdentifiedLanguageScore', ], 'LanguageCodes' => [ 'shape' => 'LanguageCodeList', ], 'ToxicityDetection' => [ 'shape' => 'ToxicityDetection', ], ], ], 'Type' => [ 'type' => 'string', 'enum' => [ 'CONVERSATION', 'DICTATION', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TranscribeArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCallAnalyticsCategoryRequest' => [ 'type' => 'structure', 'required' => [ 'CategoryName', 'Rules', ], 'members' => [ 'CategoryName' => [ 'shape' => 'CategoryName', ], 'Rules' => [ 'shape' => 'RuleList', ], 'InputType' => [ 'shape' => 'InputType', ], ], ], 'UpdateCallAnalyticsCategoryResponse' => [ 'type' => 'structure', 'members' => [ 'CategoryProperties' => [ 'shape' => 'CategoryProperties', ], ], ], 'UpdateMedicalVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyName', 'LanguageCode', 'VocabularyFileUri', ], 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'VocabularyFileUri' => [ 'shape' => 'Uri', ], ], ], 'UpdateMedicalVocabularyResponse' => [ 'type' => 'structure', 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], 'VocabularyState' => [ 'shape' => 'VocabularyState', ], ], ], 'UpdateVocabularyFilterRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyFilterName', ], 'members' => [ 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'Words' => [ 'shape' => 'Words', ], 'VocabularyFilterFileUri' => [ 'shape' => 'Uri', ], 'DataAccessRoleArn' => [ 'shape' => 'DataAccessRoleArn', ], ], ], 'UpdateVocabularyFilterResponse' => [ 'type' => 'structure', 'members' => [ 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], ], ], 'UpdateVocabularyRequest' => [ 'type' => 'structure', 'required' => [ 'VocabularyName', 'LanguageCode', ], 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'Phrases' => [ 'shape' => 'Phrases', ], 'VocabularyFileUri' => [ 'shape' => 'Uri', ], 'DataAccessRoleArn' => [ 'shape' => 'DataAccessRoleArn', ], ], ], 'UpdateVocabularyResponse' => [ 'type' => 'structure', 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], 'VocabularyState' => [ 'shape' => 'VocabularyState', ], ], ], 'Uri' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '(s3://|http(s*)://).+', ], 'Vocabularies' => [ 'type' => 'list', 'member' => [ 'shape' => 'VocabularyInfo', ], ], 'VocabularyFilterInfo' => [ 'type' => 'structure', 'members' => [ 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], ], ], 'VocabularyFilterMethod' => [ 'type' => 'string', 'enum' => [ 'remove', 'mask', 'tag', ], ], 'VocabularyFilterName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'VocabularyFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'VocabularyFilterInfo', ], ], 'VocabularyInfo' => [ 'type' => 'structure', 'members' => [ 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'LastModifiedTime' => [ 'shape' => 'DateTime', ], 'VocabularyState' => [ 'shape' => 'VocabularyState', ], ], ], 'VocabularyName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'VocabularyState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'READY', 'FAILED', ], ], 'Word' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Words' => [ 'type' => 'list', 'member' => [ 'shape' => 'Word', ], 'min' => 1, ], ],];
