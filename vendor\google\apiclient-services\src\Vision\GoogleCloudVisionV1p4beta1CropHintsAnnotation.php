<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Vision;

class GoogleCloudVisionV1p4beta1CropHintsAnnotation extends \Google\Collection
{
  protected $collection_key = 'cropHints';
  protected $cropHintsType = GoogleCloudVisionV1p4beta1CropHint::class;
  protected $cropHintsDataType = 'array';

  /**
   * @param GoogleCloudVisionV1p4beta1CropHint[]
   */
  public function setCropHints($cropHints)
  {
    $this->cropHints = $cropHints;
  }
  /**
   * @return GoogleCloudVisionV1p4beta1CropHint[]
   */
  public function getCropHints()
  {
    return $this->cropHints;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudVisionV1p4beta1CropHintsAnnotation::class, 'Google_Service_Vision_GoogleCloudVisionV1p4beta1CropHintsAnnotation');
