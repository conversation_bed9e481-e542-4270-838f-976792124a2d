{"__meta": {"id": "X9d0efad41f4e6059055932793ed9301b", "datetime": "2025-06-26 22:43:27", "utime": **********.116106, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750977806.634411, "end": **********.116121, "duration": 0.48170995712280273, "duration_str": "482ms", "measures": [{"label": "Booting", "start": 1750977806.634411, "relative_start": 0, "end": **********.044522, "relative_end": **********.044522, "duration": 0.4101109504699707, "duration_str": "410ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.044533, "relative_start": 0.4101219177246094, "end": **********.116122, "relative_end": 9.5367431640625e-07, "duration": 0.07158899307250977, "duration_str": "71.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45045064, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020659999999999998, "accumulated_duration_str": "20.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0729501, "duration": 0.019649999999999997, "duration_str": "19.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.111}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1013272, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.111, "width_percent": 3.001}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1083019, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.112, "width_percent": 1.888}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IjdrU1g4bmx1cWNiUGdHS2lHZ2NUWEE9PSIsInZhbHVlIjoiTTNVaHl2eVFHQzhjVFAvMXFvZEVyZz09IiwibWFjIjoiYTI1NjY1ZDdmNWFlZmZhZDZlNDQwYTQzMDgxZDI3ZGI0Zjg1MjA1Y2RmMTZhMzEzNDU4NjkxMGVmNWJmZmM5YSIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-781341196 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-781341196\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-385604322 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-385604322\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-22752553 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22752553\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-393077411 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjdrU1g4bmx1cWNiUGdHS2lHZ2NUWEE9PSIsInZhbHVlIjoiTTNVaHl2eVFHQzhjVFAvMXFvZEVyZz09IiwibWFjIjoiYTI1NjY1ZDdmNWFlZmZhZDZlNDQwYTQzMDgxZDI3ZGI0Zjg1MjA1Y2RmMTZhMzEzNDU4NjkxMGVmNWJmZmM5YSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977793868%7C18%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdXS3lDait2UDFVK2lDbWM4RDl4R0E9PSIsInZhbHVlIjoicE5jYUZnYnBJaGo3TVp6eGEwS3pLdEYvUEVHSVhjbFVLM1o5eTNZU2xNNWEvTXBPcXNHYkxsQjFDeUJtS2Y3WEoxVzIvMXZaalRpUWVrOFpUSHpEK3FkVGtLZ1hNMG0vdWFZZXMybWg1R0pNbnFmN2xzRm5FOVdKQTBaemo2dkJWQmo3UWNVbWJiak5QVjA3ZTJ4QmNzWXROVGpUcEs4eC9hdStJelB4WEl5QW1zSXJLQkZsVFkrVXRRa3kwVkYvVVUrWWpJdkV1eCt0aU5CVTFNMGprY3BNMGpYUDhSbm9FK2tpcDBuYTY0KzF2NGpNOUFVYWhkd210aTZ6dE40NGxGTmRJVkJ5eXNnODQzdndzaGdaMVhlb3luVGt0U3RwaW1Uby9rdWgyWlk2bEMxN2xCZWl0Wjl3czdQNWVwWnR1MUdtOEdpSlg1WDZZaGZmRkE3RWJadUtTeC9KV3VXWDA1QkNhSmozQVBBMmJhSUdibitHMm4zV0xpbnlLNTk5dFZvcXFheXNFcS9tYTdHYmI0UnBYSU5XdWJERmcxVHZZRVJCb1JrZ2NmMUVaUUNzcytzTStSek95MGZ6OVltTE02OVdrVDhMR2VqeUxIUmhWSmdjNktZdVRlNVpLbnlLbkpUZFpkeU1HMVM2MkNJNEw0UVhHTEYxTXNWbURyaWoiLCJtYWMiOiI3N2U2MmFkNThmMzRmZTI3NmRkMDUwODI2OTBhOTEzNDY5MjE0MmQ1MjQzZGVmMjhlZGE1Njk2YzFjZThlOWU3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZLa2pwZnRQa1BnV3RSQ1R0YkRoV0E9PSIsInZhbHVlIjoia1diQjlIcHlHRzBuZmd4RHRCRzlvYnAwZUhMdm9pWUt6M1IvRDFDamkrdG1qTVVlTGVJN216ZlNDZXkxT2hWbGI1TVJ6eFkvNWtoU0ZTL0R6N21TOHYzdjhQTE9PVlowb0dtandxQ2xSUFE4Y1RVay90LzJPR2N5bWRUTy92K1habVRzNHh3V2xKaG4xZVdYVUdJVUFpZDZwNmdUaGh3Um1hbzc5VkFRS1BaS2xGV0laT0lsSGJUQUNMbzBSSXFYYVE2dGxHdUVzVVpxQitZWk1NVWpaV0lTOWlnUkQ4RE9YNzRSTXFHMHBKOHdMSGVHSDhpSzA2MThxWm01eWw1Nk9seUN0YjB5TnI1V2E2L3doOHNZVTR6L1A4WWFYdXBjT1BxSmI0MnExNDNmcERuRjB1TGZxWDFZME8yTUtjQ1ZFNDJuOXEreHdDd0lRY1FxTTFadzVHdWdCTEpCMGJoSWJtQ05KSUVDV3YvUmUxRDZ4T3lYZFNpM2JFd3FBSW5kWWU1M1ptbVpIY1dBRXNsMWdtdU14Q21ES3pLeTVpMkRQcmxONEdzWHhXQllvd3hXcFVSQ0xKSnd5NmRBeExJWlJkMUJrMU5OejhZT1BHTmZVb2o2cmpLYVJnc1MzMkJDUlhLSFRJQVhpL3NyUUh5eWhEYUJmOE56bWc2STd4VnEiLCJtYWMiOiI2NTIwYTA0NzMwOWVkMTY3N2MzYWZjZDg5YzE1NTQ3M2M4MTdlOTljYTg5N2E1YjI5YmFkOGZhMWM3NWJkMzQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-393077411\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1101661737 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101661737\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:43:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlBRE9VNnJmajlmTWt6V0taV3JTSVE9PSIsInZhbHVlIjoiZmowa01oVTcxemN5bDVIOC9GU2E2UEcvQkpuOGIyeldkQXRmbytGRndOWjVPc2dHb3hXV0NVdmQyRVV6NC8yN051OFhtOWQvdmhteVVKVlVxeE4rR1BnY3ZxYTJDSlEvSXRqNDdVYkNUUmk4RVhzYXM0WmtoYWloLzdJMWc5eGxSbVNZSkF3eFpxeTQrNC96NzVwK1pUcnNkWnlpWDVHTHVaL053MlU3TTJXeW12bnpFQUJ2V0xYRTJlVkxGbWt0UDlTM3FvSS9XNFRzSlVIeklzNHZKcTJ3TkRVbVVlZUFnYkwwb294SFlwREZPbTZNNHp4M1ljcE5aTllNdkd0ZEtWTExWcFpzN0lxaXVXMHNuWUQvaEtlTDR2ZzZUWTZXdmxXaExwQmd6aHBOS1Rna1JadmNqSG1Lb3lqNUJ3UDRUT29OT2p2cEZRcUEwNm4zZjZlMXBucGRFTGVBLzI3MnZVMm5ISytLR2l3SWZ4N0lpTXRiMTF3QVQ0OHpiTTBYTUJwdkNIQTFlVCtKTzRoY2VuditsT01nUWQ2RkIvc3VVL2FjL2pmTWlqckJjY1J6aUJ5Mm45K1JTb0dJSTVTTUs4dHpxYVlpZUhJbzNERjlHSEtPKzVQSVR3anpSZUljWCttd3I3YXV0Z0RMQUxEWXRKNzBzdFZ0eDZYTjNha0IiLCJtYWMiOiJmMzhhOTQ3ZDU4NGM1NGQwNTFiMjU0ODg5NGQ2MTEzZDdkYjE3NWEzYTYzYWVmNTEwNzBjOTY4OGI1OTE2MDQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:43:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVOajBmeVdGT0YxVTFVb3NBK1Y0VUE9PSIsInZhbHVlIjoiVHF1b1AxbnhydUh3Q0hqUExCNFpHTkFMVldpKzNpTzBaZlY5SDhucXl6TTJsa3RkeURRYjZheU01TTBtOTJTTTJaZnBLN1RiZUFpUlZDbXUxY2lrOUw5VVhoakZha0IwdVBQWXgxRnlFRkg3UDBSR24wT3dtcWFiL0szWUtpaTc4SU9nR3haSW9sWGZVbkdhUll3cmFUU0dvOEt1WDVFb080YUtpKzNQeTRUcy9wVjgwSFRvRHo5U2RiVVpwUEN0MEFNZWtLVVlSODBDWDZIUElQZ0UzUEt1SWx1UjVYTjhrMGFSSmYyQy8wNDFHeEdoTFBZbktMRXE1MzdNWGFGMSs3Q1VkTWdmbHFJM2dLZ3RNSUlCQUJnb0I1eWw2eDZUWWtJa1lwT0c3WjduUnd6NVh1OXNGOHFubUxRb0taZWlxMDAvYU8yTjZwOGFuUzRYbHovQkNxNnRjZWFGVHE4VHprRXV5dlF5NUZ0Mk5xbWhVV24vUHpySnhJb2tBaWFrZUZUYjF1NGwwT3dYNEhzRGpYWnB5ZU1jdm1GdkpYNkJXS3RlWjlZaityUUpmREVIRURGZzk3VnJGSkoxclNnOWZQQUFWVTcxYk94bnpIM3BpVHU5RmwvV1lJK2toUFRMREV6a3N0Z3g5TC9icDE0NStpS1lUdlBDeTZQdTIrNHoiLCJtYWMiOiIxYTYzZGMzNGVmOThlNTk0MDIyYzY5ZjVlYTZjNmVhMDBjM2EyOGQ1NzkwMGIyOTViODgwODA3OTMwNjQ5OWIzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:43:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlBRE9VNnJmajlmTWt6V0taV3JTSVE9PSIsInZhbHVlIjoiZmowa01oVTcxemN5bDVIOC9GU2E2UEcvQkpuOGIyeldkQXRmbytGRndOWjVPc2dHb3hXV0NVdmQyRVV6NC8yN051OFhtOWQvdmhteVVKVlVxeE4rR1BnY3ZxYTJDSlEvSXRqNDdVYkNUUmk4RVhzYXM0WmtoYWloLzdJMWc5eGxSbVNZSkF3eFpxeTQrNC96NzVwK1pUcnNkWnlpWDVHTHVaL053MlU3TTJXeW12bnpFQUJ2V0xYRTJlVkxGbWt0UDlTM3FvSS9XNFRzSlVIeklzNHZKcTJ3TkRVbVVlZUFnYkwwb294SFlwREZPbTZNNHp4M1ljcE5aTllNdkd0ZEtWTExWcFpzN0lxaXVXMHNuWUQvaEtlTDR2ZzZUWTZXdmxXaExwQmd6aHBOS1Rna1JadmNqSG1Lb3lqNUJ3UDRUT29OT2p2cEZRcUEwNm4zZjZlMXBucGRFTGVBLzI3MnZVMm5ISytLR2l3SWZ4N0lpTXRiMTF3QVQ0OHpiTTBYTUJwdkNIQTFlVCtKTzRoY2VuditsT01nUWQ2RkIvc3VVL2FjL2pmTWlqckJjY1J6aUJ5Mm45K1JTb0dJSTVTTUs4dHpxYVlpZUhJbzNERjlHSEtPKzVQSVR3anpSZUljWCttd3I3YXV0Z0RMQUxEWXRKNzBzdFZ0eDZYTjNha0IiLCJtYWMiOiJmMzhhOTQ3ZDU4NGM1NGQwNTFiMjU0ODg5NGQ2MTEzZDdkYjE3NWEzYTYzYWVmNTEwNzBjOTY4OGI1OTE2MDQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:43:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVOajBmeVdGT0YxVTFVb3NBK1Y0VUE9PSIsInZhbHVlIjoiVHF1b1AxbnhydUh3Q0hqUExCNFpHTkFMVldpKzNpTzBaZlY5SDhucXl6TTJsa3RkeURRYjZheU01TTBtOTJTTTJaZnBLN1RiZUFpUlZDbXUxY2lrOUw5VVhoakZha0IwdVBQWXgxRnlFRkg3UDBSR24wT3dtcWFiL0szWUtpaTc4SU9nR3haSW9sWGZVbkdhUll3cmFUU0dvOEt1WDVFb080YUtpKzNQeTRUcy9wVjgwSFRvRHo5U2RiVVpwUEN0MEFNZWtLVVlSODBDWDZIUElQZ0UzUEt1SWx1UjVYTjhrMGFSSmYyQy8wNDFHeEdoTFBZbktMRXE1MzdNWGFGMSs3Q1VkTWdmbHFJM2dLZ3RNSUlCQUJnb0I1eWw2eDZUWWtJa1lwT0c3WjduUnd6NVh1OXNGOHFubUxRb0taZWlxMDAvYU8yTjZwOGFuUzRYbHovQkNxNnRjZWFGVHE4VHprRXV5dlF5NUZ0Mk5xbWhVV24vUHpySnhJb2tBaWFrZUZUYjF1NGwwT3dYNEhzRGpYWnB5ZU1jdm1GdkpYNkJXS3RlWjlZaityUUpmREVIRURGZzk3VnJGSkoxclNnOWZQQUFWVTcxYk94bnpIM3BpVHU5RmwvV1lJK2toUFRMREV6a3N0Z3g5TC9icDE0NStpS1lUdlBDeTZQdTIrNHoiLCJtYWMiOiIxYTYzZGMzNGVmOThlNTk0MDIyYzY5ZjVlYTZjNmVhMDBjM2EyOGQ1NzkwMGIyOTViODgwODA3OTMwNjQ5OWIzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:43:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-999051582 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjdrU1g4bmx1cWNiUGdHS2lHZ2NUWEE9PSIsInZhbHVlIjoiTTNVaHl2eVFHQzhjVFAvMXFvZEVyZz09IiwibWFjIjoiYTI1NjY1ZDdmNWFlZmZhZDZlNDQwYTQzMDgxZDI3ZGI0Zjg1MjA1Y2RmMTZhMzEzNDU4NjkxMGVmNWJmZmM5YSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999051582\", {\"maxDepth\":0})</script>\n"}}