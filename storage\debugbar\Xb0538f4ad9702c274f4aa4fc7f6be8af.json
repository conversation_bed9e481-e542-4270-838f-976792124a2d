{"__meta": {"id": "Xb0538f4ad9702c274f4aa4fc7f6be8af", "datetime": "2025-06-26 22:42:51", "utime": **********.31052, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750977770.852853, "end": **********.310535, "duration": 0.45768189430236816, "duration_str": "458ms", "measures": [{"label": "Booting", "start": 1750977770.852853, "relative_start": 0, "end": **********.258923, "relative_end": **********.258923, "duration": 0.4060699939727783, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.258931, "relative_start": 0.40607786178588867, "end": **********.310536, "relative_end": 9.5367431640625e-07, "duration": 0.0516049861907959, "duration_str": "51.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044808, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00252, "accumulated_duration_str": "2.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.28814, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.492}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.297816, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.492, "width_percent": 15.476}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.303255, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.968, "width_percent": 21.032}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6ImtHUzRVRUJ0YXBURTkzck1ySFdNR3c9PSIsInZhbHVlIjoiL2VXNE1oRVRRT2tyTVFxSWlmeWhrUT09IiwibWFjIjoiNzk1YmRkMGZjZWMyOGEyODI1MTI4M2JmOWQ5YzQ4MmZlZjUzMDZlZjhhMjExOGU2NjU3NGNlYjA5OGVmMzA1MiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1733386451 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1733386451\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1342479431 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1342479431\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-65060947 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65060947\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2022663566 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6ImtHUzRVRUJ0YXBURTkzck1ySFdNR3c9PSIsInZhbHVlIjoiL2VXNE1oRVRRT2tyTVFxSWlmeWhrUT09IiwibWFjIjoiNzk1YmRkMGZjZWMyOGEyODI1MTI4M2JmOWQ5YzQ4MmZlZjUzMDZlZjhhMjExOGU2NjU3NGNlYjA5OGVmMzA1MiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977762503%7C14%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRsMmI1TC9NbjF1VkhScHlaczhsVEE9PSIsInZhbHVlIjoicDFzMHhzRDJMc05PS3VuRFFPMDBoOXd4bHlrOXVZdmF2NCt4aitqenpXRDJTVWFOT25uWFl6SDZpVmdYMFNFSkVLWHZ1d0hYNHJEM1pNcWgrRUszNytocEFMZjYydng5dGd6V21SWGVjUW9pQldpOEtpZUpjMTN5M0svWVB6a1ZOdEhHVGNzRVVCK1RYM0ZyYUVkMDJRWmxHNmdSZWFYTVlJU2Z0YzFrSW13RXZPNzBYVmVLQmtpbVJUajJwMXJSbDdRM3dlWU5PYzFGK2wrT1M3c1BGc0RBNHpseUF2TmdOSEsySnpqaTloUVhDVE80WUdtd1pvSExBNjJEZlc0czQxeU9MZVVsVlFVUGZkekVMbklMT3YzRUJMUnVWaVRzblpQYS9rNndod1N0NDB4Q2EyTFZCMUlLTnFVTFU3eU9qcHVoalY3QkFBQlBNYUVQU2dEMVZkVmg4S1h5VUx6dHBsYnZnTlo0OXB5RkpqNVE3UUUyWmlWMmxxTEc2OFZMU0FHYmNGK3hhZ3UxR0RNUVVCSDZQdithR2ZEWFlEMjNjUDdjTzExeW5uY25BSC9PRHNNaHRXRTJXWTBvN3hDNWNSclM5UzVGVTRVVWVkU0dkVWpYaFoyWDRpYXYrSHhOQ2lJV3d2RlJoMHg5V25lY2x2NUN4a0wrSVdMVFcyLzIiLCJtYWMiOiJjOGM3OGUxODQxOTJjNWMyYmEzOTkzOTI3Zjg1NDMwZDgxYzYwZjQzMzlkMWQ5NDgwMzgwYjM4YTRhMTEwMTU1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjczdXNxM05LZ1ZwZXVDVlRGNkhqRlE9PSIsInZhbHVlIjoiTWFhSGRlRzc0KzFyMHBTaXlvR0ZzWkg1V05WdjhRelhCYXgyYTc0Q1dBU2dpVWI1VzdYTkJ5MXFLL0M1bDBXTm84bGlYTDNyZ29jNit0ZVltbThtV0J6TXBwbktyaHc2aUtuRGNMcW1ZQ0hqQXppckRSUVNRczZiY25IdFhyUEJ1bStQSnRCbVI4TnJZS1l5aVJWTEpUR1NFRlh3MHV5aU5CejlJbUd4bUQvOHJnVElRVXJIUHJPM3JSTHB5bDJBek0wbEtMNTFyNVlCVDJOaWgrUGlpSGhiQy9CbUd5cVRGTVV1L2xMNjErOWpRbWl1OUlHQUwyNWFxWXpQRXdpZlVqTDZmclMyL3RleHFCZ29GbVk3YUJhSHY4VU5hcWJ2Yy9XbEFacEZQZVhCV3FzbC9UM3RialpPcDBZaCs0U2M1T1ZTVm53d281aGNrYzJVeHVuRTVDa21FaHR3YjJ5THBJSmNWOExWNG1HekJsb1BQcThhRjBGblpvek9TQjBScDZYcXRJNTJZMTUxYVBtUk5HczU3dUJ0M21pU1c4WUhPZHBGMXI0QTJVcis5V0Zld1N2aVh6TmJvZWtEbGcxOXRmTzRQZk5ibmxJVm92bGFNclA5OVpDTE4vWVc4V0ovVWkxTmNGNE9iQmRLTXcxZUYzYlFUNXd6azV3Uy92R0MiLCJtYWMiOiIxNTZkMjFlOTZhNjMyMjA5Y2U0ZjlhZDRiYjkxMDhhMGJmNzEyNTExY2FhMzIwNGU4MGEwYTVkNWY2MjA3M2E2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2022663566\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1945505799 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:42:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNjSkF6STRNTU12RnZJdzdHdDExZ3c9PSIsInZhbHVlIjoiZmk0SzErN0RWY3dubUlza21YTE5sbVRSQ2ZvQ2NZVmhhYnNheHJHeXpicDBCUDRXODNNcGRuMkptbDFiMzJEMitjYTR1KzJGNXUwZE9Kd25RMkxUK2ZRNExvYng2QnFGZTN0S2VzdzQ2YlBIMVJsdnk5VHFJMXp0SzgxQkJPOEtVb2hGTUIwdkw1UW1WaVBzR216OWNUYmUvUjlhSmp1bUJxeDdkZlNNSWJjYnhGYXFIdTdXbXkybERmclRxUjVUdFduLzg4WE1tampZbWVpa0ZNaVNac1pKbkVTMGFuQ05NQ2Z3Z1lyZTFNSWNScTJoQW9xd2JWNnFRZ204Zk1acXo3cUF3QnRFUmhGN2k0NlI0YlJjVy9LYmluM0h0U21QTkMvSkE1cHZUK25qZ2RVZXEzcjJhcDNNM2xxNStTV2FLYmx3YndwMUllMHk4LzFFdDhzU1ZpOTYzTGIxSW9PUjdySWVqM3dxWElVdU5qSVB1SFEwSW53T1g5TDZzYyttcEJKVjQ0bUZ0Zk5DMS9xVkQrU016eDlEQ0ozcjFDN054YXg2M3lhcEVTSzJlbmluVFJTWXJwWUFZUXZtQWtxTm80NHdsTklUNFR3Z3ozbHJaTTl1cnRYQlBJdFI3U0lscnlucDdnYjlxTk5YQ3NGV3Y5V2hBT3R3R2swVDBVTW4iLCJtYWMiOiIyOTIyYWIzNGYyOTViNmJiMWE1ZTlhZGI0NTRiNmM3OTQ1ZWYzNzc5Njg3OTIxM2ZiNWYzMjNlOTRjODIzNjlhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImR0aHZHQ0pOMUlPTkMvZGx1OXNmRkE9PSIsInZhbHVlIjoiTE9yTGsxQW1VM1lhQWNqQnp4THdOWmhITUtpTGgzeEZmMkVHQXJUNkg3VDZjZTR1ekNDNmEvZ2RPeU0yTHlqckZSSzFqNGVpeWpiMWZTTkI4SzVidThEUDNtMkZtQnRHNVN4bWk5UTBTVGdKeWVFNk51bzdZNk5UNXJackV0U3psaThVMGZ1RVArZEUxelhHQU9na1JZbmFCUS9KRG5aOXNBa0FJOW5TY0VrQWlIWEpMclFxOVVYeTVCNG0ycVZWejdXVHhucUNRVEtsMGIrQUdsWTAzT2hXRnoxSWs0N1YyUjYyN0FzNmg1OVBFSFAxcjIzclVtME5wb2NGQmg5ZlIyUDh1bXp2M1RXWnB6VVhxaEtscUNJMUxiUmdKdkxFcXpYM0M0Qk9KVkZ5S0c0aG4xQXVpZ0V2cm1JMVJOM3ZGbDVPWVJBNGc2TGZKcjRJWTVTRWtTaFZTcEl3bURaTUVpbC80YlVEM2JzaFgrbTRFUllIbEt4dmx4M3E3a2tQNHFRNnhqdzFZNkhEMjVyWEFyUUZEaHVxS2V5eDJDTHhRYlFFVHE5ZDRVT2VjdGlPaWdSNmxoN1h5K2liRUxZMTRsWGRmWHJlaVgvWnVTb2dBTjBYOFFLeGQ3amtscytzaUkxTmhRRWRmTFhCc2JVTEFuUlpPam5Cc3NaZ2wzUkQiLCJtYWMiOiI1ZWU0MTJiNDgxZTY2NjRmODQ3YjdiNjViODlhZjMxODBkOTY3Mzg3MzdmOWZlMzVjMzI3MTgwNzA0NzJjZTk1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNjSkF6STRNTU12RnZJdzdHdDExZ3c9PSIsInZhbHVlIjoiZmk0SzErN0RWY3dubUlza21YTE5sbVRSQ2ZvQ2NZVmhhYnNheHJHeXpicDBCUDRXODNNcGRuMkptbDFiMzJEMitjYTR1KzJGNXUwZE9Kd25RMkxUK2ZRNExvYng2QnFGZTN0S2VzdzQ2YlBIMVJsdnk5VHFJMXp0SzgxQkJPOEtVb2hGTUIwdkw1UW1WaVBzR216OWNUYmUvUjlhSmp1bUJxeDdkZlNNSWJjYnhGYXFIdTdXbXkybERmclRxUjVUdFduLzg4WE1tampZbWVpa0ZNaVNac1pKbkVTMGFuQ05NQ2Z3Z1lyZTFNSWNScTJoQW9xd2JWNnFRZ204Zk1acXo3cUF3QnRFUmhGN2k0NlI0YlJjVy9LYmluM0h0U21QTkMvSkE1cHZUK25qZ2RVZXEzcjJhcDNNM2xxNStTV2FLYmx3YndwMUllMHk4LzFFdDhzU1ZpOTYzTGIxSW9PUjdySWVqM3dxWElVdU5qSVB1SFEwSW53T1g5TDZzYyttcEJKVjQ0bUZ0Zk5DMS9xVkQrU016eDlEQ0ozcjFDN054YXg2M3lhcEVTSzJlbmluVFJTWXJwWUFZUXZtQWtxTm80NHdsTklUNFR3Z3ozbHJaTTl1cnRYQlBJdFI3U0lscnlucDdnYjlxTk5YQ3NGV3Y5V2hBT3R3R2swVDBVTW4iLCJtYWMiOiIyOTIyYWIzNGYyOTViNmJiMWE1ZTlhZGI0NTRiNmM3OTQ1ZWYzNzc5Njg3OTIxM2ZiNWYzMjNlOTRjODIzNjlhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImR0aHZHQ0pOMUlPTkMvZGx1OXNmRkE9PSIsInZhbHVlIjoiTE9yTGsxQW1VM1lhQWNqQnp4THdOWmhITUtpTGgzeEZmMkVHQXJUNkg3VDZjZTR1ekNDNmEvZ2RPeU0yTHlqckZSSzFqNGVpeWpiMWZTTkI4SzVidThEUDNtMkZtQnRHNVN4bWk5UTBTVGdKeWVFNk51bzdZNk5UNXJackV0U3psaThVMGZ1RVArZEUxelhHQU9na1JZbmFCUS9KRG5aOXNBa0FJOW5TY0VrQWlIWEpMclFxOVVYeTVCNG0ycVZWejdXVHhucUNRVEtsMGIrQUdsWTAzT2hXRnoxSWs0N1YyUjYyN0FzNmg1OVBFSFAxcjIzclVtME5wb2NGQmg5ZlIyUDh1bXp2M1RXWnB6VVhxaEtscUNJMUxiUmdKdkxFcXpYM0M0Qk9KVkZ5S0c0aG4xQXVpZ0V2cm1JMVJOM3ZGbDVPWVJBNGc2TGZKcjRJWTVTRWtTaFZTcEl3bURaTUVpbC80YlVEM2JzaFgrbTRFUllIbEt4dmx4M3E3a2tQNHFRNnhqdzFZNkhEMjVyWEFyUUZEaHVxS2V5eDJDTHhRYlFFVHE5ZDRVT2VjdGlPaWdSNmxoN1h5K2liRUxZMTRsWGRmWHJlaVgvWnVTb2dBTjBYOFFLeGQ3amtscytzaUkxTmhRRWRmTFhCc2JVTEFuUlpPam5Cc3NaZ2wzUkQiLCJtYWMiOiI1ZWU0MTJiNDgxZTY2NjRmODQ3YjdiNjViODlhZjMxODBkOTY3Mzg3MzdmOWZlMzVjMzI3MTgwNzA0NzJjZTk1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1945505799\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-598210747 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6ImtHUzRVRUJ0YXBURTkzck1ySFdNR3c9PSIsInZhbHVlIjoiL2VXNE1oRVRRT2tyTVFxSWlmeWhrUT09IiwibWFjIjoiNzk1YmRkMGZjZWMyOGEyODI1MTI4M2JmOWQ5YzQ4MmZlZjUzMDZlZjhhMjExOGU2NjU3NGNlYjA5OGVmMzA1MiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-598210747\", {\"maxDepth\":0})</script>\n"}}