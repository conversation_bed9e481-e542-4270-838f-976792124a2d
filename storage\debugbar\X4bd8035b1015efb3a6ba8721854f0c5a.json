{"__meta": {"id": "X4bd8035b1015efb3a6ba8721854f0c5a", "datetime": "2025-06-26 23:21:14", "utime": **********.909488, "method": "GET", "uri": "/budget/eyJpdiI6IitUUkg3UTRnWGVYT1hkZm9Da0NPZXc9PSIsInZhbHVlIjoiZStjelcvdEROa3hBMXEzZVd5NkdKUT09IiwibWFjIjoiNzk4NWQxMmViNWIzMzdiYjg3Njg1OTI5MWExNzQ3ZjQ3MDBmODgxNDZhZWU1OWUwZmNmN2JiMWI4MTMxZmM1YyIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 69, "messages": [{"message": "[23:21:14] LOG.info: Datos de ingresos del presupuesto: {\"32\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"630000\"},\"33\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"104040\"},\"40\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"0\"},\"41\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"9000\"},\"42\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"17000\"},\"43\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"135000\"}}", "message_html": null, "is_string": false, "label": "info", "time": **********.56934, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Budget Total Arrays: [{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"630000\"},{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"104040\"},{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"0\"},{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"9000\"},{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"17000\"},{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"135000\"}]", "message_html": null, "is_string": false, "label": "info", "time": **********.569536, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Budget Total: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":895040}", "message_html": null, "is_string": false, "label": "info", "time": **********.569679, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: فاتورة بيع مباشرة (فترة) - الفئة: مبيعات الروابي, رقم الفاتورة: 1, المبلغ: 1750", "message_html": null, "is_string": false, "label": "info", "time": **********.579399, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Período: yearly, Categoría: مبيعات الروابي, Mes: Jan-Dec, Monto: 1750", "message_html": null, "is_string": false, "label": "info", "time": **********.58489, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Período: yearly, Categoría: مبيعات المصيف, Mes: Jan-Dec, Monto: 0", "message_html": null, "is_string": false, "label": "info", "time": **********.603927, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Período: yearly, Categoría: سلف للموظفين, Mes: Jan-Dec, Monto: 0", "message_html": null, "is_string": false, "label": "info", "time": **********.631883, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Período: yearly, Categoría: احتياطي الانماء, Mes: Jan-Dec, Monto: 0", "message_html": null, "is_string": false, "label": "info", "time": **********.643826, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Período: yearly, Categoría: احتياطي بنك ساب, Mes: Jan-Dec, Monto: 0", "message_html": null, "is_string": false, "label": "info", "time": **********.654672, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Período: yearly, Categoría: استثمارات قطاع التمويل, Mes: Jan-Dec, Monto: 0", "message_html": null, "is_string": false, "label": "info", "time": **********.665356, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Selected categories: [\"income-33\",\"expense-31\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.693106, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.693241, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including expense category: جاري مشتريات المصيف (ID: 31)", "message_html": null, "is_string": false, "label": "info", "time": **********.693369, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.693489, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered expense totals: []", "message_html": null, "is_string": false, "label": "info", "time": **********.693558, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.693659, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":93600}", "message_html": null, "is_string": false, "label": "info", "time": **********.693731, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Selected categories: [\"income-33\",\"expense-31\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.713716, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.713841, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including expense category: جاري مشتريات المصيف (ID: 31)", "message_html": null, "is_string": false, "label": "info", "time": **********.71398, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.714095, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered expense totals: {\"Jan-Dec\":154.56}", "message_html": null, "is_string": false, "label": "info", "time": **********.714167, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.714235, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":93600}", "message_html": null, "is_string": false, "label": "info", "time": **********.714302, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Selected categories: [\"income-33\",\"expense-31\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.730766, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.730885, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including expense category: جاري مشتريات المصيف (ID: 31)", "message_html": null, "is_string": false, "label": "info", "time": **********.73101, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.731122, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered expense totals: {\"Jan-Dec\":154.56}", "message_html": null, "is_string": false, "label": "info", "time": **********.731197, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.731265, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":93600}", "message_html": null, "is_string": false, "label": "info", "time": **********.731331, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Selected categories: [\"income-33\",\"expense-31\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.748089, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.748215, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including expense category: جاري مشتريات المصيف (ID: 31)", "message_html": null, "is_string": false, "label": "info", "time": **********.748333, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.748437, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered expense totals: {\"Jan-Dec\":154.56}", "message_html": null, "is_string": false, "label": "info", "time": **********.748505, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.74857, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":93600}", "message_html": null, "is_string": false, "label": "info", "time": **********.748634, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Selected categories: [\"income-33\",\"expense-31\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.766729, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.766896, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including expense category: جاري مشتريات المصيف (ID: 31)", "message_html": null, "is_string": false, "label": "info", "time": **********.767057, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.767281, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered expense totals: {\"Jan-Dec\":154.56}", "message_html": null, "is_string": false, "label": "info", "time": **********.767389, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.767493, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":93600}", "message_html": null, "is_string": false, "label": "info", "time": **********.767593, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Selected categories: [\"income-33\",\"expense-31\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.785817, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.785944, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including expense category: جاري مشتريات المصيف (ID: 31)", "message_html": null, "is_string": false, "label": "info", "time": **********.786068, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.786179, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered expense totals: {\"Jan-Dec\":154.56}", "message_html": null, "is_string": false, "label": "info", "time": **********.786254, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.786323, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":93600}", "message_html": null, "is_string": false, "label": "info", "time": **********.786388, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Selected categories: [\"income-33\",\"expense-31\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.803932, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.804053, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including expense category: جاري مشتريات المصيف (ID: 31)", "message_html": null, "is_string": false, "label": "info", "time": **********.804175, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.804282, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered expense totals: {\"Jan-Dec\":154.56}", "message_html": null, "is_string": false, "label": "info", "time": **********.804365, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.80444, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":93600}", "message_html": null, "is_string": false, "label": "info", "time": **********.804556, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Selected categories: [\"income-33\",\"expense-31\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.822113, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including income category: مبيعات المصيف (ID: 33)", "message_html": null, "is_string": false, "label": "info", "time": **********.822247, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Including expense category: جاري مشتريات المصيف (ID: 31)", "message_html": null, "is_string": false, "label": "info", "time": **********.822378, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered income totals: {\"Jan-Dec\":0}", "message_html": null, "is_string": false, "label": "info", "time": **********.822494, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered expense totals: {\"Jan-Dec\":154.56}", "message_html": null, "is_string": false, "label": "info", "time": **********.822566, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":104040}", "message_html": null, "is_string": false, "label": "info", "time": **********.822641, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Filtered budget expense totals: {\"January\":0,\"February\":0,\"March\":0,\"April\":0,\"May\":0,\"June\":0,\"July\":0,\"August\":0,\"September\":0,\"October\":0,\"November\":0,\"December\":0,\"Jan-Mar\":0,\"Apr-Jun\":0,\"Jul-Sep\":0,\"Oct-Dec\":0,\"Jan-Jun\":0,\"Jul-Dec\":0,\"Jan-Dec\":93600}", "message_html": null, "is_string": false, "label": "info", "time": **********.822707, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Período: yearly", "message_html": null, "is_string": false, "label": "info", "time": **********.825964, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: Categorías de ingresos: مبيعات الروابي, مبيعات المصيف, سلف للموظفين, احتياطي الانماء, احتياطي بنك ساب, استثمارات قطاع التمويل", "message_html": null, "is_string": false, "label": "info", "time": **********.8261, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:14] LOG.info: <PERSON><PERSON> de ingresos: {\"32\":{\"Jan-Dec\":1750},\"33\":{\"Jan-Dec\":0},\"40\":{\"Jan-Dec\":0},\"41\":{\"Jan-Dec\":0},\"42\":{\"Jan-Dec\":0},\"43\":{\"Jan-Dec\":0}}", "message_html": null, "is_string": false, "label": "info", "time": **********.826177, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.104029, "end": **********.909556, "duration": 0.8055269718170166, "duration_str": "806ms", "measures": [{"label": "Booting", "start": **********.104029, "relative_start": 0, "end": **********.493375, "relative_end": **********.493375, "duration": 0.3893461227416992, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.493385, "relative_start": 0.3893561363220215, "end": **********.909557, "relative_end": 1.1920928955078125e-06, "duration": 0.4161720275878906, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54125584, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x budget.show", "param_count": null, "params": [], "start": **********.832873, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/budget/show.blade.phpbudget.show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fbudget%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 1, "name_original": "budget.show"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.842194, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.847208, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.887519, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.899089, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.901529, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.901985, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET budget/{budget}", "middleware": "web, verified, auth, XSS, revalidate", "as": "budget.show", "controller": "App\\Http\\Controllers\\BudgetController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=209\" onclick=\"\">app/Http/Controllers/BudgetController.php:209-826</a>"}, "queries": {"nb_statements": 160, "nb_failed_statements": 0, "accumulated_duration": 0.06489000000000002, "accumulated_duration_str": "64.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5248702, "duration": 0.011869999999999999, "duration_str": "11.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 18.292}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.545297, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 18.292, "width_percent": 0.909}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.559978, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 19.202, "width_percent": 0.693}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.561789, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 19.895, "width_percent": 0.755}, {"sql": "select * from `budgets` where `budgets`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 221}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.56619, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:221", "source": "app/Http/Controllers/BudgetController.php:221", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=221", "ajax": false, "filename": "BudgetController.php", "line": "221"}, "connection": "kdmkjkqknb", "start_percent": 20.65, "width_percent": 0.863}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'income'", "type": "query", "params": [], "bindings": ["15", "income"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 301}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.570341, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:301", "source": "app/Http/Controllers/BudgetController.php:301", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=301", "ajax": false, "filename": "BudgetController.php", "line": "301"}, "connection": "kdmkjkqknb", "start_percent": 21.513, "width_percent": 0.74}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 32 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "32", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.572824, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 22.253, "width_percent": 0.524}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 429}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.575725, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 22.777, "width_percent": 0.539}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 99}, {"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 429}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.577639, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 23.316, "width_percent": 0.524}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 32 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "32", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.57962, "duration": 0.********999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 23.84, "width_percent": 0.555}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.58159, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 24.395, "width_percent": 0.524}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5832472, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 24.919, "width_percent": 0.431}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 33 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "33", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5851371, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 25.351, "width_percent": 0.709}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 33 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "33", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.587168, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 26.059, "width_percent": 0.385}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.589185, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 26.445, "width_percent": 0.478}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.593081, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 26.922, "width_percent": 1.341}, {"sql": "select * from `product_services` where `product_services`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/InvoiceProduct.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\InvoiceProduct.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 455}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5957892, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "InvoiceProduct.php:30", "source": "app/Models/InvoiceProduct.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=30", "ajax": false, "filename": "InvoiceProduct.php", "line": "30"}, "connection": "kdmkjkqknb", "start_percent": 28.263, "width_percent": 0.724}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.598155, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 28.988, "width_percent": 1.033}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.601011, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 30.02, "width_percent": 1.125}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 40 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "40", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6044009, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 31.145, "width_percent": 1.125}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 40 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "40", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.607537, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 32.27, "width_percent": 0.955}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6244018, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 33.225, "width_percent": 0.632}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.626047, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 33.857, "width_percent": 0.339}, {"sql": "select * from `product_services` where `product_services`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/InvoiceProduct.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\InvoiceProduct.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 455}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.627219, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "InvoiceProduct.php:30", "source": "app/Models/InvoiceProduct.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=30", "ajax": false, "filename": "InvoiceProduct.php", "line": "30"}, "connection": "kdmkjkqknb", "start_percent": 34.196, "width_percent": 0.262}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.628484, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 34.458, "width_percent": 0.385}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6301918, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 34.844, "width_percent": 0.401}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 41 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "41", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.632229, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 35.244, "width_percent": 0.509}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 41 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "41", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.633964, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 35.753, "width_percent": 0.693}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.636123, "duration": 0.********999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 36.446, "width_percent": 0.555}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.637698, "duration": 0.********999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 37.001, "width_percent": 0.555}, {"sql": "select * from `product_services` where `product_services`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/InvoiceProduct.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\InvoiceProduct.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 455}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.639106, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "InvoiceProduct.php:30", "source": "app/Models/InvoiceProduct.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=30", "ajax": false, "filename": "InvoiceProduct.php", "line": "30"}, "connection": "kdmkjkqknb", "start_percent": 37.556, "width_percent": 0.478}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6406348, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 38.034, "width_percent": 0.663}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6424012, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 38.696, "width_percent": 0.416}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 42 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "42", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6440601, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 39.112, "width_percent": 0.431}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 42 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "42", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.645687, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 39.544, "width_percent": 0.416}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.647229, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 39.96, "width_percent": 0.339}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.64853, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 40.299, "width_percent": 0.308}, {"sql": "select * from `product_services` where `product_services`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/InvoiceProduct.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\InvoiceProduct.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 455}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.649672, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "InvoiceProduct.php:30", "source": "app/Models/InvoiceProduct.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=30", "ajax": false, "filename": "InvoiceProduct.php", "line": "30"}, "connection": "kdmkjkqknb", "start_percent": 40.607, "width_percent": 0.231}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.651053, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 40.838, "width_percent": 0.832}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.653018, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 41.671, "width_percent": 0.324}, {"sql": "select * from `invoices` where `created_by` = 15 and `category_id` = 43 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "43", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 425}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.654909, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:425", "source": "app/Http/Controllers/BudgetController.php:425", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=425", "ajax": false, "filename": "BudgetController.php", "line": "425"}, "connection": "kdmkjkqknb", "start_percent": 41.994, "width_percent": 0.385}, {"sql": "select * from `invoices` where `created_by` = 15 and (`category_id` != 43 or `category_id` is null) and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "43", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6565351, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 42.379, "width_percent": 0.462}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.658166, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 42.842, "width_percent": 0.324}, {"sql": "select * from `product_services` where `product_services`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 449}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6595998, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:449", "source": "app/Http/Controllers/BudgetController.php:449", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=449", "ajax": false, "filename": "BudgetController.php", "line": "449"}, "connection": "kdmkjkqknb", "start_percent": 43.165, "width_percent": 0.385}, {"sql": "select * from `product_services` where `product_services`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/InvoiceProduct.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\InvoiceProduct.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 455}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.660988, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "InvoiceProduct.php:30", "source": "app/Models/InvoiceProduct.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=30", "ajax": false, "filename": "InvoiceProduct.php", "line": "30"}, "connection": "kdmkjkqknb", "start_percent": 43.551, "width_percent": 0.277}, {"sql": "select * from `invoices` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 470}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.662328, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:470", "source": "app/Http/Controllers/BudgetController.php:470", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=470", "ajax": false, "filename": "BudgetController.php", "line": "470"}, "connection": "kdmkjkqknb", "start_percent": 43.828, "width_percent": 0.339}, {"sql": "select * from `invoice_products` where `invoice_products`.`invoice_id` = 1 and `invoice_products`.`invoice_id` is not null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Invoice.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Invoice.php", "line": 67}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 476}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6639168, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:73", "source": "app/Models/Invoice.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=73", "ajax": false, "filename": "Invoice.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 44.167, "width_percent": 0.385}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'expense'", "type": "query", "params": [], "bindings": ["15", "expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 500}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.665564, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:500", "source": "app/Http/Controllers/BudgetController.php:500", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=500", "ajax": false, "filename": "BudgetController.php", "line": "500"}, "connection": "kdmkjkqknb", "start_percent": 44.552, "width_percent": 0.478}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 30 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "30", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.667851, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 45.03, "width_percent": 0.925}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.66979, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 45.955, "width_percent": 0.478}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 30 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "30", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6719098, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 46.432, "width_percent": 0.663}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 629}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.67403, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 47.095, "width_percent": 0.447}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 629}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.67577, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 47.542, "width_percent": 0.478}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6774158, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 48.02, "width_percent": 0.616}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.67907, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 48.636, "width_percent": 0.354}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.680368, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 48.991, "width_percent": 0.308}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.681787, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 49.299, "width_percent": 0.493}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6833181, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 49.792, "width_percent": 0.693}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6854908, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 50.485, "width_percent": 0.586}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.687065, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 51.071, "width_percent": 0.385}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 30 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "30", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.689338, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 51.456, "width_percent": 1.017}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6914048, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 52.473, "width_percent": 0.385}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 31 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "31", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.693968, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 52.859, "width_percent": 0.524}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.695483, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 53.383, "width_percent": 0.401}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 31 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "31", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6968641, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 53.783, "width_percent": 0.431}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 629}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.698817, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 54.215, "width_percent": 0.416}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 629}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.700161, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 54.631, "width_percent": 0.462}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7017472, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 55.093, "width_percent": 0.431}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7032359, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 55.525, "width_percent": 0.354}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.704475, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 55.879, "width_percent": 0.247}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.705662, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 56.126, "width_percent": 0.216}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.706784, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 56.342, "width_percent": 0.262}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.707959, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 56.603, "width_percent": 0.401}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7092972, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 57.004, "width_percent": 0.431}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 31 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "31", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7107189, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 57.436, "width_percent": 0.385}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7121902, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 57.821, "width_percent": 0.447}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 34 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "34", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7144902, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 58.268, "width_percent": 0.416}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.715939, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 58.684, "width_percent": 0.339}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 34 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "34", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.717483, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 59.023, "width_percent": 0.431}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7190082, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 59.454, "width_percent": 0.431}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.720509, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 59.886, "width_percent": 0.262}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.721729, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 60.148, "width_percent": 0.293}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.722959, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 60.441, "width_percent": 0.277}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.724166, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 60.718, "width_percent": 0.262}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7254128, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 60.98, "width_percent": 0.354}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.726664, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 61.335, "width_percent": 0.231}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 34 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "34", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.727889, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 61.566, "width_percent": 0.324}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7292879, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 61.889, "width_percent": 0.354}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 35 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "35", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7315068, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 62.244, "width_percent": 0.37}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.732907, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 62.614, "width_percent": 0.308}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 35 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "35", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.734378, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 62.922, "width_percent": 0.493}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.735947, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 63.415, "width_percent": 0.385}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.737441, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 63.8, "width_percent": 0.308}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7386959, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 64.108, "width_percent": 0.339}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.739985, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 64.448, "width_percent": 0.293}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.741503, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 64.74, "width_percent": 0.416}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7428448, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 65.156, "width_percent": 0.37}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.744087, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 65.526, "width_percent": 0.293}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 35 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "35", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.745324, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 65.819, "width_percent": 0.324}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.746691, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 66.143, "width_percent": 0.339}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 36 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "36", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.748806, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 66.482, "width_percent": 0.324}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7501578, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 66.805, "width_percent": 0.37}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 36 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "36", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7517369, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 67.175, "width_percent": 0.647}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.753412, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 67.822, "width_percent": 0.632}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.755034, "duration": 0.********999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 68.454, "width_percent": 0.555}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.756425, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 69.009, "width_percent": 0.57}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.758318, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 69.579, "width_percent": 0.478}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7598128, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 70.057, "width_percent": 0.354}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.76115, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 70.411, "width_percent": 0.385}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.762418, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 70.797, "width_percent": 0.293}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 36 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "36", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7636468, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 71.09, "width_percent": 0.339}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.765042, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 71.429, "width_percent": 0.539}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 37 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "37", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.76782, "duration": 0.********999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 71.968, "width_percent": 0.555}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7693892, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 72.523, "width_percent": 0.493}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 37 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "37", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.770879, "duration": 0.********999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 73.016, "width_percent": 0.555}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.772523, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 73.571, "width_percent": 0.586}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7742898, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 74.156, "width_percent": 0.339}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.776041, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 74.495, "width_percent": 0.324}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.777372, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 74.819, "width_percent": 0.354}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7786639, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 75.173, "width_percent": 0.324}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.779917, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 75.497, "width_percent": 0.339}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7811809, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 75.836, "width_percent": 0.339}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 37 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "37", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.782501, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 76.175, "width_percent": 0.37}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.784111, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 76.545, "width_percent": 0.616}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 38 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "38", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.786596, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 77.161, "width_percent": 0.416}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7880778, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 77.577, "width_percent": 0.354}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 38 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "38", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.789715, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 77.932, "width_percent": 0.586}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.791308, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 78.517, "width_percent": 0.57}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.79289, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 79.088, "width_percent": 0.462}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.794252, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 79.55, "width_percent": 0.401}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.795591, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 79.951, "width_percent": 0.416}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.796886, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 80.367, "width_percent": 0.324}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.798157, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 80.69, "width_percent": 0.57}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.799581, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 81.261, "width_percent": 0.339}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 38 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "38", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8010042, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 81.6, "width_percent": 0.447}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8024979, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 82.047, "width_percent": 0.339}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and `category_id` = 39 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "39", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 609}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.804841, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:609", "source": "app/Http/Controllers/BudgetController.php:609", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=609", "ajax": false, "filename": "BudgetController.php", "line": "609"}, "connection": "kdmkjkqknb", "start_percent": 82.386, "width_percent": 0.586}, {"sql": "select sum(`amount`) as aggregate from `payments` where `created_by` = 15 and YEAR(date) ='2025' and MONTH(date) >='1' and MONTH(date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 617}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8064418, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:617", "source": "app/Http/Controllers/BudgetController.php:617", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=617", "ajax": false, "filename": "BudgetController.php", "line": "617"}, "connection": "kdmkjkqknb", "start_percent": 82.971, "width_percent": 0.385}, {"sql": "select * from `bills` where `created_by` = 15 and `category_id` = 39 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "39", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 624}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.807861, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:624", "source": "app/Http/Controllers/BudgetController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=624", "ajax": false, "filename": "BudgetController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 83.356, "width_percent": 0.493}, {"sql": "select * from `bills` where `created_by` = 15 and YEAR(send_date) ='2025' and MONTH(send_date) >='1' and MONTH(send_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 637}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.809339, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:637", "source": "app/Http/Controllers/BudgetController.php:637", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=637", "ajax": false, "filename": "BudgetController.php", "line": "637"}, "connection": "kdmkjkqknb", "start_percent": 83.85, "width_percent": 0.447}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.810825, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 84.297, "width_percent": 0.277}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.81202, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 84.574, "width_percent": 0.293}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.813248, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 84.867, "width_percent": 0.339}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.814599, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 85.206, "width_percent": 0.293}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 4 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.815847, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 85.499, "width_percent": 0.277}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 4 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 642}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.817195, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 85.776, "width_percent": 0.493}, {"sql": "select * from `purchases` where `created_by` = 15 and `category_id` = 39 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "39", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 651}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.818892, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:651", "source": "app/Http/Controllers/BudgetController.php:651", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=651", "ajax": false, "filename": "BudgetController.php", "line": "651"}, "connection": "kdmkjkqknb", "start_percent": 86.269, "width_percent": 0.647}, {"sql": "select * from `purchases` where `created_by` = 15 and YEAR(purchase_date) ='2025' and MONTH(purchase_date) >='1' and MONTH(purchase_date) <='12'", "type": "query", "params": [], "bindings": ["15", "2025", "1", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 663}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.820565, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:663", "source": "app/Http/Controllers/BudgetController.php:663", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=663", "ajax": false, "filename": "BudgetController.php", "line": "663"}, "connection": "kdmkjkqknb", "start_percent": 86.916, "width_percent": 0.478}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'income'", "type": "query", "params": [], "bindings": ["15", "income"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 791}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.822866, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:791", "source": "app/Http/Controllers/BudgetController.php:791", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=791", "ajax": false, "filename": "BudgetController.php", "line": "791"}, "connection": "kdmkjkqknb", "start_percent": 87.394, "width_percent": 0.524}, {"sql": "select * from `product_service_categories` where `created_by` = 15 and `type` = 'expense'", "type": "query", "params": [], "bindings": ["15", "expense"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 792}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8244462, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BudgetController.php:792", "source": "app/Http/Controllers/BudgetController.php:792", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=792", "ajax": false, "filename": "BudgetController.php", "line": "792"}, "connection": "kdmkjkqknb", "start_percent": 87.918, "width_percent": 0.431}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8431048, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 88.35, "width_percent": 1.156}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.845316, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 89.505, "width_percent": 0.663}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.849853, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 90.168, "width_percent": 0.771}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.852305, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 90.939, "width_percent": 0.57}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.8540978, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "kdmkjkqknb", "start_percent": 91.509, "width_percent": 0.586}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4669}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.888006, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 92.094, "width_percent": 0.709}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.889709, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 92.803, "width_percent": 5.609}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.894958, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 98.413, "width_percent": 0.509}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 15 and `seen` = 0", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.8969772, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:18", "source": "view::partials.admin.header:18", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=18", "ajax": false, "filename": "header.blade.php", "line": "18"}, "connection": "kdmkjkqknb", "start_percent": 98.921, "width_percent": 0.431}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4735}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 6233}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8994968, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4735", "source": "app/Models/Utility.php:4735", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4735", "ajax": false, "filename": "Utility.php", "line": "4735"}, "connection": "kdmkjkqknb", "start_percent": 99.353, "width_percent": 0.647}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 28, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\Bill": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=1", "ajax": false, "filename": "Bill.php", "line": "?"}}, "App\\Models\\BillProduct": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillProduct.php&line=1", "ajax": false, "filename": "BillProduct.php", "line": "?"}}, "App\\Models\\BillAccount": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillAccount.php&line=1", "ajax": false, "filename": "BillAccount.php", "line": "?"}}, "App\\Models\\Invoice": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "App\\Models\\InvoiceProduct": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FInvoiceProduct.php&line=1", "ajax": false, "filename": "InvoiceProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Budget": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBudget.php&line=1", "ajax": false, "filename": "Budget.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 135, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 70, "messages": [{"message": "[ability => view budget plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-942653715 data-indent-pad=\"  \"><span class=sf-dump-note>view budget plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view budget plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942653715\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.565428, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1340408043 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340408043\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.856335, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.856968, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.857233, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.85741, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.857697, "xdebug_link": null}, {"message": "[ability => statement report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-702420568 data-indent-pad=\"  \"><span class=sf-dump-note>statement report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702420568\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.857996, "xdebug_link": null}, {"message": "[ability => invoice report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2087043433 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2087043433\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.858266, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1952449643 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952449643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.858565, "xdebug_link": null}, {"message": "[ability => stock report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-201016952 data-indent-pad=\"  \"><span class=sf-dump-note>stock report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201016952\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.858832, "xdebug_link": null}, {"message": "[ability => loss & profit report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-680700035 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-680700035\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.859098, "xdebug_link": null}, {"message": "[ability => manage transaction, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1003897336 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003897336\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.859346, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1760551747 data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760551747\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.859604, "xdebug_link": null}, {"message": "[ability => expense report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1036282902 data-indent-pad=\"  \"><span class=sf-dump-note>expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036282902\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.859857, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.860113, "xdebug_link": null}, {"message": "[ability => tax report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2076037246 data-indent-pad=\"  \"><span class=sf-dump-note>tax report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2076037246\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.860371, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.860559, "xdebug_link": null}, {"message": "[ability => manage report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-374252888 data-indent-pad=\"  \"><span class=sf-dump-note>manage report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374252888\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.861198, "xdebug_link": null}, {"message": "[ability => show pos dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-931173156 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931173156\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.861433, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-894916375 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-894916375\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.862332, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-509402562 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-509402562\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.862865, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-289245056 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289245056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.863302, "xdebug_link": null}, {"message": "[ability => manage pay slip, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-431237609 data-indent-pad=\"  \"><span class=sf-dump-note>manage pay slip</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-431237609\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.863759, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1024058535 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024058535\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.864354, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-164210801 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-164210801\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.865001, "xdebug_link": null}, {"message": "[ability => manage attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1679130102 data-indent-pad=\"  \"><span class=sf-dump-note>manage attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679130102\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.865616, "xdebug_link": null}, {"message": "[ability => create attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-230465935 data-indent-pad=\"  \"><span class=sf-dump-note>create attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-230465935\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.866229, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1206259836 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206259836\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.866766, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-413405868 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413405868\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.867345, "xdebug_link": null}, {"message": "[ability => manage transfer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1311249477 data-indent-pad=\"  \"><span class=sf-dump-note>manage transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311249477\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.868072, "xdebug_link": null}, {"message": "[ability => manage resignation, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage resignation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage resignation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.868698, "xdebug_link": null}, {"message": "[ability => manage travel, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-42320534 data-indent-pad=\"  \"><span class=sf-dump-note>manage travel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage travel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-42320534\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.869271, "xdebug_link": null}, {"message": "[ability => manage promotion, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage promotion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage promotion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.869868, "xdebug_link": null}, {"message": "[ability => manage complaint, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1318472989 data-indent-pad=\"  \"><span class=sf-dump-note>manage complaint</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage complaint</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1318472989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870446, "xdebug_link": null}, {"message": "[ability => manage warning, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-353647466 data-indent-pad=\"  \"><span class=sf-dump-note>manage warning</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage warning</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353647466\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.871001, "xdebug_link": null}, {"message": "[ability => manage termination, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-410422450 data-indent-pad=\"  \"><span class=sf-dump-note>manage termination</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage termination</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-410422450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.87155, "xdebug_link": null}, {"message": "[ability => manage announcement, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-173269059 data-indent-pad=\"  \"><span class=sf-dump-note>manage announcement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage announcement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-173269059\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.872172, "xdebug_link": null}, {"message": "[ability => manage holiday, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-543413402 data-indent-pad=\"  \"><span class=sf-dump-note>manage holiday</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage holiday</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-543413402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.872778, "xdebug_link": null}, {"message": "[ability => manage document, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage document</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage document</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.873213, "xdebug_link": null}, {"message": "[ability => manage company policy, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company policy</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage company policy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.873711, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.874002, "xdebug_link": null}, {"message": "[ability => manage bank account, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.874251, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.87451, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-437194738 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437194738\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.874728, "xdebug_link": null}, {"message": "[ability => manage proposal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1733249693 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733249693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.875026, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1219508734 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219508734\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.875295, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.87552, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.875891, "xdebug_link": null}, {"message": "[ability => manage goal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876324, "xdebug_link": null}, {"message": "[ability => manage constant tax, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1284544479 data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284544479\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876535, "xdebug_link": null}, {"message": "[ability => manage print settings, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1676285450 data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676285450\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876723, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1699214815 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1699214815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.876896, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-622825530 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622825530\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.877053, "xdebug_link": null}, {"message": "[ability => manage role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-132820094 data-indent-pad=\"  \"><span class=sf-dump-note>manage role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132820094\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.877221, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-591031373 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591031373\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.877547, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-388431067 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-388431067\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.877788, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1259202589 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1259202589\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.878007, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-370677448 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-370677448\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.878244, "xdebug_link": null}, {"message": "[ability => show warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-523864294 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523864294\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.879007, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1410070216 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1410070216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.87974, "xdebug_link": null}, {"message": "[ability => show pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1782841956 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782841956\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.880551, "xdebug_link": null}, {"message": "[ability => create barcode, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1868812038 data-indent-pad=\"  \"><span class=sf-dump-note>create barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1868812038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.881325, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-305569587 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305569587\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.882119, "xdebug_link": null}, {"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-958158024 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958158024\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.882862, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-724646909 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724646909\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.883978, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1110259165 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1110259165\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.885107, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2059207036 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059207036\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.886015, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2005772252 data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005772252\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.886214, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1909382731 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1909382731\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.886938, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-44355745 data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-44355745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.887234, "xdebug_link": null}]}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget/eyJpdiI6IitUUkg3UTRnWGVYT1hkZm9Da0NPZXc9PSIsInZhbHVlIjoiZStjelcvdEROa3hBMXEzZVd5NkdKUT09IiwibWFjIjoiNzk4NWQxMmViNWIzMzdiYjg3Njg1OTI5MWExNzQ3ZjQ3MDBmODgxNDZhZWU1OWUwZmNmN2JiMWI4MTMxZmM1YyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/budget/eyJpdiI6IitUUkg3UTRnWGVYT1hkZm9Da0NPZXc9PSIsInZhbHVlIjoiZStjelcvdEROa3hBMXEzZVd5NkdKUT09IiwibWFjIjoiNzk4NWQxMmViNWIzMzdiYjg3Njg1OTI5MWExNzQ3ZjQ3MDBmODgxNDZhZWU1OWUwZmNmN2JiMWI4MTMxZmM1YyIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-520869851 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-520869851\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1931978494 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1931978494\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-673670198 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-673670198\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-77754674 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980072902%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpDTFhQRGkwS3M2YmMxejVoYm5nYXc9PSIsInZhbHVlIjoiZ1pvOFpKV3I4UHUzK2VBcHg5ZGtlYmJ4cU9wcS9QNGp1OTkyYkFjMlRmRVVCcWJmYU8wc0lSTTZyV1QrS1hHbFVvdGFLcTYvT3RtQ2FsanN2U0JxMU1LdWZrWnpsVFlBVGc3dUlvYXgxQVkyUE5GOWV1Z3lwSkZFaTR0dmFISVNkc3lLZEpNR2VjR0NuQTFiTTNLQ3NvMWxqR1FGR1Z0QTkvUzVPSElnSCsyeFRBYnFqZ05pYnVTWnRMTnJQZWZLL3dONWFveUZ5OWNZU2NCak8vWlhqcnkzVWY3V1BZZTB4K2JBbExLK3FnS3ZLa1psajJjTFd3aGFrMUFpTTRuQ3BJU09sSE5vMjN6U1JNbE44NEcvWXU0ekZvTkxCbXkyYXpaRUw3dStLbjhLalRHYWV1aG5JbEZqYWlpNDFPdzJ2WGVEdXZWU2d0R05iTDJiSmJvVFhGTHRnMHp0WE11V3dBSFRIY2hkOHJCb2xybk9DTWk2QU1wSXdkakdwSDZJcnZzcmp3TEFoZTdXaVJ2NEFlQnZEL3lwUjFSM3NmSXVVaUhOd29OaHJUenZLTW5nYzY4d1hsdE8rRVVFejNPY0pVa3RBM3VsNFlQREVvNG84M0Q2WFM5Ykg5V1lqMTQvWW5vZDlERUZZV3oyV2plRStHTW1YNmNtM2ZVUjV5NWwiLCJtYWMiOiJlZTg3NzBjNzFkOGM4MWQ3OWUwMDY3ZjY3NmRiNWVlZWQyYTYwOTQ1ZDE3YWU5OGM3NzZiYzY3NjEyYTU0YmY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJWVWUyZ3gvNXhPY0cvbjdOUkxWc2c9PSIsInZhbHVlIjoiQVBUbXBEemE2WkpFN0pMaFE1Yy9PcVdZMzNQQm5saGx3ZEE1Zkw1dW9JNVQ5cUtyV055U3NncnFIUUE0WnVDWCs4MURscVFUVDFXdlRIUlR5NS9SeFQ1ZWI0SC8vRkZ5NW9oY2VsSUxzQ0x0b3dQdzJJS1VYZWVpMjNMaDdnVjRjVmdmME45aGtFUEN4WmFIYytFZGdZazZlaWZMRjBJWWpoMTZkOEJHNWo4SkNocmVjV2Y3SmJoakVlUVFUbSt2VWNGQldVSnVxb3BVaUJ6bnM5QnpMMDFZZkZpbGwwN1QzcWhjTlBuL2pMSnkyYXJUVnBWQVF1TGNFTVBYd1ZWTms5c0Q4bklmZk9lUkdhTmZ0bGpBRVFicHhwcnVpS1d1aXQwUmREZHRLa3A4Wk1jYVZoUVVLdjU0bzQwZndtRDBJTXZtRFlrQ2RIZWR6ODhPT3R2c1hPVlkxM2pSdmlHc2NVcENIUVc5dm93emtRS0xKRXpzaENrTE03OHpyak1JRThqaTM0MW1ubjdsbnhCSC9KNXpVSUdPbkdIakFGM1RuZzlmdzFjMFloMDBsT0VTVWNxY1NCMjJGZjMrT1poSk5hc1Z5cnJNUmtBZTBQc0JpVGovWXNPOTVpZTVWK09STGM3ZTZDRGhIaTJ4bGxsTTVwdGlSSDNGUnhTUlYyM1MiLCJtYWMiOiI3MTk4NDI4ZTYxMzE4M2YwNTViYzg3Y2E4MjgxM2MzYWRjNDVhYjE3MmZmYjBmNGRmZDI1ODAwYmJjMDhkMDIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77754674\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-909453350 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-909453350\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1497162588 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:21:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRuNVQ0ays0THFlaHU5VURKSms3b1E9PSIsInZhbHVlIjoiR1AyY21wMm9lbFpsVEtFeFB2bDNlSE1KNXNGN0lkY25BdEQrSW1wZ1BhQ25aQUlURTI3OW5mZlVjd2VBUUo3ZHd0SWhIbHEvRmoyeXJ5bEkzNklHSVBwaTdHQkJsYmVyU3ZtVjhYNWFoRFJ0SkV3VHhiVThsaU56cHFXWEMwSXdib3d2TWpTYWpnRlNRckExVnNlSUNIMmlHSWVTekxIMG5QSUt0aEZBaGtjQmxrWW03L2lzT1htTXBMSm9zUFpmM0txcTdNUU5DMnF0MnRFZkpMWnVNVjRoNExQSTRNbzZQVjM4WjBhdEZGWUE2YVp4VXZnbC9rWXY5dFlORjJZNnNlWTc2SEdxSEU2aDdMUmdBdWdiQXFQalEwOUNFR1lTa2gzVnMvaDZ4a3JEWW1PYmlXcDg1ZldjV0REd1hsRUVISmY5L3dPUThVWXJzNjFJTTR6NjlyN3p5QUxyOFhJNDBEbVJMVWUxVkU3aTYzOWhodjFJRkpQcUxWQkY2dzVPTUVmVXdncDRibm55bkt5Y3hHanZqSGp5OUVHTlZCNWRyeUZNRUJySHRQZ25UU2cyK3k1ZnNxR0JWamQ1elp3eWRTRVNReVVYRUpwZGpiWkFTWTlNU1JGaE1Va2IxMXh1bS9oaVNiTFUxbGRFZjhYS0VBMERhU2ZYN2FqV0tzaHciLCJtYWMiOiJlNjU0MDEwNDI5MGIxOTJjYzM4NmFlNTc5ODgyOWRmNTc2YzkyMWE3MTYzMzQ1MmY5NGE3MTVhNDhkOGUzM2MzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlhkV0grMFgvTXhOS0E4UnI5RlRLZkE9PSIsInZhbHVlIjoiU0pHTHlPTmpuNzhxVmNMZldqNjVkVXRFbWRYeWw1d2t5VFV2MWpNVjh1MzFGSGNadjhZNFI2c3BNMy9OUEx0V0R5OG1Ga0lHd1VDUDRQRmthaW9Jd3JFUUxoc0ZCUVRZUjc0NzFwTkpybVd5aDFabkF1U01aZGlYSDM4MlkxdEVRTUNoaDE3SkdkSnZlbXh2ODBIcmFmZUI4M3FralVRR2N2SVg1LzI4M1F5aVJWbEJaVVhYeEdBL2x3ZHNKZXVuYkV4TXhLZU84ZnhJZlp4VGltaXdleHJ4RVhoU2IzeGRVQWRRbFRFd3FaS1Y3QTdvRzZHOTdYWktRT3FZc1JuUHFSWk5yNFpVaDhUK0E3NCtyTGtUZVdWeVQ2QWwzTDZQc0ppQzlyM2d3b2lSMWU4OWN2a3BhTjdtZ0JaenpXOVJtbWhHbFdOc1ZPSE5QVHE3dnFsNEFMaGV0ZUxtdUQ3a2prNCtmWmg4RVMvRUVMdTY3VmpGWkNZc1hzeFZFVDZYTVZlRFBxZDFDSis5TitnRXBRNFFuOTBCeHVxcXQ1K0dFMTFpZXFrS0xyeXVjVkVOTXNCNnZtTXRXVVZnY0RzNEVZN2d6RHg2eDhlWjRFckRMMk9LOTlXdW5lRkJsRW9OMGdMSjlGdS9aa0VaRWNIdDFvNkg5Q1RlTmpQRkw5NFEiLCJtYWMiOiJjZmQyNTNjMGY3MjFjMjNjMGFlYzlmZGNkNzczNDlmZGNhNWQ1YjY2N2EzM2I2MmJjYWE5ZDA4ZmY3MGUwZDc4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRuNVQ0ays0THFlaHU5VURKSms3b1E9PSIsInZhbHVlIjoiR1AyY21wMm9lbFpsVEtFeFB2bDNlSE1KNXNGN0lkY25BdEQrSW1wZ1BhQ25aQUlURTI3OW5mZlVjd2VBUUo3ZHd0SWhIbHEvRmoyeXJ5bEkzNklHSVBwaTdHQkJsYmVyU3ZtVjhYNWFoRFJ0SkV3VHhiVThsaU56cHFXWEMwSXdib3d2TWpTYWpnRlNRckExVnNlSUNIMmlHSWVTekxIMG5QSUt0aEZBaGtjQmxrWW03L2lzT1htTXBMSm9zUFpmM0txcTdNUU5DMnF0MnRFZkpMWnVNVjRoNExQSTRNbzZQVjM4WjBhdEZGWUE2YVp4VXZnbC9rWXY5dFlORjJZNnNlWTc2SEdxSEU2aDdMUmdBdWdiQXFQalEwOUNFR1lTa2gzVnMvaDZ4a3JEWW1PYmlXcDg1ZldjV0REd1hsRUVISmY5L3dPUThVWXJzNjFJTTR6NjlyN3p5QUxyOFhJNDBEbVJMVWUxVkU3aTYzOWhodjFJRkpQcUxWQkY2dzVPTUVmVXdncDRibm55bkt5Y3hHanZqSGp5OUVHTlZCNWRyeUZNRUJySHRQZ25UU2cyK3k1ZnNxR0JWamQ1elp3eWRTRVNReVVYRUpwZGpiWkFTWTlNU1JGaE1Va2IxMXh1bS9oaVNiTFUxbGRFZjhYS0VBMERhU2ZYN2FqV0tzaHciLCJtYWMiOiJlNjU0MDEwNDI5MGIxOTJjYzM4NmFlNTc5ODgyOWRmNTc2YzkyMWE3MTYzMzQ1MmY5NGE3MTVhNDhkOGUzM2MzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlhkV0grMFgvTXhOS0E4UnI5RlRLZkE9PSIsInZhbHVlIjoiU0pHTHlPTmpuNzhxVmNMZldqNjVkVXRFbWRYeWw1d2t5VFV2MWpNVjh1MzFGSGNadjhZNFI2c3BNMy9OUEx0V0R5OG1Ga0lHd1VDUDRQRmthaW9Jd3JFUUxoc0ZCUVRZUjc0NzFwTkpybVd5aDFabkF1U01aZGlYSDM4MlkxdEVRTUNoaDE3SkdkSnZlbXh2ODBIcmFmZUI4M3FralVRR2N2SVg1LzI4M1F5aVJWbEJaVVhYeEdBL2x3ZHNKZXVuYkV4TXhLZU84ZnhJZlp4VGltaXdleHJ4RVhoU2IzeGRVQWRRbFRFd3FaS1Y3QTdvRzZHOTdYWktRT3FZc1JuUHFSWk5yNFpVaDhUK0E3NCtyTGtUZVdWeVQ2QWwzTDZQc0ppQzlyM2d3b2lSMWU4OWN2a3BhTjdtZ0JaenpXOVJtbWhHbFdOc1ZPSE5QVHE3dnFsNEFMaGV0ZUxtdUQ3a2prNCtmWmg4RVMvRUVMdTY3VmpGWkNZc1hzeFZFVDZYTVZlRFBxZDFDSis5TitnRXBRNFFuOTBCeHVxcXQ1K0dFMTFpZXFrS0xyeXVjVkVOTXNCNnZtTXRXVVZnY0RzNEVZN2d6RHg2eDhlWjRFckRMMk9LOTlXdW5lRkJsRW9OMGdMSjlGdS9aa0VaRWNIdDFvNkg5Q1RlTmpQRkw5NFEiLCJtYWMiOiJjZmQyNTNjMGY3MjFjMjNjMGFlYzlmZGNkNzczNDlmZGNhNWQ1YjY2N2EzM2I2MmJjYWE5ZDA4ZmY3MGUwZDc4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1497162588\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1845420706 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/budget/eyJpdiI6IitUUkg3UTRnWGVYT1hkZm9Da0NPZXc9PSIsInZhbHVlIjoiZStjelcvdEROa3hBMXEzZVd5NkdKUT09IiwibWFjIjoiNzk4NWQxMmViNWIzMzdiYjg3Njg1OTI5MWExNzQ3ZjQ3MDBmODgxNDZhZWU1OWUwZmNmN2JiMWI4MTMxZmM1YyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1845420706\", {\"maxDepth\":0})</script>\n"}}