<?php
// This file was auto-generated from sdk-root/src/data/mgh/2017-05-31/paginators-1.json
return [ 'pagination' => [ 'ListApplicationStates' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ApplicationStateList', ], 'ListCreatedArtifacts' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'CreatedArtifactList', ], 'ListDiscoveredResources' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'DiscoveredResourceList', ], 'ListMigrationTasks' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'MigrationTaskSummaryList', ], 'ListProgressUpdateStreams' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ProgressUpdateStreamSummaryList', ], ],];
