<?php
// This file was auto-generated from sdk-root/src/data/privatenetworks/2021-12-03/paginators-1.json
return [ 'pagination' => [ 'ListDeviceIdentifiers' => [ 'input_token' => 'startToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'deviceIdentifiers', ], 'ListNetworkResources' => [ 'input_token' => 'startToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'networkResources', ], 'ListNetworkSites' => [ 'input_token' => 'startToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'networkSites', ], 'ListNetworks' => [ 'input_token' => 'startToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'networks', ], 'ListOrders' => [ 'input_token' => 'startToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'orders', ], ],];
