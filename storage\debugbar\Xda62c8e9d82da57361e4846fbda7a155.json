{"__meta": {"id": "Xda62c8e9d82da57361e4846fbda7a155", "datetime": "2025-06-26 23:21:15", "utime": **********.666812, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.26288, "end": **********.666831, "duration": 0.40395092964172363, "duration_str": "404ms", "measures": [{"label": "Booting", "start": **********.26288, "relative_start": 0, "end": **********.594688, "relative_end": **********.594688, "duration": 0.33180785179138184, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.594696, "relative_start": 0.3318159580230713, "end": **********.666842, "relative_end": 1.0967254638671875e-05, "duration": 0.07214593887329102, "duration_str": "72.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45045064, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02359, "accumulated_duration_str": "23.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6212919, "duration": 0.02266, "duration_str": "22.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.058}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.654139, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.058, "width_percent": 1.992}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.660217, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.05, "width_percent": 1.95}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget/eyJpdiI6IitUUkg3UTRnWGVYT1hkZm9Da0NPZXc9PSIsInZhbHVlIjoiZStjelcvdEROa3hBMXEzZVd5NkdKUT09IiwibWFjIjoiNzk4NWQxMmViNWIzMzdiYjg3Njg1OTI5MWExNzQ3ZjQ3MDBmODgxNDZhZWU1OWUwZmNmN2JiMWI4MTMxZmM1YyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1312145363 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1312145363\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-588629642 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-588629642\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2089770165 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2089770165\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/budget/eyJpdiI6IitUUkg3UTRnWGVYT1hkZm9Da0NPZXc9PSIsInZhbHVlIjoiZStjelcvdEROa3hBMXEzZVd5NkdKUT09IiwibWFjIjoiNzk4NWQxMmViNWIzMzdiYjg3Njg1OTI5MWExNzQ3ZjQ3MDBmODgxNDZhZWU1OWUwZmNmN2JiMWI4MTMxZmM1YyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980072902%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRuNVQ0ays0THFlaHU5VURKSms3b1E9PSIsInZhbHVlIjoiR1AyY21wMm9lbFpsVEtFeFB2bDNlSE1KNXNGN0lkY25BdEQrSW1wZ1BhQ25aQUlURTI3OW5mZlVjd2VBUUo3ZHd0SWhIbHEvRmoyeXJ5bEkzNklHSVBwaTdHQkJsYmVyU3ZtVjhYNWFoRFJ0SkV3VHhiVThsaU56cHFXWEMwSXdib3d2TWpTYWpnRlNRckExVnNlSUNIMmlHSWVTekxIMG5QSUt0aEZBaGtjQmxrWW03L2lzT1htTXBMSm9zUFpmM0txcTdNUU5DMnF0MnRFZkpMWnVNVjRoNExQSTRNbzZQVjM4WjBhdEZGWUE2YVp4VXZnbC9rWXY5dFlORjJZNnNlWTc2SEdxSEU2aDdMUmdBdWdiQXFQalEwOUNFR1lTa2gzVnMvaDZ4a3JEWW1PYmlXcDg1ZldjV0REd1hsRUVISmY5L3dPUThVWXJzNjFJTTR6NjlyN3p5QUxyOFhJNDBEbVJMVWUxVkU3aTYzOWhodjFJRkpQcUxWQkY2dzVPTUVmVXdncDRibm55bkt5Y3hHanZqSGp5OUVHTlZCNWRyeUZNRUJySHRQZ25UU2cyK3k1ZnNxR0JWamQ1elp3eWRTRVNReVVYRUpwZGpiWkFTWTlNU1JGaE1Va2IxMXh1bS9oaVNiTFUxbGRFZjhYS0VBMERhU2ZYN2FqV0tzaHciLCJtYWMiOiJlNjU0MDEwNDI5MGIxOTJjYzM4NmFlNTc5ODgyOWRmNTc2YzkyMWE3MTYzMzQ1MmY5NGE3MTVhNDhkOGUzM2MzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhkV0grMFgvTXhOS0E4UnI5RlRLZkE9PSIsInZhbHVlIjoiU0pHTHlPTmpuNzhxVmNMZldqNjVkVXRFbWRYeWw1d2t5VFV2MWpNVjh1MzFGSGNadjhZNFI2c3BNMy9OUEx0V0R5OG1Ga0lHd1VDUDRQRmthaW9Jd3JFUUxoc0ZCUVRZUjc0NzFwTkpybVd5aDFabkF1U01aZGlYSDM4MlkxdEVRTUNoaDE3SkdkSnZlbXh2ODBIcmFmZUI4M3FralVRR2N2SVg1LzI4M1F5aVJWbEJaVVhYeEdBL2x3ZHNKZXVuYkV4TXhLZU84ZnhJZlp4VGltaXdleHJ4RVhoU2IzeGRVQWRRbFRFd3FaS1Y3QTdvRzZHOTdYWktRT3FZc1JuUHFSWk5yNFpVaDhUK0E3NCtyTGtUZVdWeVQ2QWwzTDZQc0ppQzlyM2d3b2lSMWU4OWN2a3BhTjdtZ0JaenpXOVJtbWhHbFdOc1ZPSE5QVHE3dnFsNEFMaGV0ZUxtdUQ3a2prNCtmWmg4RVMvRUVMdTY3VmpGWkNZc1hzeFZFVDZYTVZlRFBxZDFDSis5TitnRXBRNFFuOTBCeHVxcXQ1K0dFMTFpZXFrS0xyeXVjVkVOTXNCNnZtTXRXVVZnY0RzNEVZN2d6RHg2eDhlWjRFckRMMk9LOTlXdW5lRkJsRW9OMGdMSjlGdS9aa0VaRWNIdDFvNkg5Q1RlTmpQRkw5NFEiLCJtYWMiOiJjZmQyNTNjMGY3MjFjMjNjMGFlYzlmZGNkNzczNDlmZGNhNWQ1YjY2N2EzM2I2MmJjYWE5ZDA4ZmY3MGUwZDc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1164808375 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164808375\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1636337087 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:21:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9SL1BzTnRLaWJXN1pDTGZUT255ZXc9PSIsInZhbHVlIjoiSUN5clNDOVBSa2dJZGI0c2cxUUZnc3ZhV2laNXdHSFgwTFZQTEdNSGxZRWtpczJxWEw3ZHZ6eU5XdGNadEw4WjVuMCsxdUdBVklqWGZSZkJTdmZjK2xTYW96OTIzdUdJdjRxTk9FZVJPVGYyMC90Q1dWVFp4OE5MRml0K3A0bkJoemsvVTgrYTVrMHFVZ0JqR3FXQUlFTjZaYy9URlo3TDhMTlZMNEx2SEdPQ3E5ZVBBZldTVzNNM0VrNDQrem1rQkJiVDBiSTF3MDdXSlh3aEpCTFh2V2E4VDNnN2FLY3NwaG52TldOUWVpVDlXTlZ0RTEwSjY3cDV3NnNVQzFpL1QyVWpPMDNkVmViNHoxT0tld050NWFzN0RlbVVnbzJqM2hoUSs1WXR5aDFaZENFQkxIYVEyT25aOTJGQmVzMDN3LzFZUjU5U1gyeW9QSGljZGdFR2xoVjNldGcxMzhqMmU3V1JNT3V1Z241QTlZL3RZam9FUGpsbnR4c1pyeTZYejVaRUN5dGkrdWxFYnFTU1crQ1RhSkdBeHltWkxVMlFFYUJDNEVFMG9hWWUzY2JZWkNqYTNPYmZsSi9TclhSdCs1cndjd2FEQWltS3k3RXlhczR6OFNMNjc5Mk15TFdKT1RSVU91TzZPYndqbkovSm94TnJqQjA0UlpHNU1LSHAiLCJtYWMiOiJlZWIwOTdlODEwNmU4MzQ1MjM1OTI1ZTg3ZWYxN2RhMjE0MWI0YTM3ZTRkYzZlOTNkYzEwYTI2MmI3MzAwZjJiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZyNllKeWtUVHAxdGJnMWFRbWRkR1E9PSIsInZhbHVlIjoidVNoQU5uQmk2dk5CbWVndXEvZWhkcXU2M2ZIclVLMnJRY2RGMTZoenJMMHRrbWJHODBHVm14NzY5QTFXWXlGcUJpQ1R2Yit2L3hGNVQrdkVGR3E0YUI2MjVyYUhlRlhGNlN1THh2RVQxdWxMYmNFcDBwcWpjV2VvcDNsQUl5ZVhxcXRQa3JUcVNsS21JZ1czVHlERm1TVDRVMGV0UTZWaUxiUU9NaklkYk1zTXFVVmV0NWFmck4wRmpmbitiMzVnNGtOWTFwTVBvR3RjamZWVm9GdDd2SG9sNFpTR1djTks0ZW5HOU9qU3BzN21ueEtIcTlpTGVBSlB0c0JTRXRNREx4SnJ2NER1UDJodDA2Qy9XR3VCOEVUYzNqWnoxMU9tMU5jRUNYOERzN21EbFVJRk9uL0RVaDFIVXpTMEdBWDVwNXhodE9mTkVyTlBINDFFclVIc1N3UlZLUmhZV3V1Z3ZFRGh1VUhFUDhwLzMwaXo0dzhCK2N3Z0pkQThtUU9FRENLSHBkWG5xWlRxc0RKNm1YSWYvMWNRaU5sbHVnQ2k4NWRUKytaYzlBUVhXclhQUzBMVFd3YTBKTnNOUUhjbGpWa28zeUdxczJUei9GckJ3OEpSNWJ5UWNZeG1vOHNneW1MZitUbUlyM0V4SzdOU3luWmVXZW94Q0R2cFVPSEkiLCJtYWMiOiI2ZjNjOTU2NTkzOWY1NzVmZGIxOTY4MjkzZjAwNWJmNDNmYWIyMDRmMzA4NDRiODhlYTVlYjYxNGU1YmFhZmVlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9SL1BzTnRLaWJXN1pDTGZUT255ZXc9PSIsInZhbHVlIjoiSUN5clNDOVBSa2dJZGI0c2cxUUZnc3ZhV2laNXdHSFgwTFZQTEdNSGxZRWtpczJxWEw3ZHZ6eU5XdGNadEw4WjVuMCsxdUdBVklqWGZSZkJTdmZjK2xTYW96OTIzdUdJdjRxTk9FZVJPVGYyMC90Q1dWVFp4OE5MRml0K3A0bkJoemsvVTgrYTVrMHFVZ0JqR3FXQUlFTjZaYy9URlo3TDhMTlZMNEx2SEdPQ3E5ZVBBZldTVzNNM0VrNDQrem1rQkJiVDBiSTF3MDdXSlh3aEpCTFh2V2E4VDNnN2FLY3NwaG52TldOUWVpVDlXTlZ0RTEwSjY3cDV3NnNVQzFpL1QyVWpPMDNkVmViNHoxT0tld050NWFzN0RlbVVnbzJqM2hoUSs1WXR5aDFaZENFQkxIYVEyT25aOTJGQmVzMDN3LzFZUjU5U1gyeW9QSGljZGdFR2xoVjNldGcxMzhqMmU3V1JNT3V1Z241QTlZL3RZam9FUGpsbnR4c1pyeTZYejVaRUN5dGkrdWxFYnFTU1crQ1RhSkdBeHltWkxVMlFFYUJDNEVFMG9hWWUzY2JZWkNqYTNPYmZsSi9TclhSdCs1cndjd2FEQWltS3k3RXlhczR6OFNMNjc5Mk15TFdKT1RSVU91TzZPYndqbkovSm94TnJqQjA0UlpHNU1LSHAiLCJtYWMiOiJlZWIwOTdlODEwNmU4MzQ1MjM1OTI1ZTg3ZWYxN2RhMjE0MWI0YTM3ZTRkYzZlOTNkYzEwYTI2MmI3MzAwZjJiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZyNllKeWtUVHAxdGJnMWFRbWRkR1E9PSIsInZhbHVlIjoidVNoQU5uQmk2dk5CbWVndXEvZWhkcXU2M2ZIclVLMnJRY2RGMTZoenJMMHRrbWJHODBHVm14NzY5QTFXWXlGcUJpQ1R2Yit2L3hGNVQrdkVGR3E0YUI2MjVyYUhlRlhGNlN1THh2RVQxdWxMYmNFcDBwcWpjV2VvcDNsQUl5ZVhxcXRQa3JUcVNsS21JZ1czVHlERm1TVDRVMGV0UTZWaUxiUU9NaklkYk1zTXFVVmV0NWFmck4wRmpmbitiMzVnNGtOWTFwTVBvR3RjamZWVm9GdDd2SG9sNFpTR1djTks0ZW5HOU9qU3BzN21ueEtIcTlpTGVBSlB0c0JTRXRNREx4SnJ2NER1UDJodDA2Qy9XR3VCOEVUYzNqWnoxMU9tMU5jRUNYOERzN21EbFVJRk9uL0RVaDFIVXpTMEdBWDVwNXhodE9mTkVyTlBINDFFclVIc1N3UlZLUmhZV3V1Z3ZFRGh1VUhFUDhwLzMwaXo0dzhCK2N3Z0pkQThtUU9FRENLSHBkWG5xWlRxc0RKNm1YSWYvMWNRaU5sbHVnQ2k4NWRUKytaYzlBUVhXclhQUzBMVFd3YTBKTnNOUUhjbGpWa28zeUdxczJUei9GckJ3OEpSNWJ5UWNZeG1vOHNneW1MZitUbUlyM0V4SzdOU3luWmVXZW94Q0R2cFVPSEkiLCJtYWMiOiI2ZjNjOTU2NTkzOWY1NzVmZGIxOTY4MjkzZjAwNWJmNDNmYWIyMDRmMzA4NDRiODhlYTVlYjYxNGU1YmFhZmVlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636337087\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-260565093 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/budget/eyJpdiI6IitUUkg3UTRnWGVYT1hkZm9Da0NPZXc9PSIsInZhbHVlIjoiZStjelcvdEROa3hBMXEzZVd5NkdKUT09IiwibWFjIjoiNzk4NWQxMmViNWIzMzdiYjg3Njg1OTI5MWExNzQ3ZjQ3MDBmODgxNDZhZWU1OWUwZmNmN2JiMWI4MTMxZmM1YyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260565093\", {\"maxDepth\":0})</script>\n"}}