{"__meta": {"id": "X42af6b3fa692420731b1da3af492205c", "datetime": "2025-06-26 22:42:36", "utime": **********.426882, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750977755.940792, "end": **********.426897, "duration": 0.48610496520996094, "duration_str": "486ms", "measures": [{"label": "Booting", "start": 1750977755.940792, "relative_start": 0, "end": **********.339021, "relative_end": **********.339021, "duration": 0.39822888374328613, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.339033, "relative_start": 0.3982408046722412, "end": **********.426899, "relative_end": 1.9073486328125e-06, "duration": 0.08786606788635254, "duration_str": "87.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45030352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02208, "accumulated_duration_str": "22.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.37753, "duration": 0.020550000000000002, "duration_str": "20.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.071}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4094162, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.071, "width_percent": 4.438}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.417988, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.509, "width_percent": 2.491}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1410451994 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1410451994\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1123338143 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1123338143\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2064044377 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2064044377\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1999907197 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977750219%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjkrTVdERGVyTzRzcFU1U01Zbk5aMkE9PSIsInZhbHVlIjoiTmpIRDFKVWFZQ3hyZDJlQlJ2bEpoRXF1bXQ5a3Vuejh5cUFyWFdaZEN3SE5GNmF0dHBRV0VUWmdpeUllOEVneGhFcm5VKytveThHalI5MXVKTWI0NWczcllMRk55ZG9scHdFeTZPSzJkS3FMS3pUSWRNRysxZ2JIY0lRZm0xa0NXUTEwU2NOWWpITzVWWU5DQStUSURYNThLL2laRVZaQldpVGZXNmdraHA5STJSclJYQWFJWmhVaUVnK1JaQkdvakhjaEwyUUZaUk02a0hCSjVQbk5CREJhcGZjbHpFVzJ1RGxiNlpJb3k3RmovYmJWWTBFSUw0djVxaW9Od1JHMk9PQjdVTmpTYUo2a0pvR2VzeE8zMSsvUDVQSFdKSXFNUDY1TFFONlBtOVBXUTNZUFY5ZnkyejRlNWl2VGFwLzI2eVpwc2NuUzc5RVJxbzlCMldqaVA5M0JOTi94Y0w4cDRQNVN0WU1kRFFjZnh5N1N3T3dpOGRSQUVqbW1uSElXVk5HVkFQaS80YVR4Z2Q2YlpwUEptclAvbXQvV0p6bUllYXo5OVEvQzEyZkhnUHNteVZ4UmtsY1Z4b2VXRk5sclRCcjZnU3lnb2xnMHFYbFdwbmMzR1c2V2xOK2VjMVpMMmVhNUJ6T1N5aUltSFVJM1FZbElRMnNpQ0RTYWFFMkgiLCJtYWMiOiI3ZmQ2MjQ2MGZjMWQzNWQzYjFjZjczYzIyMjU0NThjNjhkMWQxZTdkOTMyNjVmMmJiYzMxYzk1NWRiNmVlYTUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhoUkFyVmVHWElZV29tQVc4MFhTVkE9PSIsInZhbHVlIjoiRFN6N0JLQyt2Z3c2L0phMFhHdC9XTUF0Y212U05lREJVRzl6cVlaY0RNWG1IS1NhYnBlOG9qVThwUFdmakJ0TzhCOWQvME9tVUF1aHRUTDAwRGJTcTZlaGtCZ1h1TEhNWm5mMkhqRkhsRldMS3A2Q2VGMGpHcCtpSko4NmpMdVRVU2ZHdlNGMDhxeDc3V0FDWU1ZOTBmVmROM3MwaW4vdjA3VEttSzhuRGJObWlaV291YXZnUnFoZTJzU0paWXp0UUZYUUlLcU14dlNNUnFVc2FxUUVuTFBaeTZIRzlBdmR3bXltQlNVZm0rY3JOd2h2M1VGMG9HRnRZaE9SdlJLQlhEU0NaeU1qNlRsS3E5REVQUUZ5VDVHL3R2UHM1bTV5MU9rcTh4M0UvbVpWZElCelFLSjF1Y2dCMHJ2cTBKWmVwSGVSbjdKRGwzZTJ4bHYwa1pydHorOGpqREkwdUNRSk9rd2Q0V1ExbWgyMlo3bjhTdkc0VGdWSy9YN2tkYTFrMVNCNm5oSFNZWm4zYlUyV3V4S1NLaFBTZWJ1QXJFTjdZYUFuWEptR254UDdGeUg2MFZLanJMZ1R1eTN4RC9vT1FOcFZvM2J3ZDZYSE1pT1JMTmpjMGdnUXZYa1p2ZnVSSENwdkhUelJtMVhmdS9wRjlzT3R1M3BXYURLeVhtS2UiLCJtYWMiOiJjOWMzYWVmMTA2NGQ0ZmY5OTQzMmFkMjI3ZTMyYmZmZTNhYWQyMmY0NzBhMzhlYzgyNjc4OTQzMDQwMjFhNDAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999907197\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-229560632 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229560632\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2052022495 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:42:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZNazNDUldoU2U3WGQ3b2RZRGdDc3c9PSIsInZhbHVlIjoicHM4THJDcW5uZkNLMGV1SWZudm9WenRycmhlWnlyNHJ1R3NFRHRmZjlaSk1POHYrRmQxVUM1eTMwK0xVVE42SWd6aEpZMTk3alhzWWJ3V2wzN29JcVNVRDFXcDhzRk1iUkhWeW1JTVNvWE1SWjZ5cDNXWjJ3cURocGpGK0cyNE8rcVlQSm9ZTGFCZ2w0a3Byb3ZMWTFhaWd1TnhMeUpnblZBTGovSVNONTNvSDFUcmhjcnRrQXJmTGNJTThqbUF4bFBqbWYvZEpQcW9tUEpwaEVud0ZSZHdUUFY0TlRDdkRKQTJodmhGclhDdzNhZmUrV0Y2UDdiM2U4TUx2eFZLN2VEZnZoZXZOWjUyMHk2TzVnSktCTnlLblpZUDRUWjd2dTc3UDRqdld2eTd6cjNSdFg3SVE5c1E4akpXRitQcjZVVitvQ0NBRzZUanl6cGlhQVUyRk1HRjl6Q09iODVycTZUYzZ6dHBoTVNKVGJuem4reDhML3hRUXNRUDI3SGZaSFQyMHVBRURhcDZwL0JIWENYU2N4d1dwUkZDc3VYSmZxM3pEL1dpMmtSM3AwK1QrYnc1c0JoSzZoMkFGM0l2Wkl2MElTd1oxWDBpK3IwT1M2RTdNRk12Rmo2MXBveTArV1BWaDdNLytjdGo5YlIvVmlmT3lZNW10ZkhGSEw4YS8iLCJtYWMiOiIzNWE1ZTQ1ZTk5Y2EwZTYxZmFiYzZkMmViN2IxNDM0ZDY5OWJhOWMyZjFhYTQyZGNiMjBkYWNmODc5NDkwZjExIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVwSFJBSWtFSkFEOXN5ZERUeGdNNWc9PSIsInZhbHVlIjoiSmFwZWR5V29KWVRiNU5kUEwzQkhpdjh5T0tjRlc0S0tacnk1WURVeGJ3VEZzZ3JFdU9JdmppcW16ZFdVdlhvUm8rK1RoSWJZN0V5bG9QbmdjL3NCYVFNRU1LOVZtVGFXWUpKbVlZWU5Cd0Zlb2VRVWNKYXJ6Rm5VTWplNkNCUjhCdEhySjFHTXI2dkcrSEVJMEh3VXlsanlWWk14dEUyU1pXL001a2pLcHd0QlVQZEpCOENXazZXYXdtTkRzYkZPMnVTSkZtbWhlKzNUZC9Nb09mN0svNnBDMWprOU4yWlFJOS82TDhVdGUyYnlMK0xCc0xDYVBXOWplZEZ0T0tvbUxrVlZJaGxBZnoxdDhSMDNmbU5UdUdGYWptanNzcm5tRGxSM3NJK0d1OWtHaDl4VE5DckpPd1dDYjBIN0NJeDZCZGhaalJZWDF5Rzl6RFVSR0ROa0tEWHBLS3RYZXhiOVNnbVFxZWx0Skg1cVpOZGg2bWJmb2FXcDFQN0lXVUhaeTgvU2dBMFdsTGlUbWZJSXBucVNROXpGSnI2MlhaUXRJQStPR2pzQlVkWERVUjhKL3loeERkUHpHdU5Eb2FJb2JiQ3BnNWJiUG9MVndJV0lPdDVMZFRGL3NSc01FNmtETVV1akY1aHJGbFlZTXZqQzlraUNNMlJlWGJtTlF3ckciLCJtYWMiOiJlNGExMWEyMzU2NDBiODgwZGVlYjM2OTIyOGNiYjdjMTVkYTY5ODc1YWFmYTUwOGIzOTdlMTM3YjZjMDQ2NDZlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZNazNDUldoU2U3WGQ3b2RZRGdDc3c9PSIsInZhbHVlIjoicHM4THJDcW5uZkNLMGV1SWZudm9WenRycmhlWnlyNHJ1R3NFRHRmZjlaSk1POHYrRmQxVUM1eTMwK0xVVE42SWd6aEpZMTk3alhzWWJ3V2wzN29JcVNVRDFXcDhzRk1iUkhWeW1JTVNvWE1SWjZ5cDNXWjJ3cURocGpGK0cyNE8rcVlQSm9ZTGFCZ2w0a3Byb3ZMWTFhaWd1TnhMeUpnblZBTGovSVNONTNvSDFUcmhjcnRrQXJmTGNJTThqbUF4bFBqbWYvZEpQcW9tUEpwaEVud0ZSZHdUUFY0TlRDdkRKQTJodmhGclhDdzNhZmUrV0Y2UDdiM2U4TUx2eFZLN2VEZnZoZXZOWjUyMHk2TzVnSktCTnlLblpZUDRUWjd2dTc3UDRqdld2eTd6cjNSdFg3SVE5c1E4akpXRitQcjZVVitvQ0NBRzZUanl6cGlhQVUyRk1HRjl6Q09iODVycTZUYzZ6dHBoTVNKVGJuem4reDhML3hRUXNRUDI3SGZaSFQyMHVBRURhcDZwL0JIWENYU2N4d1dwUkZDc3VYSmZxM3pEL1dpMmtSM3AwK1QrYnc1c0JoSzZoMkFGM0l2Wkl2MElTd1oxWDBpK3IwT1M2RTdNRk12Rmo2MXBveTArV1BWaDdNLytjdGo5YlIvVmlmT3lZNW10ZkhGSEw4YS8iLCJtYWMiOiIzNWE1ZTQ1ZTk5Y2EwZTYxZmFiYzZkMmViN2IxNDM0ZDY5OWJhOWMyZjFhYTQyZGNiMjBkYWNmODc5NDkwZjExIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVwSFJBSWtFSkFEOXN5ZERUeGdNNWc9PSIsInZhbHVlIjoiSmFwZWR5V29KWVRiNU5kUEwzQkhpdjh5T0tjRlc0S0tacnk1WURVeGJ3VEZzZ3JFdU9JdmppcW16ZFdVdlhvUm8rK1RoSWJZN0V5bG9QbmdjL3NCYVFNRU1LOVZtVGFXWUpKbVlZWU5Cd0Zlb2VRVWNKYXJ6Rm5VTWplNkNCUjhCdEhySjFHTXI2dkcrSEVJMEh3VXlsanlWWk14dEUyU1pXL001a2pLcHd0QlVQZEpCOENXazZXYXdtTkRzYkZPMnVTSkZtbWhlKzNUZC9Nb09mN0svNnBDMWprOU4yWlFJOS82TDhVdGUyYnlMK0xCc0xDYVBXOWplZEZ0T0tvbUxrVlZJaGxBZnoxdDhSMDNmbU5UdUdGYWptanNzcm5tRGxSM3NJK0d1OWtHaDl4VE5DckpPd1dDYjBIN0NJeDZCZGhaalJZWDF5Rzl6RFVSR0ROa0tEWHBLS3RYZXhiOVNnbVFxZWx0Skg1cVpOZGg2bWJmb2FXcDFQN0lXVUhaeTgvU2dBMFdsTGlUbWZJSXBucVNROXpGSnI2MlhaUXRJQStPR2pzQlVkWERVUjhKL3loeERkUHpHdU5Eb2FJb2JiQ3BnNWJiUG9MVndJV0lPdDVMZFRGL3NSc01FNmtETVV1akY1aHJGbFlZTXZqQzlraUNNMlJlWGJtTlF3ckciLCJtYWMiOiJlNGExMWEyMzU2NDBiODgwZGVlYjM2OTIyOGNiYjdjMTVkYTY5ODc1YWFmYTUwOGIzOTdlMTM3YjZjMDQ2NDZlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2052022495\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}