{"__meta": {"id": "Xfbc4e18be0708c7de62826a4a4aa1a3f", "datetime": "2025-06-26 23:20:47", "utime": **********.699356, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.273235, "end": **********.699374, "duration": 0.42613887786865234, "duration_str": "426ms", "measures": [{"label": "Booting", "start": **********.273235, "relative_start": 0, "end": **********.644715, "relative_end": **********.644715, "duration": 0.37147998809814453, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.644724, "relative_start": 0.3714888095855713, "end": **********.699376, "relative_end": 2.1457672119140625e-06, "duration": 0.05465221405029297, "duration_str": "54.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044440, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00227, "accumulated_duration_str": "2.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.677898, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.604}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6878638, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.604, "width_percent": 17.621}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.693195, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.225, "width_percent": 12.775}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-532875607 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-532875607\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-212604188 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-212604188\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-564295983 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-564295983\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1421525455 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980041966%7C18%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlraExWTGtoMEkxVGxQQzhzQnNVOFE9PSIsInZhbHVlIjoiaFNVQWRHd2JRbkF6cFI3Sm5WLzdQV1IxTVYxNU11TU80ZlpqZGMrTjdtODY3OVFmU3Rtc3RnU3VIOVlGdzI3b3RwUGJ5TURZRmFUcWljQlhabnNaQU5WR3A2b2xaa01uM1JFZjBHdGJUN2FsbmZBM3Z5RkpwZmhBbGQzbzdvTlI3SVNxK2J6WEZFd3VPdjZBdmVPcG5QWFkzUUlqZ1NqVUNEVENPaHVuZUNaN1F1OTA4OVRIRi9CZTR4dGhMUUU3K2FPQ2kwT1NPOXlPTXlWZWMwcHAvT2ZRcUI2TDJJMmg4b2ZyUlVsdHRhY1FUTVdWRHRXZjU3Yk4rV2REQTFvVUR0b3lCcjNmU20wQWNPZ2lvd0F6UDkwTk9vRDhrcjFlTEtyTkFEenhhOTlRUjladEZuUFJqQjNaRFdONHBtQTgzUjAzS3JCa2wwdFl4Z1lMYnplUzh4b2IyT0ZpWFVEM0t5RmZuMGhydTRDR1B3SHpSWmk0QlJvWmQ1SUI4SkVEM0lVZit4NUF1c3ZPSkVEcEVCTGdocmNMbWtFUzVucmRHNUl1RVNQN1MwM0N4RTRqU3FTcEVlVkNyNEdHYnFIRHcvaE9zOXZtdWE5NGpyS0ZEaU9maXd4VmRwSUxzbVBndy9IbmF3aHBaNU9lTGlTMWFqck1HTXJMNS9KNEFDSngiLCJtYWMiOiI1OTBiYjNmNGJiMTcxNTJiMTIyZGY1MzM0MDA5YmFhZjZhNzdjZmZlZGI5Yzc1YjEwMWRhZmFkNDIzOGVhZTlkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlQ3NEV6dk0vOTNhK2xXQm81UGdVMlE9PSIsInZhbHVlIjoibDRJR1F1TEVPc1FYVVRtbUFjOVJvUm4vOVB5SGRzMk4xTCtqZkVlV3hjbFBLWStpNXVTMGhzaDVOcXFBdUcxc1RRS3FsdFpUdU9RT0lRenhMYmpsdGp5aUxnVlhRYnJzQXlqTmZ0enBkVlRrVFZ3QXdGeGpYUjJyeVdWV09zZ2w2dXlPa01xL2Jnc0VTUk5HVzlYWmZtRkR3Ylc1UnlUdTJvVnRrS1dhUGs0UUR3ZGVwWDljSGV5VWZVNy9adDhvM25rRlVMNEkvS1IyY0U1NitLWjB6Qk16WUp3NUIxc3A0czFvdHN0SGNKWDU1b2VCRHZ4YTZmRjJYMEJLSzk5aklTbXQ0K0sydDd6dll1R05MOWM5djRTSWJjNldXcnk4OGJ0V0lpOXBsWlBSbHYrelFCOTEzRjFlT3NIVk40Y3ExWkhtcGp4L29EV1dtd2hCMndUS2xoa2hHdFMxV013NW9ya3VWZU5FUGd1K1N2aFFVcXdma2hNdlhMZW9naHBmbUw5L1poYVV3d09pY1BMdjlnaHh0UXgrRkRqWExUUUtGQ0ltR0xZa3l2S21OcEtpMTUwTVhvK0p6U3BTN1dJdi9PeVM3SlNUcWFHUkN3RTR5WHdrZFdxZzhoMysyU1grbjZtSU56R1V2OWRFWFpkRTlMQTVmemFaR0F3QWF2eGYiLCJtYWMiOiI1OTgyNDE2YTlmYjg2NTE4YjNhMzU4MWYyNDBkN2ZhMTA5ZDA1MTVjNzg3NTJiMmMyYzQyMGFhYTY3NDExNzA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1421525455\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-368174739 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368174739\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-684312876 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNuQXR0YzdpUlBscjdva09vS0FyeUE9PSIsInZhbHVlIjoiaXZ5S3hkUWRCdnN5TXU5ZHdLWGQ0Rm1GVnRBVmV6SU4zUnVUZWx6WkJnUHBYTkRpL0dxWWE4Z0pTMGdwU3dtWFpPTU1HNkM5UWRLUGVINk1UYTFkVmFOK3FjV0JLS3psbTNQTzlXRi9FWG9VSXRCcGhZc05tOWFkYURzbEw0bTNsTlNxb1JNSDdHS3dEU0M1UjJMT1Y4VklJV25hT3BrTDF5VkVTR2JKR3V3cFJ6MkxJNzh6aFg5U1VPaVl1ZHFJYTF4RnZKZ0hUYml6V1YrNFNEeXpHMWduYXFkbmRaZzRzbTBSc1B3QVkrMmFHMkViakxFeUNrREFrRytOWnk2S0h1T2pxUXhQUHlGM3NyaHArWk9aRFhzN0VJWEJ6QzBnNUhWRWZRUU8yZTlrT3p4MFNZQXlXRXFNeUs5ZTZIbzNnY2JEc0d2OHFPb3RmQTR5UHhvNUZOZS9mN2tWejJWYXFwbTlaVkxYc09TOTlmOUNhM042Yi81cnU0emRIVnJLbU1TSXRUcGZPQ3RaOERuNmFjd0FPWGJ4N0VPeG1OOENVTENFaTFRek5UcmJmamNaa2RjaTFWa255VjN4V2ZzNkxWVlBIRTRFK05TN1Nab0tQalBPcjR5STFwdjFOb1pXb25Nd0xGa09Fa2IrVnJGc2Z0L1FBdkQ3WHRUYVdxZHgiLCJtYWMiOiI2MjBiZjBhYTBkNGE3ZDU0NGJkODI0NDA0ZDRjOTkwMTA1ZDYzZjFhNDkyYTE1Y2QxOTc0NDlhY2RhY2EwMjk2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilh0eE5JdUxXMDA1ajN4am5TeVRmUnc9PSIsInZhbHVlIjoiSlZKaDcwVWd1M0pKb3llYWtBdzArZUxuYktBSG53NEp3VmwyN29YekdoQnlERG56Wno2TUFlU2VkS1REVTFGRlJGaU1kdGhDbzBvRldGQkJ2WFBEZjRlT3liSzU4NjZ3OFlRdytvbExkcWhCM21TcVFEVXlLTWc1bXhWb2ZBRGtQdHBySEJsblgyNjhyeEFmdkpidkRnREtuQWh1Vy8zbElTOU5xenNVcG5vT1ZCRHd4OFVUU2NQY08rcFFONUZydFRCbzVBVzl4bTB1blAzU2d0MnM5T2IxL0FEdlZkVVQ2NUlJNnUrSDlFQ2w1TzlSUVVMTSt4cGwyNlJNZlRqOHFud1M5VzhqZ0tGaEFkRjFFS1NyZHNqaUFLTWhnNVVaN28vRytVc3FIY3RwalZ4MXY0Rm9tbHpwU0NoTTIzbG5YNFFoeGFFejhGUE9sVVlkdVpzcWFqbG52enlZM25zWFJHTUk3MlowK3NTeWxhdUkxd3ZwYVdybkxXTWNSNVJqMVUwck9Rdm1hVnNHMHN0TXJrSzlkL1N2NDU0MnZubWppOEYrWXJWQi9aQU8ydEJjMURjMnBIR09FZ2FQRFBDd28rZnRIZDM2MmxVQUgrOU1YVjNQS1N3MXlVQ1pTZDRLNGlXOXZNUVlpV00rNFdPN3NiMmtDS3E4YVB1b25DdVoiLCJtYWMiOiI2M2JmZjkxZTA2YmI1NDJiZGRmZWMyYzNlOGIwNjk0ZWQzYjQ4N2JiZjNiZGVkYmUzOWRmNmZkZGIyYzg4YWM0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNuQXR0YzdpUlBscjdva09vS0FyeUE9PSIsInZhbHVlIjoiaXZ5S3hkUWRCdnN5TXU5ZHdLWGQ0Rm1GVnRBVmV6SU4zUnVUZWx6WkJnUHBYTkRpL0dxWWE4Z0pTMGdwU3dtWFpPTU1HNkM5UWRLUGVINk1UYTFkVmFOK3FjV0JLS3psbTNQTzlXRi9FWG9VSXRCcGhZc05tOWFkYURzbEw0bTNsTlNxb1JNSDdHS3dEU0M1UjJMT1Y4VklJV25hT3BrTDF5VkVTR2JKR3V3cFJ6MkxJNzh6aFg5U1VPaVl1ZHFJYTF4RnZKZ0hUYml6V1YrNFNEeXpHMWduYXFkbmRaZzRzbTBSc1B3QVkrMmFHMkViakxFeUNrREFrRytOWnk2S0h1T2pxUXhQUHlGM3NyaHArWk9aRFhzN0VJWEJ6QzBnNUhWRWZRUU8yZTlrT3p4MFNZQXlXRXFNeUs5ZTZIbzNnY2JEc0d2OHFPb3RmQTR5UHhvNUZOZS9mN2tWejJWYXFwbTlaVkxYc09TOTlmOUNhM042Yi81cnU0emRIVnJLbU1TSXRUcGZPQ3RaOERuNmFjd0FPWGJ4N0VPeG1OOENVTENFaTFRek5UcmJmamNaa2RjaTFWa255VjN4V2ZzNkxWVlBIRTRFK05TN1Nab0tQalBPcjR5STFwdjFOb1pXb25Nd0xGa09Fa2IrVnJGc2Z0L1FBdkQ3WHRUYVdxZHgiLCJtYWMiOiI2MjBiZjBhYTBkNGE3ZDU0NGJkODI0NDA0ZDRjOTkwMTA1ZDYzZjFhNDkyYTE1Y2QxOTc0NDlhY2RhY2EwMjk2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilh0eE5JdUxXMDA1ajN4am5TeVRmUnc9PSIsInZhbHVlIjoiSlZKaDcwVWd1M0pKb3llYWtBdzArZUxuYktBSG53NEp3VmwyN29YekdoQnlERG56Wno2TUFlU2VkS1REVTFGRlJGaU1kdGhDbzBvRldGQkJ2WFBEZjRlT3liSzU4NjZ3OFlRdytvbExkcWhCM21TcVFEVXlLTWc1bXhWb2ZBRGtQdHBySEJsblgyNjhyeEFmdkpidkRnREtuQWh1Vy8zbElTOU5xenNVcG5vT1ZCRHd4OFVUU2NQY08rcFFONUZydFRCbzVBVzl4bTB1blAzU2d0MnM5T2IxL0FEdlZkVVQ2NUlJNnUrSDlFQ2w1TzlSUVVMTSt4cGwyNlJNZlRqOHFud1M5VzhqZ0tGaEFkRjFFS1NyZHNqaUFLTWhnNVVaN28vRytVc3FIY3RwalZ4MXY0Rm9tbHpwU0NoTTIzbG5YNFFoeGFFejhGUE9sVVlkdVpzcWFqbG52enlZM25zWFJHTUk3MlowK3NTeWxhdUkxd3ZwYVdybkxXTWNSNVJqMVUwck9Rdm1hVnNHMHN0TXJrSzlkL1N2NDU0MnZubWppOEYrWXJWQi9aQU8ydEJjMURjMnBIR09FZ2FQRFBDd28rZnRIZDM2MmxVQUgrOU1YVjNQS1N3MXlVQ1pTZDRLNGlXOXZNUVlpV00rNFdPN3NiMmtDS3E4YVB1b25DdVoiLCJtYWMiOiI2M2JmZjkxZTA2YmI1NDJiZGRmZWMyYzNlOGIwNjk0ZWQzYjQ4N2JiZjNiZGVkYmUzOWRmNmZkZGIyYzg4YWM0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-684312876\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1077567357 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077567357\", {\"maxDepth\":0})</script>\n"}}