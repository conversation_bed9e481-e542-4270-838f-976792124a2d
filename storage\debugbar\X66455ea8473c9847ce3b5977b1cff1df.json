{"__meta": {"id": "X66455ea8473c9847ce3b5977b1cff1df", "datetime": "2025-06-26 23:20:50", "utime": **********.297973, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980049.8816, "end": **********.297987, "duration": 0.41638708114624023, "duration_str": "416ms", "measures": [{"label": "Booting", "start": 1750980049.8816, "relative_start": 0, "end": **********.250332, "relative_end": **********.250332, "duration": 0.368732213973999, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.250342, "relative_start": 0.3687419891357422, "end": **********.297988, "relative_end": 9.5367431640625e-07, "duration": 0.04764604568481445, "duration_str": "47.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00276, "accumulated_duration_str": "2.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.27642, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.286519, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 75, "width_percent": 13.768}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.291718, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.768, "width_percent": 11.232}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget/eyJpdiI6InAzMzVvWHBjZ3htWVJTc21RMmxleFE9PSIsInZhbHVlIjoiYmwzaHQxK1RGRnhXaE5sam5NOXBLZz09IiwibWFjIjoiMmZiNGY5Yzc1ZGU5MjBlZjYyMmZlMzg4ZDdmOTQ1NjIzYzgzNTEyODFlNDc3MGY5ODUwMmZmMjRlNGRhNGU5OCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1033631445 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1033631445\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-975716253 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-975716253\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-427213754 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-427213754\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-566487344 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"229 characters\">http://localhost/budget/eyJpdiI6InAzMzVvWHBjZ3htWVJTc21RMmxleFE9PSIsInZhbHVlIjoiYmwzaHQxK1RGRnhXaE5sam5NOXBLZz09IiwibWFjIjoiMmZiNGY5Yzc1ZGU5MjBlZjYyMmZlMzg4ZDdmOTQ1NjIzYzgzNTEyODFlNDc3MGY5ODUwMmZmMjRlNGRhNGU5OCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980047617%7C19%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxQcDRSdWhvNmR3bTQ5L1dCS05TVXc9PSIsInZhbHVlIjoiQml5NGZkYXRLQUNMNTczdmtxM3NEaXEzN2V0RUJVWnFFanRndC9ocEdTVTRJVkZrcFMyV1p0dzdrMFo1c240ck5pakJpeEhsM1JKSHdvdEJ3NmcxUERFb1RHSmJYclN5WFBXSkNsNlZ5TUVteXBPdnlQekpaenJLanpUN0wrYmtXWWdGN1djTTlzOE9oc0NNb0k0WHpEYzFjaUJQOGRzSmdEQlVUc1U4eHNaTnRhL1oxNFBva0hJYzNscS9wTWxiQnI0ajRtTmpzRVEwOUpxR1dUWkRabEN6Sjd4UnNGOU5pY25kMUsvUGRjc3Vna0tDcFRkRjlsMEhTU3MxY3Vka3cvWGdGOEF5YlF1bHBHTG9TbFFUL3FNQVlQK3hQUDNEYjVoUjIzRU13WmpBZlRxVHZRZTJOY2k1ZkVGajVlbm1EdFYxQkVadUpBa3VIQ0FVd0RnVjhDcjM1VDNpempBajAraHBCT080T3pha0VuenJUUjRNSWdlS0tPUkx1RHhBbWJrN3FIMkhLY1VCTGhZc011MVBaa1pzTm9hcTRTVUNNWUV6aGRyNDRib3VJT290cmVTd0M2aHVPMDErUzNJRjNyY05jaXExSXZKbWowaHVHUzJQSmI4RTR3RlEvYUFwcU1aMTdTakZ3OVpPQy93M2pwamduaXk4dzBIZE82TmYiLCJtYWMiOiJmYzkxNDFiMmQ3NWExMzE0YTFlZGJjMGMyNjUwM2JmMDVmNjliZTU5YzNiYmM0ZTMxYjBmNjRlNGVkZGFlMjZmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllBTm1FVENJNnNnQ3lKbmxoVm42VXc9PSIsInZhbHVlIjoiQjNwam5TblhYc2lCVU8zWEFzRzJXSUQ0cVF6eG9CN3lRaW5vTFVyRUk1c0ViUmxvdVhqVkw2SEdJa04rUUVPcmVWU0hNWW1ydnVSVDRURmUxblExK0hyR29sSncwekowQmxkUDZlY0xwMFRJTU8vTUsrQmRxQ1hGL3p5WGhIZ202ZWZpL0NtVlVXNnVmKzNkNWVvOUQwZXN6WVNVSE9HUVVEU3pNTHFTdHgyak8rQ1Yxc1d0aVJWT0hkQXdPUTZTdk5sTEs0ZGJOcWtzMFVJSElJbVJjYlV1SVRNd3lOQWgwYmJFVUVkTWtZZU1lU3pmSnEzNEp4amlRcEZZNm5rZnk4aUJjR2NTM3JVT3RPMDJnOEEwb2xxTVlSdzh6T0hWYmQ4Rm5ZOXovaUM3Qk5UY1VGYzdCdVY4QmR3RWkxVDNtVkRDdlY0TkFpNlp2NmFMdjl2WDBwVE5WMGVPdEdYbFhUZnF4cmU0TGhXZFNlYmwyQ0VHUnVmY0JLdWxpajVsbzlOQ2dxanBFNUFBSS9iWERvZU9TdlhHeTVES1ZTc1l2bk1SSWhqOTI4NTBXSzViSUcrRkwvMXJlcmRQbXhxcUlYM3FjZ2xUTnY5SnROa2xCUEpNMEdiRFd4T2pDaEVaMHRMUWZiWjJ1RnRXWnErR0kvOGViZjdSdldQT2lJNWUiLCJtYWMiOiJiOWI3NGY1MDYwMDgyNDJiMzFiNDI0MjY1Y2U4YmJkOWI3YWU0M2UxNWNlMjEzOGMyN2E1OTU3MGMzZjlkMTYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-566487344\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-864955058 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-864955058\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-614892483 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhNSnFuVG00cG4rQ1dHeDBHbWNONXc9PSIsInZhbHVlIjoiVjJrdFdxeHNCbHhyVnVaSklYSGwzcXRaenJkR2tzaWlsOS9Pai9QQlVwNDFvZmZkTDhObGRaeDc3UENHbHROSXNkTStyb1FFcldCWHp0eFZOMTNaeGJidVZuUjVvdDJSTDAzK2lhc3dPREQzNHZqUkdsK2hLZHVWeXJaSGtSaFZweUJKVjdISHdXVk9CWm5JVlhVSTIyQ1JibU43aW5ONWF0SGt5UlhPc1FXNTZWZTVJaSt1aW9pTTRQa1MzbzJGZWYrTkRCbkFLUVRWL2xORjlWaEt2UWF1T0Z2d1VOV3g1NnhZNFBTYjVjQnE3ekN0MDNZZEs4Q21XUjU3OWxla1VrQ045RUlkUEVoZUNJS2Z5ZGpHdDVYQ0NTUk5qQW8zWlFmRmNTUVdGakFuZWhQY0tNV0ZRR1FjeERUeHloeWtsZk01Z2wwb2dwN1B0QVVlVmRnci9NTjhyOVU5YjRVV3dzbEtHcEdKNlNZWFlmeDJRR2FPcWhkR1AzM0FyZlgwbkdqWmdiaDBMWU1GQXJ0Zk04Q1JGN2cxNGE1aFlvYlpmQjRkZEM0YmFtN2M4SlQrayt1RGE1V29lV2NwN1JzbGRQMzJJWHptbndYdERpYndLb01VMFFhTmI3OTlTOWJiWkVKWmhlVS9QeHU0TkN2ZEZrZkkwY3NQc21oSWJFUTAiLCJtYWMiOiJkNzU0YzBjYmE2NzE5YjZkY2ZmYmQ2MDU4NmE3OTFhNjI5MDZjNDViYzhhZjJiNWRlZmQ2ZTc0N2MzYjU1ZGE4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IldaUHRWOERCM0owcUZFcWJ5MTlIZFE9PSIsInZhbHVlIjoiV2g0K0FTVGhrQ0ZJaXppWU9KTGErc2lyd3NKOWhreVRtZitRa04rVncvRTFGaEZzNm1YR0NCczgzRmlYSnNJZHJQQlVuclAydUxxYjUvQnhuMUNobk4rc2ZUejllV3k1YWxtemxVZFB6WlNtRTluUWFrWDNSRTdSYUVRb0pPWUhWRzROcU5CZkdCZWJjNmJXUUpTMFJpTUNUbCtWU09DR1greDNRVTkvbmt5WTJkbmoyWEw0Si9DcFl5VWVadXhsN3QwWTNreERWR0RPenloSFVqNmNYWFh6NFdCdFpIYzd1RlhLZnFOdHJ6V1Q2VHBjQkxweDFuKzA5WllaZnRyWWdGOTFwRVl4bjhteFNRSnFWQVdwV0NiWUhCQS9lOGQyKzhlMWpTTXdLNlNOeWFkbjA3VHNMVVc4VUFxT21XZ0UrOFlXZWVxOUpJZDRIcks4RnN2Mk5pK3pSY0ppZWt6eVhPcG5rT2RCZm9jRjlwbGhPeU43UlJ4TkRwYXFiOXhlcHZoeWpmejYzMk9hdTZSN0M2R0V1NEZEbkpzY0k5MUJwNHFtQjNnNzV6ZWpiOUs2RHJtSEk3eEdGM01PMkp2ZHc4TGZEVXhxMmhiMEZXUHBIVXVlWENaSldjRXUrYkI5SERrcWhWR2JZR2FiZ1NXazVYLzdUeDEzbkFnUlB4V2ciLCJtYWMiOiJjMmVjMTU3MjI5N2M5ZmI0YmY4YzdkNmFhMmJiYmMwMWQwODRjMmM4NjhkZjQ0MTk1YmZmMDljYzE0YmE3NDdlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhNSnFuVG00cG4rQ1dHeDBHbWNONXc9PSIsInZhbHVlIjoiVjJrdFdxeHNCbHhyVnVaSklYSGwzcXRaenJkR2tzaWlsOS9Pai9QQlVwNDFvZmZkTDhObGRaeDc3UENHbHROSXNkTStyb1FFcldCWHp0eFZOMTNaeGJidVZuUjVvdDJSTDAzK2lhc3dPREQzNHZqUkdsK2hLZHVWeXJaSGtSaFZweUJKVjdISHdXVk9CWm5JVlhVSTIyQ1JibU43aW5ONWF0SGt5UlhPc1FXNTZWZTVJaSt1aW9pTTRQa1MzbzJGZWYrTkRCbkFLUVRWL2xORjlWaEt2UWF1T0Z2d1VOV3g1NnhZNFBTYjVjQnE3ekN0MDNZZEs4Q21XUjU3OWxla1VrQ045RUlkUEVoZUNJS2Z5ZGpHdDVYQ0NTUk5qQW8zWlFmRmNTUVdGakFuZWhQY0tNV0ZRR1FjeERUeHloeWtsZk01Z2wwb2dwN1B0QVVlVmRnci9NTjhyOVU5YjRVV3dzbEtHcEdKNlNZWFlmeDJRR2FPcWhkR1AzM0FyZlgwbkdqWmdiaDBMWU1GQXJ0Zk04Q1JGN2cxNGE1aFlvYlpmQjRkZEM0YmFtN2M4SlQrayt1RGE1V29lV2NwN1JzbGRQMzJJWHptbndYdERpYndLb01VMFFhTmI3OTlTOWJiWkVKWmhlVS9QeHU0TkN2ZEZrZkkwY3NQc21oSWJFUTAiLCJtYWMiOiJkNzU0YzBjYmE2NzE5YjZkY2ZmYmQ2MDU4NmE3OTFhNjI5MDZjNDViYzhhZjJiNWRlZmQ2ZTc0N2MzYjU1ZGE4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IldaUHRWOERCM0owcUZFcWJ5MTlIZFE9PSIsInZhbHVlIjoiV2g0K0FTVGhrQ0ZJaXppWU9KTGErc2lyd3NKOWhreVRtZitRa04rVncvRTFGaEZzNm1YR0NCczgzRmlYSnNJZHJQQlVuclAydUxxYjUvQnhuMUNobk4rc2ZUejllV3k1YWxtemxVZFB6WlNtRTluUWFrWDNSRTdSYUVRb0pPWUhWRzROcU5CZkdCZWJjNmJXUUpTMFJpTUNUbCtWU09DR1greDNRVTkvbmt5WTJkbmoyWEw0Si9DcFl5VWVadXhsN3QwWTNreERWR0RPenloSFVqNmNYWFh6NFdCdFpIYzd1RlhLZnFOdHJ6V1Q2VHBjQkxweDFuKzA5WllaZnRyWWdGOTFwRVl4bjhteFNRSnFWQVdwV0NiWUhCQS9lOGQyKzhlMWpTTXdLNlNOeWFkbjA3VHNMVVc4VUFxT21XZ0UrOFlXZWVxOUpJZDRIcks4RnN2Mk5pK3pSY0ppZWt6eVhPcG5rT2RCZm9jRjlwbGhPeU43UlJ4TkRwYXFiOXhlcHZoeWpmejYzMk9hdTZSN0M2R0V1NEZEbkpzY0k5MUJwNHFtQjNnNzV6ZWpiOUs2RHJtSEk3eEdGM01PMkp2ZHc4TGZEVXhxMmhiMEZXUHBIVXVlWENaSldjRXUrYkI5SERrcWhWR2JZR2FiZ1NXazVYLzdUeDEzbkFnUlB4V2ciLCJtYWMiOiJjMmVjMTU3MjI5N2M5ZmI0YmY4YzdkNmFhMmJiYmMwMWQwODRjMmM4NjhkZjQ0MTk1YmZmMDljYzE0YmE3NDdlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614892483\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2050273815 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"229 characters\">http://localhost/budget/eyJpdiI6InAzMzVvWHBjZ3htWVJTc21RMmxleFE9PSIsInZhbHVlIjoiYmwzaHQxK1RGRnhXaE5sam5NOXBLZz09IiwibWFjIjoiMmZiNGY5Yzc1ZGU5MjBlZjYyMmZlMzg4ZDdmOTQ1NjIzYzgzNTEyODFlNDc3MGY5ODUwMmZmMjRlNGRhNGU5OCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050273815\", {\"maxDepth\":0})</script>\n"}}