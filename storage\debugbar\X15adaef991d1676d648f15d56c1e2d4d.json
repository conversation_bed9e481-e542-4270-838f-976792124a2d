{"__meta": {"id": "X15adaef991d1676d648f15d56c1e2d4d", "datetime": "2025-06-26 23:17:52", "utime": **********.924562, "method": "POST", "uri": "/bill/vender", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.421641, "end": **********.924581, "duration": 0.5029399394989014, "duration_str": "503ms", "measures": [{"label": "Booting", "start": **********.421641, "relative_start": 0, "end": **********.779474, "relative_end": **********.779474, "duration": 0.3578329086303711, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.779484, "relative_start": 0.35784292221069336, "end": **********.924583, "relative_end": 1.9073486328125e-06, "duration": 0.14509892463684082, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46269528, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x bill.vender_detail", "param_count": null, "params": [], "start": **********.846305, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/bill/vender_detail.blade.phpbill.vender_detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fbill%2Fvender_detail.blade.php&line=1", "ajax": false, "filename": "vender_detail.blade.php", "line": "?"}, "render_count": 1, "name_original": "bill.vender_detail"}]}, "route": {"uri": "POST bill/vender", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\BillController@vender", "namespace": null, "prefix": "", "where": [], "as": "bill.vender", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=1198\" onclick=\"\">app/Http/Controllers/BillController.php:1198-1203</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020880000000000003, "accumulated_duration_str": "20.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.807489, "duration": 0.02009, "duration_str": "20.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.216}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.836027, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.216, "width_percent": 1.916}, {"sql": "select * from `venders` where `id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 1200}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.838747, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BillController.php:1200", "source": "app/Http/Controllers/BillController.php:1200", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=1200", "ajax": false, "filename": "BillController.php", "line": "1200"}, "connection": "kdmkjkqknb", "start_percent": 98.132, "width_percent": 1.868}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Vender": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=1", "ajax": false, "filename": "Vender.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/create?0=\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/bill/vender", "status_code": "<pre class=sf-dump id=sf-dump-1239561640 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1239561640\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-770247520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-770247520\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1350758446 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1350758446\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1832874394 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/bill/create?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979871305%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkM3OTRVWUxraExFTkRLcVU5Y2ZHbGc9PSIsInZhbHVlIjoieDU2U1lZYXFnajdwK2xYZ3MrQlRoY1RKVGVjWWpCZmUxaVQwb1VrMnUrR0dlUzZhZWhqb1ZtOUhTa0UzaGRjZ0F0RStqL2FZb0VpYTZweGp0amFsZk5idFpxUE5UTDBRcGtxU1lxV3N4WHFZeXUxL1cyMGF5U1crVDNaK0xMUTBtOXRkR2NQRmhvTk1ud2FLaVlLMThzajJYcitDVktEcmFGRXpCWmRrM1ViM2d6Ulc3R0thQ09VdzdmUnQ4MnBmVW1oZVhEQlcvWFpuMjkwZjhENFpJM3NPNzF4ZEpBL2t4RUMwMldndm1CQVNSYlo3cGRpUUo5dm9iT3U2eStoZjhnYVd2SkpnU1pDSzBWK2g4eFBFeFVRMUNMSFNsT2kvd0pLOUd4cGJidWNsS3ZlbmgyUDN1MTNReGphcm15dUE3bzkzeGtybDJzYmQveDV4ZG9FOXZZeWtjRk9aWnJkMnZVVFNVM2pFNk80U2ZTYTU1b2dYcDdQL2pLaFkzSTFka1NhSExpcXF1cXhqNDlYY1hLWXVQQXFKUVdXOXBBSWsyV2I5R3NQbkpmaFEyU1ZOQlh6aS9zVXM1T2x6RVg4YzcvTjVoSHJkQ2JlMDBaM3VIVkNlUlVaellRT3hvUURGZmc0cVlnUlJla04zdmI4VC9QLzRGVFJ1TWR5bTVpb1IiLCJtYWMiOiIzMmRkZjNjMjYxZDFlZTc4Y2RhZDI3NmJmYzNjMjFkYjc1Yzg0ZjA5NWM1YjFhNzFhMGRkNDVkOWI0YWYzYTU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InpFN2hlNW1zeUNLRWZ0dTYwZ3NoSnc9PSIsInZhbHVlIjoienppdnFXMDhiQlkxdnVOaGZGS3RIUUtPb3k5RmQxZUJIa205Q0YrUklUd1IzVEtpcE1Mcy92ZFNyeUpEYkhRWTNxWmhoUk5jYmlwV0ZlYkxGTmdtdUQxNENOMHNnb0JVMXB4L1pKdjdYUkF0R1E4T0pFYU9UbDNzTHlVQk95dDBiUmFxek9kQ0s1V3JjZUdTVytzenJuZXMzQ1czckpVY05HNkdUenFNcnFpVXdKZzlNT3k0azVoUkh3TnJ5TGRGbFh2TEhaMjkvTC8zWlEvUmRKOEJYWEZORlRUYzFZK2lkQlBXT055ODMwZEtEN1RWQldEZzJ3SVBVeTZoSHo4QWo0WjN6aTlpM3hHaXJ2UkM0cldkRGo5QURTVFYxVEYrNG5CZklkd3JJNjdPVkJzU2RnUjFZNWpBamhoTHZ5SWRyV1Y5VVBoY0xxbnZpMGUycmVSKzhiZDRVK2RNMWttMy9BRmp6TERGbjVpbnVMWmR1b0UwVjBMa1FHcTJPNFNzbXdVV2ZBMk1TWUI0Y0NxVHVpUGw1bjRnMlIzS2xkckNkNldMK1Y3d1lQcXJzYU1RNndYd2xPTlNNeDRWaHlzSGg5ZHpNWFlUNFBVaFNQdTZuc1RsVUxIZ2xCVzFpSmVWdndvUDR1WEJiVE5jbGlHMDVMZjliUWJnc0MrNEZkeHEiLCJtYWMiOiIxMzJiZTQ3MDQ0MjllODU0N2E1ZTdiNDc0YWJkZjIyOGNmOGVkN2UyNTM2OGUxZTU4Yzg2NThmMWNlZjU2NmVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832874394\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1156552287 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156552287\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-331012320 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:17:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9BWXZzN2xDVkZOcXI1a3ZSeFdRTmc9PSIsInZhbHVlIjoiSVk3OVU0Q1JJejkzVHlXcjc3NU11Y2QvQ25OTHVSR0FhOXFMcGpJTlZUT2NqNVc1eEs0V3JzdlJuSFpXUnNkMHVrQzREZVNudzZHdnlmZUpFNXZGTjUvaXd2L0hSVzhtalAzRGdlbVROUHRyWGxLeGpzeklUKytyK1lOSkQyR1V0RW9jUnRrZEw5WWduWlROR0x1TTFrKyt5K2xjb05GZ0lWdmx5SHBRc3UxM0VqU1lyeWFQeDNGbjRQcklkR3o1Y2hDNXU2S2U0UFZrSW85QmdPbS9obEdaSjhGR01rOG5zTzZ0UzNOVmgzRjVtQ1NnOXlHenZMVEdHaExJZmFHYUY0d0E2SXpDY3E4NlJ6YXJRVEhjbHdUc2hHcjlyMlg5M3VFaDRYRDBpM1l1b0ZEclA1SEx6VGFSSEpjZXhjdlV5OGpsQnU0ODRYMnBiYm5TMlZ2OFZtb1JNR2NPQ0FNNkxRQml6ZlZscmNnQzNiSnhrWnU5R283SGI2eWl6SzQwM1huZGhCNW9MTy9DcmJaYWJaM21CVlI1RXVwUVIzcUFSZjNpVjBDTzY2ZEI3djlPdXY0S25aZENqaWdaVGhPRUd5UE9zbGtZM3hDTGZTQlhOT2FJMnFZOWNHRVc3ZXl4dXpOWjMrVW5FbkdrT25DSzRQTnhjTThUWG1CZGVyV2IiLCJtYWMiOiI5Zjk3NzU1NmJmNzU1Nzk5ZGUzNWU5NmZkMzNhMjc4MmRkY2M2ZDc3Zjc4MzEwMWU3MmY0YzBkNTQwYThiY2Y3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:17:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkE0YVhvdTJudW1CNUdqdHpiWjdEWnc9PSIsInZhbHVlIjoibEpZWXd4UE01Z212SngwdVB5ZHdzdTJxYzNvRWNOT0lSbHpEWmx6YUF4K3pCMHhMQzFLcStlMHYweGhaNEtQYzVMM0JNTEZQMDlFUGJuQzB6ckxTU3RpRjVNMm9CODhyL2V6SnlpZG5lOHpNWE9ZRE1GQWsvYkdZcDNOSm5FTjhGZ29GNlZ3K3BGTWZGMlBRMTI5RVMxTTB2YlhmOXprRkZxdFpUNktlaWo5a2dJa0hkOWlQSlFVazdsQXo4UThLWi82RjhpVEFWeVhzT1pjUW1qYkwwUEU1d0JidjB5RG5kckdFby9Ub0J4dWpYSERLWDBGWU02NE1iQVl3ZkEySENzcklxbm5xTEwzSlUvOE93SWdxMVkvODJ4N2k4dDJzTXEzK1R3cTg0akNyV2NCbUVtOE5ldDNSWnMzS2ptV3VjY0NDSWdZQlQvMnZLYUxjVTFOUVJFemN5ZEdrcTFOQ1JXWFMybzIxQ3ZGWDVRZERVVmxkWHhJcDFwYjNyK2I2aU1LTmI2cUtXaDR5Zy9ZVnZ3WC9MeldqNXRaajV3ZGZQRHVXU2xCL3lDN29GcU1GYnV0V1J0Z1hxTnJJRnB1TWRQRTlVREF0SHNrQ21aWE9ldXBvVy9uQVFCTjhhbUp4SjRtVXNtUERSb0Fzazk0VUtKaDBKdkhINTd6U1RsTXkiLCJtYWMiOiI3MGFjNmIzZjNhN2NiMzRkODcwZDM5NzE1ODk3MGJkMmZhYTE3OWI4MGU2Mzk1YzFlYWY1ZWJmMWY0MDY4OWM3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:17:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9BWXZzN2xDVkZOcXI1a3ZSeFdRTmc9PSIsInZhbHVlIjoiSVk3OVU0Q1JJejkzVHlXcjc3NU11Y2QvQ25OTHVSR0FhOXFMcGpJTlZUT2NqNVc1eEs0V3JzdlJuSFpXUnNkMHVrQzREZVNudzZHdnlmZUpFNXZGTjUvaXd2L0hSVzhtalAzRGdlbVROUHRyWGxLeGpzeklUKytyK1lOSkQyR1V0RW9jUnRrZEw5WWduWlROR0x1TTFrKyt5K2xjb05GZ0lWdmx5SHBRc3UxM0VqU1lyeWFQeDNGbjRQcklkR3o1Y2hDNXU2S2U0UFZrSW85QmdPbS9obEdaSjhGR01rOG5zTzZ0UzNOVmgzRjVtQ1NnOXlHenZMVEdHaExJZmFHYUY0d0E2SXpDY3E4NlJ6YXJRVEhjbHdUc2hHcjlyMlg5M3VFaDRYRDBpM1l1b0ZEclA1SEx6VGFSSEpjZXhjdlV5OGpsQnU0ODRYMnBiYm5TMlZ2OFZtb1JNR2NPQ0FNNkxRQml6ZlZscmNnQzNiSnhrWnU5R283SGI2eWl6SzQwM1huZGhCNW9MTy9DcmJaYWJaM21CVlI1RXVwUVIzcUFSZjNpVjBDTzY2ZEI3djlPdXY0S25aZENqaWdaVGhPRUd5UE9zbGtZM3hDTGZTQlhOT2FJMnFZOWNHRVc3ZXl4dXpOWjMrVW5FbkdrT25DSzRQTnhjTThUWG1CZGVyV2IiLCJtYWMiOiI5Zjk3NzU1NmJmNzU1Nzk5ZGUzNWU5NmZkMzNhMjc4MmRkY2M2ZDc3Zjc4MzEwMWU3MmY0YzBkNTQwYThiY2Y3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:17:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkE0YVhvdTJudW1CNUdqdHpiWjdEWnc9PSIsInZhbHVlIjoibEpZWXd4UE01Z212SngwdVB5ZHdzdTJxYzNvRWNOT0lSbHpEWmx6YUF4K3pCMHhMQzFLcStlMHYweGhaNEtQYzVMM0JNTEZQMDlFUGJuQzB6ckxTU3RpRjVNMm9CODhyL2V6SnlpZG5lOHpNWE9ZRE1GQWsvYkdZcDNOSm5FTjhGZ29GNlZ3K3BGTWZGMlBRMTI5RVMxTTB2YlhmOXprRkZxdFpUNktlaWo5a2dJa0hkOWlQSlFVazdsQXo4UThLWi82RjhpVEFWeVhzT1pjUW1qYkwwUEU1d0JidjB5RG5kckdFby9Ub0J4dWpYSERLWDBGWU02NE1iQVl3ZkEySENzcklxbm5xTEwzSlUvOE93SWdxMVkvODJ4N2k4dDJzTXEzK1R3cTg0akNyV2NCbUVtOE5ldDNSWnMzS2ptV3VjY0NDSWdZQlQvMnZLYUxjVTFOUVJFemN5ZEdrcTFOQ1JXWFMybzIxQ3ZGWDVRZERVVmxkWHhJcDFwYjNyK2I2aU1LTmI2cUtXaDR5Zy9ZVnZ3WC9MeldqNXRaajV3ZGZQRHVXU2xCL3lDN29GcU1GYnV0V1J0Z1hxTnJJRnB1TWRQRTlVREF0SHNrQ21aWE9ldXBvVy9uQVFCTjhhbUp4SjRtVXNtUERSb0Fzazk0VUtKaDBKdkhINTd6U1RsTXkiLCJtYWMiOiI3MGFjNmIzZjNhN2NiMzRkODcwZDM5NzE1ODk3MGJkMmZhYTE3OWI4MGU2Mzk1YzFlYWY1ZWJmMWY0MDY4OWM3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:17:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331012320\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-307505544 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost/bill/create?0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-307505544\", {\"maxDepth\":0})</script>\n"}}