{"__meta": {"id": "X4d6f5f35ab48d263be776f998127ca27", "datetime": "2025-06-26 22:15:08", "utime": **********.731072, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.267299, "end": **********.731089, "duration": 0.4637901782989502, "duration_str": "464ms", "measures": [{"label": "Booting", "start": **********.267299, "relative_start": 0, "end": **********.670209, "relative_end": **********.670209, "duration": 0.4029099941253662, "duration_str": "403ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.670219, "relative_start": 0.4029200077056885, "end": **********.731091, "relative_end": 1.9073486328125e-06, "duration": 0.06087207794189453, "duration_str": "60.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026824, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00334, "accumulated_duration_str": "3.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.704583, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.569}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.716132, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.569, "width_percent": 15.868}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.722145, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.437, "width_percent": 18.563}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1071565158 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1071565158\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-449763226 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-449763226\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-952193142 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-952193142\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1833670380 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2001 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750976078409%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktxT2swUmswQzFwNWdGOERhQUdmd2c9PSIsInZhbHVlIjoiYzBIMUpGVTVzNGNMQnV1bkVVSUhQZStybW8yaHluSTRlTTB2UnQ0aWdoN3ZuY3kwSkpXakMydVBNNGZPZXN2L2YyL01jM2hJaG1zMjRIaGZyNExCaVQ4eEZTTndIbDhkSk1UWlNVQWF0SE1HS2xqYXY5TG96OWtzMEx6ZGxvK3NJbkJkMXY5a1dpcEtCL1FZNnJTTUJpT05mQmg0MzdhcVFyRzlTUUdMMjVycWR5dit1TjBhSFlkUzgvTjg5VXVqcG01aEhOSTZURE9oQmJtbEhZcVdDWllMZ1hkdGJwWHQ5dUpxVnVLS1RPY3RQb1pya2lvSDNpTlJVYUljU0s1TDlnaHozQkJTN3lvMnZ5NlBvMDlYTzltakNLeGZFUlpEZmVpL3o5UzMrbGV1L0gxcWRsajJOZnFZSWlaZjA1dUNRTmh1WVh1M05DeTFjZXZGQjZVaTFsWDh2TGNTYlZESUV4amE2ai80NENLa1dMT00wRHFXc0ViRm9UcSt5cmNqTURiRnFXNDErRVJEb2I1b0paaUdMNDlnMlVTTUNSVXVmWlM1dWRhWEpIZCtaN3pTS0o2dXg2c0kyMjZWMDY2SVNxVzlEV1JXRXo1YUdwZTFHeG5jVmFnMXllTU9sN1ZMWC9ySnpiMGExbHRySzVKeGtTMmJyc0VFeWF0NG5kOXIiLCJtYWMiOiI0MDNiMTE3Y2RmNDgwNDJmN2Q2YWQ2MDdmMGM1NGZhZTFiMmYwNDY4YTVmZGM4ZGY3ZWVmYWJlYjU0MTEyYWQxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVqbWo2NjJnV0R6UVh2RS9sZ2dYZXc9PSIsInZhbHVlIjoiTmhXamtrbXMwMjJQQlVQUWloS0x4WEVnQ0pZZUJIdHY0VWFaNUtpcjh5amVxUFpaMXNkandWaFg3MHE1WkFJMlA0bUovTnJOcmc0RGhmVXlndEY0WlNuZjN3UVJuTU9oOExiVUlVY2NOc0ZjdUlVN0R3aFFhMTRycDN1bDRIdlFBQ0dub0hRbzJJSHE2UzhEbWFiTDhQNnVuRzQ5eE5pZC9xZktvYmJKKzhpbnAyUmFyRFJVUVltNUMxVGhIUW5JdThhMERhM2dIbU1nWWdJWStXTjdYSWNxMVhaeC9oKzRwNUpFc25OUW1QVG1XQkJOaGVRRlBXT2loZE5WUTlDTWxFcFp1TVd4UzQ3WkQyMGo2em9SamlKK3Q0RnJYTXNoV0JvZG5lNnhOc080MmQyQmlXMzFVQThidkF2M3czMEUvQ1ZPbUREcU5zS285MzduT2VySnRKL0drVmh0ZVEzd1BlOWRYOEYxMWJRejhQbUNlbC9DRm9MTXhCd01VeFY3YmVnVTl1UXFhSFN3TmdXMmFRWmJrZlovWjFtaXpnQ042YUx3UVdDZngwRkVBS3RMV1RCTFE2K29qZVdYbGtrNUh2SHEvS2Fqb1lxQUhiamdNdFg3YnhpR0V2QnhNNWZYU0NIaW9PWFJ5Z3ZTK2paNDZCdTdralZiaGdWdXcvb0YiLCJtYWMiOiIwMDE4NjRkMThiMzQ4MjExYTI5OTVjNWM3MmNhM2JjMzVmOGI3ZWFhM2MyYTIzODI5Y2U2YmE2MGE4MjJiYzAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1833670380\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1547879871 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1547879871\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-194290325 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:15:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlQME1hSnAxanFlL2xPMmUxQXk0M1E9PSIsInZhbHVlIjoiWHhKZlVDZXpxMDBDeWl1UFhEY1hORy95c1JuSnJkeTd0bHhrWW1uc3NXcXpvVXJZRW9hK3BTWlhjLzgyUHpFZWE4cHRBcTF4VVQrRmUxR0xUUHdUS3E2cVZDRGpENm9xbHJGYjFmU2tRNVVHUVZ4SFdJTlRkdkloWWF3ZVZyME1RUlJZczQxZWl4ZnA1dVZ6UjlOTmM4Z1BCT21ianh1dGExQklqYkJmaEhkM3lySmVVa3FxQ1F0b05UTVhmTi9DdjRsRFp2cjFQaUQwNFJCQk1CLzRaNDYwQUNTUWJYbG1nWmZqTXBUWjEwQWRJVURSdEZONnJhbGFqeTQvTjdQN1hOMWFBUUZiQ3dJSWZUbjhhRG5UL08vN1ZYb0VaZ2hvOEtNTHVYNDNnZk1BQ1BZQlVaTDN6Zjg2S2FwMVJ4ckF4QXpVZWcwOWlQRnFZUE5OZzZaY1Ayc0pRSHFkcDJtZm00Q2VSUUlXZWdOcmVjdGlSTjJDTjhwSG41dHRFMVh6Q0R5bXhiYnB5K3Y2RmFoMnBOUzBlelhGWlhzN0JHeFpwZFluaFNScnhrZFJNdWE3Ri9LYXF6U3k5WW1sN0htSEx1V0hyWXVPd2ZTOUlRYVNRRDFrTjlqUDEzb3ovSllkNGVSaDVVamJPVjFXSjBFc2MwTkdFYitFLzViOVFFRTgiLCJtYWMiOiI4ZjZkMDg3YWQ5NWI2ZWJiZDYwNmY3ZTlhNDYxNjgyOTJiNDczYmIwN2FjZThkNDliYzJmNWQ1NTMxMTEwYmYxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:15:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJoT2lKYVpIajVLeDlzcWpZRjdLcUE9PSIsInZhbHVlIjoiT1gzUFNYd2hkYUptTUZFOWFyRGYrLzNicnVaeDVpelhZRHZTSkg0WHYxNitnaUNBNGhtT0oyVDdMdkVvYWtoUEVqeTV1dFJ6NERQUXg3Kys1MVZqMEkrWkRjWGRUS1VDVndkYTRZbmRBS0ticzBQbm5OcVVQSG91ZTNXTWRGZGw1RUp4V2E5K2lleHpTM2VvL3E5NFNkaHFxNWN3cXk4MmgyWWpIeGMvOEp5OGVWYUl3TW45VFZtTzY2S2h1OXRreEZ6VHVsUGR0QUlrZDNTRndMTEZ6aEZSNXpTK0JHOWRYK2hKNkNsT1lXZU15eWFkMExTR1V3RkMxRDJINzNPbnh3L2l6N0RNWXFTRDFFZWlrem1pQ3VldzhaeC9NNXMyMzcrY2xXdko1bGowdjlkQ283bWVPcXk0eGFCbGpVM2tycm9xSlhwVWQwK284dkxlT25iOUlLZEdNWTkwZUloVmV0NnU1NitKVVo3dmtpckZtRjBWNVBGdkJTSDUwNkNGVzEzZk8rOERVc3J3RjJUR0o1c1huNmhjSFNIQ0k0ZXdqVWtleVk5Vlhob2tBaWpuMlNOb3JlVkJsNEtqcEp4VVM0ek84c28rS3I3ZHBYUytxM1J4Q3YwUlZOZkZpRFBuWWU1Y3ZEY2p2ZSt3UWkxNXFucTZxeE9QdWJadCtoVXQiLCJtYWMiOiI0ZmYxZmUxYzZmMDRmNzg1YjRjOTMwOGIwMjM1ZmUwMGQ5M2UwZThhYzJhZGVlMWY4OTcyZTUyYjAzODUxZjRhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:15:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlQME1hSnAxanFlL2xPMmUxQXk0M1E9PSIsInZhbHVlIjoiWHhKZlVDZXpxMDBDeWl1UFhEY1hORy95c1JuSnJkeTd0bHhrWW1uc3NXcXpvVXJZRW9hK3BTWlhjLzgyUHpFZWE4cHRBcTF4VVQrRmUxR0xUUHdUS3E2cVZDRGpENm9xbHJGYjFmU2tRNVVHUVZ4SFdJTlRkdkloWWF3ZVZyME1RUlJZczQxZWl4ZnA1dVZ6UjlOTmM4Z1BCT21ianh1dGExQklqYkJmaEhkM3lySmVVa3FxQ1F0b05UTVhmTi9DdjRsRFp2cjFQaUQwNFJCQk1CLzRaNDYwQUNTUWJYbG1nWmZqTXBUWjEwQWRJVURSdEZONnJhbGFqeTQvTjdQN1hOMWFBUUZiQ3dJSWZUbjhhRG5UL08vN1ZYb0VaZ2hvOEtNTHVYNDNnZk1BQ1BZQlVaTDN6Zjg2S2FwMVJ4ckF4QXpVZWcwOWlQRnFZUE5OZzZaY1Ayc0pRSHFkcDJtZm00Q2VSUUlXZWdOcmVjdGlSTjJDTjhwSG41dHRFMVh6Q0R5bXhiYnB5K3Y2RmFoMnBOUzBlelhGWlhzN0JHeFpwZFluaFNScnhrZFJNdWE3Ri9LYXF6U3k5WW1sN0htSEx1V0hyWXVPd2ZTOUlRYVNRRDFrTjlqUDEzb3ovSllkNGVSaDVVamJPVjFXSjBFc2MwTkdFYitFLzViOVFFRTgiLCJtYWMiOiI4ZjZkMDg3YWQ5NWI2ZWJiZDYwNmY3ZTlhNDYxNjgyOTJiNDczYmIwN2FjZThkNDliYzJmNWQ1NTMxMTEwYmYxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:15:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJoT2lKYVpIajVLeDlzcWpZRjdLcUE9PSIsInZhbHVlIjoiT1gzUFNYd2hkYUptTUZFOWFyRGYrLzNicnVaeDVpelhZRHZTSkg0WHYxNitnaUNBNGhtT0oyVDdMdkVvYWtoUEVqeTV1dFJ6NERQUXg3Kys1MVZqMEkrWkRjWGRUS1VDVndkYTRZbmRBS0ticzBQbm5OcVVQSG91ZTNXTWRGZGw1RUp4V2E5K2lleHpTM2VvL3E5NFNkaHFxNWN3cXk4MmgyWWpIeGMvOEp5OGVWYUl3TW45VFZtTzY2S2h1OXRreEZ6VHVsUGR0QUlrZDNTRndMTEZ6aEZSNXpTK0JHOWRYK2hKNkNsT1lXZU15eWFkMExTR1V3RkMxRDJINzNPbnh3L2l6N0RNWXFTRDFFZWlrem1pQ3VldzhaeC9NNXMyMzcrY2xXdko1bGowdjlkQ283bWVPcXk0eGFCbGpVM2tycm9xSlhwVWQwK284dkxlT25iOUlLZEdNWTkwZUloVmV0NnU1NitKVVo3dmtpckZtRjBWNVBGdkJTSDUwNkNGVzEzZk8rOERVc3J3RjJUR0o1c1huNmhjSFNIQ0k0ZXdqVWtleVk5Vlhob2tBaWpuMlNOb3JlVkJsNEtqcEp4VVM0ek84c28rS3I3ZHBYUytxM1J4Q3YwUlZOZkZpRFBuWWU1Y3ZEY2p2ZSt3UWkxNXFucTZxeE9QdWJadCtoVXQiLCJtYWMiOiI0ZmYxZmUxYzZmMDRmNzg1YjRjOTMwOGIwMjM1ZmUwMGQ5M2UwZThhYzJhZGVlMWY4OTcyZTUyYjAzODUxZjRhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:15:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194290325\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-220595498 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-220595498\", {\"maxDepth\":0})</script>\n"}}