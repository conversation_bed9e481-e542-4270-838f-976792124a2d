{"__meta": {"id": "X8217a74e273576a63807164964ef5430", "datetime": "2025-06-26 23:20:30", "utime": **********.124221, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980029.687087, "end": **********.124235, "duration": 0.437147855758667, "duration_str": "437ms", "measures": [{"label": "Booting", "start": 1750980029.687087, "relative_start": 0, "end": **********.070661, "relative_end": **********.070661, "duration": 0.3835740089416504, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.07067, "relative_start": 0.38358283042907715, "end": **********.124237, "relative_end": 2.1457672119140625e-06, "duration": 0.05356717109680176, "duration_str": "53.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45030352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00337, "accumulated_duration_str": "3.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.099926, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.546}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1108701, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.546, "width_percent": 15.43}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1170819, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.976, "width_percent": 16.024}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IkI3MGNuWnlFZEdMVXRJMGFPeURVWEE9PSIsInZhbHVlIjoiQUpGUEpFd1MydUJxSXJGME03Y3FBZz09IiwibWFjIjoiNjBiNWNkZDFkZmNjYWRiOWUxODI3NjBkNTk0NGQ3ODk1MGYyYzBmMDA5YTUyMGY2NzM0ODI1YWE5OWY0ZDhlOSIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-701737778 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-701737778\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1184509463 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1184509463\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2135010661 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135010661\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1352109939 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IkI3MGNuWnlFZEdMVXRJMGFPeURVWEE9PSIsInZhbHVlIjoiQUpGUEpFd1MydUJxSXJGME03Y3FBZz09IiwibWFjIjoiNjBiNWNkZDFkZmNjYWRiOWUxODI3NjBkNTk0NGQ3ODk1MGYyYzBmMDA5YTUyMGY2NzM0ODI1YWE5OWY0ZDhlOSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980008295%7C14%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlA1TEJRVENLczJqVUlITHBHZWNCWVE9PSIsInZhbHVlIjoiWXdjeXFqT0IzaGtkUzlXREhONmExTmFLakw4NnpwVlVNR0U3T3lBRXZVQ1NtSWl1TFMvRXlnb01vemZJbTkxNG9TaFZQbi9uN1J6S1A0aFZWN2R6MmNTakxZQzVranEvOG83bU00T1A3MW01S25zTXgwV1o1b29qdFkrQWhUc0d6bkk3S0NVUWdJcWRoSkRVZm5KVUlybGVTZWVQUnhyT1lUOCsremM4REphclUwS1VxYW1qS01mTlYvbTVWY2U4clpNaHh1b0JodGF0ZVl1emx4cmlKcGlYSzB5cGcwUUVhNFpQcitPRUJQNmlLSnpkVXEvL0Y4cENwOGh4N290akk1S0xaem1JZWloUmU5eG1YVTZTODNIRW5KK2dpNTJ0VjNzVVBLMjlWazEzNDRmbk1kWjMxYzgzTUNaQXhxN0tEb1R5b0R5UVlTTGpwK3MxRXF0VHVHeEg4SGtWRVBCby9weDJ0eFYwa1hqK04yclliMm8yN29odXd5N1lvV3llc05ScVViaEV1L3V5eW1tRktXandsUG1FWWtqeHlZR04xbWhOZWIreW4xci9EbFNOT3A0MlJMTTljdG9MODVVLzRsc1pkdnZZZTFjMDRPUy9kSjVqdm13N0M3cGF2cWhSZjRqRFVWOVphRDVBWTVxbDlwbEg2TG9YQ0RTbUlEd20iLCJtYWMiOiJiOTFhYWVkMDhhNmNkNWM0YmFlYzE4NWY4ZTJkNzQ4ODg2MjIxMWUzYmUyNzRjZTI1YjQ0ZmZjMGYyZDZiMmM2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkRJdGxCUmthWXRpbVRnb01rQTcveEE9PSIsInZhbHVlIjoiOEpNVlBHbStQaG1WTjBIYnhLY3BNM2JwMHQrYzJRbHFvTUZ6NlFWY2RLY3RUckg0bkRnakx5bXpwcUt0YzlIL3ZscGNycmxPTWxPdUlRMEpuay9PQVFTSjNKMTFXZVJUSTJYQUpYbU1FbzhiOGRvYlg5TVc5czNHbHVpRTZWNy8wUlI2ZlBFRHQybEptQjVUOFA3WFl1b1hQbkplaEo5SEVnOVlpczV2MDB6ZVoxYklsVmRnZU1SVVgzSHF3bW1lVEpqMXpkUXJScGlmTlYzVkJpSzQ2bmdzaVpNVFluZlJtSjIrM0k4dTlYQUJlZndpR2RQRlFIYnB0WUlsbDQwL2U1T2MxS2Mrbk9JSVBDRkxkci9PditvVWVSallTUlk4QkZrRjdXRTRNbGwrT1Z2QUEvY1lUQXpod0FaRDR5YWRKWTJSb3VTdXFuS25UZFEwaEFUQXRpMnUyQmFhV0JxeE9MR0MvenN3TDVON3VFUklYWEl6akxEOEJKS3pqUzdzNElhNHVaS3VNdkhjaEk3ZG52emI1eHNpMzVCYTdWL2VEcjU3NVZKZk91S3B5bjM4eFBlZzJvekpuenZLMXBVOWhISDVXWXZHdyt0YlV4YzMwZHI5QU1Wb0JVVmNxOFVOcnU3MWpLcXpYeTc5T0k4Y3h1bVdSSUhraFJDdStvaWciLCJtYWMiOiJlYmZmNjRkYjEwOGJjOTE5YTg4NTExMDIwNDdhYzk5ZGRkY2JiNzBmMjNjZDgyOTE1MTAzYmI5MDQzNzMxNzY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352109939\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2105322339 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105322339\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1196364039 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtMNjgvczI2WHF3Y0YrbnlPQVg5NWc9PSIsInZhbHVlIjoiUVlCc3JvZWJxV0xPQmwzQnJOSjBjSVZXa1I0VEdTZDJqNUhTK0VnTHFHNnUvQkY1aWtmZVBUaHU1ZEVaNFBaODF5UlcxVm9VRWJ1em91WnNVTWcyQnJJRmxqSWlQZUtDc2hLZkNDMG5rYUlMZmxKUjdlOW4vMWxmblhjUW55Y2pRcTkvYVFiRjc5NUR0YmszWVlZZ1FESnM2QXJETkMxZHNHQnJaWlJISmRWcHJxV2ZxRzJaOUxsbXZSN0lVTHNnSzFGL3o0QU55b3lwcHdMSUdMZzkvOWNoeGsrbkJYWERLTGJCakhsNEFjd29acE4yMTIyTG1rWlFKRzlsQnlJR3RXSUJ0TEJhWXgzeGdxSS9hdlBQVDZqVzNXN0phOEM3VXYwR2VGUTl1Y0FsRWlkejJNdGFYUFFtNWdCRjQ2MFRXUXZ3SkVhZFdEcjk0LzVhUzgxVFZmb1BNcFRzUUtYUU9DTkF3bXpxclB2blJWMkNjeFk3emlXR1AxbFdMUW9XYlRoWERlN3JqZUxqK3kyNDJPSDZJT3lLTjZxSElnLzJIUXlNSE1Ha1ZvWlpsSThZdDRITUxuc050OVk1SWpYZ2lEQU1HUmRacG5uUzdzQmZiVkdFV0lsOUNxMXhKNXBRR203R0tTMnBIaWFUUTlmSHUvSWRRVXpNaUVoaEM1SXoiLCJtYWMiOiIwZDc4NjhmZDk1YTBjZDM4ZGY2MGM5ZjUwZGM5MzI2ZTg5ZTRjMThjMzZmOWNlNzE5N2Y2NzhiYWU5OWJjOTc2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imk3QWc5V1AwM1VvTDlZSENyclFsV1E9PSIsInZhbHVlIjoidCtMcUJkQWQ5Y3NQRi9meFQyQi94QlUwcHcyNDNkbHZRaHI3TWtVdDlIOFIxM0R6bEZIeTdFRmJDMXpxdHp2REY2M2grekg3cEYwVGlCd2VQUU54dDgybkNkNnZ6UkhLNTJUSzZTQ2JHOVRCYkppRHFlbEYvTlRNV1ZYbFV1T2NraE9oaUpqVEdiUnoySFRXeVFyRWZuZlRvUzdrNjRVSGdwVUtFN0pxWnpCRjV3WWtZdXNuV05rcGxtK3BsWmtKb0x2REJlUW1STlB4eVJERVVGSjJHM2VqcUUzRHg4QmZmWVZ6b1FQbVVTaTJBRDJQY05UTFg1NEJVWnFLcVpYWmNhVUxwQk5lQnUrV1hpQmI4Tkd2YU9sa0piZ2NibzJBa0N4eTZUTFVvVnd1VmdQUjQ4VlI1alJuTnpyZlUzcXNjKzNpb1RPMmFYWUxvRVh4bnVLNkpENms2djJqVzQwbXZEb2t1K1B5a1QrR1N3Q3hoZGhEVWp2MEFzcFI4MldwdnJKdVBGRUJkWmw3L2ZPYVhpTEVrWmw5UTAvbzZKWUU3ZnV5enR5MnR5Vit2c3haUG1lZndnNXJBNkhoZmtLVnRPQiszc01vM0l5L0pnQ1FOM3JRNWNxWEdaaVZranZTamNueGFpRythTVdJR21reHFRSzNsT0JVeVp0YWdOUlciLCJtYWMiOiIxZTViNjNmYzI2MGMyZGFiMjdkNTkyZGI3MjZhNTE4NTE1YThlMTY2NWJhYTA1ODI1N2EzMGE0ODU5NDBmZWUyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtMNjgvczI2WHF3Y0YrbnlPQVg5NWc9PSIsInZhbHVlIjoiUVlCc3JvZWJxV0xPQmwzQnJOSjBjSVZXa1I0VEdTZDJqNUhTK0VnTHFHNnUvQkY1aWtmZVBUaHU1ZEVaNFBaODF5UlcxVm9VRWJ1em91WnNVTWcyQnJJRmxqSWlQZUtDc2hLZkNDMG5rYUlMZmxKUjdlOW4vMWxmblhjUW55Y2pRcTkvYVFiRjc5NUR0YmszWVlZZ1FESnM2QXJETkMxZHNHQnJaWlJISmRWcHJxV2ZxRzJaOUxsbXZSN0lVTHNnSzFGL3o0QU55b3lwcHdMSUdMZzkvOWNoeGsrbkJYWERLTGJCakhsNEFjd29acE4yMTIyTG1rWlFKRzlsQnlJR3RXSUJ0TEJhWXgzeGdxSS9hdlBQVDZqVzNXN0phOEM3VXYwR2VGUTl1Y0FsRWlkejJNdGFYUFFtNWdCRjQ2MFRXUXZ3SkVhZFdEcjk0LzVhUzgxVFZmb1BNcFRzUUtYUU9DTkF3bXpxclB2blJWMkNjeFk3emlXR1AxbFdMUW9XYlRoWERlN3JqZUxqK3kyNDJPSDZJT3lLTjZxSElnLzJIUXlNSE1Ha1ZvWlpsSThZdDRITUxuc050OVk1SWpYZ2lEQU1HUmRacG5uUzdzQmZiVkdFV0lsOUNxMXhKNXBRR203R0tTMnBIaWFUUTlmSHUvSWRRVXpNaUVoaEM1SXoiLCJtYWMiOiIwZDc4NjhmZDk1YTBjZDM4ZGY2MGM5ZjUwZGM5MzI2ZTg5ZTRjMThjMzZmOWNlNzE5N2Y2NzhiYWU5OWJjOTc2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imk3QWc5V1AwM1VvTDlZSENyclFsV1E9PSIsInZhbHVlIjoidCtMcUJkQWQ5Y3NQRi9meFQyQi94QlUwcHcyNDNkbHZRaHI3TWtVdDlIOFIxM0R6bEZIeTdFRmJDMXpxdHp2REY2M2grekg3cEYwVGlCd2VQUU54dDgybkNkNnZ6UkhLNTJUSzZTQ2JHOVRCYkppRHFlbEYvTlRNV1ZYbFV1T2NraE9oaUpqVEdiUnoySFRXeVFyRWZuZlRvUzdrNjRVSGdwVUtFN0pxWnpCRjV3WWtZdXNuV05rcGxtK3BsWmtKb0x2REJlUW1STlB4eVJERVVGSjJHM2VqcUUzRHg4QmZmWVZ6b1FQbVVTaTJBRDJQY05UTFg1NEJVWnFLcVpYWmNhVUxwQk5lQnUrV1hpQmI4Tkd2YU9sa0piZ2NibzJBa0N4eTZUTFVvVnd1VmdQUjQ4VlI1alJuTnpyZlUzcXNjKzNpb1RPMmFYWUxvRVh4bnVLNkpENms2djJqVzQwbXZEb2t1K1B5a1QrR1N3Q3hoZGhEVWp2MEFzcFI4MldwdnJKdVBGRUJkWmw3L2ZPYVhpTEVrWmw5UTAvbzZKWUU3ZnV5enR5MnR5Vit2c3haUG1lZndnNXJBNkhoZmtLVnRPQiszc01vM0l5L0pnQ1FOM3JRNWNxWEdaaVZranZTamNueGFpRythTVdJR21reHFRSzNsT0JVeVp0YWdOUlciLCJtYWMiOiIxZTViNjNmYzI2MGMyZGFiMjdkNTkyZGI3MjZhNTE4NTE1YThlMTY2NWJhYTA1ODI1N2EzMGE0ODU5NDBmZWUyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196364039\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2097975243 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IkI3MGNuWnlFZEdMVXRJMGFPeURVWEE9PSIsInZhbHVlIjoiQUpGUEpFd1MydUJxSXJGME03Y3FBZz09IiwibWFjIjoiNjBiNWNkZDFkZmNjYWRiOWUxODI3NjBkNTk0NGQ3ODk1MGYyYzBmMDA5YTUyMGY2NzM0ODI1YWE5OWY0ZDhlOSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097975243\", {\"maxDepth\":0})</script>\n"}}