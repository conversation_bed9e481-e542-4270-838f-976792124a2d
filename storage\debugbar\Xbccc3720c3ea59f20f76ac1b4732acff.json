{"__meta": {"id": "Xbccc3720c3ea59f20f76ac1b4732acff", "datetime": "2025-06-26 22:42:22", "utime": **********.312096, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750977741.879084, "end": **********.312113, "duration": 0.4330289363861084, "duration_str": "433ms", "measures": [{"label": "Booting", "start": 1750977741.879084, "relative_start": 0, "end": **********.259361, "relative_end": **********.259361, "duration": 0.3802769184112549, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.259372, "relative_start": 0.38028788566589355, "end": **********.312114, "relative_end": 9.5367431640625e-07, "duration": 0.05274200439453125, "duration_str": "52.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042648, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00327, "accumulated_duration_str": "3.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.287864, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.526}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.298564, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.526, "width_percent": 15.902}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.304296, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.428, "width_percent": 19.572}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2085649807 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2085649807\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-212906997 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-212906997\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-849566063 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849566063\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1322485662 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2001 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750976644779%7C9%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitqVE9IKyt4RTd2UVpwTEhiY3FHd2c9PSIsInZhbHVlIjoiM0prNUdoZGQ0NmZxWnB0RndrR0NJK2Y4MytVZU5WRk1aNFVxWElwaVV4RDhjcWJrUGpBc0pxUXRQb1ZZK29rTGRnczN3cmY1VjVqR2FDUUZQUStVcGZxcUFEdjRRdisyRGFjbXNyMnVxOW0rbUxzQjFJdkxqTnF1WnZIcjcxTERlRXl0SVlhaXR6VkZuM0I1Y1FxNWtTQkRnZkhVb2syazlROStzMzFDbGpXck0rQ3ByaDV3N0FXUmErTGQwcFNTaFZkWEFEby9wK29iei9QS0QwUTRSZWtJYU1SZVZrYTdZZ1E1QU9qSmcrSFNjelAvUlNKaHpQSzlIcmxYYTBJbjBSUVhhZEhTbjlDVHFYalphcmpRMWZydTB4MlNmbjl4V1F1WG9CUUUzY3d1Z1ZTbHVUZ1VMNjdIR2ZnZ3lGcGdyQ0JwK1hRTmJ2cW5NM3dmMGkvL0dkK0g1bWVLbFJkQ0tKcVNibGhzUG1lZTJMejZPMGpWbWw0TUNoSytBSncxeVArZUtBYXhUcldCdDZjdUxYMFdrK3V3TE56YkFOaGgzWXNPNUtHa2pmZDdTcTdjTEFLOXhuWHZlSC9RSnRQYm9jb1huVGl3ZGk4dTFOTit1aE1DU2lWRWZNVUpHbkVqeGlMZ3VIQ1JqV3ptSjhITlhKMyt0MzhoUS9VT2Q1eE4iLCJtYWMiOiJmODc3NzgxNGE4OTUwZWU0MGE2ZjViMjNmMzM5OWVlMzE2ZWE3ZjUxNmI1YTQ4NmU4MWIxMDI1MWZlYTgxZTM1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJFZXh5UzB0UkFia05XKytzYksvNnc9PSIsInZhbHVlIjoiZTZhdExPMWlQenVnNCs5L2JMM28zZG53QTBPZ2VZenUwcWR3L0FWdVp1cHBYdXN4Q05aeGpHUCtEQm9iUmU2OE9wOFQ2STVMRGYxYVMrYWNDT0RhV0RxdkZocnF6ejhCZE9JejlRQVNZeHRxNEFCczMvVGRxVVhyYVZaYzE0K2VLYVd3VFc1NjQ4RktFMU11REtlaW9HOGhxM2phTk42M2ZZaUdnOXUrS28vak8rTi9CajQyZmd6RWZJSHNQdVRqRnJpbkFGaUZRaFdiYk1sdEhlWitWRVRRbEhyTmI0bEVHUGFleklaeWhkODlqTUppb3JIdmtuMGlwV3FjMUVmUk9tc1h0QnorVksxOVZVNjV6UXhwQUNlcVpKM1k5YUczTGNTNHlIMCsrOVI2Z21oMThObkNualZVUUFlbzlWVXVzb1dBODdWNWt6MzE1N2g4a3RrZ1BpZGRpQmE2V05aZUx3a0d2ZmloNFViQ3QrSDBTbkl1VnRNVk9VV2liU0ZzczZEUEwwRzJNNklvRGFNaXpOQThlOXllYXVrajNsT21sSGtLYThyTkNadVM4UkxvcTlUZ2I4N2xwREVlSElYdWk3bFRyV25DUjAyTVM2N2pubDN3WDJyaFZTSGRiZ0tsYk1Xbis1cmVidGZVTnUrWERROFVwSE5XQnZEUzNDQmEiLCJtYWMiOiI4Y2U2OTliOWRiMTE0YzhkZGUwYTAxN2U0NzE2NjIyYWNhNzY4YzkyM2U2MGQ1ODg1MjhmMDM0M2JmYzUxOTM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1322485662\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1900295715 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900295715\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2100552644 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:42:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imw1RDRkOEZlcWJVRVpLdjcxUlZEV2c9PSIsInZhbHVlIjoiSXN6OUM1anVEVVdJb01Tc091NnNGVUpHeEh4a3BjZmFucDJSRUh3UDJnYmR4aGx4Nk1FVUI2UlJhTHZpUWR5Q29VTE9Cdi9Qc1BTT2NtSEN2OEV4eGtnOVRPM3lGNlU3c01XUldBZHk0c2ZSY01nZjFTWDNFMjVlMk45ZzAvUkV2bmNDNHF4cGV0YUFQdGozeGNQRWFWUnN2WGllb01LZ2ZDazU0bXNXWVdaZ2RuVlZwT2k5T3cxTDVJZWJBRm1LdURLM3hvOU9saHowckhCU2h2V1JmSXVTQ0k5UVg3L08xblZaekpMUDR2akJxbWhGMTVVK3krckdTUlorUVJQK3J6YkVNN1J1cnlwUnJKaUFySFRGUXRWaCtaL2dsS3RSSDBBWUpPNURibHBaRHNpcVUzWTRiZXZVMFV0TXoyaWpKNnc2N0FVcGRSQk1md2tMK2krMkVVNHdtVnJSUXdKY01NRTEvVjhxdGJ4T3JGMUxIMjBHQm00ZjJDZkt5K0Q5N0pwdTd5cGxZM2pIMW9HVHg5WWx4blJ0VEZaOWFkWHRndHQ3STJqTW9EZWd3blpzbmtxbzM1cXgyUWhLYlUzd2FKMG9NU045OUZhcTJoR2NGOHNCNmZDRzlEYVJVL2N0V1M0SjJRNTlvY0d6SXZlaFU3WXpjL2d3TGIwaUtqL2wiLCJtYWMiOiIzMGE2OTZhZmVlYzkyNmNkYTQwMjczOTNhOGNiZjFjNWExYTJmN2I1ODIzN2Q3ZjRlZDA4YWVkNjE5N2JmNzkxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkhNSzJQcG5UU0drc3JYalRiNk9TdUE9PSIsInZhbHVlIjoiaXF3VXJ4WmtYL0RWeGZyVVppa2ZiZURBKzhpdEJUSmI1Wjl4UXJkdWJKbjBiZmFzQ3k2dCtKMEUzcE42d3FwWmZpUmtUZVBQTjNDdElHaFNCMjRpVXd1cHROR0NNNUREamE3WU9qcG1qY1ZoNjFoaHBLQlpML0FVR0VoaWI1YUs3Y1BtM3ErWkZSY0ZjTTcvcUJiUXk3RkNCd0JjVEFBRkR0eFAxZVZwYTUyaTNmWFIrN0pWbWNzWnB4WE0xNlRleU10WndGSkQxQnFlZkZjNi9PSlI3cU1XeUdBUmg0alRCbmdTaWhxdFdieWFlbGs4OXhCejhLekVWT1FZQXJtM0tiSk9NbHY2c1pRNVhFWEw3NXROWi9maTVkVWY3L292YmpJa3VHeml5SDRCNjRNZ05QTUVjM0EvUjdUZC9qS0ZqQTJpcjg1MnlsMytsSmo2cWRPZFVmWTI0NDA0Sy84Sks1dEZqcWZOaUVaOUdKbWpNbkdpdnBUc0kwUVVIbjBwMjBGNzhtMVdJaTFaTGo4NGRDaG1jN245Z3pYZ1dLdVJkS3Fqaktwd2xWKzMvK1gxVnJHZGZhRTdxc1EwN21SOG1LMmdMTXNmVEtUWmY5azkxdWZCREZ6M2FMQnh0blU5bXpucXlqNTZnbFplYi9FeTRqOWxpeXlTYjA2VHc0MzkiLCJtYWMiOiI3ZWQ4N2NkMzczY2M0MmMwYTJmNTBlMDA1OWExZjg0NWZiNDgyYjk2NmNlZWYxZTQ0ODgxNGZjMWQxNTBjYmFjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imw1RDRkOEZlcWJVRVpLdjcxUlZEV2c9PSIsInZhbHVlIjoiSXN6OUM1anVEVVdJb01Tc091NnNGVUpHeEh4a3BjZmFucDJSRUh3UDJnYmR4aGx4Nk1FVUI2UlJhTHZpUWR5Q29VTE9Cdi9Qc1BTT2NtSEN2OEV4eGtnOVRPM3lGNlU3c01XUldBZHk0c2ZSY01nZjFTWDNFMjVlMk45ZzAvUkV2bmNDNHF4cGV0YUFQdGozeGNQRWFWUnN2WGllb01LZ2ZDazU0bXNXWVdaZ2RuVlZwT2k5T3cxTDVJZWJBRm1LdURLM3hvOU9saHowckhCU2h2V1JmSXVTQ0k5UVg3L08xblZaekpMUDR2akJxbWhGMTVVK3krckdTUlorUVJQK3J6YkVNN1J1cnlwUnJKaUFySFRGUXRWaCtaL2dsS3RSSDBBWUpPNURibHBaRHNpcVUzWTRiZXZVMFV0TXoyaWpKNnc2N0FVcGRSQk1md2tMK2krMkVVNHdtVnJSUXdKY01NRTEvVjhxdGJ4T3JGMUxIMjBHQm00ZjJDZkt5K0Q5N0pwdTd5cGxZM2pIMW9HVHg5WWx4blJ0VEZaOWFkWHRndHQ3STJqTW9EZWd3blpzbmtxbzM1cXgyUWhLYlUzd2FKMG9NU045OUZhcTJoR2NGOHNCNmZDRzlEYVJVL2N0V1M0SjJRNTlvY0d6SXZlaFU3WXpjL2d3TGIwaUtqL2wiLCJtYWMiOiIzMGE2OTZhZmVlYzkyNmNkYTQwMjczOTNhOGNiZjFjNWExYTJmN2I1ODIzN2Q3ZjRlZDA4YWVkNjE5N2JmNzkxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkhNSzJQcG5UU0drc3JYalRiNk9TdUE9PSIsInZhbHVlIjoiaXF3VXJ4WmtYL0RWeGZyVVppa2ZiZURBKzhpdEJUSmI1Wjl4UXJkdWJKbjBiZmFzQ3k2dCtKMEUzcE42d3FwWmZpUmtUZVBQTjNDdElHaFNCMjRpVXd1cHROR0NNNUREamE3WU9qcG1qY1ZoNjFoaHBLQlpML0FVR0VoaWI1YUs3Y1BtM3ErWkZSY0ZjTTcvcUJiUXk3RkNCd0JjVEFBRkR0eFAxZVZwYTUyaTNmWFIrN0pWbWNzWnB4WE0xNlRleU10WndGSkQxQnFlZkZjNi9PSlI3cU1XeUdBUmg0alRCbmdTaWhxdFdieWFlbGs4OXhCejhLekVWT1FZQXJtM0tiSk9NbHY2c1pRNVhFWEw3NXROWi9maTVkVWY3L292YmpJa3VHeml5SDRCNjRNZ05QTUVjM0EvUjdUZC9qS0ZqQTJpcjg1MnlsMytsSmo2cWRPZFVmWTI0NDA0Sy84Sks1dEZqcWZOaUVaOUdKbWpNbkdpdnBUc0kwUVVIbjBwMjBGNzhtMVdJaTFaTGo4NGRDaG1jN245Z3pYZ1dLdVJkS3Fqaktwd2xWKzMvK1gxVnJHZGZhRTdxc1EwN21SOG1LMmdMTXNmVEtUWmY5azkxdWZCREZ6M2FMQnh0blU5bXpucXlqNTZnbFplYi9FeTRqOWxpeXlTYjA2VHc0MzkiLCJtYWMiOiI3ZWQ4N2NkMzczY2M0MmMwYTJmNTBlMDA1OWExZjg0NWZiNDgyYjk2NmNlZWYxZTQ0ODgxNGZjMWQxNTBjYmFjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100552644\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1919242413 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1919242413\", {\"maxDepth\":0})</script>\n"}}