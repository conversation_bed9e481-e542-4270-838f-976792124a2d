{"__meta": {"id": "X5272ca2a4f65ab085b541eb6ebfe8c52", "datetime": "2025-06-26 22:42:29", "utime": 1750977749.005898, "method": "PUT", "uri": "/bill/2", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.507371, "end": 1750977749.005916, "duration": 0.49854516983032227, "duration_str": "499ms", "measures": [{"label": "Booting", "start": **********.507371, "relative_start": 0, "end": **********.851447, "relative_end": **********.851447, "duration": 0.34407615661621094, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.851457, "relative_start": 0.3440861701965332, "end": 1750977749.005918, "relative_end": 1.9073486328125e-06, "duration": 0.15446090698242188, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51487384, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.update", "controller": "App\\Http\\Controllers\\BillController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=474\" onclick=\"\">app/Http/Controllers/BillController.php:474-675</a>"}, "queries": {"nb_statements": 13, "nb_failed_statements": 0, "accumulated_duration": 0.07240999999999999, "accumulated_duration_str": "72.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.879677, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.596}, {"sql": "select * from `bills` where `id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.884485, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "kdmkjkqknb", "start_percent": 2.596, "width_percent": 0.511}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.891312, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 3.107, "width_percent": 0.552}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.904705, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 3.66, "width_percent": 0.981}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.906862, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 4.64, "width_percent": 0.635}, {"sql": "select * from `bill_products` where `bill_products`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 525}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.917337, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BillController.php:525", "source": "app/Http/Controllers/BillController.php:525", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=525", "ajax": false, "filename": "BillController.php", "line": "525"}, "connection": "kdmkjkqknb", "start_percent": 5.276, "width_percent": 0.746}, {"sql": "delete from `transaction_lines` where `reference_id` = 2 and `reference` = 'Bill'", "type": "query", "params": [], "bindings": ["2", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 610}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9198608, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "BillController.php:610", "source": "app/Http/Controllers/BillController.php:610", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=610", "ajax": false, "filename": "BillController.php", "line": "610"}, "connection": "kdmkjkqknb", "start_percent": 6.021, "width_percent": 5.414}, {"sql": "delete from `transaction_lines` where `reference_id` = 2 and `reference` = 'Bill Account'", "type": "query", "params": [], "bindings": ["2", "<PERSON> Account"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 611}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.925098, "duration": 0.05799, "duration_str": "57.99ms", "memory": 0, "memory_str": null, "filename": "BillController.php:611", "source": "app/Http/Controllers/BillController.php:611", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=611", "ajax": false, "filename": "BillController.php", "line": "611"}, "connection": "kdmkjkqknb", "start_percent": 11.435, "width_percent": 80.086}, {"sql": "select * from `bill_products` where `bill_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 613}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.984503, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BillController.php:613", "source": "app/Http/Controllers/BillController.php:613", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=613", "ajax": false, "filename": "BillController.php", "line": "613"}, "connection": "kdmkjkqknb", "start_percent": 91.521, "width_percent": 0.539}, {"sql": "select * from `product_services` where `product_services`.`id` = 0 limit 1", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 615}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9869032, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BillController.php:615", "source": "app/Http/Controllers/BillController.php:615", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=615", "ajax": false, "filename": "BillController.php", "line": "615"}, "connection": "kdmkjkqknb", "start_percent": 92.059, "width_percent": 0.608}, {"sql": "select * from `bill_accounts` where `ref_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 654}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.988642, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BillController.php:654", "source": "app/Http/Controllers/BillController.php:654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=654", "ajax": false, "filename": "BillController.php", "line": "654"}, "connection": "kdmkjkqknb", "start_percent": 92.667, "width_percent": 0.359}, {"sql": "select * from `transaction_lines` where `reference_id` = 2 and `reference_sub_id` = 2 and `reference` = 'Bill Account' limit 1", "type": "query", "params": [], "bindings": ["2", "2", "<PERSON> Account"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5664}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 665}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.990306, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5664", "source": "app/Models/Utility.php:5664", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5664", "ajax": false, "filename": "Utility.php", "line": "5664"}, "connection": "kdmkjkqknb", "start_percent": 93.026, "width_percent": 2.693}, {"sql": "insert into `transaction_lines` (`account_id`, `reference`, `reference_id`, `reference_sub_id`, `date`, `credit`, `debit`, `created_by`, `updated_at`, `created_at`) values (0, 'Bill Account', 2, 2, '2025-06-20 00:00:00', 0, '10.00', 15, '2025-06-26 22:42:28', '2025-06-26 22:42:28')", "type": "query", "params": [], "bindings": ["0", "<PERSON> Account", "2", "2", "2025-06-20 00:00:00", "0", "10.00", "15", "2025-06-26 22:42:28", "2025-06-26 22:42:28"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5683}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 665}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.994115, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5683", "source": "app/Models/Utility.php:5683", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5683", "ajax": false, "filename": "Utility.php", "line": "5683"}, "connection": "kdmkjkqknb", "start_percent": 95.719, "width_percent": 4.281}]}, "models": {"data": {"App\\Models\\BillProduct": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillProduct.php&line=1", "ajax": false, "filename": "BillProduct.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Bill": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=1", "ajax": false, "filename": "Bill.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\BillAccount": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillAccount.php&line=1", "ajax": false, "filename": "BillAccount.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>edit bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.910836, "xdebug_link": null}]}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "تم تحديث بيل بنجاح.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/2", "status_code": "<pre class=sf-dump id=sf-dump-241826693 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-241826693\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1532937322 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1532937322\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>vender_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>bill_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-20</span>\"\n  \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-20</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">30</span>\"\n  \"<span class=sf-dump-key>order_number</span>\" => \"<span class=sf-dump-str title=\"3 characters\">739</span>\"\n  \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>account_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>item_name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1605;&#1589;&#1585;&#1608;&#1601;&#1575;&#1578; &#1606;&#1579;&#1585;&#1610;&#1575; &#1575;&#1604;&#1586;&#1610;&#1605; &#1601;&#1585;&#1593; &#1585;&#1608;&#1575;&#1576;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>itemTaxPrice</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>itemTaxRate</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2290</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryAtjUWZ75qAB0a16t</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977742699%7C10%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik52NnF6ZlBIZk4rRXQ5WlNPZXNYcEE9PSIsInZhbHVlIjoicGpFNUxxVUlpbWpMTm12b2srWEpacFp1dzdyd1ltQmYrQ2pJVkNzOEhRMlQzc1hDMFYrRmlZd3RXd29UUG9MMFNJREM1aTVEWlZjRVM0VlowSTMwc0xmYmh0VEZNN0JCaXlQMHF2a0swREVwR1NxVjhQd0hod21mbkxacXMrZFNkVWgydVpYcmZSRjM0cnU5aEFHaVpMWnhwTmMzZzdKUTdPOWM1c01hc1Y1a2hROUhkRDJOS0xCcGowR1NNYklhSXJxR3h4RGZva0tCaUY3SnBvZlZnQlVCVEpQT1ZDbzFqWTdMbGcya3M3V2JDNS9jU1BvQ1FGRzRCaHBPZ0NJekJPR2sza0Vnb21YYXl5UXVkbTZCU0FtNUZnZ2ZTWUx5RzhueURJN1plbjJPVzRRUW81dGs5R0NLQm9rMWQ4OFRSb3krVXd1akpzaVVFaWRhaFYzR1ZqanBhLzdubVdUTjk1elo3eDhBdW5DUmIxMWhVNU5jZkFGM1ZYclBPLzFCa2dpbjg2MnE4TjBmZkdhL1pHOCtBRE9lUTB4aHZBUEtZRUtORURTbDdGVzNIWFdvTC9pZkFwV1V0MWozVlNqT0xONTlGWXdXcGVqMTE5T1hCcTY3N1dZMlFsajRCZWNKR01uZlJHNmpMai81K2VpeStOTnBFSWVxbXgvY0I2RUEiLCJtYWMiOiJiODg1ZDUzMWU2NjRmNDYzYTNlYjc4MmM3MzM5YTQ4N2Y3MTM3YjQzMjY0MWI1MmM2ZDU2YmUxZmQwYzI4YzdmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ijd3dWJ5enB1L2NQZ1hVeHgzMUlGTlE9PSIsInZhbHVlIjoiYjM1TE56YkJUcDBaMlpuOVN5YlIzWHdZOEl3SDJXZkpmQlJQUDdGQTZ3MS9VLytlMTEwZlhReXpOT2J6QXVYbXVrRm1OY2Z6MG4yVldvL2ZJRDBwclovbk9pcmtQSG56Yy9NV0JFM1psNTBWdmV2a1JnVjFoMFpicU84eVZNSVpvTkEyYTNLQnI3bVRqOE0wejNmZGF0KzlFRVNuc3JDMDJQcG5ic3hqU0cwSFQ0QjVtTVQyWnNQdStET2MyMllOeEQxK0U2Tmwyb003YnZJRjJLTDFnZVcvRlBnZ2llUkYwUnFMMmlzZFh2WnVZak1LYWZnRWdUKzFJZnk0ZnNqT0pFVlFWRnQ5ZlNLdFRqT1hUVXd5UnhLaUtaMXhZTmh3TWgyTm1UUkwxdFlKRWprSEpTOWFPZGNjMjFxNnhqZjh0MUFqM2lkcWtBUzhVYjZzcDJXQXdET1oyVms4TDdnaHA5elBWcm41NElMMGhSeVBKZ29zZGRMRkxjNGFSNHBaZDdrUzBsanFVWWRqbE9NVUw4TGU4TTUvSUVRNHN5aUhmT09QTXRieUtTaU5Zb1d3eE5RTnlheW0xaVAwOEVBYmdXZzBkQ3djb0hrbTlJMjd2czVwWUFBc0pKZUd3NEdwcFpDK29ERzMyN1BuL3FrWUVQNmJHZmR6a3UrSENjTUoiLCJtYWMiOiJmMzYwYmEyZGYzNWEyZTliMzQ2ZjQ3YzIyZTQ0MjE4Zjc3NTgwZmNhNjA1ZjIyODYzM2ZhMGY5ZmY1Y2FmN2NkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1207219867 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207219867\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:42:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlVciswakNmVmVtcTVCTHBnVlRsckE9PSIsInZhbHVlIjoiOFV6NGF2WFhOZml4dzRSTTExL2x5eER5YXc2L2hIODVJWVlBRjRKNU14ZkNmVEhqdlVrY2ZyanU1UzVRYWQyQ0ZmM0NJNzRpT2EzZmd2UVBGZ2U3L0pHeXhCbnJXdXFLamRSWGtuUC9PQkN5WXBsOWxMQThVT2w1U3htUnN3RmZDVnpzSWl6L1BzRzNpM2R0M3NpaE1QcVhxZGQ2Mk9EczFaSS9hZlM0N3lTcUhFYXF0dzkyRGxEN2NEZTQ1YUU4QTh2OXlmd0JXZjYvU1hNVlk4TG8xVlBWdjk3L3FrYVVrbzJMaXIwZ3lpZ3ZzVU5lQjk1Zmg1d3FNR0IzeUdWMVNDRHN2WnpDZXVrUVVkdDExbVUycDVpbjdvWWZudjgreXRXVnVxeW9OOWdlSDlSWjlzd1VSTWNIZitmQ3YwNnRwR09PeVpORSt5ZmdGY2JuUWFBbmRhUmVudSs5bHlaaGp5QnJHeWdSRUNvVzgwaFVieUZ5ZEJ2NzFOdzE5b1M1UTQ5b05lNFRERXRYUDNvNk5veTN4cUtJelY0Q210QTYyRVVPUFVYeUJ1L3dTRk9DTnQxai9jTWZjdmVva3BKUU1yaEVHeHk1L2xEQm8za2J3LzcwS2xjZjYwUi94aEF3V1RlMXVNRm9VOTF5U3BLd2wycHlBelNwcENnays0aTMiLCJtYWMiOiI5NDEzOGIyMzg5NjliNGQyODE5ZmMwMGJiZWI3MGY4YmMwMWU1YWFjZDBhNzcyYjJjOWM5N2FhOTNlNGI1YmYwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZFMDhKZklaT3NLVTRxSzdYUm9VSlE9PSIsInZhbHVlIjoiVlFSdGc1NnhKUktDMlFYYXp4bW1kM0Q5TmhwN2VUYmY4bERDZHVMVDV3TlA1dTRlVWlGT3l0YTMzdFNNZlR4SldUVGM3NlYrMmZvakRxaC9HT3pFcUd5REtwaDNNNXBBdS9hZnhoU3BmYWFJR2xzeGUxWWJSenNZcFl5am1PaEkwTFNIam92NjgzM1I2OTA3YVJ6bklGS2FiLzNoR3ZDVDZoSDZYSjFEN2l5SlpBZUVxSGN0K1ZRV3VtNlJhRmwrUjkzZzVTRzFCaTZZNzBMMGdySnhxVEkycTNxem9ZZGFMdHVMSzE0bk1rRjZqRnhYRm9RZi9EcjJhdmRTQVZkS1BkMHdlVDJ3Q2lWakE0QnVOVk9aVDgzemExaVFBQ1NEdUJiMjlYQlFock5ldThrNC94bE1NS2FqTVYzL3FGSHA0eDVCS2FmcGpYS0YreVVzOUt0YUExNXZKaFgzaXR1VjZKWVlhWGRmdHlpN1MvSURHdERpVHJ4RGNreWJwK2F2VjBPblh2NHltMEx5L0pCSVBYSXR3OVhza0NXNWM1eXBqNEc4a1R0ZUtCclZXRm0zdEROSEhrZy9sVUtKeWNIYXBuMytFMTVzOHppUjVEeTMyaUk1a1RqYjlQTFZ2TnVYWTFHMElteGRsa3lYc2VLejgwemRxcUpOU2sxRnl3MmwiLCJtYWMiOiIyYjg0ZGZmMjgyNWM1ZGRiNDcwMWUxMDg1M2Q0MDQ3MzIzYzZiNWMzNDgxNGI0NTYwYzljYTgyOGE4Nzk3YWQ5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlVciswakNmVmVtcTVCTHBnVlRsckE9PSIsInZhbHVlIjoiOFV6NGF2WFhOZml4dzRSTTExL2x5eER5YXc2L2hIODVJWVlBRjRKNU14ZkNmVEhqdlVrY2ZyanU1UzVRYWQyQ0ZmM0NJNzRpT2EzZmd2UVBGZ2U3L0pHeXhCbnJXdXFLamRSWGtuUC9PQkN5WXBsOWxMQThVT2w1U3htUnN3RmZDVnpzSWl6L1BzRzNpM2R0M3NpaE1QcVhxZGQ2Mk9EczFaSS9hZlM0N3lTcUhFYXF0dzkyRGxEN2NEZTQ1YUU4QTh2OXlmd0JXZjYvU1hNVlk4TG8xVlBWdjk3L3FrYVVrbzJMaXIwZ3lpZ3ZzVU5lQjk1Zmg1d3FNR0IzeUdWMVNDRHN2WnpDZXVrUVVkdDExbVUycDVpbjdvWWZudjgreXRXVnVxeW9OOWdlSDlSWjlzd1VSTWNIZitmQ3YwNnRwR09PeVpORSt5ZmdGY2JuUWFBbmRhUmVudSs5bHlaaGp5QnJHeWdSRUNvVzgwaFVieUZ5ZEJ2NzFOdzE5b1M1UTQ5b05lNFRERXRYUDNvNk5veTN4cUtJelY0Q210QTYyRVVPUFVYeUJ1L3dTRk9DTnQxai9jTWZjdmVva3BKUU1yaEVHeHk1L2xEQm8za2J3LzcwS2xjZjYwUi94aEF3V1RlMXVNRm9VOTF5U3BLd2wycHlBelNwcENnays0aTMiLCJtYWMiOiI5NDEzOGIyMzg5NjliNGQyODE5ZmMwMGJiZWI3MGY4YmMwMWU1YWFjZDBhNzcyYjJjOWM5N2FhOTNlNGI1YmYwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZFMDhKZklaT3NLVTRxSzdYUm9VSlE9PSIsInZhbHVlIjoiVlFSdGc1NnhKUktDMlFYYXp4bW1kM0Q5TmhwN2VUYmY4bERDZHVMVDV3TlA1dTRlVWlGT3l0YTMzdFNNZlR4SldUVGM3NlYrMmZvakRxaC9HT3pFcUd5REtwaDNNNXBBdS9hZnhoU3BmYWFJR2xzeGUxWWJSenNZcFl5am1PaEkwTFNIam92NjgzM1I2OTA3YVJ6bklGS2FiLzNoR3ZDVDZoSDZYSjFEN2l5SlpBZUVxSGN0K1ZRV3VtNlJhRmwrUjkzZzVTRzFCaTZZNzBMMGdySnhxVEkycTNxem9ZZGFMdHVMSzE0bk1rRjZqRnhYRm9RZi9EcjJhdmRTQVZkS1BkMHdlVDJ3Q2lWakE0QnVOVk9aVDgzemExaVFBQ1NEdUJiMjlYQlFock5ldThrNC94bE1NS2FqTVYzL3FGSHA0eDVCS2FmcGpYS0YreVVzOUt0YUExNXZKaFgzaXR1VjZKWVlhWGRmdHlpN1MvSURHdERpVHJ4RGNreWJwK2F2VjBPblh2NHltMEx5L0pCSVBYSXR3OVhza0NXNWM1eXBqNEc4a1R0ZUtCclZXRm0zdEROSEhrZy9sVUtKeWNIYXBuMytFMTVzOHppUjVEeTMyaUk1a1RqYjlQTFZ2TnVYWTFHMElteGRsa3lYc2VLejgwemRxcUpOU2sxRnl3MmwiLCJtYWMiOiIyYjg0ZGZmMjgyNWM1ZGRiNDcwMWUxMDg1M2Q0MDQ3MzIzYzZiNWMzNDgxNGI0NTYwYzljYTgyOGE4Nzk3YWQ5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1221713250 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InltUjh0Y1A3ZkxDdnZPUWl6OVNGOFE9PSIsInZhbHVlIjoib2d1cVlqK1EwNFR3T25Ra1hBSTAzdz09IiwibWFjIjoiYTEyNzViNzg0YWM4ZTExYjljYTBiNDM2ZDdkYzY4NmQzOGJiOGEwMjk3NzMyZjBiMjBjYmQ3ZGMyM2ZiOTQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1576;&#1610;&#1604; &#1576;&#1606;&#1580;&#1575;&#1581;.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1221713250\", {\"maxDepth\":0})</script>\n"}}