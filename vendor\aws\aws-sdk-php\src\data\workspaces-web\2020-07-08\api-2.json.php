<?php
// This file was auto-generated from sdk-root/src/data/workspaces-web/2020-07-08/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-07-08', 'endpointPrefix' => 'workspaces-web', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon WorkSpaces Web', 'serviceId' => 'WorkSpaces Web', 'signatureVersion' => 'v4', 'signingName' => 'workspaces-web', 'uid' => 'workspaces-web-2020-07-08', ], 'operations' => [ 'AssociateBrowserSettings' => [ 'name' => 'AssociateBrowserSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/portals/{portalArn+}/browserSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateBrowserSettingsRequest', ], 'output' => [ 'shape' => 'AssociateBrowserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'AssociateIpAccessSettings' => [ 'name' => 'AssociateIpAccessSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/portals/{portalArn+}/ipAccessSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateIpAccessSettingsRequest', ], 'output' => [ 'shape' => 'AssociateIpAccessSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'AssociateNetworkSettings' => [ 'name' => 'AssociateNetworkSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/portals/{portalArn+}/networkSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateNetworkSettingsRequest', ], 'output' => [ 'shape' => 'AssociateNetworkSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'AssociateTrustStore' => [ 'name' => 'AssociateTrustStore', 'http' => [ 'method' => 'PUT', 'requestUri' => '/portals/{portalArn+}/trustStores', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateTrustStoreRequest', ], 'output' => [ 'shape' => 'AssociateTrustStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'AssociateUserAccessLoggingSettings' => [ 'name' => 'AssociateUserAccessLoggingSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/portals/{portalArn+}/userAccessLoggingSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateUserAccessLoggingSettingsRequest', ], 'output' => [ 'shape' => 'AssociateUserAccessLoggingSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'AssociateUserSettings' => [ 'name' => 'AssociateUserSettings', 'http' => [ 'method' => 'PUT', 'requestUri' => '/portals/{portalArn+}/userSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateUserSettingsRequest', ], 'output' => [ 'shape' => 'AssociateUserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'CreateBrowserSettings' => [ 'name' => 'CreateBrowserSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/browserSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateBrowserSettingsRequest', ], 'output' => [ 'shape' => 'CreateBrowserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateIdentityProvider' => [ 'name' => 'CreateIdentityProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/identityProviders', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIdentityProviderRequest', ], 'output' => [ 'shape' => 'CreateIdentityProviderResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateIpAccessSettings' => [ 'name' => 'CreateIpAccessSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/ipAccessSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIpAccessSettingsRequest', ], 'output' => [ 'shape' => 'CreateIpAccessSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateNetworkSettings' => [ 'name' => 'CreateNetworkSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/networkSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateNetworkSettingsRequest', ], 'output' => [ 'shape' => 'CreateNetworkSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreatePortal' => [ 'name' => 'CreatePortal', 'http' => [ 'method' => 'POST', 'requestUri' => '/portals', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePortalRequest', ], 'output' => [ 'shape' => 'CreatePortalResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateTrustStore' => [ 'name' => 'CreateTrustStore', 'http' => [ 'method' => 'POST', 'requestUri' => '/trustStores', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTrustStoreRequest', ], 'output' => [ 'shape' => 'CreateTrustStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateUserAccessLoggingSettings' => [ 'name' => 'CreateUserAccessLoggingSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/userAccessLoggingSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateUserAccessLoggingSettingsRequest', ], 'output' => [ 'shape' => 'CreateUserAccessLoggingSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateUserSettings' => [ 'name' => 'CreateUserSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/userSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateUserSettingsRequest', ], 'output' => [ 'shape' => 'CreateUserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteBrowserSettings' => [ 'name' => 'DeleteBrowserSettings', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/browserSettings/{browserSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteBrowserSettingsRequest', ], 'output' => [ 'shape' => 'DeleteBrowserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteIdentityProvider' => [ 'name' => 'DeleteIdentityProvider', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/identityProviders/{identityProviderArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIdentityProviderRequest', ], 'output' => [ 'shape' => 'DeleteIdentityProviderResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteIpAccessSettings' => [ 'name' => 'DeleteIpAccessSettings', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/ipAccessSettings/{ipAccessSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIpAccessSettingsRequest', ], 'output' => [ 'shape' => 'DeleteIpAccessSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteNetworkSettings' => [ 'name' => 'DeleteNetworkSettings', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/networkSettings/{networkSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteNetworkSettingsRequest', ], 'output' => [ 'shape' => 'DeleteNetworkSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeletePortal' => [ 'name' => 'DeletePortal', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/portals/{portalArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePortalRequest', ], 'output' => [ 'shape' => 'DeletePortalResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteTrustStore' => [ 'name' => 'DeleteTrustStore', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/trustStores/{trustStoreArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTrustStoreRequest', ], 'output' => [ 'shape' => 'DeleteTrustStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteUserAccessLoggingSettings' => [ 'name' => 'DeleteUserAccessLoggingSettings', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/userAccessLoggingSettings/{userAccessLoggingSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteUserAccessLoggingSettingsRequest', ], 'output' => [ 'shape' => 'DeleteUserAccessLoggingSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteUserSettings' => [ 'name' => 'DeleteUserSettings', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/userSettings/{userSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteUserSettingsRequest', ], 'output' => [ 'shape' => 'DeleteUserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DisassociateBrowserSettings' => [ 'name' => 'DisassociateBrowserSettings', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/portals/{portalArn+}/browserSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateBrowserSettingsRequest', ], 'output' => [ 'shape' => 'DisassociateBrowserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DisassociateIpAccessSettings' => [ 'name' => 'DisassociateIpAccessSettings', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/portals/{portalArn+}/ipAccessSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateIpAccessSettingsRequest', ], 'output' => [ 'shape' => 'DisassociateIpAccessSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DisassociateNetworkSettings' => [ 'name' => 'DisassociateNetworkSettings', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/portals/{portalArn+}/networkSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateNetworkSettingsRequest', ], 'output' => [ 'shape' => 'DisassociateNetworkSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DisassociateTrustStore' => [ 'name' => 'DisassociateTrustStore', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/portals/{portalArn+}/trustStores', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateTrustStoreRequest', ], 'output' => [ 'shape' => 'DisassociateTrustStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DisassociateUserAccessLoggingSettings' => [ 'name' => 'DisassociateUserAccessLoggingSettings', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/portals/{portalArn+}/userAccessLoggingSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateUserAccessLoggingSettingsRequest', ], 'output' => [ 'shape' => 'DisassociateUserAccessLoggingSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DisassociateUserSettings' => [ 'name' => 'DisassociateUserSettings', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/portals/{portalArn+}/userSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateUserSettingsRequest', ], 'output' => [ 'shape' => 'DisassociateUserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'GetBrowserSettings' => [ 'name' => 'GetBrowserSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/browserSettings/{browserSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBrowserSettingsRequest', ], 'output' => [ 'shape' => 'GetBrowserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetIdentityProvider' => [ 'name' => 'GetIdentityProvider', 'http' => [ 'method' => 'GET', 'requestUri' => '/identityProviders/{identityProviderArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdentityProviderRequest', ], 'output' => [ 'shape' => 'GetIdentityProviderResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetIpAccessSettings' => [ 'name' => 'GetIpAccessSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/ipAccessSettings/{ipAccessSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIpAccessSettingsRequest', ], 'output' => [ 'shape' => 'GetIpAccessSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetNetworkSettings' => [ 'name' => 'GetNetworkSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/networkSettings/{networkSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetNetworkSettingsRequest', ], 'output' => [ 'shape' => 'GetNetworkSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetPortal' => [ 'name' => 'GetPortal', 'http' => [ 'method' => 'GET', 'requestUri' => '/portals/{portalArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPortalRequest', ], 'output' => [ 'shape' => 'GetPortalResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetPortalServiceProviderMetadata' => [ 'name' => 'GetPortalServiceProviderMetadata', 'http' => [ 'method' => 'GET', 'requestUri' => '/portalIdp/{portalArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPortalServiceProviderMetadataRequest', ], 'output' => [ 'shape' => 'GetPortalServiceProviderMetadataResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetTrustStore' => [ 'name' => 'GetTrustStore', 'http' => [ 'method' => 'GET', 'requestUri' => '/trustStores/{trustStoreArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTrustStoreRequest', ], 'output' => [ 'shape' => 'GetTrustStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetTrustStoreCertificate' => [ 'name' => 'GetTrustStoreCertificate', 'http' => [ 'method' => 'GET', 'requestUri' => '/trustStores/{trustStoreArn+}/certificate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTrustStoreCertificateRequest', ], 'output' => [ 'shape' => 'GetTrustStoreCertificateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetUserAccessLoggingSettings' => [ 'name' => 'GetUserAccessLoggingSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/userAccessLoggingSettings/{userAccessLoggingSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUserAccessLoggingSettingsRequest', ], 'output' => [ 'shape' => 'GetUserAccessLoggingSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetUserSettings' => [ 'name' => 'GetUserSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/userSettings/{userSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUserSettingsRequest', ], 'output' => [ 'shape' => 'GetUserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListBrowserSettings' => [ 'name' => 'ListBrowserSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/browserSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBrowserSettingsRequest', ], 'output' => [ 'shape' => 'ListBrowserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListIdentityProviders' => [ 'name' => 'ListIdentityProviders', 'http' => [ 'method' => 'GET', 'requestUri' => '/portals/{portalArn+}/identityProviders', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdentityProvidersRequest', ], 'output' => [ 'shape' => 'ListIdentityProvidersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListIpAccessSettings' => [ 'name' => 'ListIpAccessSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/ipAccessSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIpAccessSettingsRequest', ], 'output' => [ 'shape' => 'ListIpAccessSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListNetworkSettings' => [ 'name' => 'ListNetworkSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/networkSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListNetworkSettingsRequest', ], 'output' => [ 'shape' => 'ListNetworkSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPortals' => [ 'name' => 'ListPortals', 'http' => [ 'method' => 'GET', 'requestUri' => '/portals', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPortalsRequest', ], 'output' => [ 'shape' => 'ListPortalsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTrustStoreCertificates' => [ 'name' => 'ListTrustStoreCertificates', 'http' => [ 'method' => 'GET', 'requestUri' => '/trustStores/{trustStoreArn+}/certificates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrustStoreCertificatesRequest', ], 'output' => [ 'shape' => 'ListTrustStoreCertificatesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTrustStores' => [ 'name' => 'ListTrustStores', 'http' => [ 'method' => 'GET', 'requestUri' => '/trustStores', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrustStoresRequest', ], 'output' => [ 'shape' => 'ListTrustStoresResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListUserAccessLoggingSettings' => [ 'name' => 'ListUserAccessLoggingSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/userAccessLoggingSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListUserAccessLoggingSettingsRequest', ], 'output' => [ 'shape' => 'ListUserAccessLoggingSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListUserSettings' => [ 'name' => 'ListUserSettings', 'http' => [ 'method' => 'GET', 'requestUri' => '/userSettings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListUserSettingsRequest', ], 'output' => [ 'shape' => 'ListUserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateBrowserSettings' => [ 'name' => 'UpdateBrowserSettings', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/browserSettings/{browserSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBrowserSettingsRequest', ], 'output' => [ 'shape' => 'UpdateBrowserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateIdentityProvider' => [ 'name' => 'UpdateIdentityProvider', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/identityProviders/{identityProviderArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIdentityProviderRequest', ], 'output' => [ 'shape' => 'UpdateIdentityProviderResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateIpAccessSettings' => [ 'name' => 'UpdateIpAccessSettings', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/ipAccessSettings/{ipAccessSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIpAccessSettingsRequest', ], 'output' => [ 'shape' => 'UpdateIpAccessSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateNetworkSettings' => [ 'name' => 'UpdateNetworkSettings', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/networkSettings/{networkSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateNetworkSettingsRequest', ], 'output' => [ 'shape' => 'UpdateNetworkSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdatePortal' => [ 'name' => 'UpdatePortal', 'http' => [ 'method' => 'PUT', 'requestUri' => '/portals/{portalArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePortalRequest', ], 'output' => [ 'shape' => 'UpdatePortalResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateTrustStore' => [ 'name' => 'UpdateTrustStore', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/trustStores/{trustStoreArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTrustStoreRequest', ], 'output' => [ 'shape' => 'UpdateTrustStoreResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateUserAccessLoggingSettings' => [ 'name' => 'UpdateUserAccessLoggingSettings', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/userAccessLoggingSettings/{userAccessLoggingSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateUserAccessLoggingSettingsRequest', ], 'output' => [ 'shape' => 'UpdateUserAccessLoggingSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateUserSettings' => [ 'name' => 'UpdateUserSettings', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/userSettings/{userSettingsArn+}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateUserSettingsRequest', ], 'output' => [ 'shape' => 'UpdateUserSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[\\w+=\\/,.@-]+:[a-zA-Z0-9\\-]+:[a-zA-Z0-9\\-]*:[a-zA-Z0-9]{1,12}:[a-zA-Z]+(\\/[a-fA-F0-9\\-]{36})+$', ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ARN', ], ], 'AssociateBrowserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'browserSettingsArn', 'portalArn', ], 'members' => [ 'browserSettingsArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'browserSettingsArn', ], 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'AssociateBrowserSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'browserSettingsArn', 'portalArn', ], 'members' => [ 'browserSettingsArn' => [ 'shape' => 'ARN', ], 'portalArn' => [ 'shape' => 'ARN', ], ], ], 'AssociateIpAccessSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'ipAccessSettingsArn', 'portalArn', ], 'members' => [ 'ipAccessSettingsArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'ipAccessSettingsArn', ], 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'AssociateIpAccessSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'ipAccessSettingsArn', 'portalArn', ], 'members' => [ 'ipAccessSettingsArn' => [ 'shape' => 'ARN', ], 'portalArn' => [ 'shape' => 'ARN', ], ], ], 'AssociateNetworkSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'networkSettingsArn', 'portalArn', ], 'members' => [ 'networkSettingsArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'networkSettingsArn', ], 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'AssociateNetworkSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'networkSettingsArn', 'portalArn', ], 'members' => [ 'networkSettingsArn' => [ 'shape' => 'ARN', ], 'portalArn' => [ 'shape' => 'ARN', ], ], ], 'AssociateTrustStoreRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', 'trustStoreArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], 'trustStoreArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'trustStoreArn', ], ], ], 'AssociateTrustStoreResponse' => [ 'type' => 'structure', 'required' => [ 'portalArn', 'trustStoreArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', ], 'trustStoreArn' => [ 'shape' => 'ARN', ], ], ], 'AssociateUserAccessLoggingSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', 'userAccessLoggingSettingsArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], 'userAccessLoggingSettingsArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'userAccessLoggingSettingsArn', ], ], ], 'AssociateUserAccessLoggingSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'portalArn', 'userAccessLoggingSettingsArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', ], 'userAccessLoggingSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'AssociateUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', 'userSettingsArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], 'userSettingsArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'userSettingsArn', ], ], ], 'AssociateUserSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'portalArn', 'userSettingsArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', ], 'userSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'AuthenticationType' => [ 'type' => 'string', 'enum' => [ 'Standard', 'IAM_Identity_Center', ], ], 'BrowserPolicy' => [ 'type' => 'string', 'max' => 131072, 'min' => 2, 'pattern' => '\\{[\\S\\s]*\\}\\s*', 'sensitive' => true, ], 'BrowserSettings' => [ 'type' => 'structure', 'required' => [ 'browserSettingsArn', ], 'members' => [ 'additionalEncryptionContext' => [ 'shape' => 'EncryptionContextMap', ], 'associatedPortalArns' => [ 'shape' => 'ArnList', ], 'browserPolicy' => [ 'shape' => 'BrowserPolicy', ], 'browserSettingsArn' => [ 'shape' => 'ARN', ], 'customerManagedKey' => [ 'shape' => 'keyArn', ], ], ], 'BrowserSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BrowserSettingsSummary', ], ], 'BrowserSettingsSummary' => [ 'type' => 'structure', 'required' => [ 'browserSettingsArn', ], 'members' => [ 'browserSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'BrowserType' => [ 'type' => 'string', 'enum' => [ 'Chrome', ], ], 'Certificate' => [ 'type' => 'structure', 'members' => [ 'body' => [ 'shape' => 'CertificateAuthorityBody', ], 'issuer' => [ 'shape' => 'CertificatePrincipal', ], 'notValidAfter' => [ 'shape' => 'Timestamp', ], 'notValidBefore' => [ 'shape' => 'Timestamp', ], 'subject' => [ 'shape' => 'CertificatePrincipal', ], 'thumbprint' => [ 'shape' => 'CertificateThumbprint', ], ], ], 'CertificateAuthorityBody' => [ 'type' => 'blob', ], 'CertificateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CertificateAuthorityBody', ], ], 'CertificatePrincipal' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^\\S+$', ], 'CertificateSummary' => [ 'type' => 'structure', 'members' => [ 'issuer' => [ 'shape' => 'CertificatePrincipal', ], 'notValidAfter' => [ 'shape' => 'Timestamp', ], 'notValidBefore' => [ 'shape' => 'Timestamp', ], 'subject' => [ 'shape' => 'CertificatePrincipal', ], 'thumbprint' => [ 'shape' => 'CertificateThumbprint', ], ], ], 'CertificateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CertificateSummary', ], ], 'CertificateThumbprint' => [ 'type' => 'string', 'max' => 64, 'min' => 64, 'pattern' => '^[A-Fa-f0-9]{64}$', ], 'CertificateThumbprintList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CertificateThumbprint', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CookieDomain' => [ 'type' => 'string', 'max' => 253, 'min' => 0, 'pattern' => '^(\\.?)(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\\.)*[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$', 'sensitive' => true, ], 'CookieName' => [ 'type' => 'string', 'max' => 4096, 'min' => 0, 'sensitive' => true, ], 'CookiePath' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, 'pattern' => '^/(\\S)*$', 'sensitive' => true, ], 'CookieSpecification' => [ 'type' => 'structure', 'required' => [ 'domain', ], 'members' => [ 'domain' => [ 'shape' => 'CookieDomain', ], 'name' => [ 'shape' => 'CookieName', ], 'path' => [ 'shape' => 'CookiePath', ], ], ], 'CookieSpecifications' => [ 'type' => 'list', 'member' => [ 'shape' => 'CookieSpecification', ], 'max' => 10, 'min' => 0, ], 'CookieSynchronizationConfiguration' => [ 'type' => 'structure', 'required' => [ 'allowlist', ], 'members' => [ 'allowlist' => [ 'shape' => 'CookieSpecifications', ], 'blocklist' => [ 'shape' => 'CookieSpecifications', ], ], 'sensitive' => true, ], 'CreateBrowserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'browserPolicy', ], 'members' => [ 'additionalEncryptionContext' => [ 'shape' => 'EncryptionContextMap', ], 'browserPolicy' => [ 'shape' => 'BrowserPolicy', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'customerManagedKey' => [ 'shape' => 'keyArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateBrowserSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'browserSettingsArn', ], 'members' => [ 'browserSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'CreateIdentityProviderRequest' => [ 'type' => 'structure', 'required' => [ 'identityProviderDetails', 'identityProviderName', 'identityProviderType', 'portalArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'identityProviderDetails' => [ 'shape' => 'IdentityProviderDetails', ], 'identityProviderName' => [ 'shape' => 'IdentityProviderName', ], 'identityProviderType' => [ 'shape' => 'IdentityProviderType', ], 'portalArn' => [ 'shape' => 'ARN', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateIdentityProviderResponse' => [ 'type' => 'structure', 'required' => [ 'identityProviderArn', ], 'members' => [ 'identityProviderArn' => [ 'shape' => 'SubresourceARN', ], ], ], 'CreateIpAccessSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'ipRules', ], 'members' => [ 'additionalEncryptionContext' => [ 'shape' => 'EncryptionContextMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'customerManagedKey' => [ 'shape' => 'keyArn', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'ipRules' => [ 'shape' => 'IpRuleList', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateIpAccessSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'ipAccessSettingsArn', ], 'members' => [ 'ipAccessSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'CreateNetworkSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'securityGroupIds', 'subnetIds', 'vpcId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'tags' => [ 'shape' => 'TagList', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'CreateNetworkSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'networkSettingsArn', ], 'members' => [ 'networkSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'CreatePortalRequest' => [ 'type' => 'structure', 'members' => [ 'additionalEncryptionContext' => [ 'shape' => 'EncryptionContextMap', ], 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'customerManagedKey' => [ 'shape' => 'keyArn', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'maxConcurrentSessions' => [ 'shape' => 'MaxConcurrentSessions', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreatePortalResponse' => [ 'type' => 'structure', 'required' => [ 'portalArn', 'portalEndpoint', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', ], 'portalEndpoint' => [ 'shape' => 'PortalEndpoint', ], ], ], 'CreateTrustStoreRequest' => [ 'type' => 'structure', 'required' => [ 'certificateList', ], 'members' => [ 'certificateList' => [ 'shape' => 'CertificateList', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateTrustStoreResponse' => [ 'type' => 'structure', 'required' => [ 'trustStoreArn', ], 'members' => [ 'trustStoreArn' => [ 'shape' => 'ARN', ], ], ], 'CreateUserAccessLoggingSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'kinesisStreamArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'kinesisStreamArn' => [ 'shape' => 'KinesisStreamArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateUserAccessLoggingSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'userAccessLoggingSettingsArn', ], 'members' => [ 'userAccessLoggingSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'CreateUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'copyAllowed', 'downloadAllowed', 'pasteAllowed', 'printAllowed', 'uploadAllowed', ], 'members' => [ 'additionalEncryptionContext' => [ 'shape' => 'EncryptionContextMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'cookieSynchronizationConfiguration' => [ 'shape' => 'CookieSynchronizationConfiguration', ], 'copyAllowed' => [ 'shape' => 'EnabledType', ], 'customerManagedKey' => [ 'shape' => 'keyArn', ], 'deepLinkAllowed' => [ 'shape' => 'EnabledType', ], 'disconnectTimeoutInMinutes' => [ 'shape' => 'DisconnectTimeoutInMinutes', ], 'downloadAllowed' => [ 'shape' => 'EnabledType', ], 'idleDisconnectTimeoutInMinutes' => [ 'shape' => 'IdleDisconnectTimeoutInMinutes', ], 'pasteAllowed' => [ 'shape' => 'EnabledType', ], 'printAllowed' => [ 'shape' => 'EnabledType', ], 'tags' => [ 'shape' => 'TagList', ], 'uploadAllowed' => [ 'shape' => 'EnabledType', ], ], ], 'CreateUserSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'userSettingsArn', ], 'members' => [ 'userSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'DeleteBrowserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'browserSettingsArn', ], 'members' => [ 'browserSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'browserSettingsArn', ], ], ], 'DeleteBrowserSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIdentityProviderRequest' => [ 'type' => 'structure', 'required' => [ 'identityProviderArn', ], 'members' => [ 'identityProviderArn' => [ 'shape' => 'SubresourceARN', 'location' => 'uri', 'locationName' => 'identityProviderArn', ], ], ], 'DeleteIdentityProviderResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIpAccessSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'ipAccessSettingsArn', ], 'members' => [ 'ipAccessSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'ipAccessSettingsArn', ], ], ], 'DeleteIpAccessSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteNetworkSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'networkSettingsArn', ], 'members' => [ 'networkSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'networkSettingsArn', ], ], ], 'DeleteNetworkSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePortalRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'DeletePortalResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTrustStoreRequest' => [ 'type' => 'structure', 'required' => [ 'trustStoreArn', ], 'members' => [ 'trustStoreArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'trustStoreArn', ], ], ], 'DeleteTrustStoreResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUserAccessLoggingSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'userAccessLoggingSettingsArn', ], 'members' => [ 'userAccessLoggingSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'userAccessLoggingSettingsArn', ], ], ], 'DeleteUserAccessLoggingSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'userSettingsArn', ], 'members' => [ 'userSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'userSettingsArn', ], ], ], 'DeleteUserSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^.+$', 'sensitive' => true, ], 'DisassociateBrowserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'DisassociateBrowserSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateIpAccessSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'DisassociateIpAccessSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateNetworkSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'DisassociateNetworkSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateTrustStoreRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'DisassociateTrustStoreResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateUserAccessLoggingSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'DisassociateUserAccessLoggingSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'DisassociateUserSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisconnectTimeoutInMinutes' => [ 'type' => 'integer', 'box' => true, 'max' => 600, 'min' => 1, ], 'DisplayName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^.+$', 'sensitive' => true, ], 'EnabledType' => [ 'type' => 'string', 'enum' => [ 'Disabled', 'Enabled', ], ], 'EncryptionContextMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringType', ], 'value' => [ 'shape' => 'StringType', ], ], 'ExceptionMessage' => [ 'type' => 'string', ], 'FieldName' => [ 'type' => 'string', ], 'GetBrowserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'browserSettingsArn', ], 'members' => [ 'browserSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'browserSettingsArn', ], ], ], 'GetBrowserSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'browserSettings' => [ 'shape' => 'BrowserSettings', ], ], ], 'GetIdentityProviderRequest' => [ 'type' => 'structure', 'required' => [ 'identityProviderArn', ], 'members' => [ 'identityProviderArn' => [ 'shape' => 'SubresourceARN', 'location' => 'uri', 'locationName' => 'identityProviderArn', ], ], ], 'GetIdentityProviderResponse' => [ 'type' => 'structure', 'members' => [ 'identityProvider' => [ 'shape' => 'IdentityProvider', ], ], ], 'GetIpAccessSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'ipAccessSettingsArn', ], 'members' => [ 'ipAccessSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'ipAccessSettingsArn', ], ], ], 'GetIpAccessSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'ipAccessSettings' => [ 'shape' => 'IpAccessSettings', ], ], ], 'GetNetworkSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'networkSettingsArn', ], 'members' => [ 'networkSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'networkSettingsArn', ], ], ], 'GetNetworkSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'networkSettings' => [ 'shape' => 'NetworkSettings', ], ], ], 'GetPortalRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'GetPortalResponse' => [ 'type' => 'structure', 'members' => [ 'portal' => [ 'shape' => 'Portal', ], ], ], 'GetPortalServiceProviderMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'GetPortalServiceProviderMetadataResponse' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'portalArn' => [ 'shape' => 'ARN', ], 'serviceProviderSamlMetadata' => [ 'shape' => 'SamlMetadata', ], ], ], 'GetTrustStoreCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'thumbprint', 'trustStoreArn', ], 'members' => [ 'thumbprint' => [ 'shape' => 'CertificateThumbprint', 'location' => 'querystring', 'locationName' => 'thumbprint', ], 'trustStoreArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'trustStoreArn', ], ], ], 'GetTrustStoreCertificateResponse' => [ 'type' => 'structure', 'required' => [ 'trustStoreArn', ], 'members' => [ 'certificate' => [ 'shape' => 'Certificate', ], 'trustStoreArn' => [ 'shape' => 'ARN', ], ], ], 'GetTrustStoreRequest' => [ 'type' => 'structure', 'required' => [ 'trustStoreArn', ], 'members' => [ 'trustStoreArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'trustStoreArn', ], ], ], 'GetTrustStoreResponse' => [ 'type' => 'structure', 'members' => [ 'trustStore' => [ 'shape' => 'TrustStore', ], ], ], 'GetUserAccessLoggingSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'userAccessLoggingSettingsArn', ], 'members' => [ 'userAccessLoggingSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'userAccessLoggingSettingsArn', ], ], ], 'GetUserAccessLoggingSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'userAccessLoggingSettings' => [ 'shape' => 'UserAccessLoggingSettings', ], ], ], 'GetUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'userSettingsArn', ], 'members' => [ 'userSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'userSettingsArn', ], ], ], 'GetUserSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'userSettings' => [ 'shape' => 'UserSettings', ], ], ], 'IdentityProvider' => [ 'type' => 'structure', 'required' => [ 'identityProviderArn', ], 'members' => [ 'identityProviderArn' => [ 'shape' => 'SubresourceARN', ], 'identityProviderDetails' => [ 'shape' => 'IdentityProviderDetails', ], 'identityProviderName' => [ 'shape' => 'IdentityProviderName', ], 'identityProviderType' => [ 'shape' => 'IdentityProviderType', ], ], ], 'IdentityProviderDetails' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringType', ], 'value' => [ 'shape' => 'StringType', ], 'sensitive' => true, ], 'IdentityProviderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdentityProviderSummary', ], ], 'IdentityProviderName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[^_][\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}][^_]+$', 'sensitive' => true, ], 'IdentityProviderSummary' => [ 'type' => 'structure', 'required' => [ 'identityProviderArn', ], 'members' => [ 'identityProviderArn' => [ 'shape' => 'SubresourceARN', ], 'identityProviderName' => [ 'shape' => 'IdentityProviderName', ], 'identityProviderType' => [ 'shape' => 'IdentityProviderType', ], ], ], 'IdentityProviderType' => [ 'type' => 'string', 'enum' => [ 'SAML', 'Facebook', 'Google', 'LoginWithAmazon', 'SignInWithApple', 'OIDC', ], ], 'IdleDisconnectTimeoutInMinutes' => [ 'type' => 'integer', 'box' => true, 'max' => 60, 'min' => 0, ], 'InstanceType' => [ 'type' => 'string', 'enum' => [ 'standard.regular', 'standard.large', 'standard.xlarge', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'retryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'IpAccessSettings' => [ 'type' => 'structure', 'required' => [ 'ipAccessSettingsArn', ], 'members' => [ 'additionalEncryptionContext' => [ 'shape' => 'EncryptionContextMap', ], 'associatedPortalArns' => [ 'shape' => 'ArnList', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'customerManagedKey' => [ 'shape' => 'keyArn', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'ipAccessSettingsArn' => [ 'shape' => 'ARN', ], 'ipRules' => [ 'shape' => 'IpRuleList', ], ], ], 'IpAccessSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAccessSettingsSummary', ], ], 'IpAccessSettingsSummary' => [ 'type' => 'structure', 'required' => [ 'ipAccessSettingsArn', ], 'members' => [ 'creationDate' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'ipAccessSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'IpRange' => [ 'type' => 'string', 'pattern' => '^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(?:/([0-9]|[12][0-9]|3[0-2])|)$', 'sensitive' => true, ], 'IpRule' => [ 'type' => 'structure', 'required' => [ 'ipRange', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'ipRange' => [ 'shape' => 'IpRange', ], ], ], 'IpRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpRule', ], 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'KinesisStreamArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:[\\w+=/,.@-]+:kinesis:[a-zA-Z0-9\\-]*:[a-zA-Z0-9]{1,12}:stream/.+', ], 'ListBrowserSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListBrowserSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'browserSettings' => [ 'shape' => 'BrowserSettingsList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListIdentityProvidersRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'ListIdentityProvidersResponse' => [ 'type' => 'structure', 'members' => [ 'identityProviders' => [ 'shape' => 'IdentityProviderList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListIpAccessSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListIpAccessSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'ipAccessSettings' => [ 'shape' => 'IpAccessSettingsList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListNetworkSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListNetworkSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'networkSettings' => [ 'shape' => 'NetworkSettingsList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPortalsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPortalsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'portals' => [ 'shape' => 'PortalList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'ListTrustStoreCertificatesRequest' => [ 'type' => 'structure', 'required' => [ 'trustStoreArn', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'trustStoreArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'trustStoreArn', ], ], ], 'ListTrustStoreCertificatesResponse' => [ 'type' => 'structure', 'required' => [ 'trustStoreArn', ], 'members' => [ 'certificateList' => [ 'shape' => 'CertificateSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'trustStoreArn' => [ 'shape' => 'ARN', ], ], ], 'ListTrustStoresRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListTrustStoresResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'trustStores' => [ 'shape' => 'TrustStoreSummaryList', ], ], ], 'ListUserAccessLoggingSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListUserAccessLoggingSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'userAccessLoggingSettings' => [ 'shape' => 'UserAccessLoggingSettingsList', ], ], ], 'ListUserSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListUserSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'userSettings' => [ 'shape' => 'UserSettingsList', ], ], ], 'MaxConcurrentSessions' => [ 'type' => 'integer', 'box' => true, 'max' => 5000, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'NetworkSettings' => [ 'type' => 'structure', 'required' => [ 'networkSettingsArn', ], 'members' => [ 'associatedPortalArns' => [ 'shape' => 'ArnList', ], 'networkSettingsArn' => [ 'shape' => 'ARN', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'NetworkSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkSettingsSummary', ], ], 'NetworkSettingsSummary' => [ 'type' => 'structure', 'required' => [ 'networkSettingsArn', ], 'members' => [ 'networkSettingsArn' => [ 'shape' => 'ARN', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\S+$', ], 'Portal' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'additionalEncryptionContext' => [ 'shape' => 'EncryptionContextMap', ], 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'browserSettingsArn' => [ 'shape' => 'ARN', ], 'browserType' => [ 'shape' => 'BrowserType', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'customerManagedKey' => [ 'shape' => 'keyArn', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'ipAccessSettingsArn' => [ 'shape' => 'ARN', ], 'maxConcurrentSessions' => [ 'shape' => 'MaxConcurrentSessions', ], 'networkSettingsArn' => [ 'shape' => 'ARN', ], 'portalArn' => [ 'shape' => 'ARN', ], 'portalEndpoint' => [ 'shape' => 'PortalEndpoint', ], 'portalStatus' => [ 'shape' => 'PortalStatus', ], 'rendererType' => [ 'shape' => 'RendererType', ], 'statusReason' => [ 'shape' => 'StatusReason', ], 'trustStoreArn' => [ 'shape' => 'ARN', ], 'userAccessLoggingSettingsArn' => [ 'shape' => 'ARN', ], 'userSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'PortalEndpoint' => [ 'type' => 'string', 'max' => 253, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]?((?!-)([A-Za-z0-9-]*[A-Za-z0-9])\\.)+[a-zA-Z0-9]+$', ], 'PortalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortalSummary', ], ], 'PortalStatus' => [ 'type' => 'string', 'enum' => [ 'Incomplete', 'Pending', 'Active', ], ], 'PortalSummary' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'browserSettingsArn' => [ 'shape' => 'ARN', ], 'browserType' => [ 'shape' => 'BrowserType', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'ipAccessSettingsArn' => [ 'shape' => 'ARN', ], 'maxConcurrentSessions' => [ 'shape' => 'MaxConcurrentSessions', ], 'networkSettingsArn' => [ 'shape' => 'ARN', ], 'portalArn' => [ 'shape' => 'ARN', ], 'portalEndpoint' => [ 'shape' => 'PortalEndpoint', ], 'portalStatus' => [ 'shape' => 'PortalStatus', ], 'rendererType' => [ 'shape' => 'RendererType', ], 'trustStoreArn' => [ 'shape' => 'ARN', ], 'userAccessLoggingSettingsArn' => [ 'shape' => 'ARN', ], 'userSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'QuotaCode' => [ 'type' => 'string', ], 'RendererType' => [ 'type' => 'string', 'enum' => [ 'AppStream', ], ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'SamlMetadata' => [ 'type' => 'string', 'max' => 204800, 'min' => 1, 'pattern' => '^.+$', ], 'SecurityGroupId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[\\w+\\-]+$', ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupId', ], 'max' => 5, 'min' => 1, ], 'ServiceCode' => [ 'type' => 'string', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'quotaCode' => [ 'shape' => 'QuotaCode', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'serviceCode' => [ 'shape' => 'ServiceCode', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'StatusReason' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*', ], 'StringType' => [ 'type' => 'string', 'max' => 131072, 'min' => 0, 'pattern' => '^[\\s\\S]*$', ], 'SubnetId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^subnet-([0-9a-f]{8}|[0-9a-f]{17})$', ], 'SubnetIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], 'max' => 3, 'min' => 2, ], 'SubresourceARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[\\w+=\\/,.@-]+:[a-zA-Z0-9\\-]+:[a-zA-Z0-9\\-]*:[a-zA-Z0-9]{1,12}:[a-zA-Z]+(\\/[a-fA-F0-9\\-]{36}){2,}$', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], 'sensitive' => true, ], 'TagExceptionMessage' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', 'sensitive' => true, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', 'sensitive' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'quotaCode' => [ 'shape' => 'QuotaCode', ], 'retryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], 'serviceCode' => [ 'shape' => 'ServiceCode', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'TagExceptionMessage', ], 'resourceName' => [ 'shape' => 'ARN', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TrustStore' => [ 'type' => 'structure', 'required' => [ 'trustStoreArn', ], 'members' => [ 'associatedPortalArns' => [ 'shape' => 'ArnList', ], 'trustStoreArn' => [ 'shape' => 'ARN', ], ], ], 'TrustStoreSummary' => [ 'type' => 'structure', 'members' => [ 'trustStoreArn' => [ 'shape' => 'ARN', ], ], ], 'TrustStoreSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrustStoreSummary', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBrowserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'browserSettingsArn', ], 'members' => [ 'browserPolicy' => [ 'shape' => 'BrowserPolicy', ], 'browserSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'browserSettingsArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateBrowserSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'browserSettings', ], 'members' => [ 'browserSettings' => [ 'shape' => 'BrowserSettings', ], ], ], 'UpdateIdentityProviderRequest' => [ 'type' => 'structure', 'required' => [ 'identityProviderArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'identityProviderArn' => [ 'shape' => 'SubresourceARN', 'location' => 'uri', 'locationName' => 'identityProviderArn', ], 'identityProviderDetails' => [ 'shape' => 'IdentityProviderDetails', ], 'identityProviderName' => [ 'shape' => 'IdentityProviderName', ], 'identityProviderType' => [ 'shape' => 'IdentityProviderType', ], ], ], 'UpdateIdentityProviderResponse' => [ 'type' => 'structure', 'required' => [ 'identityProvider', ], 'members' => [ 'identityProvider' => [ 'shape' => 'IdentityProvider', ], ], ], 'UpdateIpAccessSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'ipAccessSettingsArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'Description', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'ipAccessSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'ipAccessSettingsArn', ], 'ipRules' => [ 'shape' => 'IpRuleList', ], ], ], 'UpdateIpAccessSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'ipAccessSettings', ], 'members' => [ 'ipAccessSettings' => [ 'shape' => 'IpAccessSettings', ], ], ], 'UpdateNetworkSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'networkSettingsArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'networkSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'networkSettingsArn', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIdList', ], 'subnetIds' => [ 'shape' => 'SubnetIdList', ], 'vpcId' => [ 'shape' => 'VpcId', ], ], ], 'UpdateNetworkSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'networkSettings', ], 'members' => [ 'networkSettings' => [ 'shape' => 'NetworkSettings', ], ], ], 'UpdatePortalRequest' => [ 'type' => 'structure', 'required' => [ 'portalArn', ], 'members' => [ 'authenticationType' => [ 'shape' => 'AuthenticationType', ], 'displayName' => [ 'shape' => 'DisplayName', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'maxConcurrentSessions' => [ 'shape' => 'MaxConcurrentSessions', ], 'portalArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'portalArn', ], ], ], 'UpdatePortalResponse' => [ 'type' => 'structure', 'members' => [ 'portal' => [ 'shape' => 'Portal', ], ], ], 'UpdateTrustStoreRequest' => [ 'type' => 'structure', 'required' => [ 'trustStoreArn', ], 'members' => [ 'certificatesToAdd' => [ 'shape' => 'CertificateList', ], 'certificatesToDelete' => [ 'shape' => 'CertificateThumbprintList', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'trustStoreArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'trustStoreArn', ], ], ], 'UpdateTrustStoreResponse' => [ 'type' => 'structure', 'required' => [ 'trustStoreArn', ], 'members' => [ 'trustStoreArn' => [ 'shape' => 'ARN', ], ], ], 'UpdateUserAccessLoggingSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'userAccessLoggingSettingsArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'kinesisStreamArn' => [ 'shape' => 'KinesisStreamArn', ], 'userAccessLoggingSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'userAccessLoggingSettingsArn', ], ], ], 'UpdateUserAccessLoggingSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'userAccessLoggingSettings', ], 'members' => [ 'userAccessLoggingSettings' => [ 'shape' => 'UserAccessLoggingSettings', ], ], ], 'UpdateUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'userSettingsArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'cookieSynchronizationConfiguration' => [ 'shape' => 'CookieSynchronizationConfiguration', ], 'copyAllowed' => [ 'shape' => 'EnabledType', ], 'deepLinkAllowed' => [ 'shape' => 'EnabledType', ], 'disconnectTimeoutInMinutes' => [ 'shape' => 'DisconnectTimeoutInMinutes', ], 'downloadAllowed' => [ 'shape' => 'EnabledType', ], 'idleDisconnectTimeoutInMinutes' => [ 'shape' => 'IdleDisconnectTimeoutInMinutes', ], 'pasteAllowed' => [ 'shape' => 'EnabledType', ], 'printAllowed' => [ 'shape' => 'EnabledType', ], 'uploadAllowed' => [ 'shape' => 'EnabledType', ], 'userSettingsArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'userSettingsArn', ], ], ], 'UpdateUserSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'userSettings', ], 'members' => [ 'userSettings' => [ 'shape' => 'UserSettings', ], ], ], 'UserAccessLoggingSettings' => [ 'type' => 'structure', 'required' => [ 'userAccessLoggingSettingsArn', ], 'members' => [ 'associatedPortalArns' => [ 'shape' => 'ArnList', ], 'kinesisStreamArn' => [ 'shape' => 'KinesisStreamArn', ], 'userAccessLoggingSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'UserAccessLoggingSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserAccessLoggingSettingsSummary', ], ], 'UserAccessLoggingSettingsSummary' => [ 'type' => 'structure', 'required' => [ 'userAccessLoggingSettingsArn', ], 'members' => [ 'kinesisStreamArn' => [ 'shape' => 'KinesisStreamArn', ], 'userAccessLoggingSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'UserSettings' => [ 'type' => 'structure', 'required' => [ 'userSettingsArn', ], 'members' => [ 'additionalEncryptionContext' => [ 'shape' => 'EncryptionContextMap', ], 'associatedPortalArns' => [ 'shape' => 'ArnList', ], 'cookieSynchronizationConfiguration' => [ 'shape' => 'CookieSynchronizationConfiguration', ], 'copyAllowed' => [ 'shape' => 'EnabledType', ], 'customerManagedKey' => [ 'shape' => 'keyArn', ], 'deepLinkAllowed' => [ 'shape' => 'EnabledType', ], 'disconnectTimeoutInMinutes' => [ 'shape' => 'DisconnectTimeoutInMinutes', ], 'downloadAllowed' => [ 'shape' => 'EnabledType', ], 'idleDisconnectTimeoutInMinutes' => [ 'shape' => 'IdleDisconnectTimeoutInMinutes', ], 'pasteAllowed' => [ 'shape' => 'EnabledType', ], 'printAllowed' => [ 'shape' => 'EnabledType', ], 'uploadAllowed' => [ 'shape' => 'EnabledType', ], 'userSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'UserSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserSettingsSummary', ], ], 'UserSettingsSummary' => [ 'type' => 'structure', 'required' => [ 'userSettingsArn', ], 'members' => [ 'cookieSynchronizationConfiguration' => [ 'shape' => 'CookieSynchronizationConfiguration', ], 'copyAllowed' => [ 'shape' => 'EnabledType', ], 'deepLinkAllowed' => [ 'shape' => 'EnabledType', ], 'disconnectTimeoutInMinutes' => [ 'shape' => 'DisconnectTimeoutInMinutes', ], 'downloadAllowed' => [ 'shape' => 'EnabledType', ], 'idleDisconnectTimeoutInMinutes' => [ 'shape' => 'IdleDisconnectTimeoutInMinutes', ], 'pasteAllowed' => [ 'shape' => 'EnabledType', ], 'printAllowed' => [ 'shape' => 'EnabledType', ], 'uploadAllowed' => [ 'shape' => 'EnabledType', ], 'userSettingsArn' => [ 'shape' => 'ARN', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'ExceptionMessage', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'name' => [ 'shape' => 'FieldName', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'VpcId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^vpc-[0-9a-z]*$', ], 'keyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[\\w+=\\/,.@-]+:kms:[a-zA-Z0-9\\-]*:[a-zA-Z0-9]{1,12}:key\\/[a-zA-Z0-9-]+$', ], ],];
