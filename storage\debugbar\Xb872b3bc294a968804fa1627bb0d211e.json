{"__meta": {"id": "Xb872b3bc294a968804fa1627bb0d211e", "datetime": "2025-06-26 23:22:49", "utime": **********.7269, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.322229, "end": **********.726917, "duration": 0.40468811988830566, "duration_str": "405ms", "measures": [{"label": "Booting", "start": **********.322229, "relative_start": 0, "end": **********.676336, "relative_end": **********.676336, "duration": 0.354107141494751, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.676345, "relative_start": 0.35411620140075684, "end": **********.726919, "relative_end": 1.9073486328125e-06, "duration": 0.05057382583618164, "duration_str": "50.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028192, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026, "accumulated_duration_str": "2.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.700618, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.923}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.710536, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.923, "width_percent": 15.385}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.717228, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 77.308, "width_percent": 22.692}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InNYdnZRTUM3bHdPakNXME5ad08yR3c9PSIsInZhbHVlIjoibVNuQXdiOWFiME9obUFEK2tvOGRDUT09IiwibWFjIjoiMjY1NjJhMGY2YTkzMGNmNDM0ZjFhYzNjZjUxMWE4MTJmYmNjMDdjY2VmODBlMmRkNjNhYTA4NTBkZWYxNjJjMyIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1337966646 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1337966646\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1056806007 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1056806007\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1476393517 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1476393517\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1406282300 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6InNYdnZRTUM3bHdPakNXME5ad08yR3c9PSIsInZhbHVlIjoibVNuQXdiOWFiME9obUFEK2tvOGRDUT09IiwibWFjIjoiMjY1NjJhMGY2YTkzMGNmNDM0ZjFhYzNjZjUxMWE4MTJmYmNjMDdjY2VmODBlMmRkNjNhYTA4NTBkZWYxNjJjMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980165310%7C30%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjV3cFY5cWdTMW9PVGFEN1B6NVBhb1E9PSIsInZhbHVlIjoiR2NkYUJKbXFrOTNNS2w4WWJLSGtQR0VjNG1JMWo5WE41UjRNOGR1S1N0dTF3VTZSSmZtVUNtaG5KK05yTGErY040WEw4S1c5eDFYTWlKOURoYzRtS09uOFRIV2hNck5oWmNuNUJhOU5jbUJMMmlYSWRXU2psQ012YWNrUzRZakROb1k4aHJVcGVNVGhRTndzSitxa2FDdFNZeVBLMmN2RksyN2ZkZ2xDUkl3QU1KV3JWOG9OWG1FNm9IZjJ3WkkyNUFvc3VtNEg2YzhEeVhXTFZ6VWtXbndsaDNmcnZjQTRzZWk1Q0tFTXVlbjlsaEdUZ3J2RmVQKzVDUDd5bWhNOXBBR0tGUWt6bEhUUGZ3eXc5TnRnWUN1UDB5OHhuRXp2VmNJS0wrZFdFRkpvdXMrTG5WNUQ5QVh0K1kwVmczRU9udUh4ekxoT0NuWnFnWWNEeUpVeGZZNWRMYmQwZEZweVZoZklLcGdNUTU0c09EWFREZkNWeEdZSHFzYlJQdllod1FJSTRHUXFkUXdSVWJlenBGQk1UdXRDMmc1WDFqcU54ZnpuaDg5YjlGZUl2ZEVYdStYTzFwYVJTc3FQS3Zsb0lXVzRDS2JmbU5BcVVPbCswcHFncmd3VU9vRXkwQnpaSGl1M0l4VWpsL2l2L05zb1NHbGFJakVsTi9OcVllWVUiLCJtYWMiOiIzNWFiNTA1ZDMxYTg2MDAwYWU4MzkxNTI4ZDMzZmYwMTgwOGMyZDhmZWEwMmFmOTY2YTkxMjllZjQ1YmY4N2VjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImxmeW1rK1NUbjg2S213bWpBV1p1Snc9PSIsInZhbHVlIjoiczhSTUtGNS9uN3BqQ1lwdlQzdGVjdnIwbDE4U2RpV1AvVkc3Vzc0bmxHSHREa0VBcVAzakw1eUZIeDUvWXFYUlNxQ3hHU1J0bWJlRFNtcjNhVlphQTY4V3A1Ykk4K0Fyb1JLVE5jU3hoUEMvKzVObFAzelN2V0RNYmFaOTNRVWp1ZmJTTlZpNE84QmdqOE9iWjRzSnJTZEU4SUlucWIyMFAvYlJxbXNPakFLL1FNai9yQUpxWG1nVHdsQXdaYUhqNXFMeHo3SXowNEVnK0xJY2E1djJnRUFVaWlTZUxXK0pkQmxJMkdXbS9HNkgxNVNpU1F1bXpjREkzN3lYdHJZdHRVMTVMQ3MvVTVnaHpFWStJRkRlUWJSVEVYOTBpb25kSCtkRHMvZlZNM3hLK21BWmFQWjljNE9rQ2hlWHhmZDNjdExqUWFoc0ZMMXlFcWJQbHdlTjhRNGJPVkQwdmxkYmxzeEtHVXJqRi9MRUtBWGw2OU45WWNWOGswY0x0Y2E1VFM2SzlwK2pnR1h0cEJNeVoxRnVqc0pselMvSk13eHpKSm1BbzRHSFVPeFIyOFBKbWtqOUlPdVByZ1c1VlVzd1JUWThMdDBQOVdjSThTcEVPQlR1M3R5cEQxaURVVnJvSXlBV2Y2bEVXTjJDK2tJdGx4Zm04RS9hVkdFOVVxc0YiLCJtYWMiOiJlOGQ0OGE5ZDQwNTEyMDU2YzM5MmZiMjBjNWYzOTFjZjYyNmU5NGNkZTAyMGY5ZjEyYzU4NTMxYWE1YTM1Yjg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1406282300\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1055413352 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1055413352\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1800805429 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:22:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1GRmFKMU1xK0V4QzhSMTlMZ2M5cFE9PSIsInZhbHVlIjoiS0Vsa3VBVGY4bEl0NUZzdVlPLytzRGFyZHJ4T1IwdW11K0dRMU80RDdIK1psL01HbjJhTlVuUDZzRFA1TGFNUnNkMkk2YlRJR0JMVlppR0FZTzNaWS9YM2YwKzhWaFU3TktvMHpENzBVT2t1V05rWjhXbWMwZ3FwbXVTdTlJclpUSFl5dDFRNUxuNHowZFFWcEJpS3BoS1ZqNktSZy9YRG9HTVdmRFhWT3JKRDFSQzhHRjNLWWxiRFpxS1ZlNCszckRqT2JTT212LzVOL1BIK0JHWXV2cmhjd1NLOXdiK2k1U0FsdW5TLzJ5bTNhN0d0eWVGWEpjOUJaa3NkNlVkbU5xd2p0OEpyMjJ5QlAwTG0rQ29GaEFHYWpDRlQ3dmsvZWMvcFQ0bUZXbzJ3S3VlVHhqTmpiRzQxLzI1akI1VmxiMTBXMkJHeFY0cjNubTdwbzR4M0NBaVlHcWRuZExOSTM0MjVzWThOb1dVbmZZbG9lcW1KazcyUTVMeVpmOTQ2L2Zqcm4vVWxxZlh6M3dYT05OUmYwdjNoQ1ExQ29LczBtOGdNOWtZcy9maU80R1hrTVJSdG5CaHRYbWc5L3pLZGR3YU8vWkpGQk9zMXl4YmxqSm9uWkVka0YwektBb2pVK0hzaXpGc1F5OGg2LzZySXdMVTZvbVpYNzQyZ0YyWkoiLCJtYWMiOiJkNDY4ZDhkMGJjYjAyNjFjZDc3ZTQ1MzM0MzRiNzBkMzE5MmRjMDBlZDM1MWE0MmZhNzlmM2ZiMzk2OTViMjFiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZLKzAvLy9peHhNSDl6OCtnOS9yOGc9PSIsInZhbHVlIjoia1VjTE4zMk1BWmxwbnEyVHY3S2NwemxqMTY2TkVUdDVKaUVGclVJbm1UOWxPZ2JtNnhRSWQyOGtjLzFFV0dzSkRDTXlma1hrT2YzWXluNjFkb3VQeW5YRkRHYmZ5SFViZDJBRUI5YjFkNkVXVmE1Zmt5blBhUEdmWnc1YjdHRHp4YmViczhVQndDUCtmRE9Qd0FjVXdIb09sZXZVWHNOWWtSeDY2WG5mVjBaRHBHNWk1c1FUbnZUck1sVldLM2pjTlViUDBhb1RiRXFZSTZZb3NjYzJhV2xSd2ZFLyswKzBJb3MxZ05RYkZPczhIS3FkZmxSM2orQS9kQUlNNFFMcmJabDRYRndueFRWMUI1VlFBQzdxY1B6d1NXR3BxWXIzTkZMcWNZTnZ6cm1mMzZKek1PcWNBdUxURGw3TjhYMEdXVmNjdStQQ2lvSlIyaFFETm4rdkFMMzMyUlo0akk5Y2w4R2FjaVBhelc3L1ZESVpMWXBwKzFPU0ZObiszcmlJTjJPSEJlS1NIcmhiOUZxbUpDRE5pVXVua1NNVnM0blkvVXNqMDVKc3A1OFVzNXV3anYwVzl3K1FIei8wUGJCWFJOTDQyUlZwbGNZdVVvOXZoRHJ3WTNVZSsrYWRicHVHUEorVkNvQ3RIV2JWeGJla2ZmZGFRdjJVVXd0QThqYUMiLCJtYWMiOiI5ZmNlMjNmZWFiYmIwYjQwOGQ1ODU1MjVjYjQxYjg3NmEzMzIzMjM3ODFlNzA5Zjg3OWQwNTY2MjI0YzBjOTg3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1GRmFKMU1xK0V4QzhSMTlMZ2M5cFE9PSIsInZhbHVlIjoiS0Vsa3VBVGY4bEl0NUZzdVlPLytzRGFyZHJ4T1IwdW11K0dRMU80RDdIK1psL01HbjJhTlVuUDZzRFA1TGFNUnNkMkk2YlRJR0JMVlppR0FZTzNaWS9YM2YwKzhWaFU3TktvMHpENzBVT2t1V05rWjhXbWMwZ3FwbXVTdTlJclpUSFl5dDFRNUxuNHowZFFWcEJpS3BoS1ZqNktSZy9YRG9HTVdmRFhWT3JKRDFSQzhHRjNLWWxiRFpxS1ZlNCszckRqT2JTT212LzVOL1BIK0JHWXV2cmhjd1NLOXdiK2k1U0FsdW5TLzJ5bTNhN0d0eWVGWEpjOUJaa3NkNlVkbU5xd2p0OEpyMjJ5QlAwTG0rQ29GaEFHYWpDRlQ3dmsvZWMvcFQ0bUZXbzJ3S3VlVHhqTmpiRzQxLzI1akI1VmxiMTBXMkJHeFY0cjNubTdwbzR4M0NBaVlHcWRuZExOSTM0MjVzWThOb1dVbmZZbG9lcW1KazcyUTVMeVpmOTQ2L2Zqcm4vVWxxZlh6M3dYT05OUmYwdjNoQ1ExQ29LczBtOGdNOWtZcy9maU80R1hrTVJSdG5CaHRYbWc5L3pLZGR3YU8vWkpGQk9zMXl4YmxqSm9uWkVka0YwektBb2pVK0hzaXpGc1F5OGg2LzZySXdMVTZvbVpYNzQyZ0YyWkoiLCJtYWMiOiJkNDY4ZDhkMGJjYjAyNjFjZDc3ZTQ1MzM0MzRiNzBkMzE5MmRjMDBlZDM1MWE0MmZhNzlmM2ZiMzk2OTViMjFiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZLKzAvLy9peHhNSDl6OCtnOS9yOGc9PSIsInZhbHVlIjoia1VjTE4zMk1BWmxwbnEyVHY3S2NwemxqMTY2TkVUdDVKaUVGclVJbm1UOWxPZ2JtNnhRSWQyOGtjLzFFV0dzSkRDTXlma1hrT2YzWXluNjFkb3VQeW5YRkRHYmZ5SFViZDJBRUI5YjFkNkVXVmE1Zmt5blBhUEdmWnc1YjdHRHp4YmViczhVQndDUCtmRE9Qd0FjVXdIb09sZXZVWHNOWWtSeDY2WG5mVjBaRHBHNWk1c1FUbnZUck1sVldLM2pjTlViUDBhb1RiRXFZSTZZb3NjYzJhV2xSd2ZFLyswKzBJb3MxZ05RYkZPczhIS3FkZmxSM2orQS9kQUlNNFFMcmJabDRYRndueFRWMUI1VlFBQzdxY1B6d1NXR3BxWXIzTkZMcWNZTnZ6cm1mMzZKek1PcWNBdUxURGw3TjhYMEdXVmNjdStQQ2lvSlIyaFFETm4rdkFMMzMyUlo0akk5Y2w4R2FjaVBhelc3L1ZESVpMWXBwKzFPU0ZObiszcmlJTjJPSEJlS1NIcmhiOUZxbUpDRE5pVXVua1NNVnM0blkvVXNqMDVKc3A1OFVzNXV3anYwVzl3K1FIei8wUGJCWFJOTDQyUlZwbGNZdVVvOXZoRHJ3WTNVZSsrYWRicHVHUEorVkNvQ3RIV2JWeGJla2ZmZGFRdjJVVXd0QThqYUMiLCJtYWMiOiI5ZmNlMjNmZWFiYmIwYjQwOGQ1ODU1MjVjYjQxYjg3NmEzMzIzMjM3ODFlNzA5Zjg3OWQwNTY2MjI0YzBjOTg3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800805429\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-702007525 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6InNYdnZRTUM3bHdPakNXME5ad08yR3c9PSIsInZhbHVlIjoibVNuQXdiOWFiME9obUFEK2tvOGRDUT09IiwibWFjIjoiMjY1NjJhMGY2YTkzMGNmNDM0ZjFhYzNjZjUxMWE4MTJmYmNjMDdjY2VmODBlMmRkNjNhYTA4NTBkZWYxNjJjMyIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702007525\", {\"maxDepth\":0})</script>\n"}}