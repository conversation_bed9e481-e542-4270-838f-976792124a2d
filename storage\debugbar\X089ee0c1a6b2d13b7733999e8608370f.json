{"__meta": {"id": "X089ee0c1a6b2d13b7733999e8608370f", "datetime": "2025-06-26 22:42:42", "utime": **********.470844, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.045586, "end": **********.470857, "duration": 0.42527079582214355, "duration_str": "425ms", "measures": [{"label": "Booting", "start": **********.045586, "relative_start": 0, "end": **********.418688, "relative_end": **********.418688, "duration": 0.37310194969177246, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.418698, "relative_start": 0.3731119632720947, "end": **********.470859, "relative_end": 2.1457672119140625e-06, "duration": 0.05216097831726074, "duration_str": "52.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028192, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00311, "accumulated_duration_str": "3.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4477699, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.846}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.45808, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.846, "width_percent": 15.434}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.464024, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.28, "width_percent": 16.72}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6ImtHUzRVRUJ0YXBURTkzck1ySFdNR3c9PSIsInZhbHVlIjoiL2VXNE1oRVRRT2tyTVFxSWlmeWhrUT09IiwibWFjIjoiNzk1YmRkMGZjZWMyOGEyODI1MTI4M2JmOWQ5YzQ4MmZlZjUzMDZlZjhhMjExOGU2NjU3NGNlYjA5OGVmMzA1MiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-844266033 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-844266033\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-966358243 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-966358243\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-344021437 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344021437\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1533077830 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6ImtHUzRVRUJ0YXBURTkzck1ySFdNR3c9PSIsInZhbHVlIjoiL2VXNE1oRVRRT2tyTVFxSWlmeWhrUT09IiwibWFjIjoiNzk1YmRkMGZjZWMyOGEyODI1MTI4M2JmOWQ5YzQ4MmZlZjUzMDZlZjhhMjExOGU2NjU3NGNlYjA5OGVmMzA1MiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=3thn9%7C1750977758911%7C13%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImM0VEtCUi9uMEV4bHlWYTdmMXE5M2c9PSIsInZhbHVlIjoiVlYyZGJOM1c3aDk1R0Q5ZzhIUkFFanlrOEV1WU5EWG9RdmNDZ0RDLzJYcXQ0N0owUERYM1NmYm1lU21QSWhjNHV5M05weHFSNVkxSlZIWEtGUzdKOTBScm9UQkdjR2VER2NTZWxBcjBGU3pKai9JRnlYYndrQnh6cDFSb2JPbFdTU2FFYXpsRG5Ic1o3d2VKMHVINEFLNzU0VEdUTXI1WlRsNVpYTWR1VDY4OFRaVTNxQVAvK1NBMWRnR1NaSW00Nytzd3R0WGpOMTZlblB0Q283YytOTXBaYy9xbTJjc3IxdHg1TnNINWlnaWVnUzBQS1kxY1RRaXkwVlFqNDVkRHNVdWxRVlZLc2dJbFFQZTBPUzBLRXdBd3h1amo4TGtvZUpORnJFb1Yva3VITkxPL0phTE9PSS9QNG0rMU4wWEgrYWwySS9UYVZLNUVXWlFKQlFIVExid1R4aDVUUG16ZGpBNE9KaVNLQlArbS8xb1RQZ3VFWTMyQmZWWUJNQmFRRC9zS05KcGVsNU1pVThqeUVncmVNT2pvWlJ1WDljS0ZkeVZZUTRsbExpMEFHN2dUYVJVY1dDVDBwbllQaFVsd01PM3FGV295bjY5eXVFS3B0c3JURzRJSzRnbmhmUlJhRG5nVnpzRWZTNlNwUldyMUc4Y3F2Q3VSQUJ5MzlNS3oiLCJtYWMiOiJlOWMwNjY4YmE4NmE5M2EyNGEzOWE0MDc4MWJhMjA4OGZlNGY5Y2MyOGE5OGUxYTQ2ODAxYjE0ZDhmMjI3MWIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhqdlFFNzROa3UwZ214MDczanNPVmc9PSIsInZhbHVlIjoidGlKeWZSaFVlN04wTzBHZ0dIL094cStZWkNCcVVKRTlNeFRmaFJDUGR0dmpwOGF1eXNNNG1aZExXSk9ucW5rRHdPN2MyaXFwTEJYbWJCZFdwMEREa3RMUFdhTnJNS3VRc253V2dLTDNYd0tMQ2hKdUVQQnNGNVZ1Wk5mbldITFZhQUxhWUF0WnVwbVRjZGsyaVVOUDlzTTY2U3dINmN6Si9CUU1pSTB3OUozWU5WaHdtY0NIQWtsRTZBMDRMektaczd2eUJZTlJxQkRxb2JCbkdIRW1jdmNmTGlmUnNleVpaZ2xUUGcxbUtjWnQyVGpERDRVaU4yQ3pZS0cvWUVlbFZia0kzZXJPYUtNK0RQRVU3M3czenVMVVFscjJoVUFQMDVuNWFZYktoUDNZcGg1VDdzeVVtcG8zcmRubTB1ZlBBUUdCbHlhZU1DRlcxTStZNkcxd01yajg3TnFDMndaRFYyYWh4WDlYRjlmZTFhUXdkQU1qTFl4UGR2UjFveThqbjZwRG0ybFcxWTc1cXhGYmJWQXpxZFh2TkptcUJJb0RKOWVqdXNzS2s4dkw0dXpaUDJ2cHZYeEo5dVFhU1NBUkQ1TVRDYTFFa1MyUEFhNDNzWlgydVFUNS81d0pYZ01qRnZEeXpETWpEc3I5VmV3ZDVGS1FJd1h5d3R3R0twZHciLCJtYWMiOiI3NmJkZDRjYmIwOWZlZWE5Yjg2NjQwZjI5ODAwOTA3NWNkNWNkOGVmNWVjNTMyOWE4MDY0ZWY2ZmZiYzFlZDI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533077830\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-623895655 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623895655\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2036011862 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 22:42:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZNVDQrV3NQY2gxSHNmN1ZUT3dlM1E9PSIsInZhbHVlIjoiTVpRK2lPaUQveHZ2eDluWXJ2ZG5FZkk1MWp4QXEwMWJWa0RaaExWK2VHUGRHWmh1QkhjK1U0ZU9PZHB4bzBtVk1RejZieHlVSlFEdGZadzYrWjhIYnNLRW11eWticnZnMHUxb2xNUmIxYXM3TjU3N1FEN0hSNktISTQ5d2dtaEdRTjZyTmc1WUpRYXpkRDY0OXIyRFp5cTRwWVQrSDg0YzA2TitQZ0l1dGFDNjNFVm5oamo1SXd2aFRzY0ttUCt6ZUdVRWJWQ2lPWHNmWlBvcnR0enRMVjhVaThrRkRoSk92VEliOHd5M2lxTC9UaHZPaE1OdEpINHFrQ2ZVRHM2UlFTSHorR2pQQk4raU9QVFM1NEJJaSt1T2g0aGFXQldRcE1pQU1KdmJlMUN2QkRZYXVzdWp6N0ZiVUVvWXJtV3I3aldJQ1lYaVJ5NC9SSW02WnU4bkZDZnI0R1orMi85TDBTV1Q1NytxRnl5Y1NlR3dxOVF3djJXeHdhRVQwZGduQmg1ek5vQkRRSXByR00wQ25BeGQybVIyM2JvcEhLcis2UmFNUk1WaFh1TnVTTkRUM0FCRG82RjdTSjEyYkRvdENZbVFsbzR1ek1pUFdxalVkd2NDNDFoTVAzbWNMV1BHLzZvNnpVcjRsZy9FamY3NGcyQkR2dUpMSDJqTlV2K3UiLCJtYWMiOiIxZjg0ZDU5Y2FmOTgxZGVjNWNlZjg4Y2E4ZmU3MmY1OWZkMDE2ZWZiYjU0OThlMzUwMjA0NTY1OWY0OWFhYmYxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVrd1h4TERWeDlBNXNwREdsNTFQcWc9PSIsInZhbHVlIjoiNmRQQm1UVkN3TkZzdjZSTU1QYUFMZENkdllqYW1GZzF1cnVnUTNCL0NTa3Bwc0FMNUp1b3RKNCt4a2k0RndJdTlIOXMzMExseEFlKzJTclZkenlyUjFvOHg3aG93Wnd1MlZWdHlXbTc4aVVYRmorWjNxVGMyaHV0WWRuN0hXMnNQU3dnalE0VnpPUmRWRmtJQmpKM1lnY0xUOVk3WS9HMDNJU21RMm5WQ08ybW0yNFl1QzFldnhpZ1p1NEw1U2dTRXNNeTg0NXZxeVhidi9wUWZudGc5YnpvRHFKMlNaNEs4OVJ0MEFGbVlNTGd1NEROc01wUTcvS0lGS2FOTnpJd3N0R0JURndoMXlsUTUydjdTZFBMSnZ1bXBHdnB3WG5UZVk1dVY1QzNoU2RKTDB4OHhWOU1wL3RMd1gyMk1NTkp3T09paUpNSjg5RHFIRFoxOGVPQmRManE0b1k5SGQwd3RZaGRLblBEQXdvVldHdUxkeUpPbEI4Wk5Lbm8wbGlwOUthQUNEdnhxcWRFWUJYS1lReXEzNU02L0crYU9sd1JUd1BiTEVoSklXN0k5MFFwTjJqeTlURHFwOVpVZ1I2MWxIZUJWSkp3bjdLT3VIcnZQRGd5UUMzU0k0TUllVE9obUtNcktPaUpKQmRKMzE0ajBqbjhQWFpvYnArbERLcUsiLCJtYWMiOiIwYjY0NTU4NTMzZmQzOThjZDNjYzdkMDQ2YTU2ZmQwY2IyODNkY2NjNjI4ODhkOWJmYWZmNTM3MzcwYzRkNWI2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 00:42:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZNVDQrV3NQY2gxSHNmN1ZUT3dlM1E9PSIsInZhbHVlIjoiTVpRK2lPaUQveHZ2eDluWXJ2ZG5FZkk1MWp4QXEwMWJWa0RaaExWK2VHUGRHWmh1QkhjK1U0ZU9PZHB4bzBtVk1RejZieHlVSlFEdGZadzYrWjhIYnNLRW11eWticnZnMHUxb2xNUmIxYXM3TjU3N1FEN0hSNktISTQ5d2dtaEdRTjZyTmc1WUpRYXpkRDY0OXIyRFp5cTRwWVQrSDg0YzA2TitQZ0l1dGFDNjNFVm5oamo1SXd2aFRzY0ttUCt6ZUdVRWJWQ2lPWHNmWlBvcnR0enRMVjhVaThrRkRoSk92VEliOHd5M2lxTC9UaHZPaE1OdEpINHFrQ2ZVRHM2UlFTSHorR2pQQk4raU9QVFM1NEJJaSt1T2g0aGFXQldRcE1pQU1KdmJlMUN2QkRZYXVzdWp6N0ZiVUVvWXJtV3I3aldJQ1lYaVJ5NC9SSW02WnU4bkZDZnI0R1orMi85TDBTV1Q1NytxRnl5Y1NlR3dxOVF3djJXeHdhRVQwZGduQmg1ek5vQkRRSXByR00wQ25BeGQybVIyM2JvcEhLcis2UmFNUk1WaFh1TnVTTkRUM0FCRG82RjdTSjEyYkRvdENZbVFsbzR1ek1pUFdxalVkd2NDNDFoTVAzbWNMV1BHLzZvNnpVcjRsZy9FamY3NGcyQkR2dUpMSDJqTlV2K3UiLCJtYWMiOiIxZjg0ZDU5Y2FmOTgxZGVjNWNlZjg4Y2E4ZmU3MmY1OWZkMDE2ZWZiYjU0OThlMzUwMjA0NTY1OWY0OWFhYmYxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVrd1h4TERWeDlBNXNwREdsNTFQcWc9PSIsInZhbHVlIjoiNmRQQm1UVkN3TkZzdjZSTU1QYUFMZENkdllqYW1GZzF1cnVnUTNCL0NTa3Bwc0FMNUp1b3RKNCt4a2k0RndJdTlIOXMzMExseEFlKzJTclZkenlyUjFvOHg3aG93Wnd1MlZWdHlXbTc4aVVYRmorWjNxVGMyaHV0WWRuN0hXMnNQU3dnalE0VnpPUmRWRmtJQmpKM1lnY0xUOVk3WS9HMDNJU21RMm5WQ08ybW0yNFl1QzFldnhpZ1p1NEw1U2dTRXNNeTg0NXZxeVhidi9wUWZudGc5YnpvRHFKMlNaNEs4OVJ0MEFGbVlNTGd1NEROc01wUTcvS0lGS2FOTnpJd3N0R0JURndoMXlsUTUydjdTZFBMSnZ1bXBHdnB3WG5UZVk1dVY1QzNoU2RKTDB4OHhWOU1wL3RMd1gyMk1NTkp3T09paUpNSjg5RHFIRFoxOGVPQmRManE0b1k5SGQwd3RZaGRLblBEQXdvVldHdUxkeUpPbEI4Wk5Lbm8wbGlwOUthQUNEdnhxcWRFWUJYS1lReXEzNU02L0crYU9sd1JUd1BiTEVoSklXN0k5MFFwTjJqeTlURHFwOVpVZ1I2MWxIZUJWSkp3bjdLT3VIcnZQRGd5UUMzU0k0TUllVE9obUtNcktPaUpKQmRKMzE0ajBqbjhQWFpvYnArbERLcUsiLCJtYWMiOiIwYjY0NTU4NTMzZmQzOThjZDNjYzdkMDQ2YTU2ZmQwY2IyODNkY2NjNjI4ODhkOWJmYWZmNTM3MzcwYzRkNWI2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 00:42:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036011862\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-280248275 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6ImtHUzRVRUJ0YXBURTkzck1ySFdNR3c9PSIsInZhbHVlIjoiL2VXNE1oRVRRT2tyTVFxSWlmeWhrUT09IiwibWFjIjoiNzk1YmRkMGZjZWMyOGEyODI1MTI4M2JmOWQ5YzQ4MmZlZjUzMDZlZjhhMjExOGU2NjU3NGNlYjA5OGVmMzA1MiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-280248275\", {\"maxDepth\":0})</script>\n"}}