{"__meta": {"id": "Xdc762325e3b818ed030194f69b60163e", "datetime": "2025-06-26 23:21:40", "utime": **********.848707, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.426393, "end": **********.848725, "duration": 0.4223320484161377, "duration_str": "422ms", "measures": [{"label": "Booting", "start": **********.426393, "relative_start": 0, "end": **********.797722, "relative_end": **********.797722, "duration": 0.37132906913757324, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.797731, "relative_start": 0.371337890625, "end": **********.848727, "relative_end": 1.9073486328125e-06, "duration": 0.05099606513977051, "duration_str": "51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0028699999999999997, "accumulated_duration_str": "2.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8238819, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.763}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.834862, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.763, "width_percent": 20.557}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.841063, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.321, "width_percent": 15.679}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IllSSXVIVXRzS2Q2NUNicVR2cXg2SHc9PSIsInZhbHVlIjoiSUZKTDVVM2lKMTVVSEptS2luOHZpQT09IiwibWFjIjoiY2UwMmEyNDdjNWQ4OTJkODczMjQwNTI0OTc5ZGI1MzEwODdhZjE5MmY1Mzg2OWFmODg2MWNhMTNkMzc0MzMwZiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1843174165 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1843174165\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1131610005 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1131610005\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1336436163 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336436163\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1790983483 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IllSSXVIVXRzS2Q2NUNicVR2cXg2SHc9PSIsInZhbHVlIjoiSUZKTDVVM2lKMTVVSEptS2luOHZpQT09IiwibWFjIjoiY2UwMmEyNDdjNWQ4OTJkODczMjQwNTI0OTc5ZGI1MzEwODdhZjE5MmY1Mzg2OWFmODg2MWNhMTNkMzc0MzMwZiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980096846%7C24%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkdpVHJRZmx0bEhsUnB3MVhWanZyT3c9PSIsInZhbHVlIjoiZXRNUHI2V3o0U1QrQTBvSk12U0Zna3gzSVM3WEpTN2h2NFZBRVZtdW4rUlhvRnhuemVOT3RkaUZ4SUVhVVFUQW9KNUcrc3grSkxGSXZkeVRaNnNWRXk3MXM0WWxPeGFLT1ZvakJwbXU1Wk9OYmZ4SU9xQkJvd3h2S1ZOWksya2NyOHUrbnp2cXNHanR1NWNlclU3NTNjNE9FdjFxVCt2eVJCWkk1WkVMYzIvRTRrTDlhWjZQMi9LZDZqYmlPK2szS1puNTlhUDRpNFRiZFpWQm8weEoxSWUwZTFTTFVrYmVxc2lQNXAwVmtyMW1EajdQSCtxMC9XbmlJdUhSWG9FUWdRLzFKSTA4WWlVSVZYL0piMHBJKytyRFpDL09XN01SL3U4YUZ6SHhHSWthZmZKeEtWMVlWMnNETnpFOE1HODF5WHpKRERSSVpmRTU2Z1FSTjQwNVd6Sk9ZU1NaWSt1ei9ETW5NYzNGbjZVZWdTR0VBZ1VSR08vSlFhODdUVjBlSDNLaHBsc3ppbVZMSzVteHV1QXlld0RqRUlxTHVXL29Fd1VoVTlVWUZ2ZlFJblBFQ05Uc29kcDZrb1dobXhONkhiUHpYTHNRaDd4dGh4VUx6ZTBVeitvL2ttTUZZejY2UHlsRTBlVU9GaXhYcVEyYm15MGtwTDMwc3hlVndGL3oiLCJtYWMiOiJkNTRkZjExZDE2MzdiMjY4ZTJkMjRkYzRjNzhhMjQ4MjRlZWIyZTZkMmIwNGNhNDViYWI0YjQ0MGRkZWNmMTQ5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlnT2dqZm92SUl4bUhlNmc0cWZ6L2c9PSIsInZhbHVlIjoiaTZia0FMNTFRekJYcnJRYk9lSnMwWnA2SUxFb2h1K0hOZjBHeElldFZMWUZwR0Nnckx5SXQveEprb0xMOFEwOEQySW11YWovQlQ2NjdkQkNUVkxmMTA4cTc5d1RzbzRBNnFvQTUxSU04ZXduZWFCTzF2QXErcmpmeExYQ1g2bVkxVnlQSW44Smsya05sR3REUnJjcFZleUszejR5ZTYwSitMUXdlMkkxVzY3YVRpbnUwMWw0Sk12Q0dFSmxqeERqT2srMzdGREwrV09LWjVJMHkyNU9TZlpCTFpmY1k3U09lb2NJK25mU0RxWWdoQ3llRzVhbXBKN045eXlXa0IyUzR4M0hNMmhVMkptWkl1V3MwZW44bzlmS1hybTd2UXJ5YXllRm1LNldFc3QzWkRjbFIxdDB1RUU4ODVmbWtnS1p4Unc0YlAxZGFXQ3Y5aVNHTVM1UUlLQ3pjSjFBenFsVllXVm5DMGpWc2pyQ3JNNXhvcHFHUmRkSmdXYVJJSytUZWpDZUJDdW16ekpYWGJJMThON3krUHdQS0xPQUQvbFpmOTFvTThIc04xcDUwaEJGWDluRnRFZW4yb0xkTEhjdjNBV2EvNnRwNGZkYUhnM3Y2SzhGSzJRdHRiSW1hUGZFQnFKYjdxcHZHUnpXcDZzMC83dlhtSVRUNytZaDdUZSsiLCJtYWMiOiI2MjBiNjA3ZjM0OTg3Y2I0N2VlZDM4MzgxODJlOWVkYmIwZDNmMjljNzA3NmZlMDY4MWRjODcyNzUzZGEzNjRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790983483\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-561613010 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-561613010\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-952191079 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:21:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImM5Y2toQmU4bFZpY040aGRLT1BnMkE9PSIsInZhbHVlIjoiYUkvdW5Sb1A1RlpLWmR0UytQY2lmM3FWT21QeFpGZDVnWTN0UEtVTTZ4L3FzZ3l5Ym9MYTRDc283ck01Q3AyMGtXaEdjcDlBUG96M1Y3alhCZnJLOG9aQUlHQ3hCa2lwWjEzSXMzVnlTQ1BZOWkwMEUwR1VyRTlZcWt2SGNuT1orZGNQbGQ0ZXlXdndjM3E2dU5Oc05tbTlGTmVwQ2NOR2JMS1FpTFhHL1RLVENFY0RKa1dUQjN6RmZmRkd0dmhzN2FMTHFwL0M3Z1hYbjFqMEh3a1FLSTdOOWZKTHY0UG10OS9MMkdYTjg3eGxTK2s3bHAvZVgrSU1jbnZWVDFjZy9hY0dWTUZoc0RUV0VOa2poblNTbFJCWCtCTU9XblZzOEQyU3V6RUxReTlET2ttaHd2RCtVbm9IR202V2QreHdlRmRhREI2QStiL3UvdFJTRFA2UFpyMnNOeC9FdHlET0VQeXpXcFpWWWdsK0N6cFFORkJITUNTc0Y2a0NBTlJZWW13TEJTaEI5RXh0cW1LZ2VGeEgvbzNrcUw5ZDRtSTZreW96eWdBdUYvTHlWa0JoV3ZtUjFEMG1jY202MkZ0SGk1NnRjU04zREVJeU1YS2tuZnlrb0EzL1V6SjRxZWYraHUyMzlxclNCKy92WnV2SmMwbjBkQy9XUzJWQ20zZzYiLCJtYWMiOiI0OWMxNDNmMmY5NGE0MTcxYTYwODlmMGY4YzExMmYzMmFmYzIxYTQ1MzNlZmZjNTY4MmNhNDJjNmNkNTc2MWNjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImpoMUE2bXJjeWZSSjJQeUZIKzV4VFE9PSIsInZhbHVlIjoibkR1WmF5KytEdnZqaFlsWjEyODYyS1FzclkvU1dZMGIxMVZnWExMUEJ2b2pzL0ZENThxUG4rbGovc0I1bkdmai9iZnRkZ092Mkt2MjRoWTNJU01UMGZCbTN6VGFBWUVGUHk5RlRxMHhqelVsRW1QQXpvbkI3V1o4c0RFVDZ5NjZhQW1TdCtSK3RqYWtrT0JyeW94Y1VabHYxaXFwTGRjSTBjOHdmNVZOeUgrWEtwdU5TUEZYdkpKSTlEMmtXakRoUmxESVJmUjY2dDFRU0M2cnJrbEQ1bXh6cEkyRmxyb1FXYzNOWFQ0Y0dJSExOUGdOYm80TVZUQUx0V2h1MFdCOHltLzFhSEtXeUptTE1vZldnQ2FrbjFpREJseEE5VXpkYmp5cys3a2o2Y2MvM256dnRXS2lheVFQWmdCajFBeXZTOUlHZ25XSHRNbVlPb3lXcEpJWEZCYTRoRGc2R0Z1WTI5ck5WVy9yMXkwZUR4OWpVVXRyV05TRkJ3ZFJPdnZKOUUzUHlYTkxYL1lhZ0JaUDhpdkx0ZURMejVTQUZrUUtuRjBmUnl2RU5xN3lFdndLR3RJZnNIajVoeG5vVms2RDdKdFIxNTJsR2s2bmRrbGFyaTkyZHVmVHdDTmtPK0RQQjNKRFFHR1RkTVB4a0RIL1d6b0U5dGxOMEtlWU1ZdmoiLCJtYWMiOiI1Y2NhYTU0MjM2MDNmN2EzMmJkMTJlMGI4MGJiNWZkYjZhNWQyZTIyMDVmY2U1NjNhYTZhMDE1MzA5ZWU3Yzk2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImM5Y2toQmU4bFZpY040aGRLT1BnMkE9PSIsInZhbHVlIjoiYUkvdW5Sb1A1RlpLWmR0UytQY2lmM3FWT21QeFpGZDVnWTN0UEtVTTZ4L3FzZ3l5Ym9MYTRDc283ck01Q3AyMGtXaEdjcDlBUG96M1Y3alhCZnJLOG9aQUlHQ3hCa2lwWjEzSXMzVnlTQ1BZOWkwMEUwR1VyRTlZcWt2SGNuT1orZGNQbGQ0ZXlXdndjM3E2dU5Oc05tbTlGTmVwQ2NOR2JMS1FpTFhHL1RLVENFY0RKa1dUQjN6RmZmRkd0dmhzN2FMTHFwL0M3Z1hYbjFqMEh3a1FLSTdOOWZKTHY0UG10OS9MMkdYTjg3eGxTK2s3bHAvZVgrSU1jbnZWVDFjZy9hY0dWTUZoc0RUV0VOa2poblNTbFJCWCtCTU9XblZzOEQyU3V6RUxReTlET2ttaHd2RCtVbm9IR202V2QreHdlRmRhREI2QStiL3UvdFJTRFA2UFpyMnNOeC9FdHlET0VQeXpXcFpWWWdsK0N6cFFORkJITUNTc0Y2a0NBTlJZWW13TEJTaEI5RXh0cW1LZ2VGeEgvbzNrcUw5ZDRtSTZreW96eWdBdUYvTHlWa0JoV3ZtUjFEMG1jY202MkZ0SGk1NnRjU04zREVJeU1YS2tuZnlrb0EzL1V6SjRxZWYraHUyMzlxclNCKy92WnV2SmMwbjBkQy9XUzJWQ20zZzYiLCJtYWMiOiI0OWMxNDNmMmY5NGE0MTcxYTYwODlmMGY4YzExMmYzMmFmYzIxYTQ1MzNlZmZjNTY4MmNhNDJjNmNkNTc2MWNjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImpoMUE2bXJjeWZSSjJQeUZIKzV4VFE9PSIsInZhbHVlIjoibkR1WmF5KytEdnZqaFlsWjEyODYyS1FzclkvU1dZMGIxMVZnWExMUEJ2b2pzL0ZENThxUG4rbGovc0I1bkdmai9iZnRkZ092Mkt2MjRoWTNJU01UMGZCbTN6VGFBWUVGUHk5RlRxMHhqelVsRW1QQXpvbkI3V1o4c0RFVDZ5NjZhQW1TdCtSK3RqYWtrT0JyeW94Y1VabHYxaXFwTGRjSTBjOHdmNVZOeUgrWEtwdU5TUEZYdkpKSTlEMmtXakRoUmxESVJmUjY2dDFRU0M2cnJrbEQ1bXh6cEkyRmxyb1FXYzNOWFQ0Y0dJSExOUGdOYm80TVZUQUx0V2h1MFdCOHltLzFhSEtXeUptTE1vZldnQ2FrbjFpREJseEE5VXpkYmp5cys3a2o2Y2MvM256dnRXS2lheVFQWmdCajFBeXZTOUlHZ25XSHRNbVlPb3lXcEpJWEZCYTRoRGc2R0Z1WTI5ck5WVy9yMXkwZUR4OWpVVXRyV05TRkJ3ZFJPdnZKOUUzUHlYTkxYL1lhZ0JaUDhpdkx0ZURMejVTQUZrUUtuRjBmUnl2RU5xN3lFdndLR3RJZnNIajVoeG5vVms2RDdKdFIxNTJsR2s2bmRrbGFyaTkyZHVmVHdDTmtPK0RQQjNKRFFHR1RkTVB4a0RIL1d6b0U5dGxOMEtlWU1ZdmoiLCJtYWMiOiI1Y2NhYTU0MjM2MDNmN2EzMmJkMTJlMGI4MGJiNWZkYjZhNWQyZTIyMDVmY2U1NjNhYTZhMDE1MzA5ZWU3Yzk2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-952191079\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-810151739 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IllSSXVIVXRzS2Q2NUNicVR2cXg2SHc9PSIsInZhbHVlIjoiSUZKTDVVM2lKMTVVSEptS2luOHZpQT09IiwibWFjIjoiY2UwMmEyNDdjNWQ4OTJkODczMjQwNTI0OTc5ZGI1MzEwODdhZjE5MmY1Mzg2OWFmODg2MWNhMTNkMzc0MzMwZiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810151739\", {\"maxDepth\":0})</script>\n"}}