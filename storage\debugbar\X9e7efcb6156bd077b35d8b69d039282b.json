{"__meta": {"id": "X9e7efcb6156bd077b35d8b69d039282b", "datetime": "2025-06-26 23:20:50", "utime": **********.313745, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980049.8816, "end": **********.31376, "duration": 0.4321601390838623, "duration_str": "432ms", "measures": [{"label": "Booting", "start": 1750980049.8816, "relative_start": 0, "end": **********.2515, "relative_end": **********.2515, "duration": 0.3698999881744385, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.251509, "relative_start": 0.36990904808044434, "end": **********.313762, "relative_end": 1.9073486328125e-06, "duration": 0.06225299835205078, "duration_str": "62.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45030352, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01804, "accumulated_duration_str": "18.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.277001, "duration": 0.0171, "duration_str": "17.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.789}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.30173, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.789, "width_percent": 2.993}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.307282, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.783, "width_percent": 2.217}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget/eyJpdiI6InAzMzVvWHBjZ3htWVJTc21RMmxleFE9PSIsInZhbHVlIjoiYmwzaHQxK1RGRnhXaE5sam5NOXBLZz09IiwibWFjIjoiMmZiNGY5Yzc1ZGU5MjBlZjYyMmZlMzg4ZDdmOTQ1NjIzYzgzNTEyODFlNDc3MGY5ODUwMmZmMjRlNGRhNGU5OCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1042955992 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1042955992\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1740070089 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1740070089\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-902502394 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902502394\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1972504910 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"229 characters\">http://localhost/budget/eyJpdiI6InAzMzVvWHBjZ3htWVJTc21RMmxleFE9PSIsInZhbHVlIjoiYmwzaHQxK1RGRnhXaE5sam5NOXBLZz09IiwibWFjIjoiMmZiNGY5Yzc1ZGU5MjBlZjYyMmZlMzg4ZDdmOTQ1NjIzYzgzNTEyODFlNDc3MGY5ODUwMmZmMjRlNGRhNGU5OCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980047617%7C19%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxQcDRSdWhvNmR3bTQ5L1dCS05TVXc9PSIsInZhbHVlIjoiQml5NGZkYXRLQUNMNTczdmtxM3NEaXEzN2V0RUJVWnFFanRndC9ocEdTVTRJVkZrcFMyV1p0dzdrMFo1c240ck5pakJpeEhsM1JKSHdvdEJ3NmcxUERFb1RHSmJYclN5WFBXSkNsNlZ5TUVteXBPdnlQekpaenJLanpUN0wrYmtXWWdGN1djTTlzOE9oc0NNb0k0WHpEYzFjaUJQOGRzSmdEQlVUc1U4eHNaTnRhL1oxNFBva0hJYzNscS9wTWxiQnI0ajRtTmpzRVEwOUpxR1dUWkRabEN6Sjd4UnNGOU5pY25kMUsvUGRjc3Vna0tDcFRkRjlsMEhTU3MxY3Vka3cvWGdGOEF5YlF1bHBHTG9TbFFUL3FNQVlQK3hQUDNEYjVoUjIzRU13WmpBZlRxVHZRZTJOY2k1ZkVGajVlbm1EdFYxQkVadUpBa3VIQ0FVd0RnVjhDcjM1VDNpempBajAraHBCT080T3pha0VuenJUUjRNSWdlS0tPUkx1RHhBbWJrN3FIMkhLY1VCTGhZc011MVBaa1pzTm9hcTRTVUNNWUV6aGRyNDRib3VJT290cmVTd0M2aHVPMDErUzNJRjNyY05jaXExSXZKbWowaHVHUzJQSmI4RTR3RlEvYUFwcU1aMTdTakZ3OVpPQy93M2pwamduaXk4dzBIZE82TmYiLCJtYWMiOiJmYzkxNDFiMmQ3NWExMzE0YTFlZGJjMGMyNjUwM2JmMDVmNjliZTU5YzNiYmM0ZTMxYjBmNjRlNGVkZGFlMjZmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllBTm1FVENJNnNnQ3lKbmxoVm42VXc9PSIsInZhbHVlIjoiQjNwam5TblhYc2lCVU8zWEFzRzJXSUQ0cVF6eG9CN3lRaW5vTFVyRUk1c0ViUmxvdVhqVkw2SEdJa04rUUVPcmVWU0hNWW1ydnVSVDRURmUxblExK0hyR29sSncwekowQmxkUDZlY0xwMFRJTU8vTUsrQmRxQ1hGL3p5WGhIZ202ZWZpL0NtVlVXNnVmKzNkNWVvOUQwZXN6WVNVSE9HUVVEU3pNTHFTdHgyak8rQ1Yxc1d0aVJWT0hkQXdPUTZTdk5sTEs0ZGJOcWtzMFVJSElJbVJjYlV1SVRNd3lOQWgwYmJFVUVkTWtZZU1lU3pmSnEzNEp4amlRcEZZNm5rZnk4aUJjR2NTM3JVT3RPMDJnOEEwb2xxTVlSdzh6T0hWYmQ4Rm5ZOXovaUM3Qk5UY1VGYzdCdVY4QmR3RWkxVDNtVkRDdlY0TkFpNlp2NmFMdjl2WDBwVE5WMGVPdEdYbFhUZnF4cmU0TGhXZFNlYmwyQ0VHUnVmY0JLdWxpajVsbzlOQ2dxanBFNUFBSS9iWERvZU9TdlhHeTVES1ZTc1l2bk1SSWhqOTI4NTBXSzViSUcrRkwvMXJlcmRQbXhxcUlYM3FjZ2xUTnY5SnROa2xCUEpNMEdiRFd4T2pDaEVaMHRMUWZiWjJ1RnRXWnErR0kvOGViZjdSdldQT2lJNWUiLCJtYWMiOiJiOWI3NGY1MDYwMDgyNDJiMzFiNDI0MjY1Y2U4YmJkOWI3YWU0M2UxNWNlMjEzOGMyN2E1OTU3MGMzZjlkMTYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972504910\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-74365388 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74365388\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-951389085 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iks3UUdyTXU3OTUxQzlsUU1SeWhWaGc9PSIsInZhbHVlIjoiMWZKRi82RmFWdEdtTFB0S1BWQ1VWVXRITzNYbEJLQmtZdWNMOG43VE81MGtYZmg2RG4xZVRiQWdRaHNuWVE1TnVVbEEzS2VxRUVWK3kxSFpzVk03N0RaT2xMajZvRkZTMXJQbzdQNUFHT3pLR2ExUFNqYXVNUzl2ak5RcmFLeUxYcDdVQXpFTDVIYnRjdFgxbmlOcmFyVFc5NXBUTjBta1d3aVIydC9kY2h0YURWQkN4Rkw3bC85Y3ozSmxPUEg4cHBvVFNGcmJid0drMUN0T1ZndzhnT3R6S1kvVHBmTFM5dUIrbnlDK1pxQTUzeDhwTVdnallZc2lKZ1Jyd1FLQ0hGNURkZGZjaU1HcnFPNjlmdW9CRmtSNlpuRFdUQ21nUit0NGUwaXJ4TlQramdoSHd0OEl4Ym9NQjgvYTRMOGo1SURFN25HYzU3cGl3ZFJmaDJZYm4xUU5hVEtweGxUNWRlMUFrdURKeEQzcU4xQU10TzlsaEVtWXBCS0M5U0RFRTJPdXliQkxacElnOTdxRmw2VmppSlVKanQ4N3ExTDY4MU16NllySnVYL3VkTXRVakpWZnBpYWM3NkpRN21mZzNiYVVDek5nQlAvQWQ2Uk9GNjE3ZGZMQmdCQ2YyeUFsWm1yWEVwZXpueTNTQVNLM2ExZUUyUTdaSmhEWUhKQzciLCJtYWMiOiIxNjVmYzU5ODgzZjQwNDQyNGI5MDRhYzU5ZGNmN2VhNDU1MTkxYzhiMmUyNmRiZDI2MTU3MzFjZTA3MWE1Nzc2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im13RllFdWNWZG9GRUlTUklwNXRLeXc9PSIsInZhbHVlIjoiSUVTREhRdy9kdC82djVJRWkzd2RBd3hxWDA1RkdHUU5qdkprU1JsUENudk1BWDJVa0FSYmNqNHoxcjV0SlBpKzBEdnVrbGJTYVdlRzhMMmtLQkZ2V0IraFV4SnczNDZ4S1UzMHJMR2pMNXR4SFR6M0MxTy82Sm94NlpXZmMwMlFiWEVrSjZ1SmFWUytUY1grRkY4NGg3UUFXYVV5Ump2ZmJuTWZsalcxM0FjS2ozeCsvKzhTb3pBTXhSWDBhNzEyYkRyMlVweGg1dVhaUmFtNlJibHNtd25pMDAyWjd3N2toeXFTemZnSFpmdHd3cUMwUFRhMVdqVmhtKzRHcUFFWUtyTHNZTXFUL1ZyeFVaNk1nL0xWcHVraVJBMGNxSGpVeVhkUTVtZytjSjNwc2FYMk9VQjhDWHc3dTg1V2VnL3dkWC9uS0ZydEZ4cDNaRGhHRXByeG5ydUV1eUtxU1NuNlg1Smh2K3NCWHI4Nm55TktWQVF2cmNVWVkvWTBJMmhkYzhDdXMzTm9TK09xUVdmYksxUkNxRFRvelV5TjU4UkpWdDdQclRJNUdydU83aWVzYUZYYzVEMURSTUtUSDgreUxBSnBLVlFVbFFOY0kyWVZnRmQ5ck40cUhDK0lUZi81eWxKZTc2OVBOUURZdnFFaFpWSStnOWhFWDZvRWhNQjQiLCJtYWMiOiJlZDNiMDU4MTNmZmY2M2Q1YTI3MjI0YmJjNGJmNmUzOTQyMjZhNTNmMzE5ZDg1OTA3NzkyNDAwODFkZTZmZWViIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iks3UUdyTXU3OTUxQzlsUU1SeWhWaGc9PSIsInZhbHVlIjoiMWZKRi82RmFWdEdtTFB0S1BWQ1VWVXRITzNYbEJLQmtZdWNMOG43VE81MGtYZmg2RG4xZVRiQWdRaHNuWVE1TnVVbEEzS2VxRUVWK3kxSFpzVk03N0RaT2xMajZvRkZTMXJQbzdQNUFHT3pLR2ExUFNqYXVNUzl2ak5RcmFLeUxYcDdVQXpFTDVIYnRjdFgxbmlOcmFyVFc5NXBUTjBta1d3aVIydC9kY2h0YURWQkN4Rkw3bC85Y3ozSmxPUEg4cHBvVFNGcmJid0drMUN0T1ZndzhnT3R6S1kvVHBmTFM5dUIrbnlDK1pxQTUzeDhwTVdnallZc2lKZ1Jyd1FLQ0hGNURkZGZjaU1HcnFPNjlmdW9CRmtSNlpuRFdUQ21nUit0NGUwaXJ4TlQramdoSHd0OEl4Ym9NQjgvYTRMOGo1SURFN25HYzU3cGl3ZFJmaDJZYm4xUU5hVEtweGxUNWRlMUFrdURKeEQzcU4xQU10TzlsaEVtWXBCS0M5U0RFRTJPdXliQkxacElnOTdxRmw2VmppSlVKanQ4N3ExTDY4MU16NllySnVYL3VkTXRVakpWZnBpYWM3NkpRN21mZzNiYVVDek5nQlAvQWQ2Uk9GNjE3ZGZMQmdCQ2YyeUFsWm1yWEVwZXpueTNTQVNLM2ExZUUyUTdaSmhEWUhKQzciLCJtYWMiOiIxNjVmYzU5ODgzZjQwNDQyNGI5MDRhYzU5ZGNmN2VhNDU1MTkxYzhiMmUyNmRiZDI2MTU3MzFjZTA3MWE1Nzc2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im13RllFdWNWZG9GRUlTUklwNXRLeXc9PSIsInZhbHVlIjoiSUVTREhRdy9kdC82djVJRWkzd2RBd3hxWDA1RkdHUU5qdkprU1JsUENudk1BWDJVa0FSYmNqNHoxcjV0SlBpKzBEdnVrbGJTYVdlRzhMMmtLQkZ2V0IraFV4SnczNDZ4S1UzMHJMR2pMNXR4SFR6M0MxTy82Sm94NlpXZmMwMlFiWEVrSjZ1SmFWUytUY1grRkY4NGg3UUFXYVV5Ump2ZmJuTWZsalcxM0FjS2ozeCsvKzhTb3pBTXhSWDBhNzEyYkRyMlVweGg1dVhaUmFtNlJibHNtd25pMDAyWjd3N2toeXFTemZnSFpmdHd3cUMwUFRhMVdqVmhtKzRHcUFFWUtyTHNZTXFUL1ZyeFVaNk1nL0xWcHVraVJBMGNxSGpVeVhkUTVtZytjSjNwc2FYMk9VQjhDWHc3dTg1V2VnL3dkWC9uS0ZydEZ4cDNaRGhHRXByeG5ydUV1eUtxU1NuNlg1Smh2K3NCWHI4Nm55TktWQVF2cmNVWVkvWTBJMmhkYzhDdXMzTm9TK09xUVdmYksxUkNxRFRvelV5TjU4UkpWdDdQclRJNUdydU83aWVzYUZYYzVEMURSTUtUSDgreUxBSnBLVlFVbFFOY0kyWVZnRmQ5ck40cUhDK0lUZi81eWxKZTc2OVBOUURZdnFFaFpWSStnOWhFWDZvRWhNQjQiLCJtYWMiOiJlZDNiMDU4MTNmZmY2M2Q1YTI3MjI0YmJjNGJmNmUzOTQyMjZhNTNmMzE5ZDg1OTA3NzkyNDAwODFkZTZmZWViIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-951389085\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-558961250 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"229 characters\">http://localhost/budget/eyJpdiI6InAzMzVvWHBjZ3htWVJTc21RMmxleFE9PSIsInZhbHVlIjoiYmwzaHQxK1RGRnhXaE5sam5NOXBLZz09IiwibWFjIjoiMmZiNGY5Yzc1ZGU5MjBlZjYyMmZlMzg4ZDdmOTQ1NjIzYzgzNTEyODFlNDc3MGY5ODUwMmZmMjRlNGRhNGU5OCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558961250\", {\"maxDepth\":0})</script>\n"}}