{"__meta": {"id": "X89462570f7c3ed90f5d5b806ab52bd1c", "datetime": "2025-06-26 23:16:06", "utime": **********.372977, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750979765.970506, "end": **********.372997, "duration": 0.40249109268188477, "duration_str": "402ms", "measures": [{"label": "Booting", "start": 1750979765.970506, "relative_start": 0, "end": **********.318885, "relative_end": **********.318885, "duration": 0.34837913513183594, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.318894, "relative_start": 0.3483879566192627, "end": **********.373, "relative_end": 2.86102294921875e-06, "duration": 0.05410599708557129, "duration_str": "54.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044440, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0033299999999999996, "accumulated_duration_str": "3.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.347438, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.562}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.358368, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.562, "width_percent": 22.523}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.364757, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.084, "width_percent": 15.916}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-821030138 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-821030138\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1649699806 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1649699806\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-585172307 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585172307\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2130548290 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979707065%7C6%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkkzZDlVVEplRVBFWE82UTBFTVFIUkE9PSIsInZhbHVlIjoieTEzUnFtRVBRWlhPaitnOFRzQWhvRFg5YzBibDAyWFJncHlkZ1hJOXpkMGlOdE16NHpDOGhBVTJiN0hyM0s3Q2gyS3VKK3NJRWsyTFE3S0lJNDBWVzJMQWRlV0h6V3l3Q0ZPeG92MXc5Mm5GSWtVdXNCMUoxNnJHRTVBS0NTanlsckFOUDBuOVNSSzVHL1g1WVZVNnREQTdid1F2MGNSMU4ydE84M3VKL2MzbEg5TUR4R2FrNnM3WFBLUER6clNrVlhuVGMrKzgvN0FZc3lDaXZxVG16RDdYcWFEQVVrNG5CbUpDNnhXL21rakRpWjBtc3Y3b041Skhyckw5M0NCeU1zbjVML1p5WGJYY0ZrZldUbUdOcktKb0pCaWRJckRmN1RIeU1hMFp5Q2pQYVhMNWdFN2FmOE9UNndZOE9sbm5lcHlTUkFhQVM1d0hVbVZaN0NJSGRIb014NVlKcEhCUDBOZ3liMnVIaGtJbzN3dzJmZmlueEcwbzZlYnBtdTZTNzVadTkvbjZ5aXdRN3MxQXc0YmZMVy9CU2o1NGErdG8rU3h2T1NjcFpPOW1YcDNGdXBwUFFSQ01PcUpLdWpjenNzYlpURFFJNXJlNTlSOHN6dGQ1S0hzR09GMlhwaUFjWk9qNjZ3ZklJODc2YVpXbDNxUmZPdjNPS1kvbXlHRGgiLCJtYWMiOiJiYTEwY2Q2M2I4ZGVlYzA3NjhlYTA0NDQ4ZmIzOTk0MDRiYzAwMjRjYzA2OWVhZWI1YzVjMWZhM2E4ZjIzZGVjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdLWEFORFVIdTZmN0FQa25DMGpENFE9PSIsInZhbHVlIjoiYnIreDduQnU2Z3JRMk56d1ovVVRLa05qNnNiOG9VditFMnYyMWFkSUUxQkE4NnF0SHQyemZ4ZGsvWk1sUnhaWWloVE40cDM0SVJ5VFZEZ2hRY2syTHpid0s4SUtlVXphTTcyM1NvM0JyNGVuTC9LQ1QwWnloNHFUWG5GcWtLbkpiakQxamtZZVFSdVNVb1cvRVE1cWVqNGc2ekhlaUhyWVhESCs4Wm9oVTFqcm04WEpZU1BwU3E0Q1VDM2N4VTgxdWNzcEp1bWVIb3lTVTJlMWtjdm5jR0UwWnlMbCtRN1hoSkRjaE5oeXdCZnErQ3YyaDZvV1JMVW50MlhScnFFcEpzM1pyek1UMWYxVXc3MGgvNURpVkI5VnE3Y1RMR3Y3cXpxMUpmN3BSNTFZVDFJSlZqcTFXZTZpNGpKTmo2STRZTzdQajcxVU5oWUo2QUYwSXBuK3Y4OWRPcFZZaTMvL3dkKzc4MlJUZEdQTm9JT0kwYzJ1SS9VQXViUGdwQTZCcUtRQTNCMEJGVGZYaldVckpXMFRoODdIdkswNEtMVVRaWTBUamgwWXh3Rmw5NjlPMjFVZXhTL2VGYys0TEx6NVp3SlNhNEFIdS9DT1BHOWhtSTdZMmtmZTlsWTdKUFVDa3dkenJlNUxKQytxdWZ2VGZlSmgyaHB2TkZYQzVvRGciLCJtYWMiOiJjYTk3ZWZjZTVkZWFlYzMzMTkyYjNiOTEwMzUwMDIxMzRhZjNiMDhiMWVhOGI2MTQzMjc0OWM2YTA3YTdjMmI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130548290\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-397869898 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-397869898\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:16:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBGSjE5Y2JTUEVoWXVyWklwcmVueGc9PSIsInZhbHVlIjoiTVVtekRUNGE3RnFSWVJlczlwU2VDeGRxVTNNVlBVOVNsWUhYb0VzNC92NXBDZ25QczVzcHZGT0Z2T0pWOFJzRFRsemNmS09NdjRjZUhreXM3bVAxcmEzUUhhYUF2UG1GM2EvWlNZYlFnSU1QZG5JMkhzYVJSVUZ0N2ZnRy9yY3E5dXNtQlloanZWeU9EOTJnb0l2UU5nS3ZWRWRtUXdLZmkvQ2lBYXEzellReTZuOExxM3RDeVFmQ2lPNnVkemcwbmVYOC9CODY2N0haUy9CZzJhYm5wZFZwUVVjbk03MFJhSUE1bytrbFFBaEE4WHNQUHVYckFiYjJ0L3FKY01qOW1zL21EVUhSbHZhNVoyU0o0UitnSC9OcnluNTc3bGYwZHdmSGo3NklVL213MHBBQlRxN2s0SFpTOVFjUWwvNUpwVVRNKzR3YUYrdHA2VzJWMCs3cnB4UmJKMWFLRFp6YWR5N2JIMnB5NEZhTzdoRkgxQkZUeDZUR2o0Slo2YTh6ZjdOSEJ0MEsrdzQyRWZvL0VaTm54dTFxdlRJUWNFRXRxWTVtRkwzSmNVMlhMZE42cGhZK05HZjk3VVFMK3B0OEtBY0NjUit6TGdFZ2xFMnp1R1JRbks2SGtDNnNRNWNxY3lBUlFLeVEwY1hxODhhNFA0UW5Ka3k5SFhkQkhDVVoiLCJtYWMiOiIwY2U1NmQ0YmQ1ZjZjNTUwZTJkYjkwMTlkMDI2YTU2ZTZiMDIxODAwNjcwZmRkODBmMDQwNDc4ZDE2NGFmNTU4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:16:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InlRb1lSTCtjRWNMbWk5MHovY1JRMkE9PSIsInZhbHVlIjoiTnFYenpibWhqNzBqWWFlQi9pakZkMnlSLzdaU2xuelN2RERhemIzUEROL2VzeGJFNThOZjZnaVlkbjU3YU9iREhISnFPL2pMQnBxZUg2Q0Q2WmJkY1NLYkNjSWhFOGdDRGVOaHVTSFNtWE9JU0NVbnlLbkVncC85aG4reHh4aTh5blJOZ2lvdTRMb3RLSmZYZnpFMkxPa3A2aWlEWUZ5MUt4cDRhS2tWQzNMRnpsMlVISmswMkM1K1BhSHpadkNvR2RReCtiVmQwZnpGbjV1Mlo0VjVya0Jpc0VyQVpFaVRuaVhrNkVtK3NIaU9zaHNYQkNGM2JMV1VMZkpXTTlqVkFXUnNkVmZtMXAvSzhEd0FQUEdjM282UzBsbjkvV2s4ald1UTBqTzFuYzYxeUkxYzh0a1VZbmZzcFJXbmQ1YWl0WWprZWYybkdIbnlHZGxROVc1a2xaVEwwaTlCMFkyNkVGUVRpQnJBMGVrbDZob2l4SFI0SkgyeE1SZDJSdkJGb1NCQUFMMWg3dDVXUDAyVldqMHBrRW5wajdSdUo2V0xwa0JTckYreVIwajEvaTVtaVgwUmFrUVcyVCtxYkJUQks0VEQvY0YxajlsZ0daSjhqNjlOWEJBWHBVQU5OZ1d4OFJBR0lpWmROYXdDY05DaUh1LzFMbE0yU2R3enBmblQiLCJtYWMiOiI3YWM5NGNlZGJmZWNhNDNiNTAwNTBjNGUxNDZlZTFiMmI3OTRmYWExMmYxNDJhN2ZhYWJiZWE4NTk4NjNjMjVkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:16:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBGSjE5Y2JTUEVoWXVyWklwcmVueGc9PSIsInZhbHVlIjoiTVVtekRUNGE3RnFSWVJlczlwU2VDeGRxVTNNVlBVOVNsWUhYb0VzNC92NXBDZ25QczVzcHZGT0Z2T0pWOFJzRFRsemNmS09NdjRjZUhreXM3bVAxcmEzUUhhYUF2UG1GM2EvWlNZYlFnSU1QZG5JMkhzYVJSVUZ0N2ZnRy9yY3E5dXNtQlloanZWeU9EOTJnb0l2UU5nS3ZWRWRtUXdLZmkvQ2lBYXEzellReTZuOExxM3RDeVFmQ2lPNnVkemcwbmVYOC9CODY2N0haUy9CZzJhYm5wZFZwUVVjbk03MFJhSUE1bytrbFFBaEE4WHNQUHVYckFiYjJ0L3FKY01qOW1zL21EVUhSbHZhNVoyU0o0UitnSC9OcnluNTc3bGYwZHdmSGo3NklVL213MHBBQlRxN2s0SFpTOVFjUWwvNUpwVVRNKzR3YUYrdHA2VzJWMCs3cnB4UmJKMWFLRFp6YWR5N2JIMnB5NEZhTzdoRkgxQkZUeDZUR2o0Slo2YTh6ZjdOSEJ0MEsrdzQyRWZvL0VaTm54dTFxdlRJUWNFRXRxWTVtRkwzSmNVMlhMZE42cGhZK05HZjk3VVFMK3B0OEtBY0NjUit6TGdFZ2xFMnp1R1JRbks2SGtDNnNRNWNxY3lBUlFLeVEwY1hxODhhNFA0UW5Ka3k5SFhkQkhDVVoiLCJtYWMiOiIwY2U1NmQ0YmQ1ZjZjNTUwZTJkYjkwMTlkMDI2YTU2ZTZiMDIxODAwNjcwZmRkODBmMDQwNDc4ZDE2NGFmNTU4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:16:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InlRb1lSTCtjRWNMbWk5MHovY1JRMkE9PSIsInZhbHVlIjoiTnFYenpibWhqNzBqWWFlQi9pakZkMnlSLzdaU2xuelN2RERhemIzUEROL2VzeGJFNThOZjZnaVlkbjU3YU9iREhISnFPL2pMQnBxZUg2Q0Q2WmJkY1NLYkNjSWhFOGdDRGVOaHVTSFNtWE9JU0NVbnlLbkVncC85aG4reHh4aTh5blJOZ2lvdTRMb3RLSmZYZnpFMkxPa3A2aWlEWUZ5MUt4cDRhS2tWQzNMRnpsMlVISmswMkM1K1BhSHpadkNvR2RReCtiVmQwZnpGbjV1Mlo0VjVya0Jpc0VyQVpFaVRuaVhrNkVtK3NIaU9zaHNYQkNGM2JMV1VMZkpXTTlqVkFXUnNkVmZtMXAvSzhEd0FQUEdjM282UzBsbjkvV2s4ald1UTBqTzFuYzYxeUkxYzh0a1VZbmZzcFJXbmQ1YWl0WWprZWYybkdIbnlHZGxROVc1a2xaVEwwaTlCMFkyNkVGUVRpQnJBMGVrbDZob2l4SFI0SkgyeE1SZDJSdkJGb1NCQUFMMWg3dDVXUDAyVldqMHBrRW5wajdSdUo2V0xwa0JTckYreVIwajEvaTVtaVgwUmFrUVcyVCtxYkJUQks0VEQvY0YxajlsZ0daSjhqNjlOWEJBWHBVQU5OZ1d4OFJBR0lpWmROYXdDY05DaUh1LzFMbE0yU2R3enBmblQiLCJtYWMiOiI3YWM5NGNlZGJmZWNhNDNiNTAwNTBjNGUxNDZlZTFiMmI3OTRmYWExMmYxNDJhN2ZhYWJiZWE4NTk4NjNjMjVkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:16:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1461612485 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1461612485\", {\"maxDepth\":0})</script>\n"}}