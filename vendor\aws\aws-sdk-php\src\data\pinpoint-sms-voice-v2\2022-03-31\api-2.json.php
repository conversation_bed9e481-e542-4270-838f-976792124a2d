<?php
// This file was auto-generated from sdk-root/src/data/pinpoint-sms-voice-v2/2022-03-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-03-31', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'sms-voice', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Amazon Pinpoint SMS Voice V2', 'serviceId' => 'Pinpoint SMS Voice V2', 'signatureVersion' => 'v4', 'signingName' => 'sms-voice', 'targetPrefix' => 'PinpointSMSVoiceV2', 'uid' => 'pinpoint-sms-voice-v2-2022-03-31', ], 'operations' => [ 'AssociateOriginationIdentity' => [ 'name' => 'AssociateOriginationIdentity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateOriginationIdentityRequest', ], 'output' => [ 'shape' => 'AssociateOriginationIdentityResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'AssociateProtectConfiguration' => [ 'name' => 'AssociateProtectConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateProtectConfigurationRequest', ], 'output' => [ 'shape' => 'AssociateProtectConfigurationResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateConfigurationSet' => [ 'name' => 'CreateConfigurationSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConfigurationSetRequest', ], 'output' => [ 'shape' => 'CreateConfigurationSetResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateEventDestination' => [ 'name' => 'CreateEventDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEventDestinationRequest', ], 'output' => [ 'shape' => 'CreateEventDestinationResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateOptOutList' => [ 'name' => 'CreateOptOutList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateOptOutListRequest', ], 'output' => [ 'shape' => 'CreateOptOutListResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreatePool' => [ 'name' => 'CreatePool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePoolRequest', ], 'output' => [ 'shape' => 'CreatePoolResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateProtectConfiguration' => [ 'name' => 'CreateProtectConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProtectConfigurationRequest', ], 'output' => [ 'shape' => 'CreateProtectConfigurationResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateRegistration' => [ 'name' => 'CreateRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRegistrationRequest', ], 'output' => [ 'shape' => 'CreateRegistrationResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateRegistrationAssociation' => [ 'name' => 'CreateRegistrationAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRegistrationAssociationRequest', ], 'output' => [ 'shape' => 'CreateRegistrationAssociationResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateRegistrationAttachment' => [ 'name' => 'CreateRegistrationAttachment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRegistrationAttachmentRequest', ], 'output' => [ 'shape' => 'CreateRegistrationAttachmentResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateRegistrationVersion' => [ 'name' => 'CreateRegistrationVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRegistrationVersionRequest', ], 'output' => [ 'shape' => 'CreateRegistrationVersionResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateVerifiedDestinationNumber' => [ 'name' => 'CreateVerifiedDestinationNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateVerifiedDestinationNumberRequest', ], 'output' => [ 'shape' => 'CreateVerifiedDestinationNumberResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteAccountDefaultProtectConfiguration' => [ 'name' => 'DeleteAccountDefaultProtectConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAccountDefaultProtectConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteAccountDefaultProtectConfigurationResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteConfigurationSet' => [ 'name' => 'DeleteConfigurationSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConfigurationSetRequest', ], 'output' => [ 'shape' => 'DeleteConfigurationSetResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteDefaultMessageType' => [ 'name' => 'DeleteDefaultMessageType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDefaultMessageTypeRequest', ], 'output' => [ 'shape' => 'DeleteDefaultMessageTypeResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteDefaultSenderId' => [ 'name' => 'DeleteDefaultSenderId', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDefaultSenderIdRequest', ], 'output' => [ 'shape' => 'DeleteDefaultSenderIdResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteEventDestination' => [ 'name' => 'DeleteEventDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventDestinationRequest', ], 'output' => [ 'shape' => 'DeleteEventDestinationResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteKeyword' => [ 'name' => 'DeleteKeyword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteKeywordRequest', ], 'output' => [ 'shape' => 'DeleteKeywordResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteMediaMessageSpendLimitOverride' => [ 'name' => 'DeleteMediaMessageSpendLimitOverride', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMediaMessageSpendLimitOverrideRequest', ], 'output' => [ 'shape' => 'DeleteMediaMessageSpendLimitOverrideResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteOptOutList' => [ 'name' => 'DeleteOptOutList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteOptOutListRequest', ], 'output' => [ 'shape' => 'DeleteOptOutListResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteOptedOutNumber' => [ 'name' => 'DeleteOptedOutNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteOptedOutNumberRequest', ], 'output' => [ 'shape' => 'DeleteOptedOutNumberResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeletePool' => [ 'name' => 'DeletePool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePoolRequest', ], 'output' => [ 'shape' => 'DeletePoolResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteProtectConfiguration' => [ 'name' => 'DeleteProtectConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProtectConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteProtectConfigurationResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteRegistration' => [ 'name' => 'DeleteRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRegistrationRequest', ], 'output' => [ 'shape' => 'DeleteRegistrationResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteRegistrationAttachment' => [ 'name' => 'DeleteRegistrationAttachment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRegistrationAttachmentRequest', ], 'output' => [ 'shape' => 'DeleteRegistrationAttachmentResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteRegistrationFieldValue' => [ 'name' => 'DeleteRegistrationFieldValue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRegistrationFieldValueRequest', ], 'output' => [ 'shape' => 'DeleteRegistrationFieldValueResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteTextMessageSpendLimitOverride' => [ 'name' => 'DeleteTextMessageSpendLimitOverride', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTextMessageSpendLimitOverrideRequest', ], 'output' => [ 'shape' => 'DeleteTextMessageSpendLimitOverrideResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteVerifiedDestinationNumber' => [ 'name' => 'DeleteVerifiedDestinationNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVerifiedDestinationNumberRequest', ], 'output' => [ 'shape' => 'DeleteVerifiedDestinationNumberResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteVoiceMessageSpendLimitOverride' => [ 'name' => 'DeleteVoiceMessageSpendLimitOverride', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVoiceMessageSpendLimitOverrideRequest', ], 'output' => [ 'shape' => 'DeleteVoiceMessageSpendLimitOverrideResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeAccountAttributes' => [ 'name' => 'DescribeAccountAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountAttributesRequest', ], 'output' => [ 'shape' => 'DescribeAccountAttributesResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeAccountLimits' => [ 'name' => 'DescribeAccountLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountLimitsRequest', ], 'output' => [ 'shape' => 'DescribeAccountLimitsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeConfigurationSets' => [ 'name' => 'DescribeConfigurationSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConfigurationSetsRequest', ], 'output' => [ 'shape' => 'DescribeConfigurationSetsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeKeywords' => [ 'name' => 'DescribeKeywords', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeKeywordsRequest', ], 'output' => [ 'shape' => 'DescribeKeywordsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeOptOutLists' => [ 'name' => 'DescribeOptOutLists', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOptOutListsRequest', ], 'output' => [ 'shape' => 'DescribeOptOutListsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeOptedOutNumbers' => [ 'name' => 'DescribeOptedOutNumbers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOptedOutNumbersRequest', ], 'output' => [ 'shape' => 'DescribeOptedOutNumbersResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribePhoneNumbers' => [ 'name' => 'DescribePhoneNumbers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePhoneNumbersRequest', ], 'output' => [ 'shape' => 'DescribePhoneNumbersResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribePools' => [ 'name' => 'DescribePools', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePoolsRequest', ], 'output' => [ 'shape' => 'DescribePoolsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeProtectConfigurations' => [ 'name' => 'DescribeProtectConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeProtectConfigurationsRequest', ], 'output' => [ 'shape' => 'DescribeProtectConfigurationsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeRegistrationAttachments' => [ 'name' => 'DescribeRegistrationAttachments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRegistrationAttachmentsRequest', ], 'output' => [ 'shape' => 'DescribeRegistrationAttachmentsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeRegistrationFieldDefinitions' => [ 'name' => 'DescribeRegistrationFieldDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRegistrationFieldDefinitionsRequest', ], 'output' => [ 'shape' => 'DescribeRegistrationFieldDefinitionsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeRegistrationFieldValues' => [ 'name' => 'DescribeRegistrationFieldValues', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRegistrationFieldValuesRequest', ], 'output' => [ 'shape' => 'DescribeRegistrationFieldValuesResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeRegistrationSectionDefinitions' => [ 'name' => 'DescribeRegistrationSectionDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRegistrationSectionDefinitionsRequest', ], 'output' => [ 'shape' => 'DescribeRegistrationSectionDefinitionsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeRegistrationTypeDefinitions' => [ 'name' => 'DescribeRegistrationTypeDefinitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRegistrationTypeDefinitionsRequest', ], 'output' => [ 'shape' => 'DescribeRegistrationTypeDefinitionsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeRegistrationVersions' => [ 'name' => 'DescribeRegistrationVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRegistrationVersionsRequest', ], 'output' => [ 'shape' => 'DescribeRegistrationVersionsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeRegistrations' => [ 'name' => 'DescribeRegistrations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRegistrationsRequest', ], 'output' => [ 'shape' => 'DescribeRegistrationsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeSenderIds' => [ 'name' => 'DescribeSenderIds', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSenderIdsRequest', ], 'output' => [ 'shape' => 'DescribeSenderIdsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeSpendLimits' => [ 'name' => 'DescribeSpendLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSpendLimitsRequest', ], 'output' => [ 'shape' => 'DescribeSpendLimitsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeVerifiedDestinationNumbers' => [ 'name' => 'DescribeVerifiedDestinationNumbers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeVerifiedDestinationNumbersRequest', ], 'output' => [ 'shape' => 'DescribeVerifiedDestinationNumbersResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateOriginationIdentity' => [ 'name' => 'DisassociateOriginationIdentity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateOriginationIdentityRequest', ], 'output' => [ 'shape' => 'DisassociateOriginationIdentityResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DisassociateProtectConfiguration' => [ 'name' => 'DisassociateProtectConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateProtectConfigurationRequest', ], 'output' => [ 'shape' => 'DisassociateProtectConfigurationResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DiscardRegistrationVersion' => [ 'name' => 'DiscardRegistrationVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DiscardRegistrationVersionRequest', ], 'output' => [ 'shape' => 'DiscardRegistrationVersionResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetProtectConfigurationCountryRuleSet' => [ 'name' => 'GetProtectConfigurationCountryRuleSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetProtectConfigurationCountryRuleSetRequest', ], 'output' => [ 'shape' => 'GetProtectConfigurationCountryRuleSetResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPoolOriginationIdentities' => [ 'name' => 'ListPoolOriginationIdentities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPoolOriginationIdentitiesRequest', ], 'output' => [ 'shape' => 'ListPoolOriginationIdentitiesResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRegistrationAssociations' => [ 'name' => 'ListRegistrationAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRegistrationAssociationsRequest', ], 'output' => [ 'shape' => 'ListRegistrationAssociationsResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutKeyword' => [ 'name' => 'PutKeyword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutKeywordRequest', ], 'output' => [ 'shape' => 'PutKeywordResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutOptedOutNumber' => [ 'name' => 'PutOptedOutNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutOptedOutNumberRequest', ], 'output' => [ 'shape' => 'PutOptedOutNumberResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutRegistrationFieldValue' => [ 'name' => 'PutRegistrationFieldValue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRegistrationFieldValueRequest', ], 'output' => [ 'shape' => 'PutRegistrationFieldValueResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ReleasePhoneNumber' => [ 'name' => 'ReleasePhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ReleasePhoneNumberRequest', ], 'output' => [ 'shape' => 'ReleasePhoneNumberResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ReleaseSenderId' => [ 'name' => 'ReleaseSenderId', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ReleaseSenderIdRequest', ], 'output' => [ 'shape' => 'ReleaseSenderIdResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'RequestPhoneNumber' => [ 'name' => 'RequestPhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RequestPhoneNumberRequest', ], 'output' => [ 'shape' => 'RequestPhoneNumberResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'RequestSenderId' => [ 'name' => 'RequestSenderId', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RequestSenderIdRequest', ], 'output' => [ 'shape' => 'RequestSenderIdResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SendDestinationNumberVerificationCode' => [ 'name' => 'SendDestinationNumberVerificationCode', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendDestinationNumberVerificationCodeRequest', ], 'output' => [ 'shape' => 'SendDestinationNumberVerificationCodeResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SendMediaMessage' => [ 'name' => 'SendMediaMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendMediaMessageRequest', ], 'output' => [ 'shape' => 'SendMediaMessageResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SendTextMessage' => [ 'name' => 'SendTextMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendTextMessageRequest', ], 'output' => [ 'shape' => 'SendTextMessageResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SendVoiceMessage' => [ 'name' => 'SendVoiceMessage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendVoiceMessageRequest', ], 'output' => [ 'shape' => 'SendVoiceMessageResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SetAccountDefaultProtectConfiguration' => [ 'name' => 'SetAccountDefaultProtectConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetAccountDefaultProtectConfigurationRequest', ], 'output' => [ 'shape' => 'SetAccountDefaultProtectConfigurationResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SetDefaultMessageType' => [ 'name' => 'SetDefaultMessageType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetDefaultMessageTypeRequest', ], 'output' => [ 'shape' => 'SetDefaultMessageTypeResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SetDefaultSenderId' => [ 'name' => 'SetDefaultSenderId', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetDefaultSenderIdRequest', ], 'output' => [ 'shape' => 'SetDefaultSenderIdResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SetMediaMessageSpendLimitOverride' => [ 'name' => 'SetMediaMessageSpendLimitOverride', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetMediaMessageSpendLimitOverrideRequest', ], 'output' => [ 'shape' => 'SetMediaMessageSpendLimitOverrideResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SetTextMessageSpendLimitOverride' => [ 'name' => 'SetTextMessageSpendLimitOverride', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetTextMessageSpendLimitOverrideRequest', ], 'output' => [ 'shape' => 'SetTextMessageSpendLimitOverrideResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SetVoiceMessageSpendLimitOverride' => [ 'name' => 'SetVoiceMessageSpendLimitOverride', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetVoiceMessageSpendLimitOverrideRequest', ], 'output' => [ 'shape' => 'SetVoiceMessageSpendLimitOverrideResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'SubmitRegistrationVersion' => [ 'name' => 'SubmitRegistrationVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SubmitRegistrationVersionRequest', ], 'output' => [ 'shape' => 'SubmitRegistrationVersionResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResult', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateEventDestination' => [ 'name' => 'UpdateEventDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEventDestinationRequest', ], 'output' => [ 'shape' => 'UpdateEventDestinationResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdatePhoneNumber' => [ 'name' => 'UpdatePhoneNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePhoneNumberRequest', ], 'output' => [ 'shape' => 'UpdatePhoneNumberResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdatePool' => [ 'name' => 'UpdatePool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePoolRequest', ], 'output' => [ 'shape' => 'UpdatePoolResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateProtectConfiguration' => [ 'name' => 'UpdateProtectConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateProtectConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateProtectConfigurationResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateProtectConfigurationCountryRuleSet' => [ 'name' => 'UpdateProtectConfigurationCountryRuleSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateProtectConfigurationCountryRuleSetRequest', ], 'output' => [ 'shape' => 'UpdateProtectConfigurationCountryRuleSetResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateSenderId' => [ 'name' => 'UpdateSenderId', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSenderIdRequest', ], 'output' => [ 'shape' => 'UpdateSenderIdResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'VerifyDestinationNumber' => [ 'name' => 'VerifyDestinationNumber', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'VerifyDestinationNumberRequest', ], 'output' => [ 'shape' => 'VerifyDestinationNumberResult', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'AccessDeniedExceptionReason', ], ], 'exception' => true, ], 'AccessDeniedExceptionReason' => [ 'type' => 'string', 'enum' => [ 'INSUFFICIENT_ACCOUNT_REPUTATION', 'ACCOUNT_DISABLED', ], ], 'AccountAttribute' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'AccountAttributeName', ], 'Value' => [ 'shape' => 'String', ], ], ], 'AccountAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountAttribute', ], ], 'AccountAttributeName' => [ 'type' => 'string', 'enum' => [ 'ACCOUNT_TIER', 'DEFAULT_PROTECT_CONFIGURATION_ID', ], ], 'AccountLimit' => [ 'type' => 'structure', 'required' => [ 'Name', 'Used', 'Max', ], 'members' => [ 'Name' => [ 'shape' => 'AccountLimitName', ], 'Used' => [ 'shape' => 'PrimitiveLong', ], 'Max' => [ 'shape' => 'PrimitiveLong', ], ], ], 'AccountLimitList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountLimit', ], ], 'AccountLimitName' => [ 'type' => 'string', 'enum' => [ 'PHONE_NUMBERS', 'POOLS', 'CONFIGURATION_SETS', 'OPT_OUT_LISTS', 'SENDER_IDS', 'REGISTRATIONS', 'REGISTRATION_ATTACHMENTS', 'VERIFIED_DESTINATION_NUMBERS', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 256, 'min' => 20, 'pattern' => 'arn:[A-Za-z0-9_:/-]+', ], 'AssociateOriginationIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'PoolId', 'OriginationIdentity', 'IsoCountryCode', ], 'members' => [ 'PoolId' => [ 'shape' => 'PoolIdOrArn', ], 'OriginationIdentity' => [ 'shape' => 'PhoneOrSenderIdOrArn', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociateOriginationIdentityResult' => [ 'type' => 'structure', 'members' => [ 'PoolArn' => [ 'shape' => 'String', ], 'PoolId' => [ 'shape' => 'String', ], 'OriginationIdentityArn' => [ 'shape' => 'String', ], 'OriginationIdentity' => [ 'shape' => 'String', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], ], ], 'AssociateProtectConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationId', 'ConfigurationSetName', ], 'members' => [ 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], ], ], 'AssociateProtectConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetArn', 'ConfigurationSetName', 'ProtectConfigurationArn', 'ProtectConfigurationId', ], 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'ProtectConfigurationArn' => [ 'shape' => 'ProtectConfigurationArn', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationId', ], ], ], 'AttachmentBody' => [ 'type' => 'blob', 'max' => 1572864, 'min' => 1, ], 'AttachmentStatus' => [ 'type' => 'string', 'enum' => [ 'UPLOAD_IN_PROGRESS', 'UPLOAD_COMPLETE', 'UPLOAD_FAILED', 'DELETED', ], ], 'AttachmentUploadErrorReason' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_ERROR', ], ], 'AttachmentUrl' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '\\S+', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[!-~]+', ], 'CloudWatchLogsDestination' => [ 'type' => 'structure', 'required' => [ 'IamRoleArn', 'LogGroupArn', ], 'members' => [ 'IamRoleArn' => [ 'shape' => 'IamRoleArn', ], 'LogGroupArn' => [ 'shape' => 'LogGroupArn', ], ], ], 'ConfigurationSetFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'ConfigurationSetFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'ConfigurationSetFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationSetFilter', ], 'max' => 20, 'min' => 0, ], 'ConfigurationSetFilterName' => [ 'type' => 'string', 'enum' => [ 'event-destination-name', 'matching-event-types', 'default-message-type', 'default-sender-id', 'protect-configuration-id', ], ], 'ConfigurationSetInformation' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetArn', 'ConfigurationSetName', 'EventDestinations', 'CreatedTimestamp', ], 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'EventDestinations' => [ 'shape' => 'EventDestinationList', ], 'DefaultMessageType' => [ 'shape' => 'MessageType', ], 'DefaultSenderId' => [ 'shape' => 'SenderId', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], ], ], 'ConfigurationSetInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationSetInformation', ], ], 'ConfigurationSetName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9_-]+', ], 'ConfigurationSetNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigurationSetNameOrArn', ], 'max' => 5, 'min' => 0, ], 'ConfigurationSetNameOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'ConflictExceptionReason', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceId' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ConflictExceptionReason' => [ 'type' => 'string', 'enum' => [ 'CREATE_REGISTRATION_VERSION_NOT_ALLOWED', 'DELETION_PROTECTION_ENABLED', 'DESTINATION_PHONE_NUMBER_NOT_VERIFIED', 'DESTINATION_PHONE_NUMBER_OPTED_OUT', 'DISASSOCIATE_REGISTRATION_NOT_ALLOWED', 'DISCARD_REGISTRATION_VERSION_NOT_ALLOWED', 'EDIT_REGISTRATION_FIELD_VALUES_NOT_ALLOWED', 'EVENT_DESTINATION_MISMATCH', 'KEYWORD_MISMATCH', 'LAST_PHONE_NUMBER', 'NUMBER_CAPABILITIES_MISMATCH', 'MESSAGE_TYPE_MISMATCH', 'NO_ORIGINATION_IDENTITIES_FOUND', 'OPT_OUT_LIST_MISMATCH', 'PHONE_NUMBER_ASSOCIATED_TO_POOL', 'PHONE_NUMBER_ASSOCIATED_TO_REGISTRATION', 'PHONE_NUMBER_NOT_ASSOCIATED_TO_POOL', 'PHONE_NUMBER_NOT_IN_REGISTRATION_REGION', 'REGISTRATION_ALREADY_SUBMITTED', 'REGISTRATION_NOT_COMPLETE', 'SENDER_ID_ASSOCIATED_TO_POOL', 'RESOURCE_ALREADY_EXISTS', 'RESOURCE_DELETION_NOT_ALLOWED', 'RESOURCE_MODIFICATION_NOT_ALLOWED', 'RESOURCE_NOT_ACTIVE', 'RESOURCE_NOT_EMPTY', 'SELF_MANAGED_OPT_OUTS_MISMATCH', 'SUBMIT_REGISTRATION_VERSION_NOT_ALLOWED', 'TWO_WAY_CONFIG_MISMATCH', 'VERIFICATION_CODE_EXPIRED', 'VERIFICATION_ALREADY_COMPLETE', 'PROTECT_CONFIGURATION_IS_ACCOUNT_DEFAULT', 'PROTECT_CONFIGURATION_ASSOCIATED_WITH_CONFIGURATION_SET', 'PROTECT_CONFIGURATION_NOT_ASSOCIATED_WITH_CONFIGURATION_SET', 'DESTINATION_COUNTRY_BLOCKED_BY_PROTECT_CONFIGURATION', ], ], 'ContextKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '\\S+', ], 'ContextMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ContextKey', ], 'value' => [ 'shape' => 'ContextValue', ], 'max' => 5, 'min' => 0, ], 'ContextValue' => [ 'type' => 'string', 'max' => 800, 'min' => 1, 'pattern' => '(?!\\s)^[\\s\\S]+(?<!\\s)', ], 'CreateConfigurationSetRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateConfigurationSetResult' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'Tags' => [ 'shape' => 'TagList', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'CreateEventDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', 'EventDestinationName', 'MatchingEventTypes', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], 'EventDestinationName' => [ 'shape' => 'EventDestinationName', ], 'MatchingEventTypes' => [ 'shape' => 'EventTypeList', ], 'CloudWatchLogsDestination' => [ 'shape' => 'CloudWatchLogsDestination', ], 'KinesisFirehoseDestination' => [ 'shape' => 'KinesisFirehoseDestination', ], 'SnsDestination' => [ 'shape' => 'SnsDestination', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateEventDestinationResult' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'EventDestination' => [ 'shape' => 'EventDestination', ], ], ], 'CreateOptOutListRequest' => [ 'type' => 'structure', 'required' => [ 'OptOutListName', ], 'members' => [ 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateOptOutListResult' => [ 'type' => 'structure', 'members' => [ 'OptOutListArn' => [ 'shape' => 'String', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'Tags' => [ 'shape' => 'TagList', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'CreatePoolRequest' => [ 'type' => 'structure', 'required' => [ 'OriginationIdentity', 'IsoCountryCode', 'MessageType', ], 'members' => [ 'OriginationIdentity' => [ 'shape' => 'PhoneOrSenderIdOrArn', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'DeletionProtectionEnabled' => [ 'shape' => 'Boolean', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreatePoolResult' => [ 'type' => 'structure', 'members' => [ 'PoolArn' => [ 'shape' => 'String', ], 'PoolId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'PoolStatus', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'TwoWayEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'TwoWayChannelArn' => [ 'shape' => 'TwoWayChannelArn', ], 'TwoWayChannelRole' => [ 'shape' => 'IamRoleArn', ], 'SelfManagedOptOutsEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'SharedRoutesEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'Tags' => [ 'shape' => 'TagList', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'CreateProtectConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'DeletionProtectionEnabled' => [ 'shape' => 'Boolean', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateProtectConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationArn', 'ProtectConfigurationId', 'CreatedTimestamp', 'AccountDefault', 'DeletionProtectionEnabled', ], 'members' => [ 'ProtectConfigurationArn' => [ 'shape' => 'ProtectConfigurationArn', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationId', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'AccountDefault' => [ 'shape' => 'PrimitiveBoolean', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRegistrationAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationId', 'ResourceId', ], 'members' => [ 'RegistrationId' => [ 'shape' => 'RegistrationIdOrArn', ], 'ResourceId' => [ 'shape' => 'ResourceIdOrArn', ], ], ], 'CreateRegistrationAssociationResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'RegistrationType', 'ResourceArn', 'ResourceId', 'ResourceType', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'RegistrationType' => [ 'shape' => 'RegistrationType', ], 'ResourceArn' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'CreateRegistrationAttachmentRequest' => [ 'type' => 'structure', 'members' => [ 'AttachmentBody' => [ 'shape' => 'AttachmentBody', ], 'AttachmentUrl' => [ 'shape' => 'AttachmentUrl', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateRegistrationAttachmentResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationAttachmentArn', 'RegistrationAttachmentId', 'AttachmentStatus', 'CreatedTimestamp', ], 'members' => [ 'RegistrationAttachmentArn' => [ 'shape' => 'String', ], 'RegistrationAttachmentId' => [ 'shape' => 'String', ], 'AttachmentStatus' => [ 'shape' => 'AttachmentStatus', ], 'Tags' => [ 'shape' => 'TagList', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'CreateRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationType', ], 'members' => [ 'RegistrationType' => [ 'shape' => 'RegistrationType', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateRegistrationResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'RegistrationType', 'RegistrationStatus', 'CurrentVersionNumber', 'CreatedTimestamp', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'RegistrationType' => [ 'shape' => 'RegistrationType', ], 'RegistrationStatus' => [ 'shape' => 'RegistrationStatus', ], 'CurrentVersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'AdditionalAttributes' => [ 'shape' => 'StringMap', ], 'Tags' => [ 'shape' => 'TagList', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'CreateRegistrationVersionRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationId', ], 'members' => [ 'RegistrationId' => [ 'shape' => 'RegistrationIdOrArn', ], ], ], 'CreateRegistrationVersionResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'VersionNumber', 'RegistrationVersionStatus', 'RegistrationVersionStatusHistory', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'VersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'RegistrationVersionStatus' => [ 'shape' => 'RegistrationVersionStatus', ], 'RegistrationVersionStatusHistory' => [ 'shape' => 'RegistrationVersionStatusHistory', ], ], ], 'CreateVerifiedDestinationNumberRequest' => [ 'type' => 'structure', 'required' => [ 'DestinationPhoneNumber', ], 'members' => [ 'DestinationPhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateVerifiedDestinationNumberResult' => [ 'type' => 'structure', 'required' => [ 'VerifiedDestinationNumberArn', 'VerifiedDestinationNumberId', 'DestinationPhoneNumber', 'Status', 'CreatedTimestamp', ], 'members' => [ 'VerifiedDestinationNumberArn' => [ 'shape' => 'String', ], 'VerifiedDestinationNumberId' => [ 'shape' => 'String', ], 'DestinationPhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Status' => [ 'shape' => 'VerificationStatus', ], 'Tags' => [ 'shape' => 'TagList', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteAccountDefaultProtectConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAccountDefaultProtectConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'DefaultProtectConfigurationArn', 'DefaultProtectConfigurationId', ], 'members' => [ 'DefaultProtectConfigurationArn' => [ 'shape' => 'ProtectConfigurationArn', ], 'DefaultProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationId', ], ], ], 'DeleteConfigurationSetRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], ], ], 'DeleteConfigurationSetResult' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'EventDestinations' => [ 'shape' => 'EventDestinationList', ], 'DefaultMessageType' => [ 'shape' => 'MessageType', ], 'DefaultSenderId' => [ 'shape' => 'SenderId', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteDefaultMessageTypeRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], ], ], 'DeleteDefaultMessageTypeResult' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'MessageType' => [ 'shape' => 'MessageType', ], ], ], 'DeleteDefaultSenderIdRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], ], ], 'DeleteDefaultSenderIdResult' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'SenderId' => [ 'shape' => 'SenderId', ], ], ], 'DeleteEventDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', 'EventDestinationName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], 'EventDestinationName' => [ 'shape' => 'EventDestinationName', ], ], ], 'DeleteEventDestinationResult' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'EventDestination' => [ 'shape' => 'EventDestination', ], ], ], 'DeleteKeywordRequest' => [ 'type' => 'structure', 'required' => [ 'OriginationIdentity', 'Keyword', ], 'members' => [ 'OriginationIdentity' => [ 'shape' => 'PhoneOrPoolIdOrArn', ], 'Keyword' => [ 'shape' => 'Keyword', ], ], ], 'DeleteKeywordResult' => [ 'type' => 'structure', 'members' => [ 'OriginationIdentityArn' => [ 'shape' => 'String', ], 'OriginationIdentity' => [ 'shape' => 'String', ], 'Keyword' => [ 'shape' => 'Keyword', ], 'KeywordMessage' => [ 'shape' => 'KeywordMessage', ], 'KeywordAction' => [ 'shape' => 'KeywordAction', ], ], ], 'DeleteMediaMessageSpendLimitOverrideRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMediaMessageSpendLimitOverrideResult' => [ 'type' => 'structure', 'members' => [ 'MonthlyLimit' => [ 'shape' => 'MonthlyLimit', ], ], ], 'DeleteOptOutListRequest' => [ 'type' => 'structure', 'required' => [ 'OptOutListName', ], 'members' => [ 'OptOutListName' => [ 'shape' => 'OptOutListNameOrArn', ], ], ], 'DeleteOptOutListResult' => [ 'type' => 'structure', 'members' => [ 'OptOutListArn' => [ 'shape' => 'String', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteOptedOutNumberRequest' => [ 'type' => 'structure', 'required' => [ 'OptOutListName', 'OptedOutNumber', ], 'members' => [ 'OptOutListName' => [ 'shape' => 'OptOutListNameOrArn', ], 'OptedOutNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'DeleteOptedOutNumberResult' => [ 'type' => 'structure', 'members' => [ 'OptOutListArn' => [ 'shape' => 'String', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'OptedOutNumber' => [ 'shape' => 'PhoneNumber', ], 'OptedOutTimestamp' => [ 'shape' => 'Timestamp', ], 'EndUserOptedOut' => [ 'shape' => 'PrimitiveBoolean', ], ], ], 'DeletePoolRequest' => [ 'type' => 'structure', 'required' => [ 'PoolId', ], 'members' => [ 'PoolId' => [ 'shape' => 'PoolIdOrArn', ], ], ], 'DeletePoolResult' => [ 'type' => 'structure', 'members' => [ 'PoolArn' => [ 'shape' => 'String', ], 'PoolId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'PoolStatus', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'TwoWayEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'TwoWayChannelArn' => [ 'shape' => 'TwoWayChannelArn', ], 'TwoWayChannelRole' => [ 'shape' => 'IamRoleArn', ], 'SelfManagedOptOutsEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'SharedRoutesEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteProtectConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationId', ], 'members' => [ 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], ], ], 'DeleteProtectConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationArn', 'ProtectConfigurationId', 'CreatedTimestamp', 'AccountDefault', 'DeletionProtectionEnabled', ], 'members' => [ 'ProtectConfigurationArn' => [ 'shape' => 'ProtectConfigurationArn', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationId', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'AccountDefault' => [ 'shape' => 'PrimitiveBoolean', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], ], ], 'DeleteRegistrationAttachmentRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationAttachmentId', ], 'members' => [ 'RegistrationAttachmentId' => [ 'shape' => 'RegistrationAttachmentIdOrArn', ], ], ], 'DeleteRegistrationAttachmentResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationAttachmentArn', 'RegistrationAttachmentId', 'AttachmentStatus', 'CreatedTimestamp', ], 'members' => [ 'RegistrationAttachmentArn' => [ 'shape' => 'String', ], 'RegistrationAttachmentId' => [ 'shape' => 'String', ], 'AttachmentStatus' => [ 'shape' => 'AttachmentStatus', ], 'AttachmentUploadErrorReason' => [ 'shape' => 'AttachmentUploadErrorReason', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteRegistrationFieldValueRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationId', 'FieldPath', ], 'members' => [ 'RegistrationId' => [ 'shape' => 'RegistrationIdOrArn', ], 'FieldPath' => [ 'shape' => 'FieldPath', ], ], ], 'DeleteRegistrationFieldValueResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'VersionNumber', 'FieldPath', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'VersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'FieldPath' => [ 'shape' => 'FieldPath', ], 'SelectChoices' => [ 'shape' => 'SelectChoiceList', ], 'TextValue' => [ 'shape' => 'TextValue', ], 'RegistrationAttachmentId' => [ 'shape' => 'String', ], ], ], 'DeleteRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationId', ], 'members' => [ 'RegistrationId' => [ 'shape' => 'RegistrationIdOrArn', ], ], ], 'DeleteRegistrationResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'RegistrationType', 'RegistrationStatus', 'CurrentVersionNumber', 'CreatedTimestamp', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'RegistrationType' => [ 'shape' => 'RegistrationType', ], 'RegistrationStatus' => [ 'shape' => 'RegistrationStatus', ], 'CurrentVersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'ApprovedVersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'LatestDeniedVersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'AdditionalAttributes' => [ 'shape' => 'StringMap', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteTextMessageSpendLimitOverrideRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTextMessageSpendLimitOverrideResult' => [ 'type' => 'structure', 'members' => [ 'MonthlyLimit' => [ 'shape' => 'MonthlyLimit', ], ], ], 'DeleteVerifiedDestinationNumberRequest' => [ 'type' => 'structure', 'required' => [ 'VerifiedDestinationNumberId', ], 'members' => [ 'VerifiedDestinationNumberId' => [ 'shape' => 'VerifiedDestinationNumberIdOrArn', ], ], ], 'DeleteVerifiedDestinationNumberResult' => [ 'type' => 'structure', 'required' => [ 'VerifiedDestinationNumberArn', 'VerifiedDestinationNumberId', 'DestinationPhoneNumber', 'CreatedTimestamp', ], 'members' => [ 'VerifiedDestinationNumberArn' => [ 'shape' => 'String', ], 'VerifiedDestinationNumberId' => [ 'shape' => 'String', ], 'DestinationPhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteVoiceMessageSpendLimitOverrideRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteVoiceMessageSpendLimitOverrideResult' => [ 'type' => 'structure', 'members' => [ 'MonthlyLimit' => [ 'shape' => 'MonthlyLimit', ], ], ], 'DeliveryStreamArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:\\S+', ], 'DescribeAccountAttributesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeAccountAttributesResult' => [ 'type' => 'structure', 'members' => [ 'AccountAttributes' => [ 'shape' => 'AccountAttributeList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAccountLimitsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeAccountLimitsResult' => [ 'type' => 'structure', 'members' => [ 'AccountLimits' => [ 'shape' => 'AccountLimitList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeConfigurationSetsRequest' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSetNames' => [ 'shape' => 'ConfigurationSetNameList', ], 'Filters' => [ 'shape' => 'ConfigurationSetFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeConfigurationSetsResult' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSets' => [ 'shape' => 'ConfigurationSetInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeKeywordsRequest' => [ 'type' => 'structure', 'required' => [ 'OriginationIdentity', ], 'members' => [ 'OriginationIdentity' => [ 'shape' => 'PhoneOrPoolIdOrArn', ], 'Keywords' => [ 'shape' => 'KeywordList', ], 'Filters' => [ 'shape' => 'KeywordFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeKeywordsResult' => [ 'type' => 'structure', 'members' => [ 'OriginationIdentityArn' => [ 'shape' => 'String', ], 'OriginationIdentity' => [ 'shape' => 'String', ], 'Keywords' => [ 'shape' => 'KeywordInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeOptOutListsRequest' => [ 'type' => 'structure', 'members' => [ 'OptOutListNames' => [ 'shape' => 'OptOutListNameList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeOptOutListsResult' => [ 'type' => 'structure', 'members' => [ 'OptOutLists' => [ 'shape' => 'OptOutListInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeOptedOutNumbersRequest' => [ 'type' => 'structure', 'required' => [ 'OptOutListName', ], 'members' => [ 'OptOutListName' => [ 'shape' => 'OptOutListNameOrArn', ], 'OptedOutNumbers' => [ 'shape' => 'OptedOutNumberList', ], 'Filters' => [ 'shape' => 'OptedOutFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeOptedOutNumbersResult' => [ 'type' => 'structure', 'members' => [ 'OptOutListArn' => [ 'shape' => 'String', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'OptedOutNumbers' => [ 'shape' => 'OptedOutNumberInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePhoneNumbersRequest' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberIds' => [ 'shape' => 'PhoneNumberIdList', ], 'Filters' => [ 'shape' => 'PhoneNumberFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribePhoneNumbersResult' => [ 'type' => 'structure', 'members' => [ 'PhoneNumbers' => [ 'shape' => 'PhoneNumberInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePoolsRequest' => [ 'type' => 'structure', 'members' => [ 'PoolIds' => [ 'shape' => 'PoolIdList', ], 'Filters' => [ 'shape' => 'PoolFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribePoolsResult' => [ 'type' => 'structure', 'members' => [ 'Pools' => [ 'shape' => 'PoolInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeProtectConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'ProtectConfigurationIds' => [ 'shape' => 'ProtectConfigurationIdList', ], 'Filters' => [ 'shape' => 'ProtectConfigurationFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeProtectConfigurationsResult' => [ 'type' => 'structure', 'members' => [ 'ProtectConfigurations' => [ 'shape' => 'ProtectConfigurationInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeRegistrationAttachmentsRequest' => [ 'type' => 'structure', 'members' => [ 'RegistrationAttachmentIds' => [ 'shape' => 'RegistrationAttachmentIdList', ], 'Filters' => [ 'shape' => 'RegistrationAttachmentFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeRegistrationAttachmentsResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationAttachments', ], 'members' => [ 'RegistrationAttachments' => [ 'shape' => 'RegistrationAttachmentsInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeRegistrationFieldDefinitionsRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationType', ], 'members' => [ 'RegistrationType' => [ 'shape' => 'RegistrationType', ], 'SectionPath' => [ 'shape' => 'SectionPath', ], 'FieldPaths' => [ 'shape' => 'FieldPathList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeRegistrationFieldDefinitionsResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationType', 'RegistrationFieldDefinitions', ], 'members' => [ 'RegistrationType' => [ 'shape' => 'RegistrationType', ], 'RegistrationFieldDefinitions' => [ 'shape' => 'RegistrationFieldDefinitionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeRegistrationFieldValuesRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationId', ], 'members' => [ 'RegistrationId' => [ 'shape' => 'RegistrationIdOrArn', ], 'VersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'SectionPath' => [ 'shape' => 'SectionPath', ], 'FieldPaths' => [ 'shape' => 'FieldPathList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeRegistrationFieldValuesResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'VersionNumber', 'RegistrationFieldValues', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'VersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'RegistrationFieldValues' => [ 'shape' => 'RegistrationFieldValueInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeRegistrationSectionDefinitionsRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationType', ], 'members' => [ 'RegistrationType' => [ 'shape' => 'RegistrationType', ], 'SectionPaths' => [ 'shape' => 'SectionPathList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeRegistrationSectionDefinitionsResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationType', 'RegistrationSectionDefinitions', ], 'members' => [ 'RegistrationType' => [ 'shape' => 'RegistrationType', ], 'RegistrationSectionDefinitions' => [ 'shape' => 'RegistrationSectionDefinitionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeRegistrationTypeDefinitionsRequest' => [ 'type' => 'structure', 'members' => [ 'RegistrationTypes' => [ 'shape' => 'RegistrationTypeList', ], 'Filters' => [ 'shape' => 'RegistrationTypeFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeRegistrationTypeDefinitionsResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationTypeDefinitions', ], 'members' => [ 'RegistrationTypeDefinitions' => [ 'shape' => 'RegistrationTypeDefinitionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeRegistrationVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationId', ], 'members' => [ 'RegistrationId' => [ 'shape' => 'RegistrationIdOrArn', ], 'VersionNumbers' => [ 'shape' => 'RegistrationVersionNumberList', ], 'Filters' => [ 'shape' => 'RegistrationVersionFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeRegistrationVersionsResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'RegistrationVersions', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'RegistrationVersions' => [ 'shape' => 'RegistrationVersionInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeRegistrationsRequest' => [ 'type' => 'structure', 'members' => [ 'RegistrationIds' => [ 'shape' => 'RegistrationIdList', ], 'Filters' => [ 'shape' => 'RegistrationFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeRegistrationsResult' => [ 'type' => 'structure', 'required' => [ 'Registrations', ], 'members' => [ 'Registrations' => [ 'shape' => 'RegistrationInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeSenderIdsRequest' => [ 'type' => 'structure', 'members' => [ 'SenderIds' => [ 'shape' => 'SenderIdList', ], 'Filters' => [ 'shape' => 'SenderIdFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeSenderIdsResult' => [ 'type' => 'structure', 'members' => [ 'SenderIds' => [ 'shape' => 'SenderIdInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeSpendLimitsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeSpendLimitsResult' => [ 'type' => 'structure', 'members' => [ 'SpendLimits' => [ 'shape' => 'SpendLimitList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeVerifiedDestinationNumbersRequest' => [ 'type' => 'structure', 'members' => [ 'VerifiedDestinationNumberIds' => [ 'shape' => 'VerifiedDestinationNumberIdList', ], 'DestinationPhoneNumbers' => [ 'shape' => 'DestinationPhoneNumberList', ], 'Filters' => [ 'shape' => 'VerifiedDestinationNumberFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeVerifiedDestinationNumbersResult' => [ 'type' => 'structure', 'required' => [ 'VerifiedDestinationNumbers', ], 'members' => [ 'VerifiedDestinationNumbers' => [ 'shape' => 'VerifiedDestinationNumberInformationList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DestinationCountryParameterKey' => [ 'type' => 'string', 'enum' => [ 'IN_TEMPLATE_ID', 'IN_ENTITY_ID', ], ], 'DestinationCountryParameterValue' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '\\S+', ], 'DestinationCountryParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'DestinationCountryParameterKey', ], 'value' => [ 'shape' => 'DestinationCountryParameterValue', ], 'max' => 10, 'min' => 0, ], 'DestinationPhoneNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumber', ], 'max' => 5, 'min' => 0, ], 'DisassociateOriginationIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'PoolId', 'OriginationIdentity', 'IsoCountryCode', ], 'members' => [ 'PoolId' => [ 'shape' => 'PoolIdOrArn', ], 'OriginationIdentity' => [ 'shape' => 'PhoneOrSenderIdOrArn', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DisassociateOriginationIdentityResult' => [ 'type' => 'structure', 'members' => [ 'PoolArn' => [ 'shape' => 'String', ], 'PoolId' => [ 'shape' => 'String', ], 'OriginationIdentityArn' => [ 'shape' => 'String', ], 'OriginationIdentity' => [ 'shape' => 'String', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], ], ], 'DisassociateProtectConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationId', 'ConfigurationSetName', ], 'members' => [ 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], ], ], 'DisassociateProtectConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetArn', 'ConfigurationSetName', 'ProtectConfigurationArn', 'ProtectConfigurationId', ], 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'ProtectConfigurationArn' => [ 'shape' => 'ProtectConfigurationArn', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationId', ], ], ], 'DiscardRegistrationVersionRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationId', ], 'members' => [ 'RegistrationId' => [ 'shape' => 'RegistrationIdOrArn', ], ], ], 'DiscardRegistrationVersionResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'VersionNumber', 'RegistrationVersionStatus', 'RegistrationVersionStatusHistory', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'VersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'RegistrationVersionStatus' => [ 'shape' => 'RegistrationVersionStatus', ], 'RegistrationVersionStatusHistory' => [ 'shape' => 'RegistrationVersionStatusHistory', ], ], ], 'EventDestination' => [ 'type' => 'structure', 'required' => [ 'EventDestinationName', 'Enabled', 'MatchingEventTypes', ], 'members' => [ 'EventDestinationName' => [ 'shape' => 'EventDestinationName', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'MatchingEventTypes' => [ 'shape' => 'EventTypeList', ], 'CloudWatchLogsDestination' => [ 'shape' => 'CloudWatchLogsDestination', ], 'KinesisFirehoseDestination' => [ 'shape' => 'KinesisFirehoseDestination', ], 'SnsDestination' => [ 'shape' => 'SnsDestination', ], ], ], 'EventDestinationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventDestination', ], ], 'EventDestinationName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9_-]+', ], 'EventType' => [ 'type' => 'string', 'enum' => [ 'ALL', 'TEXT_ALL', 'TEXT_SENT', 'TEXT_PENDING', 'TEXT_QUEUED', 'TEXT_SUCCESSFUL', 'TEXT_DELIVERED', 'TEXT_INVALID', 'TEXT_INVALID_MESSAGE', 'TEXT_UNREACHABLE', 'TEXT_CARRIER_UNREACHABLE', 'TEXT_BLOCKED', 'TEXT_CARRIER_BLOCKED', 'TEXT_SPAM', 'TEXT_UNKNOWN', 'TEXT_TTL_EXPIRED', 'VOICE_ALL', 'VOICE_INITIATED', 'VOICE_RINGING', 'VOICE_ANSWERED', 'VOICE_COMPLETED', 'VOICE_BUSY', 'VOICE_NO_ANSWER', 'VOICE_FAILED', 'VOICE_TTL_EXPIRED', 'MEDIA_ALL', 'MEDIA_PENDING', 'MEDIA_QUEUED', 'MEDIA_SUCCESSFUL', 'MEDIA_DELIVERED', 'MEDIA_INVALID', 'MEDIA_INVALID_MESSAGE', 'MEDIA_UNREACHABLE', 'MEDIA_CARRIER_UNREACHABLE', 'MEDIA_BLOCKED', 'MEDIA_CARRIER_BLOCKED', 'MEDIA_SPAM', 'MEDIA_UNKNOWN', 'MEDIA_TTL_EXPIRED', 'MEDIA_FILE_INACCESSIBLE', 'MEDIA_FILE_TYPE_UNSUPPORTED', 'MEDIA_FILE_SIZE_EXCEEDED', ], ], 'EventTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventType', ], 'max' => 43, 'min' => 1, ], 'FieldPath' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[A-Za-z0-9_\\.]+', ], 'FieldPathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldPath', ], 'max' => 5, 'min' => 0, ], 'FieldRequirement' => [ 'type' => 'string', 'enum' => [ 'REQUIRED', 'CONDITIONAL', 'OPTIONAL', ], ], 'FieldType' => [ 'type' => 'string', 'enum' => [ 'SELECT', 'TEXT', 'ATTACHMENT', ], ], 'FilterValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[/\\.:A-Za-z0-9_-]+', ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], 'max' => 20, 'min' => 1, ], 'GetProtectConfigurationCountryRuleSetRequest' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationId', 'NumberCapability', ], 'members' => [ 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], 'NumberCapability' => [ 'shape' => 'NumberCapability', ], ], ], 'GetProtectConfigurationCountryRuleSetResult' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationArn', 'ProtectConfigurationId', 'NumberCapability', 'CountryRuleSet', ], 'members' => [ 'ProtectConfigurationArn' => [ 'shape' => 'ProtectConfigurationArn', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationId', ], 'NumberCapability' => [ 'shape' => 'NumberCapability', ], 'CountryRuleSet' => [ 'shape' => 'ProtectConfigurationCountryRuleSet', ], ], ], 'IamRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:\\S+', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'IsoCountryCode' => [ 'type' => 'string', 'max' => 2, 'min' => 2, 'pattern' => '[A-Z]{2}', ], 'Keyword' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '[ \\S]+', ], 'KeywordAction' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC_RESPONSE', 'OPT_OUT', 'OPT_IN', ], ], 'KeywordFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'KeywordFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'KeywordFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeywordFilter', ], 'max' => 20, 'min' => 0, ], 'KeywordFilterName' => [ 'type' => 'string', 'enum' => [ 'keyword-action', ], ], 'KeywordInformation' => [ 'type' => 'structure', 'required' => [ 'Keyword', 'KeywordMessage', 'KeywordAction', ], 'members' => [ 'Keyword' => [ 'shape' => 'Keyword', ], 'KeywordMessage' => [ 'shape' => 'KeywordMessage', ], 'KeywordAction' => [ 'shape' => 'KeywordAction', ], ], ], 'KeywordInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeywordInformation', ], ], 'KeywordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Keyword', ], 'max' => 5, 'min' => 0, ], 'KeywordMessage' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '(?!\\s*$)[\\s\\S]+', ], 'KinesisFirehoseDestination' => [ 'type' => 'structure', 'required' => [ 'IamRoleArn', 'DeliveryStreamArn', ], 'members' => [ 'IamRoleArn' => [ 'shape' => 'IamRoleArn', ], 'DeliveryStreamArn' => [ 'shape' => 'DeliveryStreamArn', ], ], ], 'LanguageCode' => [ 'type' => 'string', 'enum' => [ 'DE_DE', 'EN_GB', 'EN_US', 'ES_419', 'ES_ES', 'FR_CA', 'FR_FR', 'IT_IT', 'JA_JP', 'KO_KR', 'PT_BR', 'ZH_CN', 'ZH_TW', ], ], 'ListPoolOriginationIdentitiesRequest' => [ 'type' => 'structure', 'required' => [ 'PoolId', ], 'members' => [ 'PoolId' => [ 'shape' => 'PoolIdOrArn', ], 'Filters' => [ 'shape' => 'PoolOriginationIdentitiesFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPoolOriginationIdentitiesResult' => [ 'type' => 'structure', 'members' => [ 'PoolArn' => [ 'shape' => 'String', ], 'PoolId' => [ 'shape' => 'String', ], 'OriginationIdentities' => [ 'shape' => 'OriginationIdentityMetadataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRegistrationAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationId', ], 'members' => [ 'RegistrationId' => [ 'shape' => 'RegistrationIdOrArn', ], 'Filters' => [ 'shape' => 'RegistrationAssociationFilterList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListRegistrationAssociationsResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'RegistrationType', 'RegistrationAssociations', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'RegistrationType' => [ 'shape' => 'RegistrationType', ], 'RegistrationAssociations' => [ 'shape' => 'RegistrationAssociationMetadataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResult' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'LogGroupArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:\\S+', ], 'MaxPrice' => [ 'type' => 'string', 'max' => 8, 'min' => 2, 'pattern' => '[0-9]{0,2}\\.[0-9]{1,5}', ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MediaMessageOriginationIdentity' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/\\+-]+', ], 'MediaUrlList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaUrlValue', ], 'max' => 1, 'min' => 1, ], 'MediaUrlValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 's3://([a-z0-9\\.-]{3,63})/(.+)', ], 'MessageType' => [ 'type' => 'string', 'enum' => [ 'TRANSACTIONAL', 'PROMOTIONAL', ], ], 'MessageTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageType', ], 'max' => 2, 'min' => 0, ], 'MonthlyLimit' => [ 'type' => 'long', 'box' => true, 'max' => 1000000000, 'min' => 0, ], 'NextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.+', ], 'NonEmptyTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 1, ], 'NumberCapability' => [ 'type' => 'string', 'enum' => [ 'SMS', 'VOICE', 'MMS', ], ], 'NumberCapabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NumberCapability', ], 'max' => 3, 'min' => 1, ], 'NumberStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACTIVE', 'ASSOCIATING', 'DISASSOCIATING', 'DELETED', ], ], 'NumberType' => [ 'type' => 'string', 'enum' => [ 'SHORT_CODE', 'LONG_CODE', 'TOLL_FREE', 'TEN_DLC', 'SIMULATOR', ], ], 'OptOutListInformation' => [ 'type' => 'structure', 'required' => [ 'OptOutListArn', 'OptOutListName', 'CreatedTimestamp', ], 'members' => [ 'OptOutListArn' => [ 'shape' => 'String', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'OptOutListInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptOutListInformation', ], ], 'OptOutListName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9_-]+', ], 'OptOutListNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptOutListNameOrArn', ], 'max' => 5, 'min' => 0, ], 'OptOutListNameOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'OptedOutFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'OptedOutFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'OptedOutFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptedOutFilter', ], 'max' => 20, 'min' => 0, ], 'OptedOutFilterName' => [ 'type' => 'string', 'enum' => [ 'end-user-opted-out', ], ], 'OptedOutNumberInformation' => [ 'type' => 'structure', 'required' => [ 'OptedOutNumber', 'OptedOutTimestamp', 'EndUserOptedOut', ], 'members' => [ 'OptedOutNumber' => [ 'shape' => 'PhoneNumber', ], 'OptedOutTimestamp' => [ 'shape' => 'Timestamp', ], 'EndUserOptedOut' => [ 'shape' => 'PrimitiveBoolean', ], ], ], 'OptedOutNumberInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptedOutNumberInformation', ], ], 'OptedOutNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumber', ], 'max' => 5, 'min' => 0, ], 'OriginationIdentityMetadata' => [ 'type' => 'structure', 'required' => [ 'OriginationIdentityArn', 'OriginationIdentity', 'IsoCountryCode', 'NumberCapabilities', ], 'members' => [ 'OriginationIdentityArn' => [ 'shape' => 'String', ], 'OriginationIdentity' => [ 'shape' => 'String', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'NumberCapabilities' => [ 'shape' => 'NumberCapabilityList', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'OriginationIdentityMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginationIdentityMetadata', ], ], 'PhoneNumber' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '\\+?[1-9][0-9]{1,18}', ], 'PhoneNumberFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'PhoneNumberFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'PhoneNumberFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberFilter', ], 'max' => 20, 'min' => 0, ], 'PhoneNumberFilterName' => [ 'type' => 'string', 'enum' => [ 'status', 'iso-country-code', 'message-type', 'number-capability', 'number-type', 'two-way-enabled', 'self-managed-opt-outs-enabled', 'opt-out-list-name', 'deletion-protection-enabled', 'two-way-channel-arn', ], ], 'PhoneNumberIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberIdOrArn', ], 'max' => 5, 'min' => 0, ], 'PhoneNumberIdOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'PhoneNumberInformation' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberArn', 'PhoneNumber', 'Status', 'IsoCountryCode', 'MessageType', 'NumberCapabilities', 'NumberType', 'MonthlyLeasingPrice', 'TwoWayEnabled', 'SelfManagedOptOutsEnabled', 'OptOutListName', 'DeletionProtectionEnabled', 'CreatedTimestamp', ], 'members' => [ 'PhoneNumberArn' => [ 'shape' => 'String', ], 'PhoneNumberId' => [ 'shape' => 'String', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Status' => [ 'shape' => 'NumberStatus', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'NumberCapabilities' => [ 'shape' => 'NumberCapabilityList', ], 'NumberType' => [ 'shape' => 'NumberType', ], 'MonthlyLeasingPrice' => [ 'shape' => 'String', ], 'TwoWayEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'TwoWayChannelArn' => [ 'shape' => 'TwoWayChannelArn', ], 'TwoWayChannelRole' => [ 'shape' => 'IamRoleArn', ], 'SelfManagedOptOutsEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'PoolId' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'PhoneNumberInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhoneNumberInformation', ], ], 'PhoneOrPoolIdOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'PhoneOrSenderIdOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'PoolFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'PoolFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'PoolFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PoolFilter', ], 'max' => 20, 'min' => 0, ], 'PoolFilterName' => [ 'type' => 'string', 'enum' => [ 'status', 'message-type', 'two-way-enabled', 'self-managed-opt-outs-enabled', 'opt-out-list-name', 'shared-routes-enabled', 'deletion-protection-enabled', 'two-way-channel-arn', ], ], 'PoolIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PoolIdOrArn', ], 'max' => 5, 'min' => 0, ], 'PoolIdOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'PoolInformation' => [ 'type' => 'structure', 'required' => [ 'PoolArn', 'PoolId', 'Status', 'MessageType', 'TwoWayEnabled', 'SelfManagedOptOutsEnabled', 'OptOutListName', 'SharedRoutesEnabled', 'DeletionProtectionEnabled', 'CreatedTimestamp', ], 'members' => [ 'PoolArn' => [ 'shape' => 'String', ], 'PoolId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'PoolStatus', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'TwoWayEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'TwoWayChannelArn' => [ 'shape' => 'TwoWayChannelArn', ], 'TwoWayChannelRole' => [ 'shape' => 'IamRoleArn', ], 'SelfManagedOptOutsEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'SharedRoutesEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'PoolInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PoolInformation', ], ], 'PoolOriginationIdentitiesFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'PoolOriginationIdentitiesFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'PoolOriginationIdentitiesFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PoolOriginationIdentitiesFilter', ], 'max' => 20, 'min' => 0, ], 'PoolOriginationIdentitiesFilterName' => [ 'type' => 'string', 'enum' => [ 'iso-country-code', 'number-capability', ], ], 'PoolStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', ], ], 'PrimitiveBoolean' => [ 'type' => 'boolean', ], 'PrimitiveLong' => [ 'type' => 'long', ], 'ProtectConfigurationArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => 'arn:\\S+', ], 'ProtectConfigurationCountryRuleSet' => [ 'type' => 'map', 'key' => [ 'shape' => 'IsoCountryCode', ], 'value' => [ 'shape' => 'ProtectConfigurationCountryRuleSetInformation', ], 'max' => 300, 'min' => 1, ], 'ProtectConfigurationCountryRuleSetInformation' => [ 'type' => 'structure', 'required' => [ 'ProtectStatus', ], 'members' => [ 'ProtectStatus' => [ 'shape' => 'ProtectStatus', ], ], ], 'ProtectConfigurationFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'ProtectConfigurationFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'ProtectConfigurationFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProtectConfigurationFilter', ], 'max' => 20, 'min' => 0, ], 'ProtectConfigurationFilterName' => [ 'type' => 'string', 'enum' => [ 'account-default', 'deletion-protection-enabled', ], ], 'ProtectConfigurationId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9_-]+', ], 'ProtectConfigurationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], 'max' => 5, 'min' => 0, ], 'ProtectConfigurationIdOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'ProtectConfigurationInformation' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationArn', 'ProtectConfigurationId', 'CreatedTimestamp', 'AccountDefault', 'DeletionProtectionEnabled', ], 'members' => [ 'ProtectConfigurationArn' => [ 'shape' => 'ProtectConfigurationArn', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationId', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'AccountDefault' => [ 'shape' => 'PrimitiveBoolean', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], ], ], 'ProtectConfigurationInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProtectConfigurationInformation', ], 'max' => 100, 'min' => 1, ], 'ProtectStatus' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'BLOCK', ], ], 'PutKeywordRequest' => [ 'type' => 'structure', 'required' => [ 'OriginationIdentity', 'Keyword', 'KeywordMessage', ], 'members' => [ 'OriginationIdentity' => [ 'shape' => 'PhoneOrPoolIdOrArn', ], 'Keyword' => [ 'shape' => 'Keyword', ], 'KeywordMessage' => [ 'shape' => 'KeywordMessage', ], 'KeywordAction' => [ 'shape' => 'KeywordAction', ], ], ], 'PutKeywordResult' => [ 'type' => 'structure', 'members' => [ 'OriginationIdentityArn' => [ 'shape' => 'String', ], 'OriginationIdentity' => [ 'shape' => 'String', ], 'Keyword' => [ 'shape' => 'Keyword', ], 'KeywordMessage' => [ 'shape' => 'KeywordMessage', ], 'KeywordAction' => [ 'shape' => 'KeywordAction', ], ], ], 'PutOptedOutNumberRequest' => [ 'type' => 'structure', 'required' => [ 'OptOutListName', 'OptedOutNumber', ], 'members' => [ 'OptOutListName' => [ 'shape' => 'OptOutListNameOrArn', ], 'OptedOutNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'PutOptedOutNumberResult' => [ 'type' => 'structure', 'members' => [ 'OptOutListArn' => [ 'shape' => 'String', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'OptedOutNumber' => [ 'shape' => 'PhoneNumber', ], 'OptedOutTimestamp' => [ 'shape' => 'Timestamp', ], 'EndUserOptedOut' => [ 'shape' => 'PrimitiveBoolean', ], ], ], 'PutRegistrationFieldValueRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationId', 'FieldPath', ], 'members' => [ 'RegistrationId' => [ 'shape' => 'RegistrationIdOrArn', ], 'FieldPath' => [ 'shape' => 'FieldPath', ], 'SelectChoices' => [ 'shape' => 'SelectChoiceList', ], 'TextValue' => [ 'shape' => 'TextValue', ], 'RegistrationAttachmentId' => [ 'shape' => 'RegistrationAttachmentIdOrArn', ], ], ], 'PutRegistrationFieldValueResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'VersionNumber', 'FieldPath', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'VersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'FieldPath' => [ 'shape' => 'FieldPath', ], 'SelectChoices' => [ 'shape' => 'SelectChoiceList', ], 'TextValue' => [ 'shape' => 'TextValue', ], 'RegistrationAttachmentId' => [ 'shape' => 'String', ], ], ], 'RegistrationAssociationBehavior' => [ 'type' => 'string', 'enum' => [ 'ASSOCIATE_BEFORE_SUBMIT', 'ASSOCIATE_ON_APPROVAL', 'ASSOCIATE_AFTER_COMPLETE', ], ], 'RegistrationAssociationFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'RegistrationAssociationFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'RegistrationAssociationFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationAssociationFilter', ], 'max' => 20, 'min' => 0, ], 'RegistrationAssociationFilterName' => [ 'type' => 'string', 'enum' => [ 'resource-type', 'iso-country-code', ], ], 'RegistrationAssociationMetadata' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'ResourceId', 'ResourceType', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], ], ], 'RegistrationAssociationMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationAssociationMetadata', ], ], 'RegistrationAttachmentFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'RegistrationAttachmentFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'RegistrationAttachmentFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationAttachmentFilter', ], 'max' => 20, 'min' => 0, ], 'RegistrationAttachmentFilterName' => [ 'type' => 'string', 'enum' => [ 'attachment-status', ], ], 'RegistrationAttachmentIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationIdOrArn', ], 'max' => 5, 'min' => 0, ], 'RegistrationAttachmentIdOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'RegistrationAttachmentsInformation' => [ 'type' => 'structure', 'required' => [ 'RegistrationAttachmentArn', 'RegistrationAttachmentId', 'AttachmentStatus', 'CreatedTimestamp', ], 'members' => [ 'RegistrationAttachmentArn' => [ 'shape' => 'String', ], 'RegistrationAttachmentId' => [ 'shape' => 'String', ], 'AttachmentStatus' => [ 'shape' => 'AttachmentStatus', ], 'AttachmentUploadErrorReason' => [ 'shape' => 'AttachmentUploadErrorReason', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'RegistrationAttachmentsInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationAttachmentsInformation', ], ], 'RegistrationDeniedReasonInformation' => [ 'type' => 'structure', 'required' => [ 'Reason', 'ShortDescription', ], 'members' => [ 'Reason' => [ 'shape' => 'String', ], 'ShortDescription' => [ 'shape' => 'String', ], 'LongDescription' => [ 'shape' => 'String', ], 'DocumentationTitle' => [ 'shape' => 'String', ], 'DocumentationLink' => [ 'shape' => 'String', ], ], ], 'RegistrationDeniedReasonInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationDeniedReasonInformation', ], ], 'RegistrationDisassociationBehavior' => [ 'type' => 'string', 'enum' => [ 'DISASSOCIATE_ALL_CLOSES_REGISTRATION', 'DISASSOCIATE_ALL_ALLOWS_DELETE_REGISTRATION', 'DELETE_REGISTRATION_DISASSOCIATES', ], ], 'RegistrationFieldDefinition' => [ 'type' => 'structure', 'required' => [ 'SectionPath', 'FieldPath', 'FieldType', 'FieldRequirement', 'DisplayHints', ], 'members' => [ 'SectionPath' => [ 'shape' => 'SectionPath', ], 'FieldPath' => [ 'shape' => 'FieldPath', ], 'FieldType' => [ 'shape' => 'FieldType', ], 'FieldRequirement' => [ 'shape' => 'FieldRequirement', ], 'SelectValidation' => [ 'shape' => 'SelectValidation', ], 'TextValidation' => [ 'shape' => 'TextValidation', ], 'DisplayHints' => [ 'shape' => 'RegistrationFieldDisplayHints', ], ], ], 'RegistrationFieldDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationFieldDefinition', ], ], 'RegistrationFieldDisplayHints' => [ 'type' => 'structure', 'required' => [ 'Title', 'ShortDescription', ], 'members' => [ 'Title' => [ 'shape' => 'String', ], 'ShortDescription' => [ 'shape' => 'String', ], 'LongDescription' => [ 'shape' => 'String', ], 'DocumentationTitle' => [ 'shape' => 'String', ], 'DocumentationLink' => [ 'shape' => 'String', ], 'SelectOptionDescriptions' => [ 'shape' => 'SelectOptionDescriptionsList', ], 'TextValidationDescription' => [ 'shape' => 'String', ], 'ExampleTextValue' => [ 'shape' => 'String', ], ], ], 'RegistrationFieldValueInformation' => [ 'type' => 'structure', 'required' => [ 'FieldPath', ], 'members' => [ 'FieldPath' => [ 'shape' => 'FieldPath', ], 'SelectChoices' => [ 'shape' => 'SelectChoiceList', ], 'TextValue' => [ 'shape' => 'TextValue', ], 'RegistrationAttachmentId' => [ 'shape' => 'RegistrationAttachmentIdOrArn', ], 'DeniedReason' => [ 'shape' => 'String', ], ], ], 'RegistrationFieldValueInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationFieldValueInformation', ], ], 'RegistrationFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'RegistrationFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'RegistrationFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationFilter', ], 'max' => 20, 'min' => 0, ], 'RegistrationFilterName' => [ 'type' => 'string', 'enum' => [ 'registration-type', 'registration-status', ], ], 'RegistrationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationIdOrArn', ], 'max' => 5, 'min' => 0, ], 'RegistrationIdOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'RegistrationInformation' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'RegistrationType', 'RegistrationStatus', 'CurrentVersionNumber', 'CreatedTimestamp', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'RegistrationType' => [ 'shape' => 'RegistrationType', ], 'RegistrationStatus' => [ 'shape' => 'RegistrationStatus', ], 'CurrentVersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'ApprovedVersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'LatestDeniedVersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'AdditionalAttributes' => [ 'shape' => 'StringMap', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'RegistrationInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationInformation', ], ], 'RegistrationSectionDefinition' => [ 'type' => 'structure', 'required' => [ 'SectionPath', 'DisplayHints', ], 'members' => [ 'SectionPath' => [ 'shape' => 'SectionPath', ], 'DisplayHints' => [ 'shape' => 'RegistrationSectionDisplayHints', ], ], ], 'RegistrationSectionDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationSectionDefinition', ], ], 'RegistrationSectionDisplayHints' => [ 'type' => 'structure', 'required' => [ 'Title', 'ShortDescription', ], 'members' => [ 'Title' => [ 'shape' => 'String', ], 'ShortDescription' => [ 'shape' => 'String', ], 'LongDescription' => [ 'shape' => 'String', ], 'DocumentationTitle' => [ 'shape' => 'String', ], 'DocumentationLink' => [ 'shape' => 'String', ], ], ], 'RegistrationStatus' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'SUBMITTED', 'REVIEWING', 'PROVISIONING', 'COMPLETE', 'REQUIRES_UPDATES', 'CLOSED', 'DELETED', ], ], 'RegistrationType' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9_]+', ], 'RegistrationTypeDefinition' => [ 'type' => 'structure', 'required' => [ 'RegistrationType', 'DisplayHints', ], 'members' => [ 'RegistrationType' => [ 'shape' => 'RegistrationType', ], 'SupportedAssociations' => [ 'shape' => 'SupportedAssociationList', ], 'DisplayHints' => [ 'shape' => 'RegistrationTypeDisplayHints', ], ], ], 'RegistrationTypeDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationTypeDefinition', ], ], 'RegistrationTypeDisplayHints' => [ 'type' => 'structure', 'required' => [ 'Title', ], 'members' => [ 'Title' => [ 'shape' => 'String', ], 'ShortDescription' => [ 'shape' => 'String', ], 'LongDescription' => [ 'shape' => 'String', ], 'DocumentationTitle' => [ 'shape' => 'String', ], 'DocumentationLink' => [ 'shape' => 'String', ], ], ], 'RegistrationTypeFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'RegistrationTypeFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'RegistrationTypeFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationTypeFilter', ], 'max' => 20, 'min' => 0, ], 'RegistrationTypeFilterName' => [ 'type' => 'string', 'enum' => [ 'supported-association-resource-type', 'supported-association-iso-country-code', ], ], 'RegistrationTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationType', ], 'max' => 5, 'min' => 0, ], 'RegistrationVersionFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'RegistrationVersionFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'RegistrationVersionFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationVersionFilter', ], 'max' => 20, 'min' => 0, ], 'RegistrationVersionFilterName' => [ 'type' => 'string', 'enum' => [ 'registration-version-status', ], ], 'RegistrationVersionInformation' => [ 'type' => 'structure', 'required' => [ 'VersionNumber', 'RegistrationVersionStatus', 'RegistrationVersionStatusHistory', ], 'members' => [ 'VersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'RegistrationVersionStatus' => [ 'shape' => 'RegistrationVersionStatus', ], 'RegistrationVersionStatusHistory' => [ 'shape' => 'RegistrationVersionStatusHistory', ], 'DeniedReasons' => [ 'shape' => 'RegistrationDeniedReasonInformationList', ], ], ], 'RegistrationVersionInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationVersionInformation', ], ], 'RegistrationVersionNumber' => [ 'type' => 'long', 'box' => true, 'max' => 100000, 'min' => 1, ], 'RegistrationVersionNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistrationVersionNumber', ], 'max' => 5, 'min' => 0, ], 'RegistrationVersionStatus' => [ 'type' => 'string', 'enum' => [ 'DRAFT', 'SUBMITTED', 'REVIEWING', 'APPROVED', 'DISCARDED', 'DENIED', 'REVOKED', 'ARCHIVED', ], ], 'RegistrationVersionStatusHistory' => [ 'type' => 'structure', 'required' => [ 'DraftTimestamp', ], 'members' => [ 'DraftTimestamp' => [ 'shape' => 'Timestamp', ], 'SubmittedTimestamp' => [ 'shape' => 'Timestamp', ], 'ReviewingTimestamp' => [ 'shape' => 'Timestamp', ], 'ApprovedTimestamp' => [ 'shape' => 'Timestamp', ], 'DiscardedTimestamp' => [ 'shape' => 'Timestamp', ], 'DeniedTimestamp' => [ 'shape' => 'Timestamp', ], 'RevokedTimestamp' => [ 'shape' => 'Timestamp', ], 'ArchivedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ReleasePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberIdOrArn', ], ], ], 'ReleasePhoneNumberResult' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberArn' => [ 'shape' => 'String', ], 'PhoneNumberId' => [ 'shape' => 'String', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Status' => [ 'shape' => 'NumberStatus', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'NumberCapabilities' => [ 'shape' => 'NumberCapabilityList', ], 'NumberType' => [ 'shape' => 'NumberType', ], 'MonthlyLeasingPrice' => [ 'shape' => 'String', ], 'TwoWayEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'TwoWayChannelArn' => [ 'shape' => 'TwoWayChannelArn', ], 'TwoWayChannelRole' => [ 'shape' => 'IamRoleArn', ], 'SelfManagedOptOutsEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'RegistrationId' => [ 'shape' => 'String', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ReleaseSenderIdRequest' => [ 'type' => 'structure', 'required' => [ 'SenderId', 'IsoCountryCode', ], 'members' => [ 'SenderId' => [ 'shape' => 'SenderIdOrArn', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], ], ], 'ReleaseSenderIdResult' => [ 'type' => 'structure', 'required' => [ 'SenderIdArn', 'SenderId', 'IsoCountryCode', 'MessageTypes', 'MonthlyLeasingPrice', 'Registered', ], 'members' => [ 'SenderIdArn' => [ 'shape' => 'String', ], 'SenderId' => [ 'shape' => 'SenderId', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'MessageTypes' => [ 'shape' => 'MessageTypeList', ], 'MonthlyLeasingPrice' => [ 'shape' => 'String', ], 'Registered' => [ 'shape' => 'PrimitiveBoolean', ], 'RegistrationId' => [ 'shape' => 'String', ], ], ], 'RequestPhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'IsoCountryCode', 'MessageType', 'NumberCapabilities', 'NumberType', ], 'members' => [ 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'NumberCapabilities' => [ 'shape' => 'NumberCapabilityList', ], 'NumberType' => [ 'shape' => 'RequestableNumberType', ], 'OptOutListName' => [ 'shape' => 'OptOutListNameOrArn', ], 'PoolId' => [ 'shape' => 'PoolIdOrArn', ], 'RegistrationId' => [ 'shape' => 'RegistrationIdOrArn', ], 'DeletionProtectionEnabled' => [ 'shape' => 'Boolean', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'RequestPhoneNumberResult' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberArn' => [ 'shape' => 'String', ], 'PhoneNumberId' => [ 'shape' => 'String', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Status' => [ 'shape' => 'NumberStatus', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'NumberCapabilities' => [ 'shape' => 'NumberCapabilityList', ], 'NumberType' => [ 'shape' => 'RequestableNumberType', ], 'MonthlyLeasingPrice' => [ 'shape' => 'String', ], 'TwoWayEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'TwoWayChannelArn' => [ 'shape' => 'TwoWayChannelArn', ], 'TwoWayChannelRole' => [ 'shape' => 'IamRoleArn', ], 'SelfManagedOptOutsEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'PoolId' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'RequestSenderIdRequest' => [ 'type' => 'structure', 'required' => [ 'SenderId', 'IsoCountryCode', ], 'members' => [ 'SenderId' => [ 'shape' => 'SenderId', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'MessageTypes' => [ 'shape' => 'MessageTypeList', ], 'DeletionProtectionEnabled' => [ 'shape' => 'Boolean', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'RequestSenderIdResult' => [ 'type' => 'structure', 'required' => [ 'SenderIdArn', 'SenderId', 'IsoCountryCode', 'MessageTypes', 'MonthlyLeasingPrice', 'DeletionProtectionEnabled', 'Registered', ], 'members' => [ 'SenderIdArn' => [ 'shape' => 'String', ], 'SenderId' => [ 'shape' => 'SenderId', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'MessageTypes' => [ 'shape' => 'MessageTypeList', ], 'MonthlyLeasingPrice' => [ 'shape' => 'String', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'Registered' => [ 'shape' => 'PrimitiveBoolean', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'RequestableNumberType' => [ 'type' => 'string', 'enum' => [ 'LONG_CODE', 'TOLL_FREE', 'TEN_DLC', 'SIMULATOR', ], ], 'ResourceIdOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'ResourceId' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'account', 'phone-number', 'sender-id', 'pool', 'configuration-set', 'opt-out-list', 'event-destination', 'keyword', 'opted-out-number', 'registration', 'registration-attachment', 'verified-destination-number', 'protect-configuration', ], ], 'SectionPath' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[A-Za-z0-9_]+', ], 'SectionPathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SectionPath', ], 'max' => 5, 'min' => 0, ], 'SelectChoice' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'SelectChoiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SelectChoice', ], 'max' => 100, 'min' => 0, ], 'SelectOptionDescription' => [ 'type' => 'structure', 'required' => [ 'Option', ], 'members' => [ 'Option' => [ 'shape' => 'String', ], 'Title' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], ], ], 'SelectOptionDescriptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SelectOptionDescription', ], ], 'SelectValidation' => [ 'type' => 'structure', 'required' => [ 'MinChoices', 'MaxChoices', 'Options', ], 'members' => [ 'MinChoices' => [ 'shape' => 'Integer', ], 'MaxChoices' => [ 'shape' => 'Integer', ], 'Options' => [ 'shape' => 'StringList', ], ], ], 'SendDestinationNumberVerificationCodeRequest' => [ 'type' => 'structure', 'required' => [ 'VerifiedDestinationNumberId', 'VerificationChannel', ], 'members' => [ 'VerifiedDestinationNumberId' => [ 'shape' => 'VerifiedDestinationNumberIdOrArn', ], 'VerificationChannel' => [ 'shape' => 'VerificationChannel', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', ], 'OriginationIdentity' => [ 'shape' => 'VerificationMessageOriginationIdentity', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], 'Context' => [ 'shape' => 'ContextMap', ], 'DestinationCountryParameters' => [ 'shape' => 'DestinationCountryParameters', ], ], ], 'SendDestinationNumberVerificationCodeResult' => [ 'type' => 'structure', 'required' => [ 'MessageId', ], 'members' => [ 'MessageId' => [ 'shape' => 'String', ], ], ], 'SendMediaMessageRequest' => [ 'type' => 'structure', 'required' => [ 'DestinationPhoneNumber', 'OriginationIdentity', ], 'members' => [ 'DestinationPhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'OriginationIdentity' => [ 'shape' => 'MediaMessageOriginationIdentity', ], 'MessageBody' => [ 'shape' => 'TextMessageBody', ], 'MediaUrls' => [ 'shape' => 'MediaUrlList', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], 'MaxPrice' => [ 'shape' => 'MaxPrice', ], 'TimeToLive' => [ 'shape' => 'TimeToLive', ], 'Context' => [ 'shape' => 'ContextMap', ], 'DryRun' => [ 'shape' => 'PrimitiveBoolean', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], ], ], 'SendMediaMessageResult' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'String', ], ], ], 'SendTextMessageRequest' => [ 'type' => 'structure', 'required' => [ 'DestinationPhoneNumber', ], 'members' => [ 'DestinationPhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'OriginationIdentity' => [ 'shape' => 'TextMessageOriginationIdentity', ], 'MessageBody' => [ 'shape' => 'TextMessageBody', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'Keyword' => [ 'shape' => 'Keyword', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], 'MaxPrice' => [ 'shape' => 'MaxPrice', ], 'TimeToLive' => [ 'shape' => 'TimeToLive', ], 'Context' => [ 'shape' => 'ContextMap', ], 'DestinationCountryParameters' => [ 'shape' => 'DestinationCountryParameters', ], 'DryRun' => [ 'shape' => 'PrimitiveBoolean', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], ], ], 'SendTextMessageResult' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'String', ], ], ], 'SendVoiceMessageRequest' => [ 'type' => 'structure', 'required' => [ 'DestinationPhoneNumber', 'OriginationIdentity', ], 'members' => [ 'DestinationPhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'OriginationIdentity' => [ 'shape' => 'VoiceMessageOriginationIdentity', ], 'MessageBody' => [ 'shape' => 'VoiceMessageBody', ], 'MessageBodyTextType' => [ 'shape' => 'VoiceMessageBodyTextType', ], 'VoiceId' => [ 'shape' => 'VoiceId', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], 'MaxPricePerMinute' => [ 'shape' => 'MaxPrice', ], 'TimeToLive' => [ 'shape' => 'TimeToLive', ], 'Context' => [ 'shape' => 'ContextMap', ], 'DryRun' => [ 'shape' => 'PrimitiveBoolean', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], ], ], 'SendVoiceMessageResult' => [ 'type' => 'structure', 'members' => [ 'MessageId' => [ 'shape' => 'String', ], ], ], 'SenderId' => [ 'type' => 'string', 'max' => 11, 'min' => 1, 'pattern' => '[A-Za-z0-9_-]+', ], 'SenderIdAndCountry' => [ 'type' => 'structure', 'required' => [ 'SenderId', 'IsoCountryCode', ], 'members' => [ 'SenderId' => [ 'shape' => 'SenderIdOrArn', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], ], ], 'SenderIdFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'SenderIdFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'SenderIdFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SenderIdFilter', ], 'max' => 20, 'min' => 0, ], 'SenderIdFilterName' => [ 'type' => 'string', 'enum' => [ 'sender-id', 'iso-country-code', 'message-type', 'deletion-protection-enabled', 'registered', ], ], 'SenderIdInformation' => [ 'type' => 'structure', 'required' => [ 'SenderIdArn', 'SenderId', 'IsoCountryCode', 'MessageTypes', 'MonthlyLeasingPrice', 'DeletionProtectionEnabled', 'Registered', ], 'members' => [ 'SenderIdArn' => [ 'shape' => 'String', ], 'SenderId' => [ 'shape' => 'SenderId', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'MessageTypes' => [ 'shape' => 'MessageTypeList', ], 'MonthlyLeasingPrice' => [ 'shape' => 'String', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'Registered' => [ 'shape' => 'PrimitiveBoolean', ], 'RegistrationId' => [ 'shape' => 'String', ], ], ], 'SenderIdInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SenderIdInformation', ], ], 'SenderIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SenderIdAndCountry', ], 'max' => 5, 'min' => 0, ], 'SenderIdOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'ServiceQuotaExceededExceptionReason', ], ], 'exception' => true, ], 'ServiceQuotaExceededExceptionReason' => [ 'type' => 'string', 'enum' => [ 'ASSOCIATIONS_PER_REGISTRATION', 'CONFIGURATION_SETS_PER_ACCOUNT', 'DAILY_DESTINATION_CALL_LIMIT', 'EVENT_DESTINATIONS_PER_CONFIGURATION_SET', 'KEYWORDS_PER_PHONE_NUMBER', 'KEYWORDS_PER_POOL', 'MONTHLY_SPEND_LIMIT_REACHED_FOR_MEDIA', 'MONTHLY_SPEND_LIMIT_REACHED_FOR_TEXT', 'MONTHLY_SPEND_LIMIT_REACHED_FOR_VOICE', 'OPT_OUT_LISTS_PER_ACCOUNT', 'ORIGINATION_IDENTITIES_PER_POOL', 'PHONE_NUMBERS_PER_ACCOUNT', 'PHONE_NUMBERS_PER_REGISTRATION', 'POOLS_PER_ACCOUNT', 'REGISTRATION_ATTACHMENTS_CREATED_PER_DAY', 'REGISTRATION_ATTACHMENTS_PER_ACCOUNT', 'REGISTRATION_VERSIONS_CREATED_PER_DAY', 'REGISTRATIONS_PER_ACCOUNT', 'SENDER_IDS_PER_ACCOUNT', 'TAGS_PER_RESOURCE', 'VERIFIED_DESTINATION_NUMBERS_PER_ACCOUNT', 'VERIFICATION_ATTEMPTS_PER_DAY', 'PROTECT_CONFIGURATIONS_PER_ACCOUNT', ], ], 'SetAccountDefaultProtectConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationId', ], 'members' => [ 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], ], ], 'SetAccountDefaultProtectConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'DefaultProtectConfigurationArn', 'DefaultProtectConfigurationId', ], 'members' => [ 'DefaultProtectConfigurationArn' => [ 'shape' => 'ProtectConfigurationArn', ], 'DefaultProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationId', ], ], ], 'SetDefaultMessageTypeRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', 'MessageType', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], 'MessageType' => [ 'shape' => 'MessageType', ], ], ], 'SetDefaultMessageTypeResult' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'MessageType' => [ 'shape' => 'MessageType', ], ], ], 'SetDefaultSenderIdRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', 'SenderId', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], 'SenderId' => [ 'shape' => 'SenderId', ], ], ], 'SetDefaultSenderIdResult' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'SenderId' => [ 'shape' => 'SenderId', ], ], ], 'SetMediaMessageSpendLimitOverrideRequest' => [ 'type' => 'structure', 'required' => [ 'MonthlyLimit', ], 'members' => [ 'MonthlyLimit' => [ 'shape' => 'MonthlyLimit', ], ], ], 'SetMediaMessageSpendLimitOverrideResult' => [ 'type' => 'structure', 'members' => [ 'MonthlyLimit' => [ 'shape' => 'MonthlyLimit', ], ], ], 'SetTextMessageSpendLimitOverrideRequest' => [ 'type' => 'structure', 'required' => [ 'MonthlyLimit', ], 'members' => [ 'MonthlyLimit' => [ 'shape' => 'MonthlyLimit', ], ], ], 'SetTextMessageSpendLimitOverrideResult' => [ 'type' => 'structure', 'members' => [ 'MonthlyLimit' => [ 'shape' => 'MonthlyLimit', ], ], ], 'SetVoiceMessageSpendLimitOverrideRequest' => [ 'type' => 'structure', 'required' => [ 'MonthlyLimit', ], 'members' => [ 'MonthlyLimit' => [ 'shape' => 'MonthlyLimit', ], ], ], 'SetVoiceMessageSpendLimitOverrideResult' => [ 'type' => 'structure', 'members' => [ 'MonthlyLimit' => [ 'shape' => 'MonthlyLimit', ], ], ], 'SnsDestination' => [ 'type' => 'structure', 'required' => [ 'TopicArn', ], 'members' => [ 'TopicArn' => [ 'shape' => 'SnsTopicArn', ], ], ], 'SnsTopicArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:\\S+', ], 'SpendLimit' => [ 'type' => 'structure', 'required' => [ 'Name', 'EnforcedLimit', 'MaxLimit', 'Overridden', ], 'members' => [ 'Name' => [ 'shape' => 'SpendLimitName', ], 'EnforcedLimit' => [ 'shape' => 'PrimitiveLong', ], 'MaxLimit' => [ 'shape' => 'PrimitiveLong', ], 'Overridden' => [ 'shape' => 'PrimitiveBoolean', ], ], ], 'SpendLimitList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SpendLimit', ], ], 'SpendLimitName' => [ 'type' => 'string', 'enum' => [ 'TEXT_MESSAGE_MONTHLY_SPEND_LIMIT', 'VOICE_MESSAGE_MONTHLY_SPEND_LIMIT', 'MEDIA_MESSAGE_MONTHLY_SPEND_LIMIT', ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'StringMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'SubmitRegistrationVersionRequest' => [ 'type' => 'structure', 'required' => [ 'RegistrationId', ], 'members' => [ 'RegistrationId' => [ 'shape' => 'RegistrationIdOrArn', ], ], ], 'SubmitRegistrationVersionResult' => [ 'type' => 'structure', 'required' => [ 'RegistrationArn', 'RegistrationId', 'VersionNumber', 'RegistrationVersionStatus', 'RegistrationVersionStatusHistory', ], 'members' => [ 'RegistrationArn' => [ 'shape' => 'String', ], 'RegistrationId' => [ 'shape' => 'String', ], 'VersionNumber' => [ 'shape' => 'RegistrationVersionNumber', ], 'RegistrationVersionStatus' => [ 'shape' => 'RegistrationVersionStatus', ], 'RegistrationVersionStatusHistory' => [ 'shape' => 'RegistrationVersionStatusHistory', ], ], ], 'SupportedAssociation' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'AssociationBehavior', 'DisassociationBehavior', ], 'members' => [ 'ResourceType' => [ 'shape' => 'String', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'AssociationBehavior' => [ 'shape' => 'RegistrationAssociationBehavior', ], 'DisassociationBehavior' => [ 'shape' => 'RegistrationDisassociationBehavior', ], ], ], 'SupportedAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedAssociation', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.+', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'NonEmptyTagList', ], ], ], 'TagResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*', ], 'TextMessageBody' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '(?!\\s*$)[\\s\\S]+', ], 'TextMessageOriginationIdentity' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/\\+-]+', ], 'TextValidation' => [ 'type' => 'structure', 'required' => [ 'MinLength', 'MaxLength', 'Pattern', ], 'members' => [ 'MinLength' => [ 'shape' => 'Integer', ], 'MaxLength' => [ 'shape' => 'Integer', ], 'Pattern' => [ 'shape' => 'String', ], ], ], 'TextValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'TimeToLive' => [ 'type' => 'integer', 'box' => true, 'max' => 259200, 'min' => 5, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TwoWayChannelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '\\S+', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateEventDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'ConfigurationSetName', 'EventDestinationName', ], 'members' => [ 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetNameOrArn', ], 'EventDestinationName' => [ 'shape' => 'EventDestinationName', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'MatchingEventTypes' => [ 'shape' => 'EventTypeList', ], 'CloudWatchLogsDestination' => [ 'shape' => 'CloudWatchLogsDestination', ], 'KinesisFirehoseDestination' => [ 'shape' => 'KinesisFirehoseDestination', ], 'SnsDestination' => [ 'shape' => 'SnsDestination', ], ], ], 'UpdateEventDestinationResult' => [ 'type' => 'structure', 'members' => [ 'ConfigurationSetArn' => [ 'shape' => 'String', ], 'ConfigurationSetName' => [ 'shape' => 'ConfigurationSetName', ], 'EventDestination' => [ 'shape' => 'EventDestination', ], ], ], 'UpdatePhoneNumberRequest' => [ 'type' => 'structure', 'required' => [ 'PhoneNumberId', ], 'members' => [ 'PhoneNumberId' => [ 'shape' => 'PhoneNumberIdOrArn', ], 'TwoWayEnabled' => [ 'shape' => 'Boolean', ], 'TwoWayChannelArn' => [ 'shape' => 'TwoWayChannelArn', ], 'TwoWayChannelRole' => [ 'shape' => 'IamRoleArn', ], 'SelfManagedOptOutsEnabled' => [ 'shape' => 'Boolean', ], 'OptOutListName' => [ 'shape' => 'OptOutListNameOrArn', ], 'DeletionProtectionEnabled' => [ 'shape' => 'Boolean', ], ], ], 'UpdatePhoneNumberResult' => [ 'type' => 'structure', 'members' => [ 'PhoneNumberArn' => [ 'shape' => 'String', ], 'PhoneNumberId' => [ 'shape' => 'String', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Status' => [ 'shape' => 'NumberStatus', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'NumberCapabilities' => [ 'shape' => 'NumberCapabilityList', ], 'NumberType' => [ 'shape' => 'NumberType', ], 'MonthlyLeasingPrice' => [ 'shape' => 'String', ], 'TwoWayEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'TwoWayChannelArn' => [ 'shape' => 'TwoWayChannelArn', ], 'TwoWayChannelRole' => [ 'shape' => 'IamRoleArn', ], 'SelfManagedOptOutsEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'RegistrationId' => [ 'shape' => 'String', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'UpdatePoolRequest' => [ 'type' => 'structure', 'required' => [ 'PoolId', ], 'members' => [ 'PoolId' => [ 'shape' => 'PoolIdOrArn', ], 'TwoWayEnabled' => [ 'shape' => 'Boolean', ], 'TwoWayChannelArn' => [ 'shape' => 'TwoWayChannelArn', ], 'TwoWayChannelRole' => [ 'shape' => 'IamRoleArn', ], 'SelfManagedOptOutsEnabled' => [ 'shape' => 'Boolean', ], 'OptOutListName' => [ 'shape' => 'OptOutListNameOrArn', ], 'SharedRoutesEnabled' => [ 'shape' => 'Boolean', ], 'DeletionProtectionEnabled' => [ 'shape' => 'Boolean', ], ], ], 'UpdatePoolResult' => [ 'type' => 'structure', 'members' => [ 'PoolArn' => [ 'shape' => 'String', ], 'PoolId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'PoolStatus', ], 'MessageType' => [ 'shape' => 'MessageType', ], 'TwoWayEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'TwoWayChannelArn' => [ 'shape' => 'TwoWayChannelArn', ], 'TwoWayChannelRole' => [ 'shape' => 'IamRoleArn', ], 'SelfManagedOptOutsEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'OptOutListName' => [ 'shape' => 'OptOutListName', ], 'SharedRoutesEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateProtectConfigurationCountryRuleSetRequest' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationId', 'NumberCapability', 'CountryRuleSetUpdates', ], 'members' => [ 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], 'NumberCapability' => [ 'shape' => 'NumberCapability', ], 'CountryRuleSetUpdates' => [ 'shape' => 'ProtectConfigurationCountryRuleSet', ], ], ], 'UpdateProtectConfigurationCountryRuleSetResult' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationArn', 'ProtectConfigurationId', 'NumberCapability', 'CountryRuleSet', ], 'members' => [ 'ProtectConfigurationArn' => [ 'shape' => 'ProtectConfigurationArn', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationId', ], 'NumberCapability' => [ 'shape' => 'NumberCapability', ], 'CountryRuleSet' => [ 'shape' => 'ProtectConfigurationCountryRuleSet', ], ], ], 'UpdateProtectConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationId', ], 'members' => [ 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationIdOrArn', ], 'DeletionProtectionEnabled' => [ 'shape' => 'Boolean', ], ], ], 'UpdateProtectConfigurationResult' => [ 'type' => 'structure', 'required' => [ 'ProtectConfigurationArn', 'ProtectConfigurationId', 'CreatedTimestamp', 'AccountDefault', 'DeletionProtectionEnabled', ], 'members' => [ 'ProtectConfigurationArn' => [ 'shape' => 'ProtectConfigurationArn', ], 'ProtectConfigurationId' => [ 'shape' => 'ProtectConfigurationId', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], 'AccountDefault' => [ 'shape' => 'PrimitiveBoolean', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], ], ], 'UpdateSenderIdRequest' => [ 'type' => 'structure', 'required' => [ 'SenderId', 'IsoCountryCode', ], 'members' => [ 'SenderId' => [ 'shape' => 'SenderIdOrArn', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'DeletionProtectionEnabled' => [ 'shape' => 'Boolean', ], ], ], 'UpdateSenderIdResult' => [ 'type' => 'structure', 'required' => [ 'SenderIdArn', 'SenderId', 'IsoCountryCode', 'MessageTypes', 'MonthlyLeasingPrice', 'DeletionProtectionEnabled', 'Registered', ], 'members' => [ 'SenderIdArn' => [ 'shape' => 'String', ], 'SenderId' => [ 'shape' => 'SenderId', ], 'IsoCountryCode' => [ 'shape' => 'IsoCountryCode', ], 'MessageTypes' => [ 'shape' => 'MessageTypeList', ], 'MonthlyLeasingPrice' => [ 'shape' => 'String', ], 'DeletionProtectionEnabled' => [ 'shape' => 'PrimitiveBoolean', ], 'Registered' => [ 'shape' => 'PrimitiveBoolean', ], 'RegistrationId' => [ 'shape' => 'String', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], 'Fields' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Message', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'CANNOT_ADD_OPTED_OUT_NUMBER', 'CANNOT_PARSE', 'COUNTRY_CODE_MISMATCH', 'DESTINATION_COUNTRY_BLOCKED', 'FIELD_VALIDATION_FAILED', 'ATTACHMENT_TYPE_NOT_SUPPORTED', 'INVALID_ARN', 'INVALID_FILTER_VALUES', 'INVALID_IDENTITY_FOR_DESTINATION_COUNTRY', 'INVALID_NEXT_TOKEN', 'INVALID_PARAMETER', 'INVALID_REQUEST', 'INVALID_REGISTRATION_ASSOCIATION', 'MAXIMUM_SIZE_EXCEEDED', 'MEDIA_TYPE_NOT_SUPPORTED', 'MISSING_PARAMETER', 'PARAMETERS_CANNOT_BE_USED_TOGETHER', 'PHONE_NUMBER_CANNOT_BE_OPTED_IN', 'PHONE_NUMBER_CANNOT_BE_RELEASED', 'PRICE_OVER_THRESHOLD', 'RESOURCE_NOT_ACCESSIBLE', 'REQUESTED_SPEND_LIMIT_HIGHER_THAN_SERVICE_LIMIT', 'SENDER_ID_NOT_REGISTERED', 'SENDER_ID_NOT_SUPPORTED', 'SENDER_ID_REQUIRES_REGISTRATION', 'TWO_WAY_TOPIC_NOT_PRESENT', 'TWO_WAY_NOT_ENABLED', 'TWO_WAY_NOT_SUPPORTED_IN_COUNTRY', 'TWO_WAY_NOT_SUPPORTED_IN_REGION', 'TWO_WAY_CHANNEL_NOT_PRESENT', 'UNKNOWN_REGISTRATION_FIELD', 'UNKNOWN_REGISTRATION_SECTION', 'UNKNOWN_REGISTRATION_TYPE', 'UNKNOWN_REGISTRATION_VERSION', 'UNKNOWN_OPERATION', 'REGISTRATION_FIELD_CANNOT_BE_DELETED', 'VERIFICATION_CODE_MISMATCH', 'VOICE_CAPABILITY_NOT_AVAILABLE', 'OTHER', ], ], 'VerificationChannel' => [ 'type' => 'string', 'enum' => [ 'TEXT', 'VOICE', ], ], 'VerificationCode' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '[A-Za-z0-9]+', ], 'VerificationMessageOriginationIdentity' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/\\+-]+', ], 'VerificationStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'VERIFIED', ], ], 'VerifiedDestinationNumberFilter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'VerifiedDestinationNumberFilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'VerifiedDestinationNumberFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VerifiedDestinationNumberFilter', ], 'max' => 20, 'min' => 0, ], 'VerifiedDestinationNumberFilterName' => [ 'type' => 'string', 'enum' => [ 'status', ], ], 'VerifiedDestinationNumberIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VerifiedDestinationNumberIdOrArn', ], 'max' => 5, 'min' => 0, ], 'VerifiedDestinationNumberIdOrArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/-]+', ], 'VerifiedDestinationNumberInformation' => [ 'type' => 'structure', 'required' => [ 'VerifiedDestinationNumberArn', 'VerifiedDestinationNumberId', 'DestinationPhoneNumber', 'Status', 'CreatedTimestamp', ], 'members' => [ 'VerifiedDestinationNumberArn' => [ 'shape' => 'String', ], 'VerifiedDestinationNumberId' => [ 'shape' => 'String', ], 'DestinationPhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Status' => [ 'shape' => 'VerificationStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'VerifiedDestinationNumberInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VerifiedDestinationNumberInformation', ], ], 'VerifyDestinationNumberRequest' => [ 'type' => 'structure', 'required' => [ 'VerifiedDestinationNumberId', 'VerificationCode', ], 'members' => [ 'VerifiedDestinationNumberId' => [ 'shape' => 'VerifiedDestinationNumberIdOrArn', ], 'VerificationCode' => [ 'shape' => 'VerificationCode', ], ], ], 'VerifyDestinationNumberResult' => [ 'type' => 'structure', 'required' => [ 'VerifiedDestinationNumberArn', 'VerifiedDestinationNumberId', 'DestinationPhoneNumber', 'Status', 'CreatedTimestamp', ], 'members' => [ 'VerifiedDestinationNumberArn' => [ 'shape' => 'String', ], 'VerifiedDestinationNumberId' => [ 'shape' => 'String', ], 'DestinationPhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Status' => [ 'shape' => 'VerificationStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'VoiceId' => [ 'type' => 'string', 'enum' => [ 'AMY', 'ASTRID', 'BIANCA', 'BRIAN', 'CAMILA', 'CARLA', 'CARMEN', 'CELINE', 'CHANTAL', 'CONCHITA', 'CRISTIANO', 'DORA', 'EMMA', 'ENRIQUE', 'EWA', 'FILIZ', 'GERAINT', 'GIORGIO', 'GWYNETH', 'HANS', 'INES', 'IVY', 'JACEK', 'JAN', 'JOANNA', 'JOEY', 'JUSTIN', 'KARL', 'KENDRA', 'KIMBERLY', 'LEA', 'LIV', 'LOTTE', 'LUCIA', 'LUPE', 'MADS', 'MAJA', 'MARLENE', 'MATHIEU', 'MATTHEW', 'MAXIM', 'MIA', 'MIGUEL', 'MIZUKI', 'NAJA', 'NICOLE', 'PENELOPE', 'RAVEENA', 'RICARDO', 'RUBEN', 'RUSSELL', 'SALLI', 'SEOYEON', 'TAKUMI', 'TATYANA', 'VICKI', 'VITORIA', 'ZEINA', 'ZHIYU', ], ], 'VoiceMessageBody' => [ 'type' => 'string', 'max' => 6000, 'min' => 1, 'pattern' => '(?!\\s*$)[\\s\\S]+', ], 'VoiceMessageBodyTextType' => [ 'type' => 'string', 'enum' => [ 'TEXT', 'SSML', ], ], 'VoiceMessageOriginationIdentity' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[A-Za-z0-9_:/\\+-]+', ], ],];
