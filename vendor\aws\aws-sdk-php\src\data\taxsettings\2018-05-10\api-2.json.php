<?php
// This file was auto-generated from sdk-root/src/data/taxsettings/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'endpointPrefix' => 'tax', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Tax Settings', 'serviceId' => 'TaxSettings', 'signatureVersion' => 'v4', 'signingName' => 'tax', 'uid' => 'taxsettings-2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'BatchDeleteTaxRegistration' => [ 'name' => 'BatchDeleteTaxRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/BatchDeleteTaxRegistration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteTaxRegistrationRequest', ], 'output' => [ 'shape' => 'BatchDeleteTaxRegistrationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'BatchPutTaxRegistration' => [ 'name' => 'BatchPutTaxRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/BatchPutTaxRegistration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchPutTaxRegistrationRequest', ], 'output' => [ 'shape' => 'BatchPutTaxRegistrationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteTaxRegistration' => [ 'name' => 'DeleteTaxRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteTaxRegistration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTaxRegistrationRequest', ], 'output' => [ 'shape' => 'DeleteTaxRegistrationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTaxRegistration' => [ 'name' => 'GetTaxRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetTaxRegistration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTaxRegistrationRequest', ], 'output' => [ 'shape' => 'GetTaxRegistrationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTaxRegistrationDocument' => [ 'name' => 'GetTaxRegistrationDocument', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetTaxRegistrationDocument', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTaxRegistrationDocumentRequest', ], 'output' => [ 'shape' => 'GetTaxRegistrationDocumentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTaxRegistrations' => [ 'name' => 'ListTaxRegistrations', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListTaxRegistrations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTaxRegistrationsRequest', ], 'output' => [ 'shape' => 'ListTaxRegistrationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutTaxRegistration' => [ 'name' => 'PutTaxRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/PutTaxRegistration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutTaxRegistrationRequest', ], 'output' => [ 'shape' => 'PutTaxRegistrationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccountDetails' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'accountMetaData' => [ 'shape' => 'AccountMetaData', ], 'taxInheritanceDetails' => [ 'shape' => 'TaxInheritanceDetails', ], 'taxRegistration' => [ 'shape' => 'TaxRegistrationWithJurisdiction', ], ], 'sensitive' => true, ], 'AccountDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountDetails', ], ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '^\\d+$', ], 'AccountIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], 'max' => 5, 'min' => 1, ], 'AccountMetaData' => [ 'type' => 'structure', 'members' => [ 'accountName' => [ 'shape' => 'AccountName', ], 'address' => [ 'shape' => 'Address', ], 'addressRoleMap' => [ 'shape' => 'AddressRoleMap', ], 'addressType' => [ 'shape' => 'AddressRoleType', ], 'seller' => [ 'shape' => 'Seller', ], ], 'sensitive' => true, ], 'AccountName' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]*$', ], 'AdditionalInfoRequest' => [ 'type' => 'structure', 'members' => [ 'canadaAdditionalInfo' => [ 'shape' => 'CanadaAdditionalInfo', ], 'estoniaAdditionalInfo' => [ 'shape' => 'EstoniaAdditionalInfo', ], 'georgiaAdditionalInfo' => [ 'shape' => 'GeorgiaAdditionalInfo', ], 'israelAdditionalInfo' => [ 'shape' => 'IsraelAdditionalInfo', ], 'italyAdditionalInfo' => [ 'shape' => 'ItalyAdditionalInfo', ], 'kenyaAdditionalInfo' => [ 'shape' => 'KenyaAdditionalInfo', ], 'malaysiaAdditionalInfo' => [ 'shape' => 'MalaysiaAdditionalInfo', ], 'polandAdditionalInfo' => [ 'shape' => 'PolandAdditionalInfo', ], 'romaniaAdditionalInfo' => [ 'shape' => 'RomaniaAdditionalInfo', ], 'saudiArabiaAdditionalInfo' => [ 'shape' => 'SaudiArabiaAdditionalInfo', ], 'southKoreaAdditionalInfo' => [ 'shape' => 'SouthKoreaAdditionalInfo', ], 'spainAdditionalInfo' => [ 'shape' => 'SpainAdditionalInfo', ], 'turkeyAdditionalInfo' => [ 'shape' => 'TurkeyAdditionalInfo', ], 'ukraineAdditionalInfo' => [ 'shape' => 'UkraineAdditionalInfo', ], ], ], 'AdditionalInfoResponse' => [ 'type' => 'structure', 'members' => [ 'brazilAdditionalInfo' => [ 'shape' => 'BrazilAdditionalInfo', ], 'canadaAdditionalInfo' => [ 'shape' => 'CanadaAdditionalInfo', ], 'estoniaAdditionalInfo' => [ 'shape' => 'EstoniaAdditionalInfo', ], 'georgiaAdditionalInfo' => [ 'shape' => 'GeorgiaAdditionalInfo', ], 'indiaAdditionalInfo' => [ 'shape' => 'IndiaAdditionalInfo', ], 'israelAdditionalInfo' => [ 'shape' => 'IsraelAdditionalInfo', ], 'italyAdditionalInfo' => [ 'shape' => 'ItalyAdditionalInfo', ], 'kenyaAdditionalInfo' => [ 'shape' => 'KenyaAdditionalInfo', ], 'malaysiaAdditionalInfo' => [ 'shape' => 'MalaysiaAdditionalInfo', ], 'polandAdditionalInfo' => [ 'shape' => 'PolandAdditionalInfo', ], 'romaniaAdditionalInfo' => [ 'shape' => 'RomaniaAdditionalInfo', ], 'saudiArabiaAdditionalInfo' => [ 'shape' => 'SaudiArabiaAdditionalInfo', ], 'southKoreaAdditionalInfo' => [ 'shape' => 'SouthKoreaAdditionalInfo', ], 'spainAdditionalInfo' => [ 'shape' => 'SpainAdditionalInfo', ], 'turkeyAdditionalInfo' => [ 'shape' => 'TurkeyAdditionalInfo', ], 'ukraineAdditionalInfo' => [ 'shape' => 'UkraineAdditionalInfo', ], ], ], 'Address' => [ 'type' => 'structure', 'required' => [ 'addressLine1', 'city', 'countryCode', 'postalCode', ], 'members' => [ 'addressLine1' => [ 'shape' => 'AddressLine1', ], 'addressLine2' => [ 'shape' => 'AddressLine2', ], 'addressLine3' => [ 'shape' => 'AddressLine3', ], 'city' => [ 'shape' => 'City', ], 'countryCode' => [ 'shape' => 'CountryCode', ], 'districtOrCounty' => [ 'shape' => 'District', ], 'postalCode' => [ 'shape' => 'PostalCode', ], 'stateOrRegion' => [ 'shape' => 'State', ], ], ], 'AddressLine1' => [ 'type' => 'string', 'max' => 180, 'min' => 1, 'pattern' => '^(?!\\s*$)[\\s\\S]+$', ], 'AddressLine2' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'pattern' => '^(?!\\s*$)[\\s\\S]+$', ], 'AddressLine3' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'pattern' => '^(?!\\s*$)[\\s\\S]+$', ], 'AddressRoleMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'AddressRoleType', ], 'value' => [ 'shape' => 'Jurisdiction', ], ], 'AddressRoleType' => [ 'type' => 'string', 'enum' => [ 'TaxAddress', 'BillingAddress', 'ContactAddress', ], ], 'BatchDeleteTaxRegistrationError' => [ 'type' => 'structure', 'required' => [ 'accountId', 'message', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'code' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchDeleteTaxRegistrationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteTaxRegistrationError', ], ], 'BatchDeleteTaxRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'accountIds', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], ], ], 'BatchDeleteTaxRegistrationResponse' => [ 'type' => 'structure', 'required' => [ 'errors', ], 'members' => [ 'errors' => [ 'shape' => 'BatchDeleteTaxRegistrationErrors', ], ], ], 'BatchPutTaxRegistrationError' => [ 'type' => 'structure', 'required' => [ 'accountId', 'message', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'code' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'BatchPutTaxRegistrationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutTaxRegistrationError', ], ], 'BatchPutTaxRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'accountIds', 'taxRegistrationEntry', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], 'taxRegistrationEntry' => [ 'shape' => 'TaxRegistrationEntry', ], ], ], 'BatchPutTaxRegistrationResponse' => [ 'type' => 'structure', 'required' => [ 'errors', ], 'members' => [ 'errors' => [ 'shape' => 'BatchPutTaxRegistrationErrors', ], 'status' => [ 'shape' => 'TaxRegistrationStatus', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BrazilAdditionalInfo' => [ 'type' => 'structure', 'members' => [ 'ccmCode' => [ 'shape' => 'CcmCode', ], 'legalNatureCode' => [ 'shape' => 'LegalNatureCode', ], ], ], 'BusinessRepresentativeName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9\\u3130-\\u318F\\uAC00-\\uD7AF,.( )-\\\\s]*$', ], 'CanadaAdditionalInfo' => [ 'type' => 'structure', 'members' => [ 'canadaQuebecSalesTaxNumber' => [ 'shape' => 'CanadaQuebecSalesTaxNumberString', ], 'canadaRetailSalesTaxNumber' => [ 'shape' => 'CanadaRetailSalesTaxNumberString', ], 'isResellerAccount' => [ 'shape' => 'Boolean', ], 'provincialSalesTaxId' => [ 'shape' => 'CanadaProvincialSalesTaxIdString', ], ], ], 'CanadaProvincialSalesTaxIdString' => [ 'type' => 'string', 'max' => 16, 'min' => 7, 'pattern' => '^([0-9A-Z/-]+)$', ], 'CanadaQuebecSalesTaxNumberString' => [ 'type' => 'string', 'pattern' => '^([0-9]{10})(TQ[0-9]{4})?$', ], 'CanadaRetailSalesTaxNumberString' => [ 'type' => 'string', 'pattern' => '^([0-9]{6}-[0-9]{1})$', ], 'CcmCode' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^\\d+$', ], 'CertifiedEmailId' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,20}$', ], 'CigNumber' => [ 'type' => 'string', 'pattern' => '^([0-9A-Z]{1,15})$', ], 'City' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^(?!\\s*$)[\\s\\S]+$', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'message', ], 'members' => [ 'errorCode' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CountryCode' => [ 'type' => 'string', 'max' => 2, 'min' => 2, 'pattern' => '^[a-zA-Z]+$', ], 'CupNumber' => [ 'type' => 'string', 'pattern' => '^([0-9A-Z]{1,15})$', ], 'DateOfBirth' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^(\\d{4}-(0[0-9]|1[0-2])-([0-2][0-9]|3[0-1]))$', ], 'DeleteTaxRegistrationRequest' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'DeleteTaxRegistrationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DestinationFilePath' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]*$', ], 'DestinationS3Location' => [ 'type' => 'structure', 'required' => [ 'bucket', ], 'members' => [ 'bucket' => [ 'shape' => 'S3BucketName', ], 'prefix' => [ 'shape' => 'S3Prefix', ], ], ], 'District' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^(?!\\s*$)[\\s\\S]+$', ], 'ErrorCode' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '^[\\s\\S]*$', ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[\\s\\S]*$', 'sensitive' => true, ], 'EstoniaAdditionalInfo' => [ 'type' => 'structure', 'required' => [ 'registryCommercialCode', ], 'members' => [ 'registryCommercialCode' => [ 'shape' => 'RegistryCommercialCode', ], ], ], 'FieldName' => [ 'type' => 'string', 'pattern' => '^(?!\\s*$)[\\s\\S]+$', ], 'GeorgiaAdditionalInfo' => [ 'type' => 'structure', 'required' => [ 'personType', ], 'members' => [ 'personType' => [ 'shape' => 'PersonType', ], ], ], 'GetTaxRegistrationDocumentRequest' => [ 'type' => 'structure', 'required' => [ 'destinationS3Location', 'taxDocumentMetadata', ], 'members' => [ 'destinationS3Location' => [ 'shape' => 'DestinationS3Location', ], 'taxDocumentMetadata' => [ 'shape' => 'TaxDocumentMetadata', ], ], ], 'GetTaxRegistrationDocumentResponse' => [ 'type' => 'structure', 'members' => [ 'destinationFilePath' => [ 'shape' => 'DestinationFilePath', ], ], ], 'GetTaxRegistrationRequest' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'GetTaxRegistrationResponse' => [ 'type' => 'structure', 'members' => [ 'taxRegistration' => [ 'shape' => 'TaxRegistration', ], ], ], 'IndiaAdditionalInfo' => [ 'type' => 'structure', 'members' => [ 'pan' => [ 'shape' => 'Pan', ], ], ], 'IndividualRegistrationNumber' => [ 'type' => 'string', 'pattern' => '^([0-9]{10})$', ], 'Industries' => [ 'type' => 'string', 'enum' => [ 'CirculatingOrg', 'ProfessionalOrg', 'Banks', 'Insurance', 'PensionAndBenefitFunds', 'DevelopmentAgencies', ], ], 'InheritanceObtainedReason' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]*$', ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'message', ], 'members' => [ 'errorCode' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'IsraelAdditionalInfo' => [ 'type' => 'structure', 'required' => [ 'customerType', 'dealerType', ], 'members' => [ 'customerType' => [ 'shape' => 'IsraelCustomerType', ], 'dealerType' => [ 'shape' => 'IsraelDealerType', ], ], ], 'IsraelCustomerType' => [ 'type' => 'string', 'enum' => [ 'Business', 'Individual', ], ], 'IsraelDealerType' => [ 'type' => 'string', 'enum' => [ 'Authorized', 'Non-authorized', ], ], 'ItalyAdditionalInfo' => [ 'type' => 'structure', 'members' => [ 'cigNumber' => [ 'shape' => 'CigNumber', ], 'cupNumber' => [ 'shape' => 'CupNumber', ], 'sdiAccountId' => [ 'shape' => 'SdiAccountId', ], 'taxCode' => [ 'shape' => 'TaxCode', ], ], ], 'ItemOfBusiness' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[0-9\\u3130-\\u318F\\uAC00-\\uD7AF,.( )-\\\\s]*$', ], 'Jurisdiction' => [ 'type' => 'structure', 'required' => [ 'countryCode', ], 'members' => [ 'countryCode' => [ 'shape' => 'CountryCode', ], 'stateOrRegion' => [ 'shape' => 'State', ], ], ], 'KenyaAdditionalInfo' => [ 'type' => 'structure', 'required' => [ 'personType', ], 'members' => [ 'personType' => [ 'shape' => 'PersonType', ], ], ], 'KepEmailId' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]*$', ], 'LegalName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^(?!\\s*$)[\\s\\S]+$', ], 'LegalNatureCode' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^\\d+$', ], 'LineOfBusiness' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[0-9\\u3130-\\u318F\\uAC00-\\uD7AF,.( )-\\\\s]*$', ], 'ListTaxRegistrationsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'PaginationTokenString', ], ], ], 'ListTaxRegistrationsResponse' => [ 'type' => 'structure', 'required' => [ 'accountDetails', ], 'members' => [ 'accountDetails' => [ 'shape' => 'AccountDetailsList', ], 'nextToken' => [ 'shape' => 'PaginationTokenString', ], ], ], 'MalaysiaAdditionalInfo' => [ 'type' => 'structure', 'required' => [ 'serviceTaxCodes', ], 'members' => [ 'serviceTaxCodes' => [ 'shape' => 'MalaysiaServiceTaxCodesList', ], ], ], 'MalaysiaServiceTaxCode' => [ 'type' => 'string', 'enum' => [ 'Consultancy', 'Digital Service And Electronic Medium', 'IT Services', 'Training Or Coaching', ], ], 'MalaysiaServiceTaxCodesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MalaysiaServiceTaxCode', ], 'max' => 4, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'PaginationTokenString' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '^[-A-Za-z0-9_+\\=\\/]+$', ], 'Pan' => [ 'type' => 'string', 'pattern' => '^[A-Z]{5}[0-9]{4}[A-Z]{1}$', ], 'PersonType' => [ 'type' => 'string', 'enum' => [ 'Legal Person', 'Physical Person', 'Business', ], ], 'PolandAdditionalInfo' => [ 'type' => 'structure', 'members' => [ 'individualRegistrationNumber' => [ 'shape' => 'IndividualRegistrationNumber', ], 'isGroupVatEnabled' => [ 'shape' => 'Boolean', ], ], ], 'PostalCode' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^(?!\\s*$)[\\s\\S]+$', ], 'PutTaxRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'taxRegistrationEntry', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'taxRegistrationEntry' => [ 'shape' => 'TaxRegistrationEntry', ], ], ], 'PutTaxRegistrationResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'TaxRegistrationStatus', ], ], ], 'RegistrationId' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^(?!\\s*$)[\\s\\S]+$', ], 'RegistrationType' => [ 'type' => 'string', 'enum' => [ 'Intra-EU', 'Local', ], ], 'RegistryCommercialCode' => [ 'type' => 'string', 'max' => 8, 'min' => 8, 'pattern' => '^\\d+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'message', ], 'members' => [ 'errorCode' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RomaniaAdditionalInfo' => [ 'type' => 'structure', 'required' => [ 'taxRegistrationNumberType', ], 'members' => [ 'taxRegistrationNumberType' => [ 'shape' => 'TaxRegistrationNumberType', ], ], ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^(?=^.{3,63}$)(?!^(\\d+\\.)+\\d+$)(^(([a-z0-9]|[a-z0-9][a-z0-9\\-]*[a-z0-9])\\.)*([a-z0-9]|[a-z0-9][a-z0-9\\-]*[a-z0-9])$)$', ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^.*\\S.*$', ], 'S3Prefix' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '^.*\\S.*$', ], 'SaudiArabiaAdditionalInfo' => [ 'type' => 'structure', 'members' => [ 'taxRegistrationNumberType' => [ 'shape' => 'SaudiArabiaTaxRegistrationNumberType', ], ], ], 'SaudiArabiaTaxRegistrationNumberType' => [ 'type' => 'string', 'enum' => [ 'TaxRegistrationNumber', 'TaxIdentificationNumber', 'CommercialRegistrationNumber', ], ], 'SdiAccountId' => [ 'type' => 'string', 'pattern' => '^[0-9A-Z]{6,7}$', ], 'SecondaryTaxId' => [ 'type' => 'string', 'pattern' => '^([0-9]{10})$', ], 'Sector' => [ 'type' => 'string', 'enum' => [ 'Business', 'Individual', 'Government', ], ], 'Seller' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]*$', ], 'SourceS3Location' => [ 'type' => 'structure', 'required' => [ 'bucket', 'key', ], 'members' => [ 'bucket' => [ 'shape' => 'S3BucketName', ], 'key' => [ 'shape' => 'S3Key', ], ], ], 'SouthKoreaAdditionalInfo' => [ 'type' => 'structure', 'required' => [ 'businessRepresentativeName', 'itemOfBusiness', 'lineOfBusiness', ], 'members' => [ 'businessRepresentativeName' => [ 'shape' => 'BusinessRepresentativeName', ], 'itemOfBusiness' => [ 'shape' => 'ItemOfBusiness', ], 'lineOfBusiness' => [ 'shape' => 'LineOfBusiness', ], ], ], 'SpainAdditionalInfo' => [ 'type' => 'structure', 'required' => [ 'registrationType', ], 'members' => [ 'registrationType' => [ 'shape' => 'RegistrationType', ], ], ], 'State' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^(?!\\s*$)[\\s\\S]+$', ], 'TaxCode' => [ 'type' => 'string', 'pattern' => '^([0-9]{11}|[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z])$', ], 'TaxDocumentAccessToken' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]*$', ], 'TaxDocumentMetadata' => [ 'type' => 'structure', 'required' => [ 'taxDocumentAccessToken', 'taxDocumentName', ], 'members' => [ 'taxDocumentAccessToken' => [ 'shape' => 'TaxDocumentAccessToken', ], 'taxDocumentName' => [ 'shape' => 'TaxDocumentName', ], ], ], 'TaxDocumentMetadatas' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaxDocumentMetadata', ], 'max' => 5, 'min' => 1, ], 'TaxDocumentName' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]*$', ], 'TaxInheritanceDetails' => [ 'type' => 'structure', 'members' => [ 'inheritanceObtainedReason' => [ 'shape' => 'InheritanceObtainedReason', ], 'parentEntityId' => [ 'shape' => 'AccountId', ], ], ], 'TaxOffice' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]*$', ], 'TaxRegistration' => [ 'type' => 'structure', 'required' => [ 'legalAddress', 'legalName', 'registrationId', 'registrationType', 'status', ], 'members' => [ 'additionalTaxInformation' => [ 'shape' => 'AdditionalInfoResponse', ], 'certifiedEmailId' => [ 'shape' => 'CertifiedEmailId', ], 'legalAddress' => [ 'shape' => 'Address', ], 'legalName' => [ 'shape' => 'LegalName', ], 'registrationId' => [ 'shape' => 'RegistrationId', ], 'registrationType' => [ 'shape' => 'TaxRegistrationType', ], 'sector' => [ 'shape' => 'Sector', ], 'status' => [ 'shape' => 'TaxRegistrationStatus', ], 'taxDocumentMetadatas' => [ 'shape' => 'TaxDocumentMetadatas', ], ], 'sensitive' => true, ], 'TaxRegistrationDocument' => [ 'type' => 'structure', 'required' => [ 's3Location', ], 'members' => [ 's3Location' => [ 'shape' => 'SourceS3Location', ], ], ], 'TaxRegistrationDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaxRegistrationDocument', ], 'max' => 5, 'min' => 1, ], 'TaxRegistrationEntry' => [ 'type' => 'structure', 'required' => [ 'registrationId', 'registrationType', ], 'members' => [ 'additionalTaxInformation' => [ 'shape' => 'AdditionalInfoRequest', ], 'certifiedEmailId' => [ 'shape' => 'CertifiedEmailId', ], 'legalAddress' => [ 'shape' => 'Address', ], 'legalName' => [ 'shape' => 'LegalName', ], 'registrationId' => [ 'shape' => 'RegistrationId', ], 'registrationType' => [ 'shape' => 'TaxRegistrationType', ], 'sector' => [ 'shape' => 'Sector', ], 'verificationDetails' => [ 'shape' => 'VerificationDetails', ], ], 'sensitive' => true, ], 'TaxRegistrationNumberType' => [ 'type' => 'string', 'enum' => [ 'TaxRegistrationNumber', 'LocalRegistrationNumber', ], ], 'TaxRegistrationStatus' => [ 'type' => 'string', 'enum' => [ 'Verified', 'Pending', 'Deleted', 'Rejected', ], ], 'TaxRegistrationType' => [ 'type' => 'string', 'enum' => [ 'VAT', 'GST', 'CPF', 'CNPJ', 'SST', ], ], 'TaxRegistrationWithJurisdiction' => [ 'type' => 'structure', 'required' => [ 'jurisdiction', 'legalName', 'registrationId', 'registrationType', 'status', ], 'members' => [ 'additionalTaxInformation' => [ 'shape' => 'AdditionalInfoResponse', ], 'certifiedEmailId' => [ 'shape' => 'CertifiedEmailId', ], 'jurisdiction' => [ 'shape' => 'Jurisdiction', ], 'legalName' => [ 'shape' => 'LegalName', ], 'registrationId' => [ 'shape' => 'RegistrationId', ], 'registrationType' => [ 'shape' => 'TaxRegistrationType', ], 'sector' => [ 'shape' => 'Sector', ], 'status' => [ 'shape' => 'TaxRegistrationStatus', ], 'taxDocumentMetadatas' => [ 'shape' => 'TaxDocumentMetadatas', ], ], 'sensitive' => true, ], 'TurkeyAdditionalInfo' => [ 'type' => 'structure', 'members' => [ 'industries' => [ 'shape' => 'Industries', ], 'kepEmailId' => [ 'shape' => 'KepEmailId', ], 'secondaryTaxId' => [ 'shape' => 'SecondaryTaxId', ], 'taxOffice' => [ 'shape' => 'TaxOffice', ], ], ], 'UkraineAdditionalInfo' => [ 'type' => 'structure', 'required' => [ 'ukraineTrnType', ], 'members' => [ 'ukraineTrnType' => [ 'shape' => 'UkraineTrnType', ], ], ], 'UkraineTrnType' => [ 'type' => 'string', 'enum' => [ 'Business', 'Individual', ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'message', ], 'members' => [ 'errorCode' => [ 'shape' => 'ValidationExceptionErrorCode', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionErrorCode' => [ 'type' => 'string', 'enum' => [ 'MalformedToken', 'ExpiredToken', 'InvalidToken', 'FieldValidationFailed', 'MissingInput', ], ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'FieldName', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'VerificationDetails' => [ 'type' => 'structure', 'members' => [ 'dateOfBirth' => [ 'shape' => 'DateOfBirth', ], 'taxRegistrationDocuments' => [ 'shape' => 'TaxRegistrationDocuments', ], ], ], ],];
