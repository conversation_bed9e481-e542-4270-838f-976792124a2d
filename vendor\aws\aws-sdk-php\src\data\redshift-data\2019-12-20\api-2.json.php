<?php
// This file was auto-generated from sdk-root/src/data/redshift-data/2019-12-20/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-12-20', 'endpointPrefix' => 'redshift-data', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Redshift Data API Service', 'serviceId' => 'Redshift Data', 'signatureVersion' => 'v4', 'signingName' => 'redshift-data', 'targetPrefix' => 'RedshiftData', 'uid' => 'redshift-data-2019-12-20', ], 'operations' => [ 'BatchExecuteStatement' => [ 'name' => 'BatchExecuteStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchExecuteStatementInput', ], 'output' => [ 'shape' => 'BatchExecuteStatementOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ActiveSessionsExceededException', ], [ 'shape' => 'ActiveStatementsExceededException', ], [ 'shape' => 'BatchExecuteStatementException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CancelStatement' => [ 'name' => 'CancelStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelStatementRequest', ], 'output' => [ 'shape' => 'CancelStatementResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DatabaseConnectionException', ], ], ], 'DescribeStatement' => [ 'name' => 'DescribeStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStatementRequest', ], 'output' => [ 'shape' => 'DescribeStatementResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeTable' => [ 'name' => 'DescribeTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTableRequest', ], 'output' => [ 'shape' => 'DescribeTableResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'QueryTimeoutException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DatabaseConnectionException', ], ], ], 'ExecuteStatement' => [ 'name' => 'ExecuteStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExecuteStatementInput', ], 'output' => [ 'shape' => 'ExecuteStatementOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ActiveSessionsExceededException', ], [ 'shape' => 'ExecuteStatementException', ], [ 'shape' => 'ActiveStatementsExceededException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetStatementResult' => [ 'name' => 'GetStatementResult', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStatementResultRequest', ], 'output' => [ 'shape' => 'GetStatementResultResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDatabases' => [ 'name' => 'ListDatabases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatabasesRequest', ], 'output' => [ 'shape' => 'ListDatabasesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'QueryTimeoutException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DatabaseConnectionException', ], ], ], 'ListSchemas' => [ 'name' => 'ListSchemas', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSchemasRequest', ], 'output' => [ 'shape' => 'ListSchemasResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'QueryTimeoutException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DatabaseConnectionException', ], ], ], 'ListStatements' => [ 'name' => 'ListStatements', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStatementsRequest', ], 'output' => [ 'shape' => 'ListStatementsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTables' => [ 'name' => 'ListTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTablesRequest', ], 'output' => [ 'shape' => 'ListTablesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'QueryTimeoutException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DatabaseConnectionException', ], ], ], ], 'shapes' => [ 'ActiveSessionsExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ActiveStatementsExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'BatchExecuteStatementException' => [ 'type' => 'structure', 'required' => [ 'Message', 'StatementId', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'StatementId' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'BatchExecuteStatementInput' => [ 'type' => 'structure', 'required' => [ 'Sqls', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ClusterIdentifier' => [ 'shape' => 'ClusterIdentifierString', ], 'Database' => [ 'shape' => 'String', ], 'DbUser' => [ 'shape' => 'String', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'SessionId' => [ 'shape' => 'UUID', ], 'SessionKeepAliveSeconds' => [ 'shape' => 'SessionAliveSeconds', ], 'Sqls' => [ 'shape' => 'SqlList', ], 'StatementName' => [ 'shape' => 'StatementNameString', ], 'WithEvent' => [ 'shape' => 'Boolean', ], 'WorkgroupName' => [ 'shape' => 'WorkgroupNameString', ], ], ], 'BatchExecuteStatementOutput' => [ 'type' => 'structure', 'members' => [ 'ClusterIdentifier' => [ 'shape' => 'ClusterIdentifierString', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Database' => [ 'shape' => 'String', ], 'DbGroups' => [ 'shape' => 'DbGroupList', ], 'DbUser' => [ 'shape' => 'String', ], 'Id' => [ 'shape' => 'UUID', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'SessionId' => [ 'shape' => 'UUID', ], 'WorkgroupName' => [ 'shape' => 'WorkgroupNameString', ], ], ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoxedBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoxedDouble' => [ 'type' => 'double', 'box' => true, ], 'BoxedLong' => [ 'type' => 'long', 'box' => true, ], 'CancelStatementRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'UUID', ], ], ], 'CancelStatementResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'Boolean', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ClusterIdentifierString' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-z]([a-z0-9]|-[a-z0-9])*$', ], 'ColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnMetadata', ], ], 'ColumnMetadata' => [ 'type' => 'structure', 'members' => [ 'columnDefault' => [ 'shape' => 'String', ], 'isCaseSensitive' => [ 'shape' => 'bool', ], 'isCurrency' => [ 'shape' => 'bool', ], 'isSigned' => [ 'shape' => 'bool', ], 'label' => [ 'shape' => 'String', ], 'length' => [ 'shape' => 'Integer', ], 'name' => [ 'shape' => 'String', ], 'nullable' => [ 'shape' => 'Integer', ], 'precision' => [ 'shape' => 'Integer', ], 'scale' => [ 'shape' => 'Integer', ], 'schemaName' => [ 'shape' => 'String', ], 'tableName' => [ 'shape' => 'String', ], 'typeName' => [ 'shape' => 'String', ], ], ], 'ColumnMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnMetadata', ], ], 'DatabaseConnectionException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'DatabaseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'DbGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'DescribeStatementRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'UUID', ], ], ], 'DescribeStatementResponse' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'ClusterIdentifier' => [ 'shape' => 'String', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Database' => [ 'shape' => 'String', ], 'DbUser' => [ 'shape' => 'String', ], 'Duration' => [ 'shape' => 'Long', ], 'Error' => [ 'shape' => 'String', ], 'HasResultSet' => [ 'shape' => 'Boolean', ], 'Id' => [ 'shape' => 'UUID', ], 'QueryParameters' => [ 'shape' => 'SqlParametersList', ], 'QueryString' => [ 'shape' => 'StatementString', ], 'RedshiftPid' => [ 'shape' => 'Long', ], 'RedshiftQueryId' => [ 'shape' => 'Long', ], 'ResultRows' => [ 'shape' => 'Long', ], 'ResultSize' => [ 'shape' => 'Long', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'SessionId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'StatusString', ], 'SubStatements' => [ 'shape' => 'SubStatementList', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'WorkgroupName' => [ 'shape' => 'WorkgroupNameString', ], ], ], 'DescribeTableRequest' => [ 'type' => 'structure', 'required' => [ 'Database', ], 'members' => [ 'ClusterIdentifier' => [ 'shape' => 'ClusterIdentifierString', ], 'ConnectedDatabase' => [ 'shape' => 'String', ], 'Database' => [ 'shape' => 'String', ], 'DbUser' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'String', ], 'Schema' => [ 'shape' => 'String', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'Table' => [ 'shape' => 'String', ], 'WorkgroupName' => [ 'shape' => 'WorkgroupNameString', ], ], ], 'DescribeTableResponse' => [ 'type' => 'structure', 'members' => [ 'ColumnList' => [ 'shape' => 'ColumnList', ], 'NextToken' => [ 'shape' => 'String', ], 'TableName' => [ 'shape' => 'String', ], ], ], 'ExecuteStatementException' => [ 'type' => 'structure', 'required' => [ 'Message', 'StatementId', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'StatementId' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'ExecuteStatementInput' => [ 'type' => 'structure', 'required' => [ 'Sql', ], 'members' => [ 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ClusterIdentifier' => [ 'shape' => 'ClusterIdentifierString', ], 'Database' => [ 'shape' => 'String', ], 'DbUser' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'SqlParametersList', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'SessionId' => [ 'shape' => 'UUID', ], 'SessionKeepAliveSeconds' => [ 'shape' => 'SessionAliveSeconds', ], 'Sql' => [ 'shape' => 'StatementString', ], 'StatementName' => [ 'shape' => 'StatementNameString', ], 'WithEvent' => [ 'shape' => 'Boolean', ], 'WorkgroupName' => [ 'shape' => 'WorkgroupNameString', ], ], ], 'ExecuteStatementOutput' => [ 'type' => 'structure', 'members' => [ 'ClusterIdentifier' => [ 'shape' => 'ClusterIdentifierString', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Database' => [ 'shape' => 'String', ], 'DbGroups' => [ 'shape' => 'DbGroupList', ], 'DbUser' => [ 'shape' => 'String', ], 'Id' => [ 'shape' => 'UUID', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'SessionId' => [ 'shape' => 'UUID', ], 'WorkgroupName' => [ 'shape' => 'WorkgroupNameString', ], ], ], 'Field' => [ 'type' => 'structure', 'members' => [ 'blobValue' => [ 'shape' => 'Blob', ], 'booleanValue' => [ 'shape' => 'BoxedBoolean', ], 'doubleValue' => [ 'shape' => 'BoxedDouble', ], 'isNull' => [ 'shape' => 'BoxedBoolean', ], 'longValue' => [ 'shape' => 'BoxedLong', ], 'stringValue' => [ 'shape' => 'String', ], ], 'union' => true, ], 'FieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Field', ], ], 'GetStatementResultRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'UUID', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetStatementResultResponse' => [ 'type' => 'structure', 'required' => [ 'Records', ], 'members' => [ 'ColumnMetadata' => [ 'shape' => 'ColumnMetadataList', ], 'NextToken' => [ 'shape' => 'String', ], 'Records' => [ 'shape' => 'SqlRecords', ], 'TotalNumRows' => [ 'shape' => 'Long', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'ListDatabasesRequest' => [ 'type' => 'structure', 'required' => [ 'Database', ], 'members' => [ 'ClusterIdentifier' => [ 'shape' => 'ClusterIdentifierString', ], 'Database' => [ 'shape' => 'String', ], 'DbUser' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'String', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'WorkgroupName' => [ 'shape' => 'WorkgroupNameString', ], ], ], 'ListDatabasesResponse' => [ 'type' => 'structure', 'members' => [ 'Databases' => [ 'shape' => 'DatabaseList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListSchemasRequest' => [ 'type' => 'structure', 'required' => [ 'Database', ], 'members' => [ 'ClusterIdentifier' => [ 'shape' => 'ClusterIdentifierString', ], 'ConnectedDatabase' => [ 'shape' => 'String', ], 'Database' => [ 'shape' => 'String', ], 'DbUser' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'String', ], 'SchemaPattern' => [ 'shape' => 'String', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'WorkgroupName' => [ 'shape' => 'WorkgroupNameString', ], ], ], 'ListSchemasResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Schemas' => [ 'shape' => 'SchemaList', ], ], ], 'ListStatementsLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'ListStatementsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListStatementsLimit', ], 'NextToken' => [ 'shape' => 'String', ], 'RoleLevel' => [ 'shape' => 'Boolean', ], 'StatementName' => [ 'shape' => 'StatementNameString', ], 'Status' => [ 'shape' => 'StatusString', ], ], ], 'ListStatementsResponse' => [ 'type' => 'structure', 'required' => [ 'Statements', ], 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Statements' => [ 'shape' => 'StatementList', ], ], ], 'ListTablesRequest' => [ 'type' => 'structure', 'required' => [ 'Database', ], 'members' => [ 'ClusterIdentifier' => [ 'shape' => 'ClusterIdentifierString', ], 'ConnectedDatabase' => [ 'shape' => 'String', ], 'Database' => [ 'shape' => 'String', ], 'DbUser' => [ 'shape' => 'String', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'String', ], 'SchemaPattern' => [ 'shape' => 'String', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'TablePattern' => [ 'shape' => 'String', ], 'WorkgroupName' => [ 'shape' => 'WorkgroupNameString', ], ], ], 'ListTablesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Tables' => [ 'shape' => 'TableList', ], ], ], 'Long' => [ 'type' => 'long', ], 'PageSize' => [ 'type' => 'integer', 'max' => 1000, 'min' => 0, ], 'ParameterName' => [ 'type' => 'string', 'pattern' => '^[0-9a-zA-Z_]+$', ], 'ParameterValue' => [ 'type' => 'string', 'min' => 1, ], 'QueryTimeoutException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'SchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SecretArn' => [ 'type' => 'string', ], 'SessionAliveSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 86400, 'min' => 0, ], 'SqlList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatementString', ], 'max' => 40, 'min' => 1, ], 'SqlParameter' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'ParameterName', ], 'value' => [ 'shape' => 'ParameterValue', ], ], ], 'SqlParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SqlParameter', ], 'min' => 1, ], 'SqlRecords' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldList', ], ], 'StatementData' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Id' => [ 'shape' => 'UUID', ], 'IsBatchStatement' => [ 'shape' => 'Boolean', ], 'QueryParameters' => [ 'shape' => 'SqlParametersList', ], 'QueryString' => [ 'shape' => 'StatementString', ], 'QueryStrings' => [ 'shape' => 'StatementStringList', ], 'SecretArn' => [ 'shape' => 'SecretArn', ], 'SessionId' => [ 'shape' => 'UUID', ], 'StatementName' => [ 'shape' => 'StatementNameString', ], 'Status' => [ 'shape' => 'StatusString', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'StatementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatementData', ], ], 'StatementNameString' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'StatementStatusString' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'PICKED', 'STARTED', 'FINISHED', 'ABORTED', 'FAILED', ], ], 'StatementString' => [ 'type' => 'string', ], 'StatementStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatementString', ], ], 'StatusString' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'PICKED', 'STARTED', 'FINISHED', 'ABORTED', 'FAILED', 'ALL', ], ], 'String' => [ 'type' => 'string', ], 'SubStatementData' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'Duration' => [ 'shape' => 'Long', ], 'Error' => [ 'shape' => 'String', ], 'HasResultSet' => [ 'shape' => 'Boolean', ], 'Id' => [ 'shape' => 'UUID', ], 'QueryString' => [ 'shape' => 'StatementString', ], 'RedshiftQueryId' => [ 'shape' => 'Long', ], 'ResultRows' => [ 'shape' => 'Long', ], 'ResultSize' => [ 'shape' => 'Long', ], 'Status' => [ 'shape' => 'StatementStatusString', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'SubStatementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubStatementData', ], ], 'TableList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableMember', ], ], 'TableMember' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'schema' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UUID' => [ 'type' => 'string', 'pattern' => '^[a-z0-9]{8}(-[a-z0-9]{4}){3}-[a-z0-9]{12}(:\\d+)?$', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'WorkgroupNameString' => [ 'type' => 'string', 'max' => 128, 'min' => 3, 'pattern' => '^(([a-z0-9-]+)|(arn:(aws(-[a-z]+)*):redshift-serverless:[a-z]{2}(-gov)?-[a-z]+-\\d{1}:\\d{12}:workgroup/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}))$', ], 'bool' => [ 'type' => 'boolean', ], ],];
