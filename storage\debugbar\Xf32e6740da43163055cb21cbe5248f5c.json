{"__meta": {"id": "Xf32e6740da43163055cb21cbe5248f5c", "datetime": "2025-06-26 23:16:15", "utime": **********.919354, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.446188, "end": **********.919368, "duration": 0.47318005561828613, "duration_str": "473ms", "measures": [{"label": "Booting", "start": **********.446188, "relative_start": 0, "end": **********.866465, "relative_end": **********.866465, "duration": 0.42027711868286133, "duration_str": "420ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.866483, "relative_start": 0.42029500007629395, "end": **********.91937, "relative_end": 1.9073486328125e-06, "duration": 0.052886962890625, "duration_str": "52.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028192, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029100000000000003, "accumulated_duration_str": "2.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.896423, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.416}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.906639, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.416, "width_percent": 18.557}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.912609, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.973, "width_percent": 12.027}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IlFnTHBnWEd6RWlpM3JEWE9GVXJMQkE9PSIsInZhbHVlIjoiV2ZIR3QzT2RvM3prUkhIV1kyUFlJUT09IiwibWFjIjoiYmFlOGE1M2E3NTgyMTM2MTZlMjUyNTU0MjM1MmNmNmVjZDNhZjY1MjM3NWM3ZmJjYzc2NjUyOTQ1MzgwYjQwMiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1943495650 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1943495650\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1306819517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1306819517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1928194456 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1928194456\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1408337980 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IlFnTHBnWEd6RWlpM3JEWE9GVXJMQkE9PSIsInZhbHVlIjoiV2ZIR3QzT2RvM3prUkhIV1kyUFlJUT09IiwibWFjIjoiYmFlOGE1M2E3NTgyMTM2MTZlMjUyNTU0MjM1MmNmNmVjZDNhZjY1MjM3NWM3ZmJjYzc2NjUyOTQ1MzgwYjQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750979769862%7C8%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkwxUElMcGMweWJxTGM4Wm83Z0xGTXc9PSIsInZhbHVlIjoidHFxUysyWllUUnk0dFhuMXIvYVZqUjd2c2FJcEVjdXd5RnV5TkRPUlMxcDBUbnA1ZFU1aEk3QnBYQS8vR1dndjRYa2tlWVdtaTZmV2dxaDg3ZzhwY0ZBWUdlUEcxRHRXWklQN0tyMFJmK1ZuNzJjeHNRTkM3bE5GWXU5MjlFb2FJZG9Db2E2em5ObTBKbEcwZVVKQVRVUldXaElKY3NKQS83UTByczR4M01sSVNDdTVoSXpmNityOHUyK1NiMnVLV3JSNzFKQ2x2clBlVFlERFBzbyt0d2JSLzNHc2pXZTdEUjhwSHlFbUVwUkxTWVhoQjFOR2h1eXhZUzg3eHRkRmg3NHZQdlNlT0ZYUlJlZktQTkhXWVBhY3psOHo2NEVOSWc4MFJXUnFxdXJmc2hQOUxwb0JpWXoranMzVzBLdXQ5L0VsZjZ5WDhaRFRoMDN0MTlOOWpORzNuVmF3WHQzNThGQ2VzU1lodmpTVVI4MlE5R3FLNDJZWTA1SzYrVStQSmRibUl6eFhzMGJzU3JkOGtpaWx2NHh1YXpHa1lMRzJpaTI0RG1yMnlmTlMxMlc3eDdsRUIvRk1lT2lCSmxaRjJhQ0xtY0p4ZE0rMEVQZ2p0U0szL2NpMWE3YndPT1RPakZkS0VlQk8zdlY3eTNYdGl4Q3pDYURMRkVZV3M0MTQiLCJtYWMiOiJmYWNkOWFlNWVlN2ZlNjA0ZTdkNmRjZDcxMDRlYmI0MDRhOGVmOWZjNGQyMDExNTRhNTRkYjc3ODhiZTg1NjRhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5JS25lZ3NPbXBBTjBSTFNFdm0zMFE9PSIsInZhbHVlIjoiemdoaldaaERnWktJTUpQZUk3THJCUERqZUdia1k3K1BpZ1lCcy9VSDZjbFVRUkJ6ZnZlZFpXRGUxdUp4R3lxbEF2N0hGSmVIM28vdWRmZ1ROQlJNckZSakgzcDJDU2s0ayt1MW9ScHRaUnF6cEQvMFJ3cTV2cUl4VzUwL0NSejlPTWE1T1ZlaFBZZTE1Mzh2eE9zSG9qNm5nTEFrNXF4K1d5c1ExSGNpa0FiNHp3Ym1rUzlubHNrek1ocG1JeWdIRGt5eUc3Mkhyd3B4QllMOHFhRXEyUVlQVTY4RGtneHZxcFBJc2YxeVd4bCtMUGY0bkp5UmtlUkI3Z2pueDVHT0l2bk9iMTNzTUxwUmJRUFIzUzhxRGJ3UmRUc3ZYVGxXR1N5QnZ1NWVQRDRxNC9ncUFKbFJGb2RMcUd5cGFHZ0tLV1hvcnYxN0cxVFhUNkNpaExkZmlJS1FzdXBhN01XVHBNZGhPKzZvdUtLN1hxdEY3U1lyYkxkV081Z1ZnMFFFajZPUTdUbUhoVU9KSHpNUFJqL3UzVmVQajJtTFVERzRPRWhYd0R6ckVCakFsZ0ZPbE14NVlBOUprZU5vb0ZmWnBMK1VoZnNTYzFiV0V2VUJwSFZtNzFhQWs4ZDFrbVAwbzJOcDdveWpwS3dqT0ZFS3VzTzZGK2RYQWtIVXpYeVQiLCJtYWMiOiI4MzNmNjE0OGUzMjg4MjQyODRlYmU4NjQ5ODA5MGJkOWIxMTFiYzg5MjA3OGY0MzM5YjA3NzZkNGI1ODU4OWI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408337980\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1032179811 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1032179811\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1434380096 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:16:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im8rcHAzOVZaUG1hMGE0Q0huSCtnRlE9PSIsInZhbHVlIjoiWGk1NkppV3lsREYzc09pOUVkOCtmUHRtQ0NkT1Jkdzd2cHlURnVHU1JhUVAxZlA1OVZIQkYySDdwc1lGdDlrY1VucjBSZjlsNitzMEdGajFMYUZXS2docUJzT2xPNk1vV05jSDlaY2hxYVdVVWx5UXlSbzhXSER4Uy9PRUNXbHcyUHZrSm8xNXo5M2lNa21kRzFnNDRXL0g1UDdqaS91dDBZQnhTMGhDejJrNHRoWHliSVBCVjQ5S3MrQUJxY3pqTk1DUGEvbzlON3Nxb0RRTmlWaGZiZmJtYndqeEpBK2x0d0VGZGlNR3IvaHQ1MzV3ZTc0V1BnaHNDMWZBN3dvdHp4Tm9hWHpmY05BNlJuT3FIS29hQnMybTVwRG94R3ZOVm5oK0xhMVZ3c0JDODh2YXhQSGRkZlpWY2Q1cS9IcmlnaFFGdGdJMXArQzk1bGxvdGtCWG0wK3RaZEhMUDQ3SEtCRUsyMlBOamJQR21rZWZQQ0VOTER1ckUwbXAwbUFMUkpqNDk4Snh5amFHdEJLK0hZL0ZjelE0OUVYSVEvNHFCZmh6dzJrQTVQQ2htNHVlUTg0MDNEQjFVZ0tpL3FjZXdqQTEvaitHakM2NWFMNnQwMnViWEtlc21ZRGgvMG9RVjdqb2FVeTY2VndPZ2ZFOTUwODB6QVl2c3BZZ2lrSUEiLCJtYWMiOiJiMjkyZjYwN2EwMzczYjgyZDgzMDQ0MDBlNWM1ZWYwYWU1MjU1MTJiZjA0MzQ4MTg1NDEyNTUwMTYyY2QyMGIzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:16:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdDKzdZaXZrQjdBVFY4dnkvVWZaUXc9PSIsInZhbHVlIjoieUlrTGRNRm9qMCszRGQvZTF5NzVzVDc2RTRldDg0RGxKdUxhU1JEbmJDTkU1ZUV4SmRqelZ0d1RHeGxWODFscjhvRDZtTWQ3TFJsc3JkMXlwVDFPeW05MURoUGN6cE0xaE16RzNyVGlpamF1STJOT2lnbWpRVktLSUc5bERma0ZSS3I2RnB5WUI0SEdjeDhjdldyVmZHVWhONzBLNjZKdVMzRG4zaTI0cVVjQjJIQ2dlRWxYVytkV3gyMXA4c1R6ekU2dUxSQ2dUY1Z6SXV2M1hsb3llanJrdWdvNHE2SWJmRGN2RVdjQWJDR0laUnlVT05FNldJSUlkd1RCM3gwNkdlcXRFYXZWRXBIamFPbDh5RS9xUGxhQkdZc28wRzZCSnVIM09VTTF6WXBMcmFjVVhCZzJVTDBkdVlzSHdMNGlTTGhJRnV4NXp5Y2Y2OUJzNTFVbUU3RWxoekVUMzhDTUxkRExJOHUwYnhiN1k0Q1gvNkpVRHhHS2R3ekhVSFluZ2RQR1hqdFBaSXg1L0dHSXlidlg5bXVqWjdMbENNQUxTSmkrbFNSWkF3aGJ6N2hBSXlud2dNbDdwQkZkUXhSbnhxUkg1anByMm93VU5IWS9FQnIrVms0QjJmQ1JRR0s0Q1hqWE0zR2NVUGdQblJONDNrSnREL212RGlQRC9GY2YiLCJtYWMiOiI5NzRjYmRlZDFlNjhlMDA5Yjg2OWZmYTM0NDQ3MzE3MDIxNzNmNzcxMmZhYjRiZjdhOWYxMWJlOWU0MzRiMjgxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:16:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im8rcHAzOVZaUG1hMGE0Q0huSCtnRlE9PSIsInZhbHVlIjoiWGk1NkppV3lsREYzc09pOUVkOCtmUHRtQ0NkT1Jkdzd2cHlURnVHU1JhUVAxZlA1OVZIQkYySDdwc1lGdDlrY1VucjBSZjlsNitzMEdGajFMYUZXS2docUJzT2xPNk1vV05jSDlaY2hxYVdVVWx5UXlSbzhXSER4Uy9PRUNXbHcyUHZrSm8xNXo5M2lNa21kRzFnNDRXL0g1UDdqaS91dDBZQnhTMGhDejJrNHRoWHliSVBCVjQ5S3MrQUJxY3pqTk1DUGEvbzlON3Nxb0RRTmlWaGZiZmJtYndqeEpBK2x0d0VGZGlNR3IvaHQ1MzV3ZTc0V1BnaHNDMWZBN3dvdHp4Tm9hWHpmY05BNlJuT3FIS29hQnMybTVwRG94R3ZOVm5oK0xhMVZ3c0JDODh2YXhQSGRkZlpWY2Q1cS9IcmlnaFFGdGdJMXArQzk1bGxvdGtCWG0wK3RaZEhMUDQ3SEtCRUsyMlBOamJQR21rZWZQQ0VOTER1ckUwbXAwbUFMUkpqNDk4Snh5amFHdEJLK0hZL0ZjelE0OUVYSVEvNHFCZmh6dzJrQTVQQ2htNHVlUTg0MDNEQjFVZ0tpL3FjZXdqQTEvaitHakM2NWFMNnQwMnViWEtlc21ZRGgvMG9RVjdqb2FVeTY2VndPZ2ZFOTUwODB6QVl2c3BZZ2lrSUEiLCJtYWMiOiJiMjkyZjYwN2EwMzczYjgyZDgzMDQ0MDBlNWM1ZWYwYWU1MjU1MTJiZjA0MzQ4MTg1NDEyNTUwMTYyY2QyMGIzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:16:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdDKzdZaXZrQjdBVFY4dnkvVWZaUXc9PSIsInZhbHVlIjoieUlrTGRNRm9qMCszRGQvZTF5NzVzVDc2RTRldDg0RGxKdUxhU1JEbmJDTkU1ZUV4SmRqelZ0d1RHeGxWODFscjhvRDZtTWQ3TFJsc3JkMXlwVDFPeW05MURoUGN6cE0xaE16RzNyVGlpamF1STJOT2lnbWpRVktLSUc5bERma0ZSS3I2RnB5WUI0SEdjeDhjdldyVmZHVWhONzBLNjZKdVMzRG4zaTI0cVVjQjJIQ2dlRWxYVytkV3gyMXA4c1R6ekU2dUxSQ2dUY1Z6SXV2M1hsb3llanJrdWdvNHE2SWJmRGN2RVdjQWJDR0laUnlVT05FNldJSUlkd1RCM3gwNkdlcXRFYXZWRXBIamFPbDh5RS9xUGxhQkdZc28wRzZCSnVIM09VTTF6WXBMcmFjVVhCZzJVTDBkdVlzSHdMNGlTTGhJRnV4NXp5Y2Y2OUJzNTFVbUU3RWxoekVUMzhDTUxkRExJOHUwYnhiN1k0Q1gvNkpVRHhHS2R3ekhVSFluZ2RQR1hqdFBaSXg1L0dHSXlidlg5bXVqWjdMbENNQUxTSmkrbFNSWkF3aGJ6N2hBSXlud2dNbDdwQkZkUXhSbnhxUkg1anByMm93VU5IWS9FQnIrVms0QjJmQ1JRR0s0Q1hqWE0zR2NVUGdQblJONDNrSnREL212RGlQRC9GY2YiLCJtYWMiOiI5NzRjYmRlZDFlNjhlMDA5Yjg2OWZmYTM0NDQ3MzE3MDIxNzNmNzcxMmZhYjRiZjdhOWYxMWJlOWU0MzRiMjgxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:16:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434380096\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-13310074 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IlFnTHBnWEd6RWlpM3JEWE9GVXJMQkE9PSIsInZhbHVlIjoiV2ZIR3QzT2RvM3prUkhIV1kyUFlJUT09IiwibWFjIjoiYmFlOGE1M2E3NTgyMTM2MTZlMjUyNTU0MjM1MmNmNmVjZDNhZjY1MjM3NWM3ZmJjYzc2NjUyOTQ1MzgwYjQwMiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13310074\", {\"maxDepth\":0})</script>\n"}}