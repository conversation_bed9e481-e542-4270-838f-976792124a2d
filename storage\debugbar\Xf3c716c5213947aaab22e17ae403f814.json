{"__meta": {"id": "Xf3c716c5213947aaab22e17ae403f814", "datetime": "2025-06-26 23:21:11", "utime": **********.903839, "method": "PUT", "uri": "/budget/1", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 7, "messages": [{"message": "[23:21:11] LOG.info: بيانات الدخل المرسلة: {\"32\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"630000\"},\"33\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"104040\"},\"40\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"0\"},\"41\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"9000\"},\"42\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"17000\"},\"43\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"135000\"}}", "message_html": null, "is_string": false, "label": "info", "time": **********.828756, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:11] LOG.info: بيانات المصروفات المرسلة: {\"30\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"180000\"},\"31\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-<PERSON>\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"93600\"},\"34\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"72000\"},\"35\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"33000\"},\"36\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"48000\"},\"37\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"102000\"},\"38\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"36000\"},\"39\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"6132\"}}", "message_html": null, "is_string": false, "label": "info", "time": **********.828893, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:11] LOG.info: إعدادات العرض المرسلة: {\"budget_type\":\"both\",\"category_filter\":[\"income-33\",\"expense-31\"]}", "message_html": null, "is_string": false, "label": "info", "time": **********.828973, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:11] LOG.info: بيانات الدخل بعد التنظيف: {\"32\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"630000\"},\"33\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-<PERSON>\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"104040\"},\"41\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"9000\"},\"42\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"17000\"},\"43\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"135000\"}}", "message_html": null, "is_string": false, "label": "info", "time": **********.829124, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:11] LOG.info: بيانات المصروفات بعد التنظيف: {\"30\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"180000\"},\"31\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"93600\"},\"34\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"72000\"},\"35\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"33000\"},\"36\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"48000\"},\"37\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"102000\"},\"38\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"36000\"},\"39\":{\"January\":\"0\",\"February\":\"0\",\"March\":\"0\",\"April\":\"0\",\"May\":\"0\",\"June\":\"0\",\"July\":\"0\",\"August\":\"0\",\"September\":\"0\",\"October\":\"0\",\"November\":\"0\",\"December\":\"0\",\"Jan-Mar\":\"0\",\"Apr-Jun\":\"0\",\"Jul-Sep\":\"0\",\"Oct-Dec\":\"0\",\"Jan-Jun\":\"0\",\"Jul-Dec\":\"0\",\"Jan-Dec\":\"6132\"}}", "message_html": null, "is_string": false, "label": "info", "time": **********.829204, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:11] LOG.info: فلتر الفئات الخام: [\"income-33\",\"expense-31\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.829313, "xdebug_link": null, "collector": "log"}, {"message": "[23:21:11] LOG.info: فلتر الفئات النهائي: [\"income-33\",\"expense-31\"]", "message_html": null, "is_string": false, "label": "info", "time": **********.829388, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.426492, "end": **********.903867, "duration": 0.4773750305175781, "duration_str": "477ms", "measures": [{"label": "Booting", "start": **********.426492, "relative_start": 0, "end": **********.756226, "relative_end": **********.756226, "duration": 0.32973408699035645, "duration_str": "330ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.756234, "relative_start": 0.3297419548034668, "end": **********.903869, "relative_end": 1.9073486328125e-06, "duration": 0.14763498306274414, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51762072, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT budget/{budget}", "middleware": "web, verified, auth, XSS, revalidate", "as": "budget.update", "controller": "App\\Http\\Controllers\\BudgetController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=905\" onclick=\"\">app/Http/Controllers/BudgetController.php:905-1021</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.07063, "accumulated_duration_str": "70.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.781723, "duration": 0.01332, "duration_str": "13.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 18.859}, {"sql": "select * from `budgets` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.797846, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "kdmkjkqknb", "start_percent": 18.859, "width_percent": 0.382}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.804394, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 19.241, "width_percent": 0.496}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.817683, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 19.737, "width_percent": 0.977}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.819546, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 20.714, "width_percent": 0.694}, {"sql": "update `budgets` set `display_settings` = '\\\"{\\\\\"budget_type\\\\\":\\\\\"both\\\\\",\\\\\"category_filter\\\\\":\\\\\"[\\\\\\\\\"income-33\\\\\\\\\",\\\\\\\\\"expense-31\\\\\\\\\"]\\\\\"}\\\"', `budgets`.`updated_at` = '2025-06-26 23:21:11' where `id` = 1", "type": "query", "params": [], "bindings": ["&quot;{\\&quot;budget_type\\&quot;:\\&quot;both\\&quot;,\\&quot;category_filter\\&quot;:\\&quot;[\\\\\\&quot;income-33\\\\\\&quot;,\\\\\\&quot;expense-31\\\\\\&quot;]\\&quot;}&quot;", "2025-06-26 23:21:11", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BudgetController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BudgetController.php", "line": 1004}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8298829, "duration": 0.05551, "duration_str": "55.51ms", "memory": 0, "memory_str": null, "filename": "BudgetController.php:1004", "source": "app/Http/Controllers/BudgetController.php:1004", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBudgetController.php&line=1004", "ajax": false, "filename": "BudgetController.php", "line": "1004"}, "connection": "kdmkjkqknb", "start_percent": 21.407, "width_percent": 78.593}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Budget": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBudget.php&line=1", "ajax": false, "filename": "Budget.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit budget plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-65020342 data-indent-pad=\"  \"><span class=sf-dump-note>edit budget plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">edit budget plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65020342\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.82291, "xdebug_link": null}]}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/budget/eyJpdiI6InAzMzVvWHBjZ3htWVJTc21RMmxleFE9PSIsInZhbHVlIjoiYmwzaHQxK1RGRnhXaE5sam5NOXBLZz09IiwibWFjIjoiMmZiNGY5Yzc1ZGU5MjBlZjYyMmZlMzg4ZDdmOTQ1NjIzYzgzNTEyODFlNDc3MGY5ODUwMmZmMjRlNGRhNGU5OCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "تم تحديث خطة الميزانية بنجاح.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/budget/1", "status_code": "<pre class=sf-dump id=sf-dump-1565420427 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1565420427\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-672329339 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-672329339\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1816373260 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1605;&#1610;&#1586;&#1575;&#1606;&#1610;&#1577; &#1605;&#1583;&#1602;&#1602;&#1577;</span>\"\n  \"<span class=sf-dump-key>period</span>\" => \"<span class=sf-dump-str title=\"6 characters\">yearly</span>\"\n  \"<span class=sf-dump-key>year</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2025</span>\"\n  \"<span class=sf-dump-key>display_settings</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>budget_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">both</span>\"\n    \"<span class=sf-dump-key>category_filter</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">income-33</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">expense-31</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>income</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>32</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"6 characters\">630000</span>\"\n    </samp>]\n    <span class=sf-dump-key>33</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"6 characters\">104040</span>\"\n    </samp>]\n    <span class=sf-dump-key>40</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n    </samp>]\n    <span class=sf-dump-key>41</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"4 characters\">9000</span>\"\n    </samp>]\n    <span class=sf-dump-key>42</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"5 characters\">17000</span>\"\n    </samp>]\n    <span class=sf-dump-key>43</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"6 characters\">135000</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>expense</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>30</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"6 characters\">180000</span>\"\n    </samp>]\n    <span class=sf-dump-key>31</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"5 characters\">93600</span>\"\n    </samp>]\n    <span class=sf-dump-key>34</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"5 characters\">72000</span>\"\n    </samp>]\n    <span class=sf-dump-key>35</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"5 characters\">33000</span>\"\n    </samp>]\n    <span class=sf-dump-key>36</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"5 characters\">48000</span>\"\n    </samp>]\n    <span class=sf-dump-key>37</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"6 characters\">102000</span>\"\n    </samp>]\n    <span class=sf-dump-key>38</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"5 characters\">36000</span>\"\n    </samp>]\n    <span class=sf-dump-key>39</span> => <span class=sf-dump-note>array:19</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>January</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>February</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>March</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>April</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>May</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>June</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>July</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>August</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>September</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>October</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>November</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>December</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Mar</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Apr-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Sep</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Oct-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Jun</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jul-Dec</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>Jan-Dec</span>\" => \"<span class=sf-dump-str title=\"4 characters\">6132</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816373260\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1797318167 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">16450</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"229 characters\">http://localhost/budget/eyJpdiI6InAzMzVvWHBjZ3htWVJTc21RMmxleFE9PSIsInZhbHVlIjoiYmwzaHQxK1RGRnhXaE5sam5NOXBLZz09IiwibWFjIjoiMmZiNGY5Yzc1ZGU5MjBlZjYyMmZlMzg4ZDdmOTQ1NjIzYzgzNTEyODFlNDc3MGY5ODUwMmZmMjRlNGRhNGU5OCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980050982%7C20%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilpna1hXTVFuU3ZnUFB0OHl6bmo0bGc9PSIsInZhbHVlIjoiL0xxNDNNaWtudFFPN2s3REFlVkFxS1BEbmUrM2xTNHhmVUhxY2dOY1czUUhCRmVRSGhkS2J5ZS95dlQ0d1hXTDNGbnJCbUhBNm80bFhiSnFrcWxRWnJCVmVQY2lKdW04R0NWMHZzSCs5TXZzS1FpakJ3R3dwVnAycG1uMjFCQkpLMUdDR1p5YVhONy9ucUorbFdXUkR3aGhRdktacWFFYXNiQ0JRdXhyaTh0SkhJOFZMYUt6dnRhZm1nWUxkdEpCZEpQMzY2QjZlOXd1L0QxenpTRHJSczF1d0IrWjhCcE1SbzRKTjZjR1pQWXV6bFN6eHFpby9ucWFkeWVOejRHUVN1eXpVTkdab2pmd2pvSlhjMHpzNlM5QzZvQmZUWEtVZTBjcG01b1hRUFBObWVQNi9OZVFPQ0J2cWU0YlBBWFhoZ0dzdDFoTDFDaHlzU0lTRHF3QTQ5NStpVzhtWTJ0RWNWRkF5MngrcEk0bm0zWnFYM0lteTQycUsvNlE4WDI4WjYxTzlFajVGQ2ZEZmR4UTZzS1d1ZzV6dWtOUlBabExLcTlxRW4rVlFiYnFaUUkrdGx6NlFScXlmWTZ6cEJYeGF5SVVIT2VjRy9IMG55Tklab0NKUWhhKzN5UVA3WHpCYW1ZbkxmY3M4Qk5jMFpIUWFSRDdHNW5SV0prdWE4bkUiLCJtYWMiOiIwZTcyMzU5OGZiMDU3YThhNTExNjhhMGM5ZmVmMjliYzZkYzJmYzdkNDI3MzU4ZmJkMDA4MWNkMWI4YmYyNGRiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldiQk4vUFRpdW9wbkN3Z2QvQm9zOWc9PSIsInZhbHVlIjoiZEZ1dEpsK0cxYSt3NU9YR3ZlRm9ITGllb0ZtQ2hUeGNyZkFwR2FQMFpBaStKNitWYmdYRjZFUmRWc1lVazVIbU0vK3VRcnB3SThzdThKcDdySWRFRXA5TFpVOGtRSWhnZ0lWSEc2cmJac1pwVXBUT1N5cW1TeTk5ZEFTVTFraVBrd2hGcktVRXNsekxJWHB2WjNBcVAxbEhOZGIvU3dVcHQ4Q1hlTXVJVVFqSWZIUUlaWVFPTFI2dkxZN0dYc0duK1VrV2cwZEovS0lwNkVlR01yMjQwL3NVZ2l1RUFJaGhDa0toVnIzQUhPUGZYbE9sOTYwYjhyaEl5UlZ0WXFnbzdUcFJ1UW0rWGQvUHlOb1Q4T2lYYlB2a1JkYllBWjBlTG1nWlFSQXpUck5XMkJucVlMOUR3Vnk2YzMyVEk1MDJ2anRqVWFGejJMYk9maWV0K0lsNkN2TjQvUmNDRUE1M2NmZStVV2g1RlFYMHBPTkREN2ZERjFoQTN4ckFDazM4cENJRHdYdGpZallxc0oxa3ZReWlrajhTTTkxUndMODVHRm9yQThZbThGWFBWdUxIZTM0NzJDRHFuUXoxQXQ5azlNWjlwMnhQTm0zZXBIb3NXalREZ0lGcFBnNlRjdFM3ajI5RG1QNDV4YUc1RU42bitUQkViYjFyaEd2MnE2NXAiLCJtYWMiOiJhNWIxOWNjMTNkMDJlODIwNTk4ODU0ZDY0YmMwZmNmYzk0MTEzMWQ3MDA5YjE2NjFhYTIyMzJiNGJjYTdjMzkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1797318167\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-536466700 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536466700\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-783016661 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:21:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/budget</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRhNWtsVk9yT1cvOXJHbGsvcStTcHc9PSIsInZhbHVlIjoiRkVMbWowWkhYU1NISE5NdnpsU21Qa3ZSVmt3cUJwV3pZWTV2Q3hzV3Uxc2F4WFh1aFNqUVNuTEhvSFNHN3FUa21iTjMzclRwTGtWQ2RSbDExQmpBcE1TR09ZRnhZNW91Y3BsaWZ5WmZ3TzFBbHJGdENBN3lVQSt0aW9XZ3E4cmxFU1BtQkE3Z1NaaEc1TFNrcG5rOTI0cm5HSC82MXhNMkQrZEk3NUQrNUtOWmVwb05qR2VZZnZEc2FYdHc0Umt6QVIzRjZqaTA4OWNZTlMxL2t3UGM3UC9DMUlaUXZmbzRhVktmWjIrQStmZnlGSXFJOGVNd0x0TkpJbG9qbWRuZlhNSVRNSEszd20rNHdONFZQU1Yzam50amNMTjRBQlh4WnpjSnN4UDRoNFZIblFDQitUdmtqWnR2bGhtN2w5RDdUcG5mTkFRdkhISjMxYVU0WEIzTjA0bFhlT0x6dW1HYlRLUDBOa255c0ZvUTB2RFFUZGJBR29Yc3RCUlFPRSs4WnVPZEJIVjM3dnRackJVTHVWUFJxY2oyZTFzR2hkZVkxYVZ1Z2FOZ0RRazZST29kQkRBT3l3dTlVZWpLeWhlNXEyY2RYS0plTjdaTTBBVThsc0JUTHFKMWp1VUJYMlJxZGI3T1NGZ29qdjROeU50NEpuSEVJS1JvUkdSVVE3VEciLCJtYWMiOiIzMzlhOGZiMzIxMDA5ZGUwNmEzNGZjNDM1N2VlYzE4ODU3OGZlMzYxMmY2YTBiMjY5Y2RhZmIwNGNjNTkwNjAxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkVSeVdkaUR2dy83aU1QMVlGTHNvUlE9PSIsInZhbHVlIjoiSUk4TGhraXRHQTdYYi9JWGEycEk3aXUxbWFKOXMwQ0QrQjQ5QU5tRWlBSkl2TGJ6YXVqQ1U2R096WDJPK3RRbytGbitOaWdVNlJaM2s5QjBTZmt2VlFPRzVTM3hFMlN0MEVuWFIweUZpZW9CaUR3Y3NZWWJWM29GckpEdXZZbWF5ODdET0ovV09iYkdmL1daVXZiRjV2TzdzbUkzTTZ1MlFKYUFpVS9vTFBkNFB2d1dXb2tETlZvdFZLTC95eEhGZldwMlFydk1VM0tnUDJXNDA1ZkduclBGanhPUzFxUmVzRU5RWSt3U2x6UUdmbzRLbG5VbE96a1ArdDBEQ3VsTUxkOC9hVmhqZWNIVTZ2WDRiZGVKamRJckhJQWh4dUxMK1RmSmRiVnFRclF6bDhoVmVDNGRVQS80V05tTmpFOWhneWFjK2R2QU1GanNyMW0yUmxqN3doSm5OSkt0RDhZSkJTUlM1ZkFTcGNFMTh3OWFWVXhMWnhabCtDY2NGMk5NOHA4bnoxYndyL3o5bFdISFIyVEFpUzZhcVBYZ3BIVDBxQTl6UW1rb0tsd2pTcHc2ME5ZUjlZOXlnNk9wUE10T3hqa0QxNXc2Qm05c2xVZW9PbzFrMlVGcXJwWDJqdTNrbThCNm5kNXZ0UUNXZHl3MkQ3WHZqa0trdENZMjYrdm8iLCJtYWMiOiJiNjMwYzBlYTNiNTU5YjRkYjkzNDA0MzMxYzJhNGE0NDkxNTNkNjc5YzFmYzY0MDU4OTUyNjE1YjA1NjE4Y2Y2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:21:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRhNWtsVk9yT1cvOXJHbGsvcStTcHc9PSIsInZhbHVlIjoiRkVMbWowWkhYU1NISE5NdnpsU21Qa3ZSVmt3cUJwV3pZWTV2Q3hzV3Uxc2F4WFh1aFNqUVNuTEhvSFNHN3FUa21iTjMzclRwTGtWQ2RSbDExQmpBcE1TR09ZRnhZNW91Y3BsaWZ5WmZ3TzFBbHJGdENBN3lVQSt0aW9XZ3E4cmxFU1BtQkE3Z1NaaEc1TFNrcG5rOTI0cm5HSC82MXhNMkQrZEk3NUQrNUtOWmVwb05qR2VZZnZEc2FYdHc0Umt6QVIzRjZqaTA4OWNZTlMxL2t3UGM3UC9DMUlaUXZmbzRhVktmWjIrQStmZnlGSXFJOGVNd0x0TkpJbG9qbWRuZlhNSVRNSEszd20rNHdONFZQU1Yzam50amNMTjRBQlh4WnpjSnN4UDRoNFZIblFDQitUdmtqWnR2bGhtN2w5RDdUcG5mTkFRdkhISjMxYVU0WEIzTjA0bFhlT0x6dW1HYlRLUDBOa255c0ZvUTB2RFFUZGJBR29Yc3RCUlFPRSs4WnVPZEJIVjM3dnRackJVTHVWUFJxY2oyZTFzR2hkZVkxYVZ1Z2FOZ0RRazZST29kQkRBT3l3dTlVZWpLeWhlNXEyY2RYS0plTjdaTTBBVThsc0JUTHFKMWp1VUJYMlJxZGI3T1NGZ29qdjROeU50NEpuSEVJS1JvUkdSVVE3VEciLCJtYWMiOiIzMzlhOGZiMzIxMDA5ZGUwNmEzNGZjNDM1N2VlYzE4ODU3OGZlMzYxMmY2YTBiMjY5Y2RhZmIwNGNjNTkwNjAxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkVSeVdkaUR2dy83aU1QMVlGTHNvUlE9PSIsInZhbHVlIjoiSUk4TGhraXRHQTdYYi9JWGEycEk3aXUxbWFKOXMwQ0QrQjQ5QU5tRWlBSkl2TGJ6YXVqQ1U2R096WDJPK3RRbytGbitOaWdVNlJaM2s5QjBTZmt2VlFPRzVTM3hFMlN0MEVuWFIweUZpZW9CaUR3Y3NZWWJWM29GckpEdXZZbWF5ODdET0ovV09iYkdmL1daVXZiRjV2TzdzbUkzTTZ1MlFKYUFpVS9vTFBkNFB2d1dXb2tETlZvdFZLTC95eEhGZldwMlFydk1VM0tnUDJXNDA1ZkduclBGanhPUzFxUmVzRU5RWSt3U2x6UUdmbzRLbG5VbE96a1ArdDBEQ3VsTUxkOC9hVmhqZWNIVTZ2WDRiZGVKamRJckhJQWh4dUxMK1RmSmRiVnFRclF6bDhoVmVDNGRVQS80V05tTmpFOWhneWFjK2R2QU1GanNyMW0yUmxqN3doSm5OSkt0RDhZSkJTUlM1ZkFTcGNFMTh3OWFWVXhMWnhabCtDY2NGMk5NOHA4bnoxYndyL3o5bFdISFIyVEFpUzZhcVBYZ3BIVDBxQTl6UW1rb0tsd2pTcHc2ME5ZUjlZOXlnNk9wUE10T3hqa0QxNXc2Qm05c2xVZW9PbzFrMlVGcXJwWDJqdTNrbThCNm5kNXZ0UUNXZHl3MkQ3WHZqa0trdENZMjYrdm8iLCJtYWMiOiJiNjMwYzBlYTNiNTU5YjRkYjkzNDA0MzMxYzJhNGE0NDkxNTNkNjc5YzFmYzY0MDU4OTUyNjE1YjA1NjE4Y2Y2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:21:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-783016661\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1326044023 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"229 characters\">http://localhost/budget/eyJpdiI6InAzMzVvWHBjZ3htWVJTc21RMmxleFE9PSIsInZhbHVlIjoiYmwzaHQxK1RGRnhXaE5sam5NOXBLZz09IiwibWFjIjoiMmZiNGY5Yzc1ZGU5MjBlZjYyMmZlMzg4ZDdmOTQ1NjIzYzgzNTEyODFlNDc3MGY5ODUwMmZmMjRlNGRhNGU5OCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1582;&#1591;&#1577; &#1575;&#1604;&#1605;&#1610;&#1586;&#1575;&#1606;&#1610;&#1577; &#1576;&#1606;&#1580;&#1575;&#1581;.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1326044023\", {\"maxDepth\":0})</script>\n"}}