<?php
namespace Aws\QApps;

use Aws\AwsClient;

/**
 * This client is used to interact with the **QApps** service.
 * @method \Aws\Result associateLibraryItemReview(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateLibraryItemReviewAsync(array $args = [])
 * @method \Aws\Result associateQAppWithUser(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateQAppWithUserAsync(array $args = [])
 * @method \Aws\Result createLibraryItem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createLibraryItemAsync(array $args = [])
 * @method \Aws\Result createQApp(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createQAppAsync(array $args = [])
 * @method \Aws\Result deleteLibraryItem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteLibraryItemAsync(array $args = [])
 * @method \Aws\Result deleteQApp(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteQAppAsync(array $args = [])
 * @method \Aws\Result disassociateLibraryItemReview(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateLibraryItemReviewAsync(array $args = [])
 * @method \Aws\Result disassociateQAppFromUser(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateQAppFromUserAsync(array $args = [])
 * @method \Aws\Result getLibraryItem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getLibraryItemAsync(array $args = [])
 * @method \Aws\Result getQApp(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQAppAsync(array $args = [])
 * @method \Aws\Result getQAppSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQAppSessionAsync(array $args = [])
 * @method \Aws\Result importDocument(array $args = [])
 * @method \GuzzleHttp\Promise\Promise importDocumentAsync(array $args = [])
 * @method \Aws\Result listLibraryItems(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listLibraryItemsAsync(array $args = [])
 * @method \Aws\Result listQApps(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listQAppsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result predictQApp(array $args = [])
 * @method \GuzzleHttp\Promise\Promise predictQAppAsync(array $args = [])
 * @method \Aws\Result startQAppSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startQAppSessionAsync(array $args = [])
 * @method \Aws\Result stopQAppSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopQAppSessionAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateLibraryItem(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateLibraryItemAsync(array $args = [])
 * @method \Aws\Result updateLibraryItemMetadata(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateLibraryItemMetadataAsync(array $args = [])
 * @method \Aws\Result updateQApp(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateQAppAsync(array $args = [])
 * @method \Aws\Result updateQAppSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateQAppSessionAsync(array $args = [])
 */
class QAppsClient extends AwsClient {}
