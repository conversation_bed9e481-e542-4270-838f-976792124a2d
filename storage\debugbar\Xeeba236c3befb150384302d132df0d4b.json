{"__meta": {"id": "Xeeba236c3befb150384302d132df0d4b", "datetime": "2025-06-26 23:20:08", "utime": **********.317917, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980007.873858, "end": **********.317932, "duration": 0.4440739154815674, "duration_str": "444ms", "measures": [{"label": "Booting", "start": 1750980007.873858, "relative_start": 0, "end": **********.251113, "relative_end": **********.251113, "duration": 0.3772549629211426, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.251121, "relative_start": 0.37726306915283203, "end": **********.317934, "relative_end": 2.1457672119140625e-06, "duration": 0.06681299209594727, "duration_str": "66.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01418, "accumulated_duration_str": "14.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2816222, "duration": 0.01326, "duration_str": "13.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.512}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3052099, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.512, "width_percent": 2.68}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.310647, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.192, "width_percent": 3.808}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-376824192 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-376824192\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1224411052 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1224411052\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-834584413 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-834584413\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-571312602 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980004460%7C13%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFieXpIcVduL2U1c3cvZ2EwU0V3MWc9PSIsInZhbHVlIjoiRjRqTFpOMGJCWGkyd3pCNEVMaGhSaXZQYXRLUUR3OFMraE5saVZ4bWhPQkRRQTdxQXJqOU9lMURvTWp6dkQ2elUxcHR1NmZvazBrajNXMEQwTzFXL096TDJxZzd1a2lSUG5XY1ViNVQ4b3B2andwTktzdVhMVk1nbXljamdUNWNUSWoyVE5FNkp5WlV0WFgxMmp4UXB5cTBjTDUyK3NSMzBIQTRmUFU2Szh1ZVkwb2t1d2dHR3FDSExYUExVbkRybkU1MitCai9LVGFWb2FZTGNhYTRpbFFsYjVKRm16WHpEcGdJMGF6WktRK25nLzc0TkVNZitucnZ4bUlHbDBLU2lCc2E4SE9RbjNOOXJKNkVoZ2Z0WmdUeVQvdXNDeDlSODVhMjJtb0YwUC8vM1V6MWZvMlF2MmdOZVh2SkJhUjd4L1JoTG9XWkQrZlloaVd0Q3NVbHArT2VNbVcwYjlmbzFTaCtaSmJkVGVvcVhMM0h0K2cva1NPdkhxbU80dUowUzgrejFLRVhvdzJrdmtuR3ljblBJQUdzTVd0bGlTeHV4dXFYSnEwNWJJcWZmUXVLNk9DWFJmSzhkeTJhOXlnaGo1WlQ2TWNJb2JDanNsY2FkTVV5dFlCdEx4RXhrTlRvVEpRZmgxbXRoQVRqdXV2Y1RpSmxqK0pDamZodFpjOVkiLCJtYWMiOiI5OWRiNDU0NjUwY2NkOTc2MzRhYmM5M2FmYmY1NWUzODFhN2U4ZmQ2NmE1NDg2YmNjNWM3OGNmZWM2ZTFjYTU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVGTmVyQ3VPZXV4dzJ2SWVSQWZVWWc9PSIsInZhbHVlIjoiQzVWeEkwSm4za3B0Q2p0U2hpc0pUVldkMVNHRGQxTjdicUZGTjZYMFBNak9qVnZZalBQczdaLzcyZVN2Qm5TWkRlL0U3TTlVRkhVWDFwQm5JVGlJd3hhREpPSzU2dnl0OFVMb0FaYUU2VUtmRHJNWDNYUlQ2NHBUVTNXTDFyNjZxaDhGV2M2VmMzQTVob092Qk05dlQrWm91eUdseVhiZjNDOEF0dHllbmw2Z2Rvb3F3cllTVHBncHJpbjVPU3kxY01NOGdSVzdyajNJdWs1ZVh5akpUcVNUMTU3am9SaCsvZEsydkhaUUNOblVGYW96cGZRamdSdmdwZTRLK2VpM3NUQnRQUzA3Qy9MZ283MWhLMnJDN1lxcjlucUhwTmRCSm5TeFM1ZjZmUFBwaFdJWmNMcHZ5Znp5Mm5Ia0daU1FhUW8zM1FTVExLT2ZKQkhxZjVuVTFLQjFrVWpUNEZ0TFptU2xWN1hrdmFjcWJLc2t0cFc3a3RjdWYyWmZyRnFZd3phTXp0NHNuUTB6Tm8zWStZM1RJTkhWWFBicjVWT1Yyc0ZuUVRaUjVtSExLYzFhOTY3TFlMKzdpUW9DWThIZXFWNnROTVhDbGVIQVllMXRjaG1RRnpWeEU1KzlFbXZhQTRleUR2YzcvSnFjQWMrdksraHFIZnhnZDV2S0hUNjAiLCJtYWMiOiJjMGQ1NmFhMjA5NDRjYzAzYTE3N2RlY2YxYTBhMzI0OTRlMjkyOTE1MzZmZGM2ZjU2YTMyMGQ3YWI4MmNiMDJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-571312602\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-643968163 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643968163\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:20:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImI3MUVxRTl2TEM4ZzBQSlBXRDNnWXc9PSIsInZhbHVlIjoiWTJ2Q2llSklpTDF2VXpaVGNQU3FYSGUwbUR3NitHL1AxS1JZa1dCWFJwYU52S0I0N1hKZXl6OVZaNEtLSVIrMC9wWkVLczJRU1NoeTUvUkdRTjZmRGxWRDJ2TXhZUlM3MTI1OVFkMWJ4dGhlM3VjUUZJNUFBbGI2MHBLL2FSRXhteXNIZHNSbkVBeUswR2trTE1uNllRUEgwbnFtZFRNcDZzQjhHd3ZiMmJSSHo4eEc4am5VK0JyVktlVm94QnYxOHFzSCtmZEVqZXh2cnRSLzRXbTBWK1pQL1Z0L3FyYS9ZKy9zeHM3dnA3d3AyVHpZK1BWQ2lLcEdZOFBCcldyOGhhcDgzR2FjY0F3dGdzQ2JWaTFCdUFJZm1jVnRMTnc5REJaVWZkQlhxK2VxUkxGcENLMDhQeVBtbXVRSE5wNmYrekF4bnV1WXp0NGZKMEY4SjNlUGFndkZ0RVp3RG9GeExNSzFQekxVUkNpS1VOcWNoN1hMUHk5TzdyK2pkbllOM2NSY2RnS0pXU0w1OFdSaUJFTTZ5b3M2cmFRaXFMY3V3Z3dqVzY3aHM2OHZiRksxdEU0SnlEV1lFUVJ0dmo5bzRrZkNOYW1QRGQ0a08zbmFLNU1CT0ZTU2EyY1JFNzVnZThQTVczTWg3d0FQWXNacTdNSkxPK2g5Nmd3UzA4MWEiLCJtYWMiOiIxZGM2N2NhOWRiNzk5N2JlOTdmNzE1MmY4MGM4ODNmYTk2ODE5OTE0NGE3MWNlNDRiMWIyYmQ1NDc5Mzc3YzczIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im84S3VUUHFwZG9CNDBLcldFQkFEUlE9PSIsInZhbHVlIjoiUWNDMGhMT1BwWWVnMzFQSWpyc3FXbWx2TmdYVE1jWGtjTEpmRk1LNzJGMlpQalhqdVo4N2N5RmFQczhVZFJxMDJaOC9lbXBQU01xYUQwNEpBYUJsYUpQbW51dDNWOFFEWUZXUkxTR0wzZnVHN3VrdUZ0K2RpbXNqNERLVkV6ZHJ1VXhMR053eEpVYjhmYm5VVlpEaGpVeS8rS053R2Y4UzVPdXN0SUdxc2RhVU9IMnhITVBrUGZaNU03bHMvMExBdHByTnVFWGRYL0hkakpzUDl6RVpqRm5WaVlRdEl6a1BSdUppbDNaQlZqbkhKYTdPL2kvZHI2enBlbkRoZDM1Y1o5d0ZWVHhmVHkxUzJqbTkvOFZoUzV3eFpYTkdaUlorV0xqUlJmVTZaN1lTRVMxcWVSa1hDQ1NHb2lLTFBUY1lxek5kQmlBcHBBQiticDV3NzFDWlZncHZlZlpLZDNhbzdPTnBpMVhodDhUUFVJRFgvWnh0VHFycXdOaGFtWVFCWG84cjc3aFFjMlovbEpPR2ZySVhVSFp2OTQ1dFpScUsxSWdLN3VUN0Y1SHNUWG9nbTlKcjcrV2lHU1FsMzNKR3FYYUdZeGFxY2dMcEtGTlgzcUN4dmRiRlFYSlR3UmdpOFh0M25nZzJzRW5xQVQ2OFdlQ29kYUZnWnZmV3pUdzQiLCJtYWMiOiI1ODFjZGEzZTFmMDg1MTI4YjEyMThhNGZhNmZlYmM5MDRhMjIyN2FiZWNjMWI1YWIzMTNjOGM1ZWE3YTBhZmZjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:20:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImI3MUVxRTl2TEM4ZzBQSlBXRDNnWXc9PSIsInZhbHVlIjoiWTJ2Q2llSklpTDF2VXpaVGNQU3FYSGUwbUR3NitHL1AxS1JZa1dCWFJwYU52S0I0N1hKZXl6OVZaNEtLSVIrMC9wWkVLczJRU1NoeTUvUkdRTjZmRGxWRDJ2TXhZUlM3MTI1OVFkMWJ4dGhlM3VjUUZJNUFBbGI2MHBLL2FSRXhteXNIZHNSbkVBeUswR2trTE1uNllRUEgwbnFtZFRNcDZzQjhHd3ZiMmJSSHo4eEc4am5VK0JyVktlVm94QnYxOHFzSCtmZEVqZXh2cnRSLzRXbTBWK1pQL1Z0L3FyYS9ZKy9zeHM3dnA3d3AyVHpZK1BWQ2lLcEdZOFBCcldyOGhhcDgzR2FjY0F3dGdzQ2JWaTFCdUFJZm1jVnRMTnc5REJaVWZkQlhxK2VxUkxGcENLMDhQeVBtbXVRSE5wNmYrekF4bnV1WXp0NGZKMEY4SjNlUGFndkZ0RVp3RG9GeExNSzFQekxVUkNpS1VOcWNoN1hMUHk5TzdyK2pkbllOM2NSY2RnS0pXU0w1OFdSaUJFTTZ5b3M2cmFRaXFMY3V3Z3dqVzY3aHM2OHZiRksxdEU0SnlEV1lFUVJ0dmo5bzRrZkNOYW1QRGQ0a08zbmFLNU1CT0ZTU2EyY1JFNzVnZThQTVczTWg3d0FQWXNacTdNSkxPK2g5Nmd3UzA4MWEiLCJtYWMiOiIxZGM2N2NhOWRiNzk5N2JlOTdmNzE1MmY4MGM4ODNmYTk2ODE5OTE0NGE3MWNlNDRiMWIyYmQ1NDc5Mzc3YzczIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im84S3VUUHFwZG9CNDBLcldFQkFEUlE9PSIsInZhbHVlIjoiUWNDMGhMT1BwWWVnMzFQSWpyc3FXbWx2TmdYVE1jWGtjTEpmRk1LNzJGMlpQalhqdVo4N2N5RmFQczhVZFJxMDJaOC9lbXBQU01xYUQwNEpBYUJsYUpQbW51dDNWOFFEWUZXUkxTR0wzZnVHN3VrdUZ0K2RpbXNqNERLVkV6ZHJ1VXhMR053eEpVYjhmYm5VVlpEaGpVeS8rS053R2Y4UzVPdXN0SUdxc2RhVU9IMnhITVBrUGZaNU03bHMvMExBdHByTnVFWGRYL0hkakpzUDl6RVpqRm5WaVlRdEl6a1BSdUppbDNaQlZqbkhKYTdPL2kvZHI2enBlbkRoZDM1Y1o5d0ZWVHhmVHkxUzJqbTkvOFZoUzV3eFpYTkdaUlorV0xqUlJmVTZaN1lTRVMxcWVSa1hDQ1NHb2lLTFBUY1lxek5kQmlBcHBBQiticDV3NzFDWlZncHZlZlpLZDNhbzdPTnBpMVhodDhUUFVJRFgvWnh0VHFycXdOaGFtWVFCWG84cjc3aFFjMlovbEpPR2ZySVhVSFp2OTQ1dFpScUsxSWdLN3VUN0Y1SHNUWG9nbTlKcjcrV2lHU1FsMzNKR3FYYUdZeGFxY2dMcEtGTlgzcUN4dmRiRlFYSlR3UmdpOFh0M25nZzJzRW5xQVQ2OFdlQ29kYUZnWnZmV3pUdzQiLCJtYWMiOiI1ODFjZGEzZTFmMDg1MTI4YjEyMThhNGZhNmZlYmM5MDRhMjIyN2FiZWNjMWI1YWIzMTNjOGM1ZWE3YTBhZmZjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:20:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}