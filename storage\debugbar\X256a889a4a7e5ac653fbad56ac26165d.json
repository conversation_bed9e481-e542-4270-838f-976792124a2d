{"__meta": {"id": "X256a889a4a7e5ac653fbad56ac26165d", "datetime": "2025-06-26 23:22:42", "utime": **********.791711, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.348988, "end": **********.791725, "duration": 0.4427368640899658, "duration_str": "443ms", "measures": [{"label": "Booting", "start": **********.348988, "relative_start": 0, "end": **********.718359, "relative_end": **********.718359, "duration": 0.3693709373474121, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.718368, "relative_start": 0.36937999725341797, "end": **********.791726, "relative_end": 1.1920928955078125e-06, "duration": 0.07335805892944336, "duration_str": "73.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45027568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02116, "accumulated_duration_str": "21.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.750141, "duration": 0.02023, "duration_str": "20.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.605}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7784622, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.605, "width_percent": 2.221}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.784441, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.826, "width_percent": 2.174}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-503542471 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-503542471\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-64034277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-64034277\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1798972362 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1798972362\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1985107130 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980132423%7C28%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkREeElySzZaNXJIK3U3SUhMRm1KTmc9PSIsInZhbHVlIjoienlrZDhwYVFrN1dHTTA0UHpRU0JnM3dnTmIwWi9tNkYwSm5zZzNjUTNobFlQNm9lWmlJL3pOb0F0OHFYNVFXbmNSbnlHUk9jTkpZdG1TYkNsL1lVTlFmazFpTnNzZVRjWDVRQ1RJUW9HYm5GdVhBZS9lUnBNSmoyMWtEV3Z4QTZhT2tjTUpzYnRBZ3dpY2RDYVJEVUhRczhSWUxUcmxlcU5BdE5sYkY0ZmdYZTVmaGkzaWpLL2NvOW5VSkVQdjRmZ2dhWmdLeWZqMjg5b0RONnR2UG1WWUUzQk53Z2l5QzI1MTlmUFV3VklLaGMzOVNNejhaMCtERXBXZUxuRFJHSlJvTUVraDlUTitrU3hjZjBMcUpRcUtGMEFFSTlxNmwvZUdSUitzc21iSjM0RDdGaW5TeG9oM05NNzZWUFpnT2t4TCtSNks3Um9CeWtzTmFVOUZFUzZiL3FHUGh6V1BQVmwzTzFWZ2lxaGNJUjA5VE10TDRxNGVUUmxQZTB4UXNtTE9ZSTlwellOUHB1bHM4SDB0YUc1MGpXVmkrUFJaTmVFYlVycitrYW5DeFIxZWJiN2pvZlhic2gwTXV3RlNrVndNTklPZHVHN1ZGYXc5UW1HOERDNHVuT3pVazRhQnlpRW9FVldHV1NYalFEcGVSMGFjRVh5ckUwREgydjJXdEMiLCJtYWMiOiI3ZGNlMWEyNWFlOTYzNmQ3ZGUxNWMyYTVmNThiNjMzOTQ2NjdkNTg4ODlhZjYwMmUzYzllZmZlMGE5ZDE2YWM4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InQwbDBmckJhN3gwWDVhWWRTQmlvMFE9PSIsInZhbHVlIjoibERFbEYyMGpxanpUZTd5OWxNd1o0VmlVbTBzVHhNOUVqNWU2RVMzQU1qalJhYXRHM2VBWTAyZWdWTGpVRU1UNDdOUFJoZ01JblF3YjRsd1IxV0t5UVlVc0tJK0RhdjdFd3pCRHVyRUlTb3J4SWl4VlBzaEEyNVJza0l6TjZaNysxWFRyN1ZLeXVzNmF0QUFZU3V6b0o5WWk1d2piY1dEdllZSi9INUVkWkpnQ09EUUh5aS9PYzB4eXhiSjF5SklMWkNZdmI5T2pqY1BaSkl0YW9OaWIvU08yVXNnQjJra056eGd1TlkxTVdUMWhHazJOT3kraUE2MFNpdlBYTWl0Z2szUUt2dzVRNmRMT1FDOU5BeDA3d01OeGx0K1k4bVhIMTl1bmpick9FNjROUHdKVkZFZG8vS1QrWTVrSUhVcTNuZ3FERXlzZEljSm95ZTZTWDU4aWJzMDl1L1RXRjBJNTNydzU3U0xRUWJac0FxUDdvM0NNZE9WUHdteUdWUWI3WDJicTF5SnlZelFFOW5FMndqYlVOWHo4QTh3RnNNTy9oejd3ZFU1ZXFVc2ZST1RuUk8zTmxQblpWZ1MybUorU0JKaWZtMmpwYWY2N1RHVjBqNzQzbEhMRzZNZlR4Y0lFU281RGZIVzZqZXRHL2JyNHdybndvbkFGMGZVVUI5Vm0iLCJtYWMiOiIyZTYxYjliNjY5ZGE2ZjBkNjQzYTY3MjYxNjU1NzM0MzQ2M2U2NzcyZjQ1MWI5ZGZjYTNlNWRmM2Y5MzZkNTQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985107130\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1770448887 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1770448887\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1163390963 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:22:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijltb1VnVnBIY0hiNEp4RExyTkNVNkE9PSIsInZhbHVlIjoiYk5idW1VN1h5SVp2eGxnZjJPdGJKWElHM3R5UExTaVV3c3ZuS1k5U3Bmd2JhdnZDcFg1ZlEwRWhXcEZSOWpnWmZVaThlSXdvRmN6Z0JCK2tWdWtuVk5zUjRsMjVZeEFUQzlmaEhIVWN6M2ptc1J5WW9VcUtpOU9YMW44MEo5eU13d0pXdHltZ3oxTWJQeEE3SHU1bUhVY2tMdWZUOWlNR2h3bmV2ak5zQnY0R2NRSjRjR3NWZDdVSldGZnV0TFBzNUFyTXEzaGhTZFhZK08rODlxYnJhYjBaWDdjdEtCMldXczZ3VmE1TzM1N1BQMjIvUGFPcHYzeUJzWUF3ek9PemlvMU5MeXpyQnRaa244dDRaTk4xd2MwNUE5T2x0ak9XWjgrU1laaWlmdVN5VXVyY2ZPQnhNbDhRWFJVVFp5cTlmK1hvOVhYMHFCVjZETk83YXA5U3Y2T3kvSWoxb1NVbWFBY2tjK3dIMXBYR2F5R2p4VGZndmc2OWhFOE85WW10T3VTaVNFTGNDMU4zaEhseTdYUHd3ZzdKeVVVaVRUZW5ib1dGcmllTGIvUWNXYXFTdkltb0ZhTGd2MWFsU25PMmVPUlZ1Q2cxY0dzdXJuK0JIL2FkekkzcEduV1FqdHdhVE95c3lETnNJR1dlWXdQc0VUWHNFTFBaSXl0SXh5TUkiLCJtYWMiOiI2YTFkZmQxYTRlOGRmOWFhZGViNTIzMWViMDk5NTM1NGNhNmRmYTE5Zjc1MWM1ZTlkZjAwMzM4YjRjZDQyNDY3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1qUVplV294WStabkNublg4UVBpekE9PSIsInZhbHVlIjoiUWE3TlNuTUpsUzlNeXBpVERzYlNabmNRdlg5bnNiejJlQkZ6VXQ4Qzh1dEgvUCsram5RNUxBdFZ6azZhY2ZTWS9GSGZSQXZ5bW5iUE91YkVCcGY5SnEwY2o0aFJzQ0czUXNPRWVpNWp4REcwRXZROGUzT3lUemI2RWUrbTMzRG4wZmZFV3dUSFh5d0k1NUZuMSs1T0hmSElJaWEwQkptZmhVckIvR253ZGFYRHh2Y000TnhIZUJ2WGNkQU9BaXhPRUFob1kxSGtwU0gwTDFzVjZXTGFXSjdmbCtZaG1qbXlDOHpnS1ZaSkVwYzRHTndXMnl0RW95RHZPMVlra2ZHUktRWm9GNDlBaVdSbXlDamtRUlM1Qy9JdW42WVZ4R3h1ODUvQXdvU0tHb3kwcWVyMTRHWlVNNzlvUkpTL204VEtiTEk1cmZZdWJJczlsNitiUEwwaTlTUVYvRHVhYkluZjZRdWExcnh1ZldxRFpNVXZ6QXJkRVhvanV6WURWcGV3bmV5N295QU1jY0VjTXA2bm9DdDB2eGR5Tmo2b2x3OEV5ekN4YWF0eC83QXJMOThVajZSZ0d4MmtCbnNNNUg5MG5laDE4RGJXLzdLczRPcVVpcVplQW1ReUFFYisxejd0L0RzZERRY0VlYjlLK05Qb1ozNEUzejc1NGtVN3pNZkwiLCJtYWMiOiJhMGY0NzgzNmUyYmNlMzVjMzQ4YTNlMDI0YTRjYWU2YzQxODgwNGQxODNhOTUzY2FhZTdmOTcxZjRjMTQzYjdiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijltb1VnVnBIY0hiNEp4RExyTkNVNkE9PSIsInZhbHVlIjoiYk5idW1VN1h5SVp2eGxnZjJPdGJKWElHM3R5UExTaVV3c3ZuS1k5U3Bmd2JhdnZDcFg1ZlEwRWhXcEZSOWpnWmZVaThlSXdvRmN6Z0JCK2tWdWtuVk5zUjRsMjVZeEFUQzlmaEhIVWN6M2ptc1J5WW9VcUtpOU9YMW44MEo5eU13d0pXdHltZ3oxTWJQeEE3SHU1bUhVY2tMdWZUOWlNR2h3bmV2ak5zQnY0R2NRSjRjR3NWZDdVSldGZnV0TFBzNUFyTXEzaGhTZFhZK08rODlxYnJhYjBaWDdjdEtCMldXczZ3VmE1TzM1N1BQMjIvUGFPcHYzeUJzWUF3ek9PemlvMU5MeXpyQnRaa244dDRaTk4xd2MwNUE5T2x0ak9XWjgrU1laaWlmdVN5VXVyY2ZPQnhNbDhRWFJVVFp5cTlmK1hvOVhYMHFCVjZETk83YXA5U3Y2T3kvSWoxb1NVbWFBY2tjK3dIMXBYR2F5R2p4VGZndmc2OWhFOE85WW10T3VTaVNFTGNDMU4zaEhseTdYUHd3ZzdKeVVVaVRUZW5ib1dGcmllTGIvUWNXYXFTdkltb0ZhTGd2MWFsU25PMmVPUlZ1Q2cxY0dzdXJuK0JIL2FkekkzcEduV1FqdHdhVE95c3lETnNJR1dlWXdQc0VUWHNFTFBaSXl0SXh5TUkiLCJtYWMiOiI2YTFkZmQxYTRlOGRmOWFhZGViNTIzMWViMDk5NTM1NGNhNmRmYTE5Zjc1MWM1ZTlkZjAwMzM4YjRjZDQyNDY3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1qUVplV294WStabkNublg4UVBpekE9PSIsInZhbHVlIjoiUWE3TlNuTUpsUzlNeXBpVERzYlNabmNRdlg5bnNiejJlQkZ6VXQ4Qzh1dEgvUCsram5RNUxBdFZ6azZhY2ZTWS9GSGZSQXZ5bW5iUE91YkVCcGY5SnEwY2o0aFJzQ0czUXNPRWVpNWp4REcwRXZROGUzT3lUemI2RWUrbTMzRG4wZmZFV3dUSFh5d0k1NUZuMSs1T0hmSElJaWEwQkptZmhVckIvR253ZGFYRHh2Y000TnhIZUJ2WGNkQU9BaXhPRUFob1kxSGtwU0gwTDFzVjZXTGFXSjdmbCtZaG1qbXlDOHpnS1ZaSkVwYzRHTndXMnl0RW95RHZPMVlra2ZHUktRWm9GNDlBaVdSbXlDamtRUlM1Qy9JdW42WVZ4R3h1ODUvQXdvU0tHb3kwcWVyMTRHWlVNNzlvUkpTL204VEtiTEk1cmZZdWJJczlsNitiUEwwaTlTUVYvRHVhYkluZjZRdWExcnh1ZldxRFpNVXZ6QXJkRVhvanV6WURWcGV3bmV5N295QU1jY0VjTXA2bm9DdDB2eGR5Tmo2b2x3OEV5ekN4YWF0eC83QXJMOThVajZSZ0d4MmtCbnNNNUg5MG5laDE4RGJXLzdLczRPcVVpcVplQW1ReUFFYisxejd0L0RzZERRY0VlYjlLK05Qb1ozNEUzejc1NGtVN3pNZkwiLCJtYWMiOiJhMGY0NzgzNmUyYmNlMzVjMzQ4YTNlMDI0YTRjYWU2YzQxODgwNGQxODNhOTUzY2FhZTdmOTcxZjRjMTQzYjdiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163390963\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1578550652 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578550652\", {\"maxDepth\":0})</script>\n"}}