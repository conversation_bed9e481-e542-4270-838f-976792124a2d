{"__meta": {"id": "X6ddc360d839f1fd9135e45bf33c05c48", "datetime": "2025-06-26 23:22:58", "utime": **********.222555, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750980177.72864, "end": **********.222569, "duration": 0.4939289093017578, "duration_str": "494ms", "measures": [{"label": "Booting", "start": 1750980177.72864, "relative_start": 0, "end": **********.153586, "relative_end": **********.153586, "duration": 0.4249458312988281, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.153595, "relative_start": 0.424954891204834, "end": **********.222571, "relative_end": 1.9073486328125e-06, "duration": 0.06897592544555664, "duration_str": "68.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042904, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02422, "accumulated_duration_str": "24.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.178343, "duration": 0.02336, "duration_str": "23.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.449}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.210107, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.449, "width_percent": 1.982}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.215672, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.431, "width_percent": 1.569}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Ijh0L3BWSXJldWNSUEp3NTQ1Vk56MVE9PSIsInZhbHVlIjoiMEZKeFVVWGZwSnBMZFpkNnEwUU5Udz09IiwibWFjIjoiYzBjYTE1ZjMzZWVhOTEzYjlmYjYwNzM3NDM2ODcyYzVhMGYyYTg2YmQ2OWE5MGJlMGMzZjdjMWVkYmNjZWQ5MCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-501275432 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-501275432\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-268342549 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-268342549\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-75654275 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75654275\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-814846252 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6Ijh0L3BWSXJldWNSUEp3NTQ1Vk56MVE9PSIsInZhbHVlIjoiMEZKeFVVWGZwSnBMZFpkNnEwUU5Udz09IiwibWFjIjoiYzBjYTE1ZjMzZWVhOTEzYjlmYjYwNzM3NDM2ODcyYzVhMGYyYTg2YmQ2OWE5MGJlMGMzZjdjMWVkYmNjZWQ5MCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1ap6d1q%7C2%7Cfx3%7C0%7C1998; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clsk=8maz9g%7C1750980169650%7C31%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9wM280OWxIUHJQVmVPQ2UzTGdsa3c9PSIsInZhbHVlIjoiUWplTEpYUVVLcExLYWJETzZucHltZW1QekVJR3ZzdmprYnM3cTdibjV0NExBSGJvTjF4SHVMS3U4SXF4OFJKcTg3NGw2YzEwYWxQeU9nV0hFYm41VTNPVVRZMERiS2dxb3YrOE9RckpDalo1Z1h6Zyt1b1UvVUp1Z3lrUGxWYUpRTlRyc1pnY3ZxdzY5dFkxVVVQbXNLNG1JYnhWd25RY0Q5MTBWK0tQWTdxS0doNVY5eUU2QWU0VmFNZjlqL252OFhXTlhTZHNvdmY1VnV1U29UZmtyTGtuRldUZzdWZEZoU0l2TnVVS1lERGZ0SU9ialVvSjQwVE55UjRpNC9EdWtUWXNwMW9IZGNtc3plT01PWkRKUS9GUEtvYnNTQU5iWlp3TTFKMnlXS0Rpc2FVSXhMWk1UTXo3SDJCMEJROVgyVDJlbzJBcVBsRkRUdm40aGduN0xTWXZRdEE3NWtWd215U1liL2syWnlqTWM0eitSQitsaXVDdTA3VmJ5M1FYZUJCR0FpTFBwRDVEZHhRb0xOOEdtanoyTVdsbUpvZ2pJbng0c2hYUVY3MVJ0V3RwUy9ndzcwZHNMQ3dBem05T3lNTDNTR0Mzb21BNkdmNUIrRUZGaFN3eGZFNERYR0VWYTM1TWloRC9vSitvSkdNWHduSGtmcktSZTJBdExpQW8iLCJtYWMiOiJhYmVhMWIwNjk4ODY2OGUxYTQ4MjdhZmZhZTY3MTRkNWJjN2U2NDk2ZDgzMTBlNDY4ZmZlZjhhNGRlYzdmN2IwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZ2dXR5VERmTERoLzNmeHYyd3IwU2c9PSIsInZhbHVlIjoiUzVEcEJ5Ti81TTlkZ1R2Nm9lK2VSbldSOXVPcWpoYlZ1OVVleWYvV2JrYlZrZXNjSUpaOFhrYTJkM0tYNjlORmlWRy9ZK3JlSkNPYUJYckNsdEI1dnhteldxc3hpSDUzOElUKzRXcjVobm1aUFd1NGc1NmQyTU50SDlOdGFhT2VYalBpbEZIYjhxczF0bjQ4UTVLRnFhTlpTRDh3dFNzMXNaMitQY0xiK0ovMVk3UTlZMzAwbnJ4dlhoTDZ2QU8xS2dpbDJvWUEwcDZiV0RrdkJIMmdzQy9ZSmdWQmhRNGpnMy8xZjVZZ0FiMTlDY1QzN1NPSk9vY0hsRzFvTWNOUmR2UUlRUWpxaVBteVFQSnQ0SDV0K0YwUlZuU2NoZEhQY2d1UkluVGpEOEtXV1BYSjJFV1MzM1dLNHllM2F2ZnNCZ3JPQTc3T3dNMjc4MlBndTVmVitkcjhxamR3YjdBNDBGRktCVitLdTNyS1BSMzBYRUVudjhSci9PSE9wUERKamhWUFBrVGZqa3d4NTl6UVFwNGQ5L2dHTDYwR29HejR2aU5PZDBNanhIS0FMZWpjWFpWUW1VQ2RBbEtRYXY4bUpQN2tKTW1ELzlCRm41V1ozeDZhb3dyeHlzZ3lzQzk4NWF2NFNEeE1sampVeHhSMFJqRkhaT1E4RlBuMkdNQWIiLCJtYWMiOiI0MzI4YTMxOGY3YThmOTQ2YmE5NTI1NTQxZTcyYzc3YjY0Y2Q3ZDI2MDhlZjUyMTZlMzAwMWZhYTBiY2VjNmU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814846252\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1069798921 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xMmxSm6qP6bYN6vvhBWLmH9YyH67TYYh36N3Aoxs</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1069798921\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1791052836 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 23:22:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBUVk9GSmpxU0cveEg4MVBUR2xRSXc9PSIsInZhbHVlIjoiam1JSFd5NlZ4TTU3QjhGV3NqbjVqRHVJN1IybXBpOHdXTFZFZWNzYnFIcGhpcGhaWWlzdG1oSXJ0S3JHV3k4Rk9FbVFYNHlXQ202ejhQNXdISW83VVF6dkgrUFhhZGdSY21Ia0hvam1EZHNlTVJjemhDYms5S3NHdDFyRGxjeDJZMVBEMGFDNXFFcUV4dlpLL0hrVGI4VHp5a1k3T1lkZGZ0L3JNQitmZkxJVG5YdjR4ZWtaWDZJaDhFOFZIK2xoZnpWU2pQUWh3RFNya0JRdWNWM0daT1VjbkY4Unh5YlVBTHJ5MnI0Z2xZQjArbDVBZGs1YlREalVqSEVWSWtVaU1xM1d4TnBwcWhLbFpteitvZTdON2dUTjNGYnRxRi8wOVNIOU1VNTA3L1hLNmRBaDYvZXlieStsalkvb2ltb1MzTEFaZHZJbmw1ZTc4ZXJIOFAwZVk5UHB6YmU0SVJsaG5mTFJQL0VzREtzV2NuWjZSV1J1d1diWEt2SkNhbWh2MWRXdm1wUVc1OE9pQ2tDNWhvZTZnYmcwQm9mTExZNWQ3VjFDNTMyTGdieUptUHlMKzNHTGJ1R2JZYWRZNGowSlRBMnBDS1lzaWEzVkx4MVVmUi9uN2dhYU9zU3VPbllZQjdyajA4STVzL2liOFE1N0RKSisvdzNQSXhrTWdOQWYiLCJtYWMiOiJjODhkYTljNjZlMWFkYmVjM2ZiODNkMGIxMjVlMGFkMDFlM2UxMmNiOGIwZDk2NmIwYzg2MWE0MzgwNGQ1MmEzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRPOXYxZXJjSS9jMnlsZXh0RnJWQmc9PSIsInZhbHVlIjoiQy9IdGNOSEQ4cWlpWU1FWTRaYmRqYzU2L09WYVhhQU4rckNQeWhvdzVaaG01ZitrT2NDWWYvRHovaFRISHlod1RMV0Q0ZGxpNzlxKzlqMStVa1ZaMmlRdEVialdXdXNWMjVQK09HdlFWNXZPTnRPdWNwa0ZsQkVvOTRoN2xad05OMGg0eDhqT0ZLakZ1OCtwTUdTZGo5VDNCQ09udDh1MXNRQ1U0eDVHTEErelh3VW84aHo4ZEE4QzZJVzJrOVQxMFdEMHY3STY2YkhraS9ka0tJUlM3dmFnd0Q5clhwM0tPUGJ0cVp0RmFENDUrMXN6Y25JOWFzeDU4Z2tXQUcyaEVHLzFwWG44YVVkOUdZczRvQnIyRHh1NWZLSDN5djJRc3FGVVE1VUZqL3RoSDRMTkN3T3BFY1lXMVNGdGU3Ymdacy8vNU9na094TzZ2RStCWFB1Y2piMlFkSXV2b3ltUUVSQ05Ca3BzNjZxdGcxWmNxRWF3SmRyY21PU1RMVHFEUmxYcENDaTNDRHloZHBVR2VsUkJZaW1UbTBHUzlQQjc5aVlYMmc2WjNteFV0ZElQd0pZeDJUeUZtMmhTM3VTWk5OaEpGMnZld0R6Y2cyTlJGRHN1Ulo0TVJUSXNIc3c3aytBdVBMWm11dmNnTlF1ZDVvQUlQaExMTFZ5YVFWOHMiLCJtYWMiOiIzZThhZjU5ZjI4NTNhYTU4OWU5MTkyMzA3ZTEwNWYzODA3YmI0OTg3NmI1YzU3YWQwNjM0MzFjZWM4NTVjYTJlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 01:22:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBUVk9GSmpxU0cveEg4MVBUR2xRSXc9PSIsInZhbHVlIjoiam1JSFd5NlZ4TTU3QjhGV3NqbjVqRHVJN1IybXBpOHdXTFZFZWNzYnFIcGhpcGhaWWlzdG1oSXJ0S3JHV3k4Rk9FbVFYNHlXQ202ejhQNXdISW83VVF6dkgrUFhhZGdSY21Ia0hvam1EZHNlTVJjemhDYms5S3NHdDFyRGxjeDJZMVBEMGFDNXFFcUV4dlpLL0hrVGI4VHp5a1k3T1lkZGZ0L3JNQitmZkxJVG5YdjR4ZWtaWDZJaDhFOFZIK2xoZnpWU2pQUWh3RFNya0JRdWNWM0daT1VjbkY4Unh5YlVBTHJ5MnI0Z2xZQjArbDVBZGs1YlREalVqSEVWSWtVaU1xM1d4TnBwcWhLbFpteitvZTdON2dUTjNGYnRxRi8wOVNIOU1VNTA3L1hLNmRBaDYvZXlieStsalkvb2ltb1MzTEFaZHZJbmw1ZTc4ZXJIOFAwZVk5UHB6YmU0SVJsaG5mTFJQL0VzREtzV2NuWjZSV1J1d1diWEt2SkNhbWh2MWRXdm1wUVc1OE9pQ2tDNWhvZTZnYmcwQm9mTExZNWQ3VjFDNTMyTGdieUptUHlMKzNHTGJ1R2JZYWRZNGowSlRBMnBDS1lzaWEzVkx4MVVmUi9uN2dhYU9zU3VPbllZQjdyajA4STVzL2liOFE1N0RKSisvdzNQSXhrTWdOQWYiLCJtYWMiOiJjODhkYTljNjZlMWFkYmVjM2ZiODNkMGIxMjVlMGFkMDFlM2UxMmNiOGIwZDk2NmIwYzg2MWE0MzgwNGQ1MmEzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRPOXYxZXJjSS9jMnlsZXh0RnJWQmc9PSIsInZhbHVlIjoiQy9IdGNOSEQ4cWlpWU1FWTRaYmRqYzU2L09WYVhhQU4rckNQeWhvdzVaaG01ZitrT2NDWWYvRHovaFRISHlod1RMV0Q0ZGxpNzlxKzlqMStVa1ZaMmlRdEVialdXdXNWMjVQK09HdlFWNXZPTnRPdWNwa0ZsQkVvOTRoN2xad05OMGg0eDhqT0ZLakZ1OCtwTUdTZGo5VDNCQ09udDh1MXNRQ1U0eDVHTEErelh3VW84aHo4ZEE4QzZJVzJrOVQxMFdEMHY3STY2YkhraS9ka0tJUlM3dmFnd0Q5clhwM0tPUGJ0cVp0RmFENDUrMXN6Y25JOWFzeDU4Z2tXQUcyaEVHLzFwWG44YVVkOUdZczRvQnIyRHh1NWZLSDN5djJRc3FGVVE1VUZqL3RoSDRMTkN3T3BFY1lXMVNGdGU3Ymdacy8vNU9na094TzZ2RStCWFB1Y2piMlFkSXV2b3ltUUVSQ05Ca3BzNjZxdGcxWmNxRWF3SmRyY21PU1RMVHFEUmxYcENDaTNDRHloZHBVR2VsUkJZaW1UbTBHUzlQQjc5aVlYMmc2WjNteFV0ZElQd0pZeDJUeUZtMmhTM3VTWk5OaEpGMnZld0R6Y2cyTlJGRHN1Ulo0TVJUSXNIc3c3aytBdVBMWm11dmNnTlF1ZDVvQUlQaExMTFZ5YVFWOHMiLCJtYWMiOiIzZThhZjU5ZjI4NTNhYTU4OWU5MTkyMzA3ZTEwNWYzODA3YmI0OTg3NmI1YzU3YWQwNjM0MzFjZWM4NTVjYTJlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 01:22:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1791052836\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-268627265 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">T6fupZNsxjBPTMwyphVrbpPjZypcUvZCA1S8Xrp1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6Ijh0L3BWSXJldWNSUEp3NTQ1Vk56MVE9PSIsInZhbHVlIjoiMEZKeFVVWGZwSnBMZFpkNnEwUU5Udz09IiwibWFjIjoiYzBjYTE1ZjMzZWVhOTEzYjlmYjYwNzM3NDM2ODcyYzVhMGYyYTg2YmQ2OWE5MGJlMGMzZjdjMWVkYmNjZWQ5MCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-268627265\", {\"maxDepth\":0})</script>\n"}}