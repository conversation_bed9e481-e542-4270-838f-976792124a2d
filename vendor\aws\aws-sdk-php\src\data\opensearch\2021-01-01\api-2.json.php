<?php
// This file was auto-generated from sdk-root/src/data/opensearch/2021-01-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-01-01', 'endpointPrefix' => 'es', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon OpenSearch Service', 'serviceId' => 'OpenSearch', 'signatureVersion' => 'v4', 'uid' => 'opensearch-2021-01-01', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AcceptInboundConnection' => [ 'name' => 'AcceptInboundConnection', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2021-01-01/opensearch/cc/inboundConnection/{ConnectionId}/accept', ], 'input' => [ 'shape' => 'AcceptInboundConnectionRequest', ], 'output' => [ 'shape' => 'AcceptInboundConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'AddDataSource' => [ 'name' => 'AddDataSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/dataSource', ], 'input' => [ 'shape' => 'AddDataSourceRequest', ], 'output' => [ 'shape' => 'AddDataSourceResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'DependencyFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'AddTags' => [ 'name' => 'AddTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/tags', ], 'input' => [ 'shape' => 'AddTagsRequest', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'AssociatePackage' => [ 'name' => 'AssociatePackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/packages/associate/{PackageID}/{DomainName}', ], 'input' => [ 'shape' => 'AssociatePackageRequest', ], 'output' => [ 'shape' => 'AssociatePackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'AuthorizeVpcEndpointAccess' => [ 'name' => 'AuthorizeVpcEndpointAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/authorizeVpcEndpointAccess', ], 'input' => [ 'shape' => 'AuthorizeVpcEndpointAccessRequest', ], 'output' => [ 'shape' => 'AuthorizeVpcEndpointAccessResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'BaseException', ], ], ], 'CancelDomainConfigChange' => [ 'name' => 'CancelDomainConfigChange', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/config/cancel', ], 'input' => [ 'shape' => 'CancelDomainConfigChangeRequest', ], 'output' => [ 'shape' => 'CancelDomainConfigChangeResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'CancelServiceSoftwareUpdate' => [ 'name' => 'CancelServiceSoftwareUpdate', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/serviceSoftwareUpdate/cancel', ], 'input' => [ 'shape' => 'CancelServiceSoftwareUpdateRequest', ], 'output' => [ 'shape' => 'CancelServiceSoftwareUpdateResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateDomain' => [ 'name' => 'CreateDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/domain', ], 'input' => [ 'shape' => 'CreateDomainRequest', ], 'output' => [ 'shape' => 'CreateDomainResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidTypeException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateOutboundConnection' => [ 'name' => 'CreateOutboundConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/cc/outboundConnection', ], 'input' => [ 'shape' => 'CreateOutboundConnectionRequest', ], 'output' => [ 'shape' => 'CreateOutboundConnectionResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'CreatePackage' => [ 'name' => 'CreatePackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/packages', ], 'input' => [ 'shape' => 'CreatePackageRequest', ], 'output' => [ 'shape' => 'CreatePackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidTypeException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateVpcEndpoint' => [ 'name' => 'CreateVpcEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/vpcEndpoints', ], 'input' => [ 'shape' => 'CreateVpcEndpointRequest', ], 'output' => [ 'shape' => 'CreateVpcEndpointResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'BaseException', ], ], ], 'DeleteDataSource' => [ 'name' => 'DeleteDataSource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/dataSource/{DataSourceName}', ], 'input' => [ 'shape' => 'DeleteDataSourceRequest', ], 'output' => [ 'shape' => 'DeleteDataSourceResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'DependencyFailureException', ], ], ], 'DeleteDomain' => [ 'name' => 'DeleteDomain', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}', ], 'input' => [ 'shape' => 'DeleteDomainRequest', ], 'output' => [ 'shape' => 'DeleteDomainResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteInboundConnection' => [ 'name' => 'DeleteInboundConnection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2021-01-01/opensearch/cc/inboundConnection/{ConnectionId}', ], 'input' => [ 'shape' => 'DeleteInboundConnectionRequest', ], 'output' => [ 'shape' => 'DeleteInboundConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DeleteOutboundConnection' => [ 'name' => 'DeleteOutboundConnection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2021-01-01/opensearch/cc/outboundConnection/{ConnectionId}', ], 'input' => [ 'shape' => 'DeleteOutboundConnectionRequest', ], 'output' => [ 'shape' => 'DeleteOutboundConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DeletePackage' => [ 'name' => 'DeletePackage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2021-01-01/packages/{PackageID}', ], 'input' => [ 'shape' => 'DeletePackageRequest', ], 'output' => [ 'shape' => 'DeletePackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteVpcEndpoint' => [ 'name' => 'DeleteVpcEndpoint', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2021-01-01/opensearch/vpcEndpoints/{VpcEndpointId}', ], 'input' => [ 'shape' => 'DeleteVpcEndpointRequest', ], 'output' => [ 'shape' => 'DeleteVpcEndpointResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'BaseException', ], ], ], 'DescribeDomain' => [ 'name' => 'DescribeDomain', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}', ], 'input' => [ 'shape' => 'DescribeDomainRequest', ], 'output' => [ 'shape' => 'DescribeDomainResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeDomainAutoTunes' => [ 'name' => 'DescribeDomainAutoTunes', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/autoTunes', ], 'input' => [ 'shape' => 'DescribeDomainAutoTunesRequest', ], 'output' => [ 'shape' => 'DescribeDomainAutoTunesResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeDomainChangeProgress' => [ 'name' => 'DescribeDomainChangeProgress', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/progress', ], 'input' => [ 'shape' => 'DescribeDomainChangeProgressRequest', ], 'output' => [ 'shape' => 'DescribeDomainChangeProgressResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeDomainConfig' => [ 'name' => 'DescribeDomainConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/config', ], 'input' => [ 'shape' => 'DescribeDomainConfigRequest', ], 'output' => [ 'shape' => 'DescribeDomainConfigResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeDomainHealth' => [ 'name' => 'DescribeDomainHealth', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/health', ], 'input' => [ 'shape' => 'DescribeDomainHealthRequest', ], 'output' => [ 'shape' => 'DescribeDomainHealthResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DescribeDomainNodes' => [ 'name' => 'DescribeDomainNodes', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/nodes', ], 'input' => [ 'shape' => 'DescribeDomainNodesRequest', ], 'output' => [ 'shape' => 'DescribeDomainNodesResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'DependencyFailureException', ], ], ], 'DescribeDomains' => [ 'name' => 'DescribeDomains', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/domain-info', ], 'input' => [ 'shape' => 'DescribeDomainsRequest', ], 'output' => [ 'shape' => 'DescribeDomainsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeDryRunProgress' => [ 'name' => 'DescribeDryRunProgress', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/dryRun', ], 'input' => [ 'shape' => 'DescribeDryRunProgressRequest', ], 'output' => [ 'shape' => 'DescribeDryRunProgressResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DescribeInboundConnections' => [ 'name' => 'DescribeInboundConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/cc/inboundConnection/search', ], 'input' => [ 'shape' => 'DescribeInboundConnectionsRequest', ], 'output' => [ 'shape' => 'DescribeInboundConnectionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DescribeInstanceTypeLimits' => [ 'name' => 'DescribeInstanceTypeLimits', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/instanceTypeLimits/{EngineVersion}/{InstanceType}', ], 'input' => [ 'shape' => 'DescribeInstanceTypeLimitsRequest', ], 'output' => [ 'shape' => 'DescribeInstanceTypeLimitsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidTypeException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeOutboundConnections' => [ 'name' => 'DescribeOutboundConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/cc/outboundConnection/search', ], 'input' => [ 'shape' => 'DescribeOutboundConnectionsRequest', ], 'output' => [ 'shape' => 'DescribeOutboundConnectionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DescribePackages' => [ 'name' => 'DescribePackages', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/packages/describe', ], 'input' => [ 'shape' => 'DescribePackagesRequest', ], 'output' => [ 'shape' => 'DescribePackagesResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeReservedInstanceOfferings' => [ 'name' => 'DescribeReservedInstanceOfferings', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/reservedInstanceOfferings', ], 'input' => [ 'shape' => 'DescribeReservedInstanceOfferingsRequest', ], 'output' => [ 'shape' => 'DescribeReservedInstanceOfferingsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], ], ], 'DescribeReservedInstances' => [ 'name' => 'DescribeReservedInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/reservedInstances', ], 'input' => [ 'shape' => 'DescribeReservedInstancesRequest', ], 'output' => [ 'shape' => 'DescribeReservedInstancesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'DescribeVpcEndpoints' => [ 'name' => 'DescribeVpcEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/vpcEndpoints/describe', ], 'input' => [ 'shape' => 'DescribeVpcEndpointsRequest', ], 'output' => [ 'shape' => 'DescribeVpcEndpointsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'BaseException', ], ], ], 'DissociatePackage' => [ 'name' => 'DissociatePackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/packages/dissociate/{PackageID}/{DomainName}', ], 'input' => [ 'shape' => 'DissociatePackageRequest', ], 'output' => [ 'shape' => 'DissociatePackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetCompatibleVersions' => [ 'name' => 'GetCompatibleVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/compatibleVersions', ], 'input' => [ 'shape' => 'GetCompatibleVersionsRequest', ], 'output' => [ 'shape' => 'GetCompatibleVersionsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'GetDataSource' => [ 'name' => 'GetDataSource', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/dataSource/{DataSourceName}', ], 'input' => [ 'shape' => 'GetDataSourceRequest', ], 'output' => [ 'shape' => 'GetDataSourceResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'DependencyFailureException', ], ], ], 'GetDomainMaintenanceStatus' => [ 'name' => 'GetDomainMaintenanceStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/domainMaintenance', ], 'input' => [ 'shape' => 'GetDomainMaintenanceStatusRequest', ], 'output' => [ 'shape' => 'GetDomainMaintenanceStatusResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'GetPackageVersionHistory' => [ 'name' => 'GetPackageVersionHistory', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/packages/{PackageID}/history', ], 'input' => [ 'shape' => 'GetPackageVersionHistoryRequest', ], 'output' => [ 'shape' => 'GetPackageVersionHistoryResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetUpgradeHistory' => [ 'name' => 'GetUpgradeHistory', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/upgradeDomain/{DomainName}/history', ], 'input' => [ 'shape' => 'GetUpgradeHistoryRequest', ], 'output' => [ 'shape' => 'GetUpgradeHistoryResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'GetUpgradeStatus' => [ 'name' => 'GetUpgradeStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/upgradeDomain/{DomainName}/status', ], 'input' => [ 'shape' => 'GetUpgradeStatusRequest', ], 'output' => [ 'shape' => 'GetUpgradeStatusResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'ListDataSources' => [ 'name' => 'ListDataSources', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/dataSource', ], 'input' => [ 'shape' => 'ListDataSourcesRequest', ], 'output' => [ 'shape' => 'ListDataSourcesResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'DependencyFailureException', ], ], ], 'ListDomainMaintenances' => [ 'name' => 'ListDomainMaintenances', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/domainMaintenances', ], 'input' => [ 'shape' => 'ListDomainMaintenancesRequest', ], 'output' => [ 'shape' => 'ListDomainMaintenancesResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'ListDomainNames' => [ 'name' => 'ListDomainNames', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/domain', ], 'input' => [ 'shape' => 'ListDomainNamesRequest', ], 'output' => [ 'shape' => 'ListDomainNamesResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListDomainsForPackage' => [ 'name' => 'ListDomainsForPackage', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/packages/{PackageID}/domains', ], 'input' => [ 'shape' => 'ListDomainsForPackageRequest', ], 'output' => [ 'shape' => 'ListDomainsForPackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListInstanceTypeDetails' => [ 'name' => 'ListInstanceTypeDetails', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/instanceTypeDetails/{EngineVersion}', ], 'input' => [ 'shape' => 'ListInstanceTypeDetailsRequest', ], 'output' => [ 'shape' => 'ListInstanceTypeDetailsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListPackagesForDomain' => [ 'name' => 'ListPackagesForDomain', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/domain/{DomainName}/packages', ], 'input' => [ 'shape' => 'ListPackagesForDomainRequest', ], 'output' => [ 'shape' => 'ListPackagesForDomainResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListScheduledActions' => [ 'name' => 'ListScheduledActions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/scheduledActions', ], 'input' => [ 'shape' => 'ListScheduledActionsRequest', ], 'output' => [ 'shape' => 'ListScheduledActionsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidPaginationTokenException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTags' => [ 'name' => 'ListTags', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/tags/', ], 'input' => [ 'shape' => 'ListTagsRequest', ], 'output' => [ 'shape' => 'ListTagsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'ListVersions' => [ 'name' => 'ListVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/versions', ], 'input' => [ 'shape' => 'ListVersionsRequest', ], 'output' => [ 'shape' => 'ListVersionsResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListVpcEndpointAccess' => [ 'name' => 'ListVpcEndpointAccess', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/listVpcEndpointAccess', ], 'input' => [ 'shape' => 'ListVpcEndpointAccessRequest', ], 'output' => [ 'shape' => 'ListVpcEndpointAccessResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'BaseException', ], ], ], 'ListVpcEndpoints' => [ 'name' => 'ListVpcEndpoints', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/vpcEndpoints', ], 'input' => [ 'shape' => 'ListVpcEndpointsRequest', ], 'output' => [ 'shape' => 'ListVpcEndpointsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'BaseException', ], ], ], 'ListVpcEndpointsForDomain' => [ 'name' => 'ListVpcEndpointsForDomain', 'http' => [ 'method' => 'GET', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/vpcEndpoints', ], 'input' => [ 'shape' => 'ListVpcEndpointsForDomainRequest', ], 'output' => [ 'shape' => 'ListVpcEndpointsForDomainResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'BaseException', ], ], ], 'PurchaseReservedInstanceOffering' => [ 'name' => 'PurchaseReservedInstanceOffering', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/purchaseReservedInstanceOffering', ], 'input' => [ 'shape' => 'PurchaseReservedInstanceOfferingRequest', ], 'output' => [ 'shape' => 'PurchaseReservedInstanceOfferingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'RejectInboundConnection' => [ 'name' => 'RejectInboundConnection', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2021-01-01/opensearch/cc/inboundConnection/{ConnectionId}/reject', ], 'input' => [ 'shape' => 'RejectInboundConnectionRequest', ], 'output' => [ 'shape' => 'RejectInboundConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'RemoveTags' => [ 'name' => 'RemoveTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/tags-removal', ], 'input' => [ 'shape' => 'RemoveTagsRequest', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], 'RevokeVpcEndpointAccess' => [ 'name' => 'RevokeVpcEndpointAccess', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/revokeVpcEndpointAccess', ], 'input' => [ 'shape' => 'RevokeVpcEndpointAccessRequest', ], 'output' => [ 'shape' => 'RevokeVpcEndpointAccessResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'BaseException', ], ], ], 'StartDomainMaintenance' => [ 'name' => 'StartDomainMaintenance', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/domainMaintenance', ], 'input' => [ 'shape' => 'StartDomainMaintenanceRequest', ], 'output' => [ 'shape' => 'StartDomainMaintenanceResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], ], ], 'StartServiceSoftwareUpdate' => [ 'name' => 'StartServiceSoftwareUpdate', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/serviceSoftwareUpdate/start', ], 'input' => [ 'shape' => 'StartServiceSoftwareUpdateRequest', ], 'output' => [ 'shape' => 'StartServiceSoftwareUpdateResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateDataSource' => [ 'name' => 'UpdateDataSource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/dataSource/{DataSourceName}', ], 'input' => [ 'shape' => 'UpdateDataSourceRequest', ], 'output' => [ 'shape' => 'UpdateDataSourceResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'DependencyFailureException', ], ], ], 'UpdateDomainConfig' => [ 'name' => 'UpdateDomainConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/config', ], 'input' => [ 'shape' => 'UpdateDomainConfigRequest', ], 'output' => [ 'shape' => 'UpdateDomainConfigResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidTypeException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdatePackage' => [ 'name' => 'UpdatePackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/packages/update', ], 'input' => [ 'shape' => 'UpdatePackageRequest', ], 'output' => [ 'shape' => 'UpdatePackageResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateScheduledAction' => [ 'name' => 'UpdateScheduledAction', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2021-01-01/opensearch/domain/{DomainName}/scheduledAction/update', ], 'input' => [ 'shape' => 'UpdateScheduledActionRequest', ], 'output' => [ 'shape' => 'UpdateScheduledActionResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'SlotNotAvailableException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateVpcEndpoint' => [ 'name' => 'UpdateVpcEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/vpcEndpoints/update', ], 'input' => [ 'shape' => 'UpdateVpcEndpointRequest', ], 'output' => [ 'shape' => 'UpdateVpcEndpointResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'InternalException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'BaseException', ], ], ], 'UpgradeDomain' => [ 'name' => 'UpgradeDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/2021-01-01/opensearch/upgradeDomain', ], 'input' => [ 'shape' => 'UpgradeDomainRequest', ], 'output' => [ 'shape' => 'UpgradeDomainResponse', ], 'errors' => [ [ 'shape' => 'BaseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'DisabledOperationException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalException', ], ], ], ], 'shapes' => [ 'AIMLOptionsInput' => [ 'type' => 'structure', 'members' => [ 'NaturalLanguageQueryGenerationOptions' => [ 'shape' => 'NaturalLanguageQueryGenerationOptionsInput', ], ], ], 'AIMLOptionsOutput' => [ 'type' => 'structure', 'members' => [ 'NaturalLanguageQueryGenerationOptions' => [ 'shape' => 'NaturalLanguageQueryGenerationOptionsOutput', ], ], ], 'AIMLOptionsStatus' => [ 'type' => 'structure', 'members' => [ 'Options' => [ 'shape' => 'AIMLOptionsOutput', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'ARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '.*', ], 'AWSAccount' => [ 'type' => 'string', 'pattern' => '^[0-9]+$', ], 'AWSDomainInformation' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'OwnerId' => [ 'shape' => 'OwnerId', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'Region' => [ 'shape' => 'Region', ], ], ], 'AcceptInboundConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionId', ], 'members' => [ 'ConnectionId' => [ 'shape' => 'ConnectionId', 'location' => 'uri', 'locationName' => 'ConnectionId', ], ], ], 'AcceptInboundConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'InboundConnection', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccessPoliciesStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'PolicyDocument', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'ActionSeverity' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'MEDIUM', 'LOW', ], ], 'ActionStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_UPDATE', 'IN_PROGRESS', 'FAILED', 'COMPLETED', 'NOT_ELIGIBLE', 'ELIGIBLE', ], ], 'ActionType' => [ 'type' => 'string', 'enum' => [ 'SERVICE_SOFTWARE_UPDATE', 'JVM_HEAP_SIZE_TUNING', 'JVM_YOUNG_GEN_TUNING', ], ], 'AddDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Name', 'DataSourceType', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Name' => [ 'shape' => 'DataSourceName', ], 'DataSourceType' => [ 'shape' => 'DataSourceType', ], 'Description' => [ 'shape' => 'DataSourceDescription', ], ], ], 'AddDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], ], 'AddTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ARN', 'TagList', ], 'members' => [ 'ARN' => [ 'shape' => 'ARN', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'AdditionalLimit' => [ 'type' => 'structure', 'members' => [ 'LimitName' => [ 'shape' => 'LimitName', ], 'LimitValues' => [ 'shape' => 'LimitValueList', ], ], ], 'AdditionalLimitList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdditionalLimit', ], ], 'AdvancedOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'AdvancedOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'AdvancedOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'AdvancedSecurityOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'InternalUserDatabaseEnabled' => [ 'shape' => 'Boolean', ], 'SAMLOptions' => [ 'shape' => 'SAMLOptionsOutput', ], 'JWTOptions' => [ 'shape' => 'JWTOptionsOutput', ], 'AnonymousAuthDisableDate' => [ 'shape' => 'DisableTimestamp', ], 'AnonymousAuthEnabled' => [ 'shape' => 'Boolean', ], ], ], 'AdvancedSecurityOptionsInput' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'InternalUserDatabaseEnabled' => [ 'shape' => 'Boolean', ], 'MasterUserOptions' => [ 'shape' => 'MasterUserOptions', ], 'SAMLOptions' => [ 'shape' => 'SAMLOptionsInput', ], 'JWTOptions' => [ 'shape' => 'JWTOptionsInput', ], 'AnonymousAuthEnabled' => [ 'shape' => 'Boolean', ], ], ], 'AdvancedSecurityOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'AdvancedSecurityOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'AssociatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', 'DomainName', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', 'location' => 'uri', 'locationName' => 'PackageID', ], 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'AssociatePackageResponse' => [ 'type' => 'structure', 'members' => [ 'DomainPackageDetails' => [ 'shape' => 'DomainPackageDetails', ], ], ], 'AuthorizeVpcEndpointAccessRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Account', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Account' => [ 'shape' => 'AWSAccount', ], ], ], 'AuthorizeVpcEndpointAccessResponse' => [ 'type' => 'structure', 'required' => [ 'AuthorizedPrincipal', ], 'members' => [ 'AuthorizedPrincipal' => [ 'shape' => 'AuthorizedPrincipal', ], ], ], 'AuthorizedPrincipal' => [ 'type' => 'structure', 'members' => [ 'PrincipalType' => [ 'shape' => 'PrincipalType', ], 'Principal' => [ 'shape' => 'String', ], ], ], 'AuthorizedPrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthorizedPrincipal', ], ], 'AutoTune' => [ 'type' => 'structure', 'members' => [ 'AutoTuneType' => [ 'shape' => 'AutoTuneType', ], 'AutoTuneDetails' => [ 'shape' => 'AutoTuneDetails', ], ], ], 'AutoTuneDate' => [ 'type' => 'timestamp', ], 'AutoTuneDesiredState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AutoTuneDetails' => [ 'type' => 'structure', 'members' => [ 'ScheduledAutoTuneDetails' => [ 'shape' => 'ScheduledAutoTuneDetails', ], ], ], 'AutoTuneList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoTune', ], ], 'AutoTuneMaintenanceSchedule' => [ 'type' => 'structure', 'members' => [ 'StartAt' => [ 'shape' => 'StartAt', ], 'Duration' => [ 'shape' => 'Duration', ], 'CronExpressionForRecurrence' => [ 'shape' => 'String', ], ], ], 'AutoTuneMaintenanceScheduleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoTuneMaintenanceSchedule', ], 'max' => 100, ], 'AutoTuneOptions' => [ 'type' => 'structure', 'members' => [ 'DesiredState' => [ 'shape' => 'AutoTuneDesiredState', ], 'RollbackOnDisable' => [ 'shape' => 'RollbackOnDisable', ], 'MaintenanceSchedules' => [ 'shape' => 'AutoTuneMaintenanceScheduleList', ], 'UseOffPeakWindow' => [ 'shape' => 'Boolean', ], ], ], 'AutoTuneOptionsInput' => [ 'type' => 'structure', 'members' => [ 'DesiredState' => [ 'shape' => 'AutoTuneDesiredState', ], 'MaintenanceSchedules' => [ 'shape' => 'AutoTuneMaintenanceScheduleList', ], 'UseOffPeakWindow' => [ 'shape' => 'Boolean', ], ], ], 'AutoTuneOptionsOutput' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'AutoTuneState', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'UseOffPeakWindow' => [ 'shape' => 'Boolean', ], ], ], 'AutoTuneOptionsStatus' => [ 'type' => 'structure', 'members' => [ 'Options' => [ 'shape' => 'AutoTuneOptions', ], 'Status' => [ 'shape' => 'AutoTuneStatus', ], ], ], 'AutoTuneState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', 'ENABLE_IN_PROGRESS', 'DISABLE_IN_PROGRESS', 'DISABLED_AND_ROLLBACK_SCHEDULED', 'DISABLED_AND_ROLLBACK_IN_PROGRESS', 'DISABLED_AND_ROLLBACK_COMPLETE', 'DISABLED_AND_ROLLBACK_ERROR', 'ERROR', ], ], 'AutoTuneStatus' => [ 'type' => 'structure', 'required' => [ 'CreationDate', 'UpdateDate', 'State', ], 'members' => [ 'CreationDate' => [ 'shape' => 'UpdateTimestamp', ], 'UpdateDate' => [ 'shape' => 'UpdateTimestamp', ], 'UpdateVersion' => [ 'shape' => 'UIntValue', ], 'State' => [ 'shape' => 'AutoTuneState', ], 'ErrorMessage' => [ 'shape' => 'String', ], 'PendingDeletion' => [ 'shape' => 'Boolean', ], ], ], 'AutoTuneType' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED_ACTION', ], ], 'AvailabilityZone' => [ 'type' => 'string', 'max' => 15, 'min' => 1, ], 'AvailabilityZoneInfo' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZoneName' => [ 'shape' => 'AvailabilityZone', ], 'ZoneStatus' => [ 'shape' => 'ZoneStatus', ], 'ConfiguredDataNodeCount' => [ 'shape' => 'NumberOfNodes', ], 'AvailableDataNodeCount' => [ 'shape' => 'NumberOfNodes', ], 'TotalShards' => [ 'shape' => 'NumberOfShards', ], 'TotalUnAssignedShards' => [ 'shape' => 'NumberOfShards', ], ], ], 'AvailabilityZoneInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZoneInfo', ], ], 'AvailabilityZoneList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', ], ], 'BackendRole' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'BaseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Boolean' => [ 'type' => 'boolean', ], 'CancelDomainConfigChangeRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'DryRun' => [ 'shape' => 'DryRun', ], ], ], 'CancelDomainConfigChangeResponse' => [ 'type' => 'structure', 'members' => [ 'CancelledChangeIds' => [ 'shape' => 'GUIDList', ], 'CancelledChangeProperties' => [ 'shape' => 'CancelledChangePropertyList', ], 'DryRun' => [ 'shape' => 'DryRun', ], ], ], 'CancelServiceSoftwareUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], ], ], 'CancelServiceSoftwareUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'ServiceSoftwareOptions' => [ 'shape' => 'ServiceSoftwareOptions', ], ], ], 'CancelledChangeProperty' => [ 'type' => 'structure', 'members' => [ 'PropertyName' => [ 'shape' => 'String', ], 'CancelledValue' => [ 'shape' => 'String', ], 'ActiveValue' => [ 'shape' => 'String', ], ], ], 'CancelledChangePropertyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CancelledChangeProperty', ], ], 'ChangeProgressDetails' => [ 'type' => 'structure', 'members' => [ 'ChangeId' => [ 'shape' => 'GUID', ], 'Message' => [ 'shape' => 'Message', ], 'ConfigChangeStatus' => [ 'shape' => 'ConfigChangeStatus', ], 'InitiatedBy' => [ 'shape' => 'InitiatedBy', ], 'StartTime' => [ 'shape' => 'UpdateTimestamp', ], 'LastUpdatedTime' => [ 'shape' => 'UpdateTimestamp', ], ], ], 'ChangeProgressStage' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ChangeProgressStageName', ], 'Status' => [ 'shape' => 'ChangeProgressStageStatus', ], 'Description' => [ 'shape' => 'Description', ], 'LastUpdated' => [ 'shape' => 'LastUpdated', ], ], ], 'ChangeProgressStageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeProgressStage', ], ], 'ChangeProgressStageName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ChangeProgressStageStatus' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ChangeProgressStatusDetails' => [ 'type' => 'structure', 'members' => [ 'ChangeId' => [ 'shape' => 'GUID', ], 'StartTime' => [ 'shape' => 'UpdateTimestamp', ], 'Status' => [ 'shape' => 'OverallChangeStatus', ], 'PendingProperties' => [ 'shape' => 'StringList', ], 'CompletedProperties' => [ 'shape' => 'StringList', ], 'TotalNumberOfStages' => [ 'shape' => 'TotalNumberOfStages', ], 'ChangeProgressStages' => [ 'shape' => 'ChangeProgressStageList', ], 'LastUpdatedTime' => [ 'shape' => 'UpdateTimestamp', ], 'ConfigChangeStatus' => [ 'shape' => 'ConfigChangeStatus', ], 'InitiatedBy' => [ 'shape' => 'InitiatedBy', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'CloudWatchLogsLogGroupArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '.*', ], 'ClusterConfig' => [ 'type' => 'structure', 'members' => [ 'InstanceType' => [ 'shape' => 'OpenSearchPartitionInstanceType', ], 'InstanceCount' => [ 'shape' => 'IntegerClass', ], 'DedicatedMasterEnabled' => [ 'shape' => 'Boolean', ], 'ZoneAwarenessEnabled' => [ 'shape' => 'Boolean', ], 'ZoneAwarenessConfig' => [ 'shape' => 'ZoneAwarenessConfig', ], 'DedicatedMasterType' => [ 'shape' => 'OpenSearchPartitionInstanceType', ], 'DedicatedMasterCount' => [ 'shape' => 'IntegerClass', ], 'WarmEnabled' => [ 'shape' => 'Boolean', ], 'WarmType' => [ 'shape' => 'OpenSearchWarmPartitionInstanceType', ], 'WarmCount' => [ 'shape' => 'IntegerClass', ], 'ColdStorageOptions' => [ 'shape' => 'ColdStorageOptions', ], 'MultiAZWithStandbyEnabled' => [ 'shape' => 'Boolean', ], ], ], 'ClusterConfigStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'ClusterConfig', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'CognitoOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'UserPoolId' => [ 'shape' => 'UserPoolId', ], 'IdentityPoolId' => [ 'shape' => 'IdentityPoolId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'CognitoOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'CognitoOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'ColdStorageOptions' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'CommitMessage' => [ 'type' => 'string', 'max' => 160, ], 'CompatibleVersionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompatibleVersionsMap', ], ], 'CompatibleVersionsMap' => [ 'type' => 'structure', 'members' => [ 'SourceVersion' => [ 'shape' => 'VersionString', ], 'TargetVersions' => [ 'shape' => 'VersionList', ], ], ], 'ConfigChangeStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'Initializing', 'Validating', 'ValidationFailed', 'ApplyingChanges', 'Completed', 'PendingUserInput', 'Cancelled', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConnectionAlias' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '[a-zA-Z][a-zA-Z0-9\\-\\_]+', ], 'ConnectionId' => [ 'type' => 'string', 'max' => 256, 'min' => 10, 'pattern' => '[a-z][a-z0-9\\-]+', ], 'ConnectionMode' => [ 'type' => 'string', 'enum' => [ 'DIRECT', 'VPC_ENDPOINT', ], ], 'ConnectionProperties' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'Endpoint', ], 'CrossClusterSearch' => [ 'shape' => 'CrossClusterSearchConnectionProperties', ], ], ], 'ConnectionStatusMessage' => [ 'type' => 'string', ], 'CreateDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'EngineVersion' => [ 'shape' => 'VersionString', ], 'ClusterConfig' => [ 'shape' => 'ClusterConfig', ], 'EBSOptions' => [ 'shape' => 'EBSOptions', ], 'AccessPolicies' => [ 'shape' => 'PolicyDocument', ], 'IPAddressType' => [ 'shape' => 'IPAddressType', ], 'SnapshotOptions' => [ 'shape' => 'SnapshotOptions', ], 'VPCOptions' => [ 'shape' => 'VPCOptions', ], 'CognitoOptions' => [ 'shape' => 'CognitoOptions', ], 'EncryptionAtRestOptions' => [ 'shape' => 'EncryptionAtRestOptions', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'NodeToNodeEncryptionOptions', ], 'AdvancedOptions' => [ 'shape' => 'AdvancedOptions', ], 'LogPublishingOptions' => [ 'shape' => 'LogPublishingOptions', ], 'DomainEndpointOptions' => [ 'shape' => 'DomainEndpointOptions', ], 'AdvancedSecurityOptions' => [ 'shape' => 'AdvancedSecurityOptionsInput', ], 'TagList' => [ 'shape' => 'TagList', ], 'AutoTuneOptions' => [ 'shape' => 'AutoTuneOptionsInput', ], 'OffPeakWindowOptions' => [ 'shape' => 'OffPeakWindowOptions', ], 'SoftwareUpdateOptions' => [ 'shape' => 'SoftwareUpdateOptions', ], 'AIMLOptions' => [ 'shape' => 'AIMLOptionsInput', ], ], ], 'CreateDomainResponse' => [ 'type' => 'structure', 'members' => [ 'DomainStatus' => [ 'shape' => 'DomainStatus', ], ], ], 'CreateOutboundConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'LocalDomainInfo', 'RemoteDomainInfo', 'ConnectionAlias', ], 'members' => [ 'LocalDomainInfo' => [ 'shape' => 'DomainInformationContainer', ], 'RemoteDomainInfo' => [ 'shape' => 'DomainInformationContainer', ], 'ConnectionAlias' => [ 'shape' => 'ConnectionAlias', ], 'ConnectionMode' => [ 'shape' => 'ConnectionMode', ], 'ConnectionProperties' => [ 'shape' => 'ConnectionProperties', ], ], ], 'CreateOutboundConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'LocalDomainInfo' => [ 'shape' => 'DomainInformationContainer', ], 'RemoteDomainInfo' => [ 'shape' => 'DomainInformationContainer', ], 'ConnectionAlias' => [ 'shape' => 'ConnectionAlias', ], 'ConnectionStatus' => [ 'shape' => 'OutboundConnectionStatus', ], 'ConnectionId' => [ 'shape' => 'ConnectionId', ], 'ConnectionMode' => [ 'shape' => 'ConnectionMode', ], 'ConnectionProperties' => [ 'shape' => 'ConnectionProperties', ], ], ], 'CreatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageName', 'PackageType', 'PackageSource', ], 'members' => [ 'PackageName' => [ 'shape' => 'PackageName', ], 'PackageType' => [ 'shape' => 'PackageType', ], 'PackageDescription' => [ 'shape' => 'PackageDescription', ], 'PackageSource' => [ 'shape' => 'PackageSource', ], ], ], 'CreatePackageResponse' => [ 'type' => 'structure', 'members' => [ 'PackageDetails' => [ 'shape' => 'PackageDetails', ], ], ], 'CreateVpcEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'DomainArn', 'VpcOptions', ], 'members' => [ 'DomainArn' => [ 'shape' => 'DomainArn', ], 'VpcOptions' => [ 'shape' => 'VPCOptions', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], ], ], 'CreateVpcEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpoint', ], 'members' => [ 'VpcEndpoint' => [ 'shape' => 'VpcEndpoint', ], ], ], 'CreatedAt' => [ 'type' => 'timestamp', ], 'CrossClusterSearchConnectionProperties' => [ 'type' => 'structure', 'members' => [ 'SkipUnavailable' => [ 'shape' => 'SkipUnavailableStatus', ], ], ], 'DataSourceDescription' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '^([a-zA-Z0-9_])*[\\\\a-zA-Z0-9_@#%*+=:?./!\\s-]*$', ], 'DataSourceDetails' => [ 'type' => 'structure', 'members' => [ 'DataSourceType' => [ 'shape' => 'DataSourceType', ], 'Name' => [ 'shape' => 'DataSourceName', ], 'Description' => [ 'shape' => 'DataSourceDescription', ], 'Status' => [ 'shape' => 'DataSourceStatus', ], ], ], 'DataSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSourceDetails', ], ], 'DataSourceName' => [ 'type' => 'string', 'max' => 80, 'min' => 3, 'pattern' => '[a-z][a-z0-9_]+', ], 'DataSourceStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DISABLED', ], ], 'DataSourceType' => [ 'type' => 'structure', 'members' => [ 'S3GlueDataCatalog' => [ 'shape' => 'S3GlueDataCatalog', ], ], 'union' => true, ], 'DeleteDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Name', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Name' => [ 'shape' => 'DataSourceName', 'location' => 'uri', 'locationName' => 'DataSourceName', ], ], ], 'DeleteDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], ], 'DeleteDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DeleteDomainResponse' => [ 'type' => 'structure', 'members' => [ 'DomainStatus' => [ 'shape' => 'DomainStatus', ], ], ], 'DeleteInboundConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionId', ], 'members' => [ 'ConnectionId' => [ 'shape' => 'ConnectionId', 'location' => 'uri', 'locationName' => 'ConnectionId', ], ], ], 'DeleteInboundConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'InboundConnection', ], ], ], 'DeleteOutboundConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionId', ], 'members' => [ 'ConnectionId' => [ 'shape' => 'ConnectionId', 'location' => 'uri', 'locationName' => 'ConnectionId', ], ], ], 'DeleteOutboundConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'OutboundConnection', ], ], ], 'DeletePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', 'location' => 'uri', 'locationName' => 'PackageID', ], ], ], 'DeletePackageResponse' => [ 'type' => 'structure', 'members' => [ 'PackageDetails' => [ 'shape' => 'PackageDetails', ], ], ], 'DeleteVpcEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointId', ], 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', 'location' => 'uri', 'locationName' => 'VpcEndpointId', ], ], ], 'DeleteVpcEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointSummary', ], 'members' => [ 'VpcEndpointSummary' => [ 'shape' => 'VpcEndpointSummary', ], ], ], 'DependencyFailureException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 424, ], 'exception' => true, ], 'DeploymentCloseDateTimeStamp' => [ 'type' => 'timestamp', ], 'DeploymentStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_UPDATE', 'IN_PROGRESS', 'COMPLETED', 'NOT_ELIGIBLE', 'ELIGIBLE', ], ], 'DeploymentType' => [ 'type' => 'string', 'max' => 128, 'min' => 2, ], 'DescribeDomainAutoTunesRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDomainAutoTunesResponse' => [ 'type' => 'structure', 'members' => [ 'AutoTunes' => [ 'shape' => 'AutoTuneList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDomainChangeProgressRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ChangeId' => [ 'shape' => 'GUID', 'location' => 'querystring', 'locationName' => 'changeid', ], ], ], 'DescribeDomainChangeProgressResponse' => [ 'type' => 'structure', 'members' => [ 'ChangeProgressStatus' => [ 'shape' => 'ChangeProgressStatusDetails', ], ], ], 'DescribeDomainConfigRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DescribeDomainConfigResponse' => [ 'type' => 'structure', 'required' => [ 'DomainConfig', ], 'members' => [ 'DomainConfig' => [ 'shape' => 'DomainConfig', ], ], ], 'DescribeDomainHealthRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DescribeDomainHealthResponse' => [ 'type' => 'structure', 'members' => [ 'DomainState' => [ 'shape' => 'DomainState', ], 'AvailabilityZoneCount' => [ 'shape' => 'NumberOfAZs', ], 'ActiveAvailabilityZoneCount' => [ 'shape' => 'NumberOfAZs', ], 'StandByAvailabilityZoneCount' => [ 'shape' => 'NumberOfAZs', ], 'DataNodeCount' => [ 'shape' => 'NumberOfNodes', ], 'DedicatedMaster' => [ 'shape' => 'Boolean', ], 'MasterEligibleNodeCount' => [ 'shape' => 'NumberOfNodes', ], 'WarmNodeCount' => [ 'shape' => 'NumberOfNodes', ], 'MasterNode' => [ 'shape' => 'MasterNodeStatus', ], 'ClusterHealth' => [ 'shape' => 'DomainHealth', ], 'TotalShards' => [ 'shape' => 'NumberOfShards', ], 'TotalUnAssignedShards' => [ 'shape' => 'NumberOfShards', ], 'EnvironmentInformation' => [ 'shape' => 'EnvironmentInfoList', ], ], ], 'DescribeDomainNodesRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DescribeDomainNodesResponse' => [ 'type' => 'structure', 'members' => [ 'DomainNodesStatusList' => [ 'shape' => 'DomainNodesStatusList', ], ], ], 'DescribeDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DescribeDomainResponse' => [ 'type' => 'structure', 'required' => [ 'DomainStatus', ], 'members' => [ 'DomainStatus' => [ 'shape' => 'DomainStatus', ], ], ], 'DescribeDomainsRequest' => [ 'type' => 'structure', 'required' => [ 'DomainNames', ], 'members' => [ 'DomainNames' => [ 'shape' => 'DomainNameList', ], ], ], 'DescribeDomainsResponse' => [ 'type' => 'structure', 'required' => [ 'DomainStatusList', ], 'members' => [ 'DomainStatusList' => [ 'shape' => 'DomainStatusList', ], ], ], 'DescribeDryRunProgressRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'DryRunId' => [ 'shape' => 'GUID', 'location' => 'querystring', 'locationName' => 'dryRunId', ], 'LoadDryRunConfig' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'loadDryRunConfig', ], ], ], 'DescribeDryRunProgressResponse' => [ 'type' => 'structure', 'members' => [ 'DryRunProgressStatus' => [ 'shape' => 'DryRunProgressStatus', ], 'DryRunConfig' => [ 'shape' => 'DomainStatus', ], 'DryRunResults' => [ 'shape' => 'DryRunResults', ], ], ], 'DescribeInboundConnectionsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInboundConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'Connections' => [ 'shape' => 'InboundConnections', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeInstanceTypeLimitsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceType', 'EngineVersion', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domainName', ], 'InstanceType' => [ 'shape' => 'OpenSearchPartitionInstanceType', 'location' => 'uri', 'locationName' => 'InstanceType', ], 'EngineVersion' => [ 'shape' => 'VersionString', 'location' => 'uri', 'locationName' => 'EngineVersion', ], ], ], 'DescribeInstanceTypeLimitsResponse' => [ 'type' => 'structure', 'members' => [ 'LimitsByRole' => [ 'shape' => 'LimitsByRole', ], ], ], 'DescribeOutboundConnectionsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeOutboundConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'Connections' => [ 'shape' => 'OutboundConnections', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePackagesFilter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'DescribePackagesFilterName', ], 'Value' => [ 'shape' => 'DescribePackagesFilterValues', ], ], ], 'DescribePackagesFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribePackagesFilter', ], ], 'DescribePackagesFilterName' => [ 'type' => 'string', 'enum' => [ 'PackageID', 'PackageName', 'PackageStatus', 'PackageType', 'EngineVersion', ], ], 'DescribePackagesFilterValue' => [ 'type' => 'string', 'pattern' => '^[0-9a-zA-Z\\*\\.\\_\\\\\\/\\?-]+$', ], 'DescribePackagesFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribePackagesFilterValue', ], 'min' => 1, ], 'DescribePackagesRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'DescribePackagesFilterList', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePackagesResponse' => [ 'type' => 'structure', 'members' => [ 'PackageDetailsList' => [ 'shape' => 'PackageDetailsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'DescribeReservedInstanceOfferingsRequest' => [ 'type' => 'structure', 'members' => [ 'ReservedInstanceOfferingId' => [ 'shape' => 'GUID', 'location' => 'querystring', 'locationName' => 'offeringId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'DescribeReservedInstanceOfferingsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ReservedInstanceOfferings' => [ 'shape' => 'ReservedInstanceOfferingList', ], ], ], 'DescribeReservedInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'ReservedInstanceId' => [ 'shape' => 'GUID', 'location' => 'querystring', 'locationName' => 'reservationId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'DescribeReservedInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'ReservedInstances' => [ 'shape' => 'ReservedInstanceList', ], ], ], 'DescribeVpcEndpointsRequest' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointIds', ], 'members' => [ 'VpcEndpointIds' => [ 'shape' => 'VpcEndpointIdList', ], ], ], 'DescribeVpcEndpointsResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpoints', 'VpcEndpointErrors', ], 'members' => [ 'VpcEndpoints' => [ 'shape' => 'VpcEndpoints', ], 'VpcEndpointErrors' => [ 'shape' => 'VpcEndpointErrorList', ], ], ], 'Description' => [ 'type' => 'string', ], 'DisableTimestamp' => [ 'type' => 'timestamp', ], 'DisabledOperationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'DissociatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', 'DomainName', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', 'location' => 'uri', 'locationName' => 'PackageID', ], 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'DissociatePackageResponse' => [ 'type' => 'structure', 'members' => [ 'DomainPackageDetails' => [ 'shape' => 'DomainPackageDetails', ], ], ], 'DomainArn' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:[a-z]+:[a-z0-9\\-]+:[0-9]+:domain\\/[a-z0-9\\-]+', ], 'DomainConfig' => [ 'type' => 'structure', 'members' => [ 'EngineVersion' => [ 'shape' => 'VersionStatus', ], 'ClusterConfig' => [ 'shape' => 'ClusterConfigStatus', ], 'EBSOptions' => [ 'shape' => 'EBSOptionsStatus', ], 'AccessPolicies' => [ 'shape' => 'AccessPoliciesStatus', ], 'IPAddressType' => [ 'shape' => 'IPAddressTypeStatus', ], 'SnapshotOptions' => [ 'shape' => 'SnapshotOptionsStatus', ], 'VPCOptions' => [ 'shape' => 'VPCDerivedInfoStatus', ], 'CognitoOptions' => [ 'shape' => 'CognitoOptionsStatus', ], 'EncryptionAtRestOptions' => [ 'shape' => 'EncryptionAtRestOptionsStatus', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'NodeToNodeEncryptionOptionsStatus', ], 'AdvancedOptions' => [ 'shape' => 'AdvancedOptionsStatus', ], 'LogPublishingOptions' => [ 'shape' => 'LogPublishingOptionsStatus', ], 'DomainEndpointOptions' => [ 'shape' => 'DomainEndpointOptionsStatus', ], 'AdvancedSecurityOptions' => [ 'shape' => 'AdvancedSecurityOptionsStatus', ], 'AutoTuneOptions' => [ 'shape' => 'AutoTuneOptionsStatus', ], 'ChangeProgressDetails' => [ 'shape' => 'ChangeProgressDetails', ], 'OffPeakWindowOptions' => [ 'shape' => 'OffPeakWindowOptionsStatus', ], 'SoftwareUpdateOptions' => [ 'shape' => 'SoftwareUpdateOptionsStatus', ], 'ModifyingProperties' => [ 'shape' => 'ModifyingPropertiesList', ], 'AIMLOptions' => [ 'shape' => 'AIMLOptionsStatus', ], ], ], 'DomainEndpointOptions' => [ 'type' => 'structure', 'members' => [ 'EnforceHTTPS' => [ 'shape' => 'Boolean', ], 'TLSSecurityPolicy' => [ 'shape' => 'TLSSecurityPolicy', ], 'CustomEndpointEnabled' => [ 'shape' => 'Boolean', ], 'CustomEndpoint' => [ 'shape' => 'DomainNameFqdn', ], 'CustomEndpointCertificateArn' => [ 'shape' => 'ARN', ], ], ], 'DomainEndpointOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'DomainEndpointOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'DomainHealth' => [ 'type' => 'string', 'enum' => [ 'Red', 'Yellow', 'Green', 'NotAvailable', ], ], 'DomainId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'DomainInfo' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'EngineType' => [ 'shape' => 'EngineType', ], ], ], 'DomainInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainInfo', ], ], 'DomainInformationContainer' => [ 'type' => 'structure', 'members' => [ 'AWSDomainInformation' => [ 'shape' => 'AWSDomainInformation', ], ], ], 'DomainMaintenanceDetails' => [ 'type' => 'structure', 'members' => [ 'MaintenanceId' => [ 'shape' => 'RequestId', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'Action' => [ 'shape' => 'MaintenanceType', ], 'NodeId' => [ 'shape' => 'NodeId', ], 'Status' => [ 'shape' => 'MaintenanceStatus', ], 'StatusMessage' => [ 'shape' => 'MaintenanceStatusMessage', ], 'CreatedAt' => [ 'shape' => 'UpdateTimestamp', ], 'UpdatedAt' => [ 'shape' => 'UpdateTimestamp', ], ], ], 'DomainMaintenanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainMaintenanceDetails', ], ], 'DomainName' => [ 'type' => 'string', 'max' => 28, 'min' => 3, 'pattern' => '[a-z][a-z0-9\\-]+', ], 'DomainNameFqdn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(((?!-)[A-Za-z0-9-]{0,62}[A-Za-z0-9])\\.)+((?!-)[A-Za-z0-9-]{1,62}[A-Za-z0-9])$', ], 'DomainNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainName', ], ], 'DomainNodesStatus' => [ 'type' => 'structure', 'members' => [ 'NodeId' => [ 'shape' => 'NodeId', ], 'NodeType' => [ 'shape' => 'NodeType', ], 'AvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'InstanceType' => [ 'shape' => 'OpenSearchPartitionInstanceType', ], 'NodeStatus' => [ 'shape' => 'NodeStatus', ], 'StorageType' => [ 'shape' => 'StorageTypeName', ], 'StorageVolumeType' => [ 'shape' => 'VolumeType', ], 'StorageSize' => [ 'shape' => 'VolumeSize', ], ], ], 'DomainNodesStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainNodesStatus', ], ], 'DomainPackageDetails' => [ 'type' => 'structure', 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', ], 'PackageName' => [ 'shape' => 'PackageName', ], 'PackageType' => [ 'shape' => 'PackageType', ], 'LastUpdated' => [ 'shape' => 'LastUpdated', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'DomainPackageStatus' => [ 'shape' => 'DomainPackageStatus', ], 'PackageVersion' => [ 'shape' => 'PackageVersion', ], 'ReferencePath' => [ 'shape' => 'ReferencePath', ], 'ErrorDetails' => [ 'shape' => 'ErrorDetails', ], ], ], 'DomainPackageDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainPackageDetails', ], ], 'DomainPackageStatus' => [ 'type' => 'string', 'enum' => [ 'ASSOCIATING', 'ASSOCIATION_FAILED', 'ACTIVE', 'DISSOCIATING', 'DISSOCIATION_FAILED', ], ], 'DomainProcessingStatusType' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Active', 'Modifying', 'UpgradingEngineVersion', 'UpdatingServiceSoftware', 'Isolated', 'Deleting', ], ], 'DomainState' => [ 'type' => 'string', 'enum' => [ 'Active', 'Processing', 'NotAvailable', ], ], 'DomainStatus' => [ 'type' => 'structure', 'required' => [ 'DomainId', 'DomainName', 'ARN', 'ClusterConfig', ], 'members' => [ 'DomainId' => [ 'shape' => 'DomainId', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'ARN' => [ 'shape' => 'ARN', ], 'Created' => [ 'shape' => 'Boolean', ], 'Deleted' => [ 'shape' => 'Boolean', ], 'Endpoint' => [ 'shape' => 'ServiceUrl', ], 'EndpointV2' => [ 'shape' => 'ServiceUrl', ], 'Endpoints' => [ 'shape' => 'EndpointsMap', ], 'DomainEndpointV2HostedZoneId' => [ 'shape' => 'HostedZoneId', ], 'Processing' => [ 'shape' => 'Boolean', ], 'UpgradeProcessing' => [ 'shape' => 'Boolean', ], 'EngineVersion' => [ 'shape' => 'VersionString', ], 'ClusterConfig' => [ 'shape' => 'ClusterConfig', ], 'EBSOptions' => [ 'shape' => 'EBSOptions', ], 'AccessPolicies' => [ 'shape' => 'PolicyDocument', ], 'IPAddressType' => [ 'shape' => 'IPAddressType', ], 'SnapshotOptions' => [ 'shape' => 'SnapshotOptions', ], 'VPCOptions' => [ 'shape' => 'VPCDerivedInfo', ], 'CognitoOptions' => [ 'shape' => 'CognitoOptions', ], 'EncryptionAtRestOptions' => [ 'shape' => 'EncryptionAtRestOptions', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'NodeToNodeEncryptionOptions', ], 'AdvancedOptions' => [ 'shape' => 'AdvancedOptions', ], 'LogPublishingOptions' => [ 'shape' => 'LogPublishingOptions', ], 'ServiceSoftwareOptions' => [ 'shape' => 'ServiceSoftwareOptions', ], 'DomainEndpointOptions' => [ 'shape' => 'DomainEndpointOptions', ], 'AdvancedSecurityOptions' => [ 'shape' => 'AdvancedSecurityOptions', ], 'AutoTuneOptions' => [ 'shape' => 'AutoTuneOptionsOutput', ], 'ChangeProgressDetails' => [ 'shape' => 'ChangeProgressDetails', ], 'OffPeakWindowOptions' => [ 'shape' => 'OffPeakWindowOptions', ], 'SoftwareUpdateOptions' => [ 'shape' => 'SoftwareUpdateOptions', ], 'DomainProcessingStatus' => [ 'shape' => 'DomainProcessingStatusType', ], 'ModifyingProperties' => [ 'shape' => 'ModifyingPropertiesList', ], 'AIMLOptions' => [ 'shape' => 'AIMLOptionsOutput', ], ], ], 'DomainStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainStatus', ], ], 'Double' => [ 'type' => 'double', ], 'DryRun' => [ 'type' => 'boolean', ], 'DryRunMode' => [ 'type' => 'string', 'enum' => [ 'Basic', 'Verbose', ], ], 'DryRunProgressStatus' => [ 'type' => 'structure', 'required' => [ 'DryRunId', 'DryRunStatus', 'CreationDate', 'UpdateDate', ], 'members' => [ 'DryRunId' => [ 'shape' => 'GUID', ], 'DryRunStatus' => [ 'shape' => 'String', ], 'CreationDate' => [ 'shape' => 'String', ], 'UpdateDate' => [ 'shape' => 'String', ], 'ValidationFailures' => [ 'shape' => 'ValidationFailures', ], ], ], 'DryRunResults' => [ 'type' => 'structure', 'members' => [ 'DeploymentType' => [ 'shape' => 'DeploymentType', ], 'Message' => [ 'shape' => 'Message', ], ], ], 'Duration' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'DurationValue', ], 'Unit' => [ 'shape' => 'TimeUnit', ], ], ], 'DurationValue' => [ 'type' => 'long', 'max' => 24, 'min' => 1, ], 'EBSOptions' => [ 'type' => 'structure', 'members' => [ 'EBSEnabled' => [ 'shape' => 'Boolean', ], 'VolumeType' => [ 'shape' => 'VolumeType', ], 'VolumeSize' => [ 'shape' => 'IntegerClass', ], 'Iops' => [ 'shape' => 'IntegerClass', ], 'Throughput' => [ 'shape' => 'IntegerClass', ], ], ], 'EBSOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'EBSOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'EncryptionAtRestOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'EncryptionAtRestOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'EncryptionAtRestOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'Endpoint' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9\\-\\.]+$', ], 'EndpointsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'ServiceUrl', ], ], 'EngineType' => [ 'type' => 'string', 'enum' => [ 'OpenSearch', 'Elasticsearch', ], ], 'EngineVersion' => [ 'type' => 'string', 'pattern' => '^Elasticsearch_[0-9]{1}\\.[0-9]{1,2}$|^OpenSearch_[0-9]{1,2}\\.[0-9]{1,2}$', ], 'EnvironmentInfo' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZoneInformation' => [ 'shape' => 'AvailabilityZoneInfoList', ], ], ], 'EnvironmentInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentInfo', ], ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'ErrorType' => [ 'shape' => 'ErrorType', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ErrorType' => [ 'type' => 'string', ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Values' => [ 'shape' => 'ValueStringList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'GUID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '\\p{XDigit}{8}-\\p{XDigit}{4}-\\p{XDigit}{4}-\\p{XDigit}{4}-\\p{XDigit}{12}', ], 'GUIDList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GUID', ], ], 'GetCompatibleVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domainName', ], ], ], 'GetCompatibleVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'CompatibleVersions' => [ 'shape' => 'CompatibleVersionsList', ], ], ], 'GetDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Name', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Name' => [ 'shape' => 'DataSourceName', 'location' => 'uri', 'locationName' => 'DataSourceName', ], ], ], 'GetDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'DataSourceType' => [ 'shape' => 'DataSourceType', ], 'Name' => [ 'shape' => 'DataSourceName', ], 'Description' => [ 'shape' => 'DataSourceDescription', ], 'Status' => [ 'shape' => 'DataSourceStatus', ], ], ], 'GetDomainMaintenanceStatusRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'MaintenanceId', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'MaintenanceId' => [ 'shape' => 'RequestId', 'location' => 'querystring', 'locationName' => 'maintenanceId', ], ], ], 'GetDomainMaintenanceStatusResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'MaintenanceStatus', ], 'StatusMessage' => [ 'shape' => 'MaintenanceStatusMessage', ], 'NodeId' => [ 'shape' => 'NodeId', ], 'Action' => [ 'shape' => 'MaintenanceType', ], 'CreatedAt' => [ 'shape' => 'UpdateTimestamp', ], 'UpdatedAt' => [ 'shape' => 'UpdateTimestamp', ], ], ], 'GetPackageVersionHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', 'location' => 'uri', 'locationName' => 'PackageID', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetPackageVersionHistoryResponse' => [ 'type' => 'structure', 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', ], 'PackageVersionHistoryList' => [ 'shape' => 'PackageVersionHistoryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetUpgradeHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetUpgradeHistoryResponse' => [ 'type' => 'structure', 'members' => [ 'UpgradeHistories' => [ 'shape' => 'UpgradeHistoryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'GetUpgradeStatusRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'GetUpgradeStatusResponse' => [ 'type' => 'structure', 'members' => [ 'UpgradeStep' => [ 'shape' => 'UpgradeStep', ], 'StepStatus' => [ 'shape' => 'UpgradeStatus', ], 'UpgradeName' => [ 'shape' => 'UpgradeName', ], ], ], 'HostedZoneId' => [ 'type' => 'string', ], 'IPAddressType' => [ 'type' => 'string', 'enum' => [ 'ipv4', 'dualstack', ], ], 'IPAddressTypeStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'IPAddressType', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'IdentityPoolId' => [ 'type' => 'string', 'max' => 55, 'min' => 1, 'pattern' => '[\\w-]+:[0-9a-f-]+', ], 'InboundConnection' => [ 'type' => 'structure', 'members' => [ 'LocalDomainInfo' => [ 'shape' => 'DomainInformationContainer', ], 'RemoteDomainInfo' => [ 'shape' => 'DomainInformationContainer', ], 'ConnectionId' => [ 'shape' => 'ConnectionId', ], 'ConnectionStatus' => [ 'shape' => 'InboundConnectionStatus', ], 'ConnectionMode' => [ 'shape' => 'ConnectionMode', ], ], ], 'InboundConnectionStatus' => [ 'type' => 'structure', 'members' => [ 'StatusCode' => [ 'shape' => 'InboundConnectionStatusCode', ], 'Message' => [ 'shape' => 'ConnectionStatusMessage', ], ], ], 'InboundConnectionStatusCode' => [ 'type' => 'string', 'enum' => [ 'PENDING_ACCEPTANCE', 'APPROVED', 'PROVISIONING', 'ACTIVE', 'REJECTING', 'REJECTED', 'DELETING', 'DELETED', ], ], 'InboundConnections' => [ 'type' => 'list', 'member' => [ 'shape' => 'InboundConnection', ], ], 'InitiatedBy' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER', 'SERVICE', ], ], 'InstanceCount' => [ 'type' => 'integer', 'min' => 1, ], 'InstanceCountLimits' => [ 'type' => 'structure', 'members' => [ 'MinimumInstanceCount' => [ 'shape' => 'MinimumInstanceCount', ], 'MaximumInstanceCount' => [ 'shape' => 'MaximumInstanceCount', ], ], ], 'InstanceLimits' => [ 'type' => 'structure', 'members' => [ 'InstanceCountLimits' => [ 'shape' => 'InstanceCountLimits', ], ], ], 'InstanceRole' => [ 'type' => 'string', ], 'InstanceRoleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceRole', ], ], 'InstanceTypeDetails' => [ 'type' => 'structure', 'members' => [ 'InstanceType' => [ 'shape' => 'OpenSearchPartitionInstanceType', ], 'EncryptionEnabled' => [ 'shape' => 'Boolean', ], 'CognitoEnabled' => [ 'shape' => 'Boolean', ], 'AppLogsEnabled' => [ 'shape' => 'Boolean', ], 'AdvancedSecurityEnabled' => [ 'shape' => 'Boolean', ], 'WarmEnabled' => [ 'shape' => 'Boolean', ], 'InstanceRole' => [ 'shape' => 'InstanceRoleList', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZoneList', ], ], ], 'InstanceTypeDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceTypeDetails', ], ], 'InstanceTypeString' => [ 'type' => 'string', 'max' => 40, 'min' => 10, 'pattern' => '^.*\\..*\\.search$', ], 'Integer' => [ 'type' => 'integer', ], 'IntegerClass' => [ 'type' => 'integer', ], 'InternalException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvalidPaginationTokenException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidTypeException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Issue' => [ 'type' => 'string', ], 'Issues' => [ 'type' => 'list', 'member' => [ 'shape' => 'Issue', ], ], 'JWTOptionsInput' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'SubjectKey' => [ 'shape' => 'SubjectKey', ], 'RolesKey' => [ 'shape' => 'RolesKey', ], 'PublicKey' => [ 'shape' => 'String', ], ], ], 'JWTOptionsOutput' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'SubjectKey' => [ 'shape' => 'String', ], 'RolesKey' => [ 'shape' => 'String', ], 'PublicKey' => [ 'shape' => 'String', ], ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'pattern' => '.*', ], 'LastUpdated' => [ 'type' => 'timestamp', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'LimitName' => [ 'type' => 'string', ], 'LimitValue' => [ 'type' => 'string', ], 'LimitValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LimitValue', ], ], 'Limits' => [ 'type' => 'structure', 'members' => [ 'StorageTypes' => [ 'shape' => 'StorageTypeList', ], 'InstanceLimits' => [ 'shape' => 'InstanceLimits', ], 'AdditionalLimits' => [ 'shape' => 'AdditionalLimitList', ], ], ], 'LimitsByRole' => [ 'type' => 'map', 'key' => [ 'shape' => 'InstanceRole', ], 'value' => [ 'shape' => 'Limits', ], ], 'ListDataSourcesRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], ], ], 'ListDataSourcesResponse' => [ 'type' => 'structure', 'members' => [ 'DataSources' => [ 'shape' => 'DataSourceList', ], ], ], 'ListDomainMaintenancesRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Action' => [ 'shape' => 'MaintenanceType', 'location' => 'querystring', 'locationName' => 'action', ], 'Status' => [ 'shape' => 'MaintenanceStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDomainMaintenancesResponse' => [ 'type' => 'structure', 'members' => [ 'DomainMaintenances' => [ 'shape' => 'DomainMaintenanceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDomainNamesRequest' => [ 'type' => 'structure', 'members' => [ 'EngineType' => [ 'shape' => 'EngineType', 'location' => 'querystring', 'locationName' => 'engineType', ], ], ], 'ListDomainNamesResponse' => [ 'type' => 'structure', 'members' => [ 'DomainNames' => [ 'shape' => 'DomainInfoList', ], ], ], 'ListDomainsForPackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', 'location' => 'uri', 'locationName' => 'PackageID', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListDomainsForPackageResponse' => [ 'type' => 'structure', 'members' => [ 'DomainPackageDetailsList' => [ 'shape' => 'DomainPackageDetailsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListInstanceTypeDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'EngineVersion', ], 'members' => [ 'EngineVersion' => [ 'shape' => 'VersionString', 'location' => 'uri', 'locationName' => 'EngineVersion', ], 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'querystring', 'locationName' => 'domainName', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'RetrieveAZs' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'retrieveAZs', ], 'InstanceType' => [ 'shape' => 'InstanceTypeString', 'location' => 'querystring', 'locationName' => 'instanceType', ], ], ], 'ListInstanceTypeDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'InstanceTypeDetails' => [ 'shape' => 'InstanceTypeDetailsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPackagesForDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPackagesForDomainResponse' => [ 'type' => 'structure', 'members' => [ 'DomainPackageDetailsList' => [ 'shape' => 'DomainPackageDetailsList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListScheduledActionsRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListScheduledActionsResponse' => [ 'type' => 'structure', 'members' => [ 'ScheduledActions' => [ 'shape' => 'ScheduledActionsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ARN', ], 'members' => [ 'ARN' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'arn', ], ], ], 'ListTagsResponse' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'ListVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'Versions' => [ 'shape' => 'VersionList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListVpcEndpointAccessRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVpcEndpointAccessResponse' => [ 'type' => 'structure', 'required' => [ 'AuthorizedPrincipalList', 'NextToken', ], 'members' => [ 'AuthorizedPrincipalList' => [ 'shape' => 'AuthorizedPrincipalList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListVpcEndpointsForDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVpcEndpointsForDomainResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointSummaryList', 'NextToken', ], 'members' => [ 'VpcEndpointSummaryList' => [ 'shape' => 'VpcEndpointSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListVpcEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListVpcEndpointsResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointSummaryList', 'NextToken', ], 'members' => [ 'VpcEndpointSummaryList' => [ 'shape' => 'VpcEndpointSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LogPublishingOption' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'CloudWatchLogsLogGroupArn', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'LogPublishingOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'LogType', ], 'value' => [ 'shape' => 'LogPublishingOption', ], ], 'LogPublishingOptionsStatus' => [ 'type' => 'structure', 'members' => [ 'Options' => [ 'shape' => 'LogPublishingOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'INDEX_SLOW_LOGS', 'SEARCH_SLOW_LOGS', 'ES_APPLICATION_LOGS', 'AUDIT_LOGS', ], ], 'Long' => [ 'type' => 'long', ], 'MaintenanceStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'TIMED_OUT', ], ], 'MaintenanceStatusMessage' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '^([\\s\\S]*)$', ], 'MaintenanceType' => [ 'type' => 'string', 'enum' => [ 'REBOOT_NODE', 'RESTART_SEARCH_PROCESS', 'RESTART_DASHBOARD', ], ], 'MasterNodeStatus' => [ 'type' => 'string', 'enum' => [ 'Available', 'UnAvailable', ], ], 'MasterUserOptions' => [ 'type' => 'structure', 'members' => [ 'MasterUserARN' => [ 'shape' => 'ARN', ], 'MasterUserName' => [ 'shape' => 'Username', ], 'MasterUserPassword' => [ 'shape' => 'Password', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, ], 'MaximumInstanceCount' => [ 'type' => 'integer', ], 'Message' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'MinimumInstanceCount' => [ 'type' => 'integer', ], 'ModifyingProperties' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'ActiveValue' => [ 'shape' => 'String', ], 'PendingValue' => [ 'shape' => 'String', ], 'ValueType' => [ 'shape' => 'PropertyValueType', ], ], ], 'ModifyingPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModifyingProperties', ], ], 'NaturalLanguageQueryGenerationCurrentState' => [ 'type' => 'string', 'enum' => [ 'NOT_ENABLED', 'ENABLE_COMPLETE', 'ENABLE_IN_PROGRESS', 'ENABLE_FAILED', 'DISABLE_COMPLETE', 'DISABLE_IN_PROGRESS', 'DISABLE_FAILED', ], ], 'NaturalLanguageQueryGenerationDesiredState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'NaturalLanguageQueryGenerationOptionsInput' => [ 'type' => 'structure', 'members' => [ 'DesiredState' => [ 'shape' => 'NaturalLanguageQueryGenerationDesiredState', ], ], ], 'NaturalLanguageQueryGenerationOptionsOutput' => [ 'type' => 'structure', 'members' => [ 'DesiredState' => [ 'shape' => 'NaturalLanguageQueryGenerationDesiredState', ], 'CurrentState' => [ 'shape' => 'NaturalLanguageQueryGenerationCurrentState', ], ], ], 'NextToken' => [ 'type' => 'string', ], 'NodeId' => [ 'type' => 'string', 'max' => 40, 'min' => 10, ], 'NodeStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'StandBy', 'NotAvailable', ], ], 'NodeToNodeEncryptionOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'NodeToNodeEncryptionOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'NodeToNodeEncryptionOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'NodeType' => [ 'type' => 'string', 'enum' => [ 'Data', 'Ultrawarm', 'Master', ], ], 'NonEmptyString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\-\\_\\.]+', ], 'NumberOfAZs' => [ 'type' => 'string', 'pattern' => '^((\\d+)|(NotAvailable))$', ], 'NumberOfNodes' => [ 'type' => 'string', 'pattern' => '^((\\d+)|(NotAvailable))$', ], 'NumberOfShards' => [ 'type' => 'string', 'pattern' => '^((\\d+)|(NotAvailable))$', ], 'OffPeakWindow' => [ 'type' => 'structure', 'members' => [ 'WindowStartTime' => [ 'shape' => 'WindowStartTime', ], ], ], 'OffPeakWindowOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'OffPeakWindow' => [ 'shape' => 'OffPeakWindow', ], ], ], 'OffPeakWindowOptionsStatus' => [ 'type' => 'structure', 'members' => [ 'Options' => [ 'shape' => 'OffPeakWindowOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'OpenSearchPartitionInstanceType' => [ 'type' => 'string', 'enum' => [ 'm3.medium.search', 'm3.large.search', 'm3.xlarge.search', 'm3.2xlarge.search', 'm4.large.search', 'm4.xlarge.search', 'm4.2xlarge.search', 'm4.4xlarge.search', 'm4.10xlarge.search', 'm5.large.search', 'm5.xlarge.search', 'm5.2xlarge.search', 'm5.4xlarge.search', 'm5.12xlarge.search', 'm5.24xlarge.search', 'r5.large.search', 'r5.xlarge.search', 'r5.2xlarge.search', 'r5.4xlarge.search', 'r5.12xlarge.search', 'r5.24xlarge.search', 'c5.large.search', 'c5.xlarge.search', 'c5.2xlarge.search', 'c5.4xlarge.search', 'c5.9xlarge.search', 'c5.18xlarge.search', 't3.nano.search', 't3.micro.search', 't3.small.search', 't3.medium.search', 't3.large.search', 't3.xlarge.search', 't3.2xlarge.search', 'or1.medium.search', 'or1.large.search', 'or1.xlarge.search', 'or1.2xlarge.search', 'or1.4xlarge.search', 'or1.8xlarge.search', 'or1.12xlarge.search', 'or1.16xlarge.search', 'ultrawarm1.medium.search', 'ultrawarm1.large.search', 'ultrawarm1.xlarge.search', 't2.micro.search', 't2.small.search', 't2.medium.search', 'r3.large.search', 'r3.xlarge.search', 'r3.2xlarge.search', 'r3.4xlarge.search', 'r3.8xlarge.search', 'i2.xlarge.search', 'i2.2xlarge.search', 'd2.xlarge.search', 'd2.2xlarge.search', 'd2.4xlarge.search', 'd2.8xlarge.search', 'c4.large.search', 'c4.xlarge.search', 'c4.2xlarge.search', 'c4.4xlarge.search', 'c4.8xlarge.search', 'r4.large.search', 'r4.xlarge.search', 'r4.2xlarge.search', 'r4.4xlarge.search', 'r4.8xlarge.search', 'r4.16xlarge.search', 'i3.large.search', 'i3.xlarge.search', 'i3.2xlarge.search', 'i3.4xlarge.search', 'i3.8xlarge.search', 'i3.16xlarge.search', 'r6g.large.search', 'r6g.xlarge.search', 'r6g.2xlarge.search', 'r6g.4xlarge.search', 'r6g.8xlarge.search', 'r6g.12xlarge.search', 'm6g.large.search', 'm6g.xlarge.search', 'm6g.2xlarge.search', 'm6g.4xlarge.search', 'm6g.8xlarge.search', 'm6g.12xlarge.search', 'c6g.large.search', 'c6g.xlarge.search', 'c6g.2xlarge.search', 'c6g.4xlarge.search', 'c6g.8xlarge.search', 'c6g.12xlarge.search', 'r6gd.large.search', 'r6gd.xlarge.search', 'r6gd.2xlarge.search', 'r6gd.4xlarge.search', 'r6gd.8xlarge.search', 'r6gd.12xlarge.search', 'r6gd.16xlarge.search', 't4g.small.search', 't4g.medium.search', ], ], 'OpenSearchWarmPartitionInstanceType' => [ 'type' => 'string', 'enum' => [ 'ultrawarm1.medium.search', 'ultrawarm1.large.search', 'ultrawarm1.xlarge.search', ], ], 'OptionState' => [ 'type' => 'string', 'enum' => [ 'RequiresIndexDocuments', 'Processing', 'Active', ], ], 'OptionStatus' => [ 'type' => 'structure', 'required' => [ 'CreationDate', 'UpdateDate', 'State', ], 'members' => [ 'CreationDate' => [ 'shape' => 'UpdateTimestamp', ], 'UpdateDate' => [ 'shape' => 'UpdateTimestamp', ], 'UpdateVersion' => [ 'shape' => 'UIntValue', ], 'State' => [ 'shape' => 'OptionState', ], 'PendingDeletion' => [ 'shape' => 'Boolean', ], ], ], 'OutboundConnection' => [ 'type' => 'structure', 'members' => [ 'LocalDomainInfo' => [ 'shape' => 'DomainInformationContainer', ], 'RemoteDomainInfo' => [ 'shape' => 'DomainInformationContainer', ], 'ConnectionId' => [ 'shape' => 'ConnectionId', ], 'ConnectionAlias' => [ 'shape' => 'ConnectionAlias', ], 'ConnectionStatus' => [ 'shape' => 'OutboundConnectionStatus', ], 'ConnectionMode' => [ 'shape' => 'ConnectionMode', ], 'ConnectionProperties' => [ 'shape' => 'ConnectionProperties', ], ], ], 'OutboundConnectionStatus' => [ 'type' => 'structure', 'members' => [ 'StatusCode' => [ 'shape' => 'OutboundConnectionStatusCode', ], 'Message' => [ 'shape' => 'ConnectionStatusMessage', ], ], ], 'OutboundConnectionStatusCode' => [ 'type' => 'string', 'enum' => [ 'VALIDATING', 'VALIDATION_FAILED', 'PENDING_ACCEPTANCE', 'APPROVED', 'PROVISIONING', 'ACTIVE', 'REJECTING', 'REJECTED', 'DELETING', 'DELETED', ], ], 'OutboundConnections' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutboundConnection', ], ], 'OverallChangeStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', ], ], 'OwnerId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '[0-9]+', ], 'PackageDescription' => [ 'type' => 'string', 'max' => 1024, ], 'PackageDetails' => [ 'type' => 'structure', 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', ], 'PackageName' => [ 'shape' => 'PackageName', ], 'PackageType' => [ 'shape' => 'PackageType', ], 'PackageDescription' => [ 'shape' => 'PackageDescription', ], 'PackageStatus' => [ 'shape' => 'PackageStatus', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], 'LastUpdatedAt' => [ 'shape' => 'LastUpdated', ], 'AvailablePackageVersion' => [ 'shape' => 'PackageVersion', ], 'ErrorDetails' => [ 'shape' => 'ErrorDetails', ], 'EngineVersion' => [ 'shape' => 'EngineVersion', ], 'AvailablePluginProperties' => [ 'shape' => 'PluginProperties', ], ], ], 'PackageDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageDetails', ], ], 'PackageID' => [ 'type' => 'string', 'pattern' => '^([FG][0-9]+)$', ], 'PackageName' => [ 'type' => 'string', 'max' => 256, 'min' => 3, 'pattern' => '[a-z][a-z0-9\\-]+', ], 'PackageSource' => [ 'type' => 'structure', 'members' => [ 'S3BucketName' => [ 'shape' => 'S3BucketName', ], 'S3Key' => [ 'shape' => 'S3Key', ], ], ], 'PackageStatus' => [ 'type' => 'string', 'enum' => [ 'COPYING', 'COPY_FAILED', 'VALIDATING', 'VALIDATION_FAILED', 'AVAILABLE', 'DELETING', 'DELETED', 'DELETE_FAILED', ], ], 'PackageType' => [ 'type' => 'string', 'enum' => [ 'TXT-DICTIONARY', 'ZIP-PLUGIN', ], ], 'PackageVersion' => [ 'type' => 'string', ], 'PackageVersionHistory' => [ 'type' => 'structure', 'members' => [ 'PackageVersion' => [ 'shape' => 'PackageVersion', ], 'CommitMessage' => [ 'shape' => 'CommitMessage', ], 'CreatedAt' => [ 'shape' => 'CreatedAt', ], 'PluginProperties' => [ 'shape' => 'PluginProperties', ], ], ], 'PackageVersionHistoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageVersionHistory', ], ], 'Password' => [ 'type' => 'string', 'max' => 128, 'min' => 8, 'pattern' => '.*', 'sensitive' => true, ], 'PluginClassName' => [ 'type' => 'string', 'max' => 1024, ], 'PluginDescription' => [ 'type' => 'string', 'max' => 1024, ], 'PluginName' => [ 'type' => 'string', 'max' => 1024, ], 'PluginProperties' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PluginName', ], 'Description' => [ 'shape' => 'PluginDescription', ], 'Version' => [ 'shape' => 'PluginVersion', ], 'ClassName' => [ 'shape' => 'PluginClassName', ], 'UncompressedSizeInBytes' => [ 'shape' => 'UncompressedPluginSizeInBytes', ], ], ], 'PluginVersion' => [ 'type' => 'string', 'max' => 1024, ], 'PolicyDocument' => [ 'type' => 'string', 'max' => 102400, 'min' => 0, 'pattern' => '.*', ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'AWS_ACCOUNT', 'AWS_SERVICE', ], ], 'PropertyValueType' => [ 'type' => 'string', 'enum' => [ 'PLAIN_TEXT', 'STRINGIFIED_JSON', ], ], 'PurchaseReservedInstanceOfferingRequest' => [ 'type' => 'structure', 'required' => [ 'ReservedInstanceOfferingId', 'ReservationName', ], 'members' => [ 'ReservedInstanceOfferingId' => [ 'shape' => 'GUID', ], 'ReservationName' => [ 'shape' => 'ReservationToken', ], 'InstanceCount' => [ 'shape' => 'InstanceCount', ], ], ], 'PurchaseReservedInstanceOfferingResponse' => [ 'type' => 'structure', 'members' => [ 'ReservedInstanceId' => [ 'shape' => 'GUID', ], 'ReservationName' => [ 'shape' => 'ReservationToken', ], ], ], 'RecurringCharge' => [ 'type' => 'structure', 'members' => [ 'RecurringChargeAmount' => [ 'shape' => 'Double', ], 'RecurringChargeFrequency' => [ 'shape' => 'String', ], ], ], 'RecurringChargeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecurringCharge', ], ], 'ReferencePath' => [ 'type' => 'string', ], 'Region' => [ 'type' => 'string', 'max' => 30, 'min' => 5, 'pattern' => '[a-z][a-z0-9\\-]+', ], 'RejectInboundConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionId', ], 'members' => [ 'ConnectionId' => [ 'shape' => 'ConnectionId', 'location' => 'uri', 'locationName' => 'ConnectionId', ], ], ], 'RejectInboundConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'InboundConnection', ], ], ], 'RemoveTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ARN', 'TagKeys', ], 'members' => [ 'ARN' => [ 'shape' => 'ARN', ], 'TagKeys' => [ 'shape' => 'StringList', ], ], ], 'RequestId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([\\s\\S]*)$', ], 'ReservationToken' => [ 'type' => 'string', 'max' => 64, 'min' => 5, 'pattern' => '.*', ], 'ReservedInstance' => [ 'type' => 'structure', 'members' => [ 'ReservationName' => [ 'shape' => 'ReservationToken', ], 'ReservedInstanceId' => [ 'shape' => 'GUID', ], 'BillingSubscriptionId' => [ 'shape' => 'Long', ], 'ReservedInstanceOfferingId' => [ 'shape' => 'String', ], 'InstanceType' => [ 'shape' => 'OpenSearchPartitionInstanceType', ], 'StartTime' => [ 'shape' => 'UpdateTimestamp', ], 'Duration' => [ 'shape' => 'Integer', ], 'FixedPrice' => [ 'shape' => 'Double', ], 'UsagePrice' => [ 'shape' => 'Double', ], 'CurrencyCode' => [ 'shape' => 'String', ], 'InstanceCount' => [ 'shape' => 'Integer', ], 'State' => [ 'shape' => 'String', ], 'PaymentOption' => [ 'shape' => 'ReservedInstancePaymentOption', ], 'RecurringCharges' => [ 'shape' => 'RecurringChargeList', ], ], ], 'ReservedInstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservedInstance', ], ], 'ReservedInstanceOffering' => [ 'type' => 'structure', 'members' => [ 'ReservedInstanceOfferingId' => [ 'shape' => 'GUID', ], 'InstanceType' => [ 'shape' => 'OpenSearchPartitionInstanceType', ], 'Duration' => [ 'shape' => 'Integer', ], 'FixedPrice' => [ 'shape' => 'Double', ], 'UsagePrice' => [ 'shape' => 'Double', ], 'CurrencyCode' => [ 'shape' => 'String', ], 'PaymentOption' => [ 'shape' => 'ReservedInstancePaymentOption', ], 'RecurringCharges' => [ 'shape' => 'RecurringChargeList', ], ], ], 'ReservedInstanceOfferingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservedInstanceOffering', ], ], 'ReservedInstancePaymentOption' => [ 'type' => 'string', 'enum' => [ 'ALL_UPFRONT', 'PARTIAL_UPFRONT', 'NO_UPFRONT', ], ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'RevokeVpcEndpointAccessRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Account', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Account' => [ 'shape' => 'AWSAccount', ], ], ], 'RevokeVpcEndpointAccessResponse' => [ 'type' => 'structure', 'members' => [], ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:(aws|aws\\-cn|aws\\-us\\-gov|aws\\-iso|aws\\-iso\\-b):iam::[0-9]+:role\\/.*', ], 'RolesKey' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'RollbackOnDisable' => [ 'type' => 'string', 'enum' => [ 'NO_ROLLBACK', 'DEFAULT_ROLLBACK', ], ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'S3GlueDataCatalog' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'RoleArn', ], ], ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'SAMLEntityId' => [ 'type' => 'string', 'max' => 512, 'min' => 8, ], 'SAMLIdp' => [ 'type' => 'structure', 'required' => [ 'MetadataContent', 'EntityId', ], 'members' => [ 'MetadataContent' => [ 'shape' => 'SAMLMetadata', ], 'EntityId' => [ 'shape' => 'SAMLEntityId', ], ], ], 'SAMLMetadata' => [ 'type' => 'string', 'max' => 1048576, 'min' => 1, ], 'SAMLOptionsInput' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'Idp' => [ 'shape' => 'SAMLIdp', ], 'MasterUserName' => [ 'shape' => 'Username', ], 'MasterBackendRole' => [ 'shape' => 'BackendRole', ], 'SubjectKey' => [ 'shape' => 'String', ], 'RolesKey' => [ 'shape' => 'String', ], 'SessionTimeoutMinutes' => [ 'shape' => 'IntegerClass', ], ], ], 'SAMLOptionsOutput' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'Idp' => [ 'shape' => 'SAMLIdp', ], 'SubjectKey' => [ 'shape' => 'String', ], 'RolesKey' => [ 'shape' => 'String', ], 'SessionTimeoutMinutes' => [ 'shape' => 'IntegerClass', ], ], ], 'ScheduleAt' => [ 'type' => 'string', 'enum' => [ 'NOW', 'TIMESTAMP', 'OFF_PEAK_WINDOW', ], ], 'ScheduledAction' => [ 'type' => 'structure', 'required' => [ 'Id', 'Type', 'Severity', 'ScheduledTime', ], 'members' => [ 'Id' => [ 'shape' => 'String', ], 'Type' => [ 'shape' => 'ActionType', ], 'Severity' => [ 'shape' => 'ActionSeverity', ], 'ScheduledTime' => [ 'shape' => 'Long', ], 'Description' => [ 'shape' => 'String', ], 'ScheduledBy' => [ 'shape' => 'ScheduledBy', ], 'Status' => [ 'shape' => 'ActionStatus', ], 'Mandatory' => [ 'shape' => 'Boolean', ], 'Cancellable' => [ 'shape' => 'Boolean', ], ], ], 'ScheduledActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScheduledAction', ], ], 'ScheduledAutoTuneActionType' => [ 'type' => 'string', 'enum' => [ 'JVM_HEAP_SIZE_TUNING', 'JVM_YOUNG_GEN_TUNING', ], ], 'ScheduledAutoTuneDescription' => [ 'type' => 'string', ], 'ScheduledAutoTuneDetails' => [ 'type' => 'structure', 'members' => [ 'Date' => [ 'shape' => 'AutoTuneDate', ], 'ActionType' => [ 'shape' => 'ScheduledAutoTuneActionType', ], 'Action' => [ 'shape' => 'ScheduledAutoTuneDescription', ], 'Severity' => [ 'shape' => 'ScheduledAutoTuneSeverityType', ], ], ], 'ScheduledAutoTuneSeverityType' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', ], ], 'ScheduledBy' => [ 'type' => 'string', 'enum' => [ 'CUSTOMER', 'SYSTEM', ], ], 'ServiceSoftwareOptions' => [ 'type' => 'structure', 'members' => [ 'CurrentVersion' => [ 'shape' => 'String', ], 'NewVersion' => [ 'shape' => 'String', ], 'UpdateAvailable' => [ 'shape' => 'Boolean', ], 'Cancellable' => [ 'shape' => 'Boolean', ], 'UpdateStatus' => [ 'shape' => 'DeploymentStatus', ], 'Description' => [ 'shape' => 'String', ], 'AutomatedUpdateDate' => [ 'shape' => 'DeploymentCloseDateTimeStamp', ], 'OptionalDeployment' => [ 'shape' => 'Boolean', ], ], ], 'ServiceUrl' => [ 'type' => 'string', ], 'SkipUnavailableStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'SlotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Long', ], ], 'SlotNotAvailableException' => [ 'type' => 'structure', 'members' => [ 'SlotSuggestions' => [ 'shape' => 'SlotList', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'SnapshotOptions' => [ 'type' => 'structure', 'members' => [ 'AutomatedSnapshotStartHour' => [ 'shape' => 'IntegerClass', ], ], ], 'SnapshotOptionsStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'SnapshotOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'SoftwareUpdateOptions' => [ 'type' => 'structure', 'members' => [ 'AutoSoftwareUpdateEnabled' => [ 'shape' => 'Boolean', ], ], ], 'SoftwareUpdateOptionsStatus' => [ 'type' => 'structure', 'members' => [ 'Options' => [ 'shape' => 'SoftwareUpdateOptions', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'StartAt' => [ 'type' => 'timestamp', ], 'StartDomainMaintenanceRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Action', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Action' => [ 'shape' => 'MaintenanceType', ], 'NodeId' => [ 'shape' => 'NodeId', ], ], ], 'StartDomainMaintenanceResponse' => [ 'type' => 'structure', 'members' => [ 'MaintenanceId' => [ 'shape' => 'RequestId', ], ], ], 'StartServiceSoftwareUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'ScheduleAt' => [ 'shape' => 'ScheduleAt', ], 'DesiredStartTime' => [ 'shape' => 'Long', ], ], ], 'StartServiceSoftwareUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'ServiceSoftwareOptions' => [ 'shape' => 'ServiceSoftwareOptions', ], ], ], 'StartTimeHours' => [ 'type' => 'long', 'max' => 23, 'min' => 0, ], 'StartTimeMinutes' => [ 'type' => 'long', 'max' => 59, 'min' => 0, ], 'StartTimestamp' => [ 'type' => 'timestamp', ], 'StorageSubTypeName' => [ 'type' => 'string', ], 'StorageType' => [ 'type' => 'structure', 'members' => [ 'StorageTypeName' => [ 'shape' => 'StorageTypeName', ], 'StorageSubTypeName' => [ 'shape' => 'StorageSubTypeName', ], 'StorageTypeLimits' => [ 'shape' => 'StorageTypeLimitList', ], ], ], 'StorageTypeLimit' => [ 'type' => 'structure', 'members' => [ 'LimitName' => [ 'shape' => 'LimitName', ], 'LimitValues' => [ 'shape' => 'LimitValueList', ], ], ], 'StorageTypeLimitList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageTypeLimit', ], ], 'StorageTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageType', ], ], 'StorageTypeName' => [ 'type' => 'string', ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SubjectKey' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'TLSSecurityPolicy' => [ 'type' => 'string', 'enum' => [ 'Policy-Min-TLS-1-0-2019-07', 'Policy-Min-TLS-1-2-2019-07', 'Policy-Min-TLS-1-2-PFS-2023-10', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*', ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*', ], 'TimeUnit' => [ 'type' => 'string', 'enum' => [ 'HOURS', ], ], 'TotalNumberOfStages' => [ 'type' => 'integer', ], 'UIntValue' => [ 'type' => 'integer', 'min' => 0, ], 'UncompressedPluginSizeInBytes' => [ 'type' => 'long', ], 'UpdateDataSourceRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'Name', 'DataSourceType', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'Name' => [ 'shape' => 'DataSourceName', 'location' => 'uri', 'locationName' => 'DataSourceName', ], 'DataSourceType' => [ 'shape' => 'DataSourceType', ], 'Description' => [ 'shape' => 'DataSourceDescription', ], 'Status' => [ 'shape' => 'DataSourceStatus', ], ], ], 'UpdateDataSourceResponse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], ], 'UpdateDomainConfigRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ClusterConfig' => [ 'shape' => 'ClusterConfig', ], 'EBSOptions' => [ 'shape' => 'EBSOptions', ], 'SnapshotOptions' => [ 'shape' => 'SnapshotOptions', ], 'VPCOptions' => [ 'shape' => 'VPCOptions', ], 'CognitoOptions' => [ 'shape' => 'CognitoOptions', ], 'AdvancedOptions' => [ 'shape' => 'AdvancedOptions', ], 'AccessPolicies' => [ 'shape' => 'PolicyDocument', ], 'IPAddressType' => [ 'shape' => 'IPAddressType', ], 'LogPublishingOptions' => [ 'shape' => 'LogPublishingOptions', ], 'EncryptionAtRestOptions' => [ 'shape' => 'EncryptionAtRestOptions', ], 'DomainEndpointOptions' => [ 'shape' => 'DomainEndpointOptions', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'NodeToNodeEncryptionOptions', ], 'AdvancedSecurityOptions' => [ 'shape' => 'AdvancedSecurityOptionsInput', ], 'AutoTuneOptions' => [ 'shape' => 'AutoTuneOptions', ], 'DryRun' => [ 'shape' => 'DryRun', ], 'DryRunMode' => [ 'shape' => 'DryRunMode', ], 'OffPeakWindowOptions' => [ 'shape' => 'OffPeakWindowOptions', ], 'SoftwareUpdateOptions' => [ 'shape' => 'SoftwareUpdateOptions', ], 'AIMLOptions' => [ 'shape' => 'AIMLOptionsInput', ], ], ], 'UpdateDomainConfigResponse' => [ 'type' => 'structure', 'required' => [ 'DomainConfig', ], 'members' => [ 'DomainConfig' => [ 'shape' => 'DomainConfig', ], 'DryRunResults' => [ 'shape' => 'DryRunResults', ], 'DryRunProgressStatus' => [ 'shape' => 'DryRunProgressStatus', ], ], ], 'UpdatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageID', 'PackageSource', ], 'members' => [ 'PackageID' => [ 'shape' => 'PackageID', ], 'PackageSource' => [ 'shape' => 'PackageSource', ], 'PackageDescription' => [ 'shape' => 'PackageDescription', ], 'CommitMessage' => [ 'shape' => 'CommitMessage', ], ], ], 'UpdatePackageResponse' => [ 'type' => 'structure', 'members' => [ 'PackageDetails' => [ 'shape' => 'PackageDetails', ], ], ], 'UpdateScheduledActionRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'ActionID', 'ActionType', 'ScheduleAt', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', 'location' => 'uri', 'locationName' => 'DomainName', ], 'ActionID' => [ 'shape' => 'String', ], 'ActionType' => [ 'shape' => 'ActionType', ], 'ScheduleAt' => [ 'shape' => 'ScheduleAt', ], 'DesiredStartTime' => [ 'shape' => 'Long', ], ], ], 'UpdateScheduledActionResponse' => [ 'type' => 'structure', 'members' => [ 'ScheduledAction' => [ 'shape' => 'ScheduledAction', ], ], ], 'UpdateTimestamp' => [ 'type' => 'timestamp', ], 'UpdateVpcEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'VpcEndpointId', 'VpcOptions', ], 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'VpcOptions' => [ 'shape' => 'VPCOptions', ], ], ], 'UpdateVpcEndpointResponse' => [ 'type' => 'structure', 'required' => [ 'VpcEndpoint', ], 'members' => [ 'VpcEndpoint' => [ 'shape' => 'VpcEndpoint', ], ], ], 'UpgradeDomainRequest' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'TargetVersion', ], 'members' => [ 'DomainName' => [ 'shape' => 'DomainName', ], 'TargetVersion' => [ 'shape' => 'VersionString', ], 'PerformCheckOnly' => [ 'shape' => 'Boolean', ], 'AdvancedOptions' => [ 'shape' => 'AdvancedOptions', ], ], ], 'UpgradeDomainResponse' => [ 'type' => 'structure', 'members' => [ 'UpgradeId' => [ 'shape' => 'String', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'TargetVersion' => [ 'shape' => 'VersionString', ], 'PerformCheckOnly' => [ 'shape' => 'Boolean', ], 'AdvancedOptions' => [ 'shape' => 'AdvancedOptions', ], 'ChangeProgressDetails' => [ 'shape' => 'ChangeProgressDetails', ], ], ], 'UpgradeHistory' => [ 'type' => 'structure', 'members' => [ 'UpgradeName' => [ 'shape' => 'UpgradeName', ], 'StartTimestamp' => [ 'shape' => 'StartTimestamp', ], 'UpgradeStatus' => [ 'shape' => 'UpgradeStatus', ], 'StepsList' => [ 'shape' => 'UpgradeStepsList', ], ], ], 'UpgradeHistoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpgradeHistory', ], ], 'UpgradeName' => [ 'type' => 'string', ], 'UpgradeStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCEEDED', 'SUCCEEDED_WITH_ISSUES', 'FAILED', ], ], 'UpgradeStep' => [ 'type' => 'string', 'enum' => [ 'PRE_UPGRADE_CHECK', 'SNAPSHOT', 'UPGRADE', ], ], 'UpgradeStepItem' => [ 'type' => 'structure', 'members' => [ 'UpgradeStep' => [ 'shape' => 'UpgradeStep', ], 'UpgradeStepStatus' => [ 'shape' => 'UpgradeStatus', ], 'Issues' => [ 'shape' => 'Issues', ], 'ProgressPercent' => [ 'shape' => 'Double', ], ], ], 'UpgradeStepsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpgradeStepItem', ], ], 'UserPoolId' => [ 'type' => 'string', 'max' => 55, 'min' => 1, 'pattern' => '[\\w-]+_[0-9a-zA-Z]+', ], 'Username' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '.*', 'sensitive' => true, ], 'VPCDerivedInfo' => [ 'type' => 'structure', 'members' => [ 'VPCId' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'StringList', ], 'AvailabilityZones' => [ 'shape' => 'StringList', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], ], ], 'VPCDerivedInfoStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'VPCDerivedInfo', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'VPCOptions' => [ 'type' => 'structure', 'members' => [ 'SubnetIds' => [ 'shape' => 'StringList', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValidationFailure' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'ValidationFailures' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationFailure', ], ], 'ValueStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'min' => 1, ], 'VersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VersionString', ], ], 'VersionStatus' => [ 'type' => 'structure', 'required' => [ 'Options', 'Status', ], 'members' => [ 'Options' => [ 'shape' => 'VersionString', ], 'Status' => [ 'shape' => 'OptionStatus', ], ], ], 'VersionString' => [ 'type' => 'string', 'max' => 18, 'min' => 14, 'pattern' => '^Elasticsearch_[0-9]{1}\\.[0-9]{1,2}$|^OpenSearch_[0-9]{1,2}\\.[0-9]{1,2}$', ], 'VolumeSize' => [ 'type' => 'string', ], 'VolumeType' => [ 'type' => 'string', 'enum' => [ 'standard', 'gp2', 'io1', 'gp3', ], ], 'VpcEndpoint' => [ 'type' => 'structure', 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'VpcEndpointOwner' => [ 'shape' => 'AWSAccount', ], 'DomainArn' => [ 'shape' => 'DomainArn', ], 'VpcOptions' => [ 'shape' => 'VPCDerivedInfo', ], 'Status' => [ 'shape' => 'VpcEndpointStatus', ], 'Endpoint' => [ 'shape' => 'Endpoint', ], ], ], 'VpcEndpointError' => [ 'type' => 'structure', 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'ErrorCode' => [ 'shape' => 'VpcEndpointErrorCode', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'VpcEndpointErrorCode' => [ 'type' => 'string', 'enum' => [ 'ENDPOINT_NOT_FOUND', 'SERVER_ERROR', ], ], 'VpcEndpointErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcEndpointError', ], ], 'VpcEndpointId' => [ 'type' => 'string', 'max' => 256, 'min' => 5, 'pattern' => '^aos-[a-zA-Z0-9]*$', ], 'VpcEndpointIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcEndpointId', ], ], 'VpcEndpointStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATE_FAILED', 'ACTIVE', 'UPDATING', 'UPDATE_FAILED', 'DELETING', 'DELETE_FAILED', ], ], 'VpcEndpointSummary' => [ 'type' => 'structure', 'members' => [ 'VpcEndpointId' => [ 'shape' => 'VpcEndpointId', ], 'VpcEndpointOwner' => [ 'shape' => 'String', ], 'DomainArn' => [ 'shape' => 'DomainArn', ], 'Status' => [ 'shape' => 'VpcEndpointStatus', ], ], ], 'VpcEndpointSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcEndpointSummary', ], ], 'VpcEndpoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcEndpoint', ], ], 'WindowStartTime' => [ 'type' => 'structure', 'required' => [ 'Hours', 'Minutes', ], 'members' => [ 'Hours' => [ 'shape' => 'StartTimeHours', ], 'Minutes' => [ 'shape' => 'StartTimeMinutes', ], ], ], 'ZoneAwarenessConfig' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZoneCount' => [ 'shape' => 'IntegerClass', ], ], ], 'ZoneStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'StandBy', 'NotAvailable', ], ], ],];
